<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ChatPostsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('chat_posts')->insert([
            'channel_id' => 1,
            'vendor_user_id' => 1,
            'partner_user_id' => null,
            'content' => '株式会社Aチャンネルの投稿1',
            'deleted' => false,
            'parent_id' => null,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        DB::table('chat_posts')->insert([
            'channel_id' => 1,
            'vendor_user_id' => 1,
            'partner_user_id' => null,
            'content' => '株式会社Aチャンネルの投稿2',
            'deleted' => false,
            'parent_id' => null,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        //スレッドの作成
        DB::table('chat_posts')->insert([
            'channel_id' => 1,
            'vendor_user_id' => 1,
            'partner_user_id' => null,
            'content' => '株式会社Aチャンネルの投稿1-1',
            'deleted' => false,
            'parent_id' => 1,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        DB::table('chat_posts')->insert([
            'channel_id' => 1,
            'vendor_user_id' => 1,
            'partner_user_id' => null,
            'content' => '株式会社Aチャンネルの投稿1-2',
            'deleted' => false,
            'parent_id' => 1,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // id=5
        DB::table('chat_posts')->insert([
            'channel_id' => 3,
            'vendor_user_id' => null,
            'partner_user_id' => 1,
            'content' => 'パートナーユーザーid=1からの投稿-2',
            'deleted' => false,
            'parent_id' => null,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // id=6
        DB::table('chat_posts')->insert([
            'channel_id' => 3,
            'vendor_user_id' => null,
            'partner_user_id' => 1,
            'content' => 'id=5のスレッドにぶら下げ',
            'deleted' => false,
            'parent_id' => 5,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // id=7
        DB::table('chat_posts')->insert([
            'channel_id' => 3,
            'vendor_user_id' => null,
            'partner_user_id' => 1,
            'content' => '@[channel]%text:all:channel% チャンネルへメンション',
            'deleted' => false,
            'parent_id' => null,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // id=8
        DB::table('chat_posts')->insert([
            'channel_id' => 3,
            'vendor_user_id' => null,
            'partner_user_id' => 1,
            'content' => '@[パートナーテスト株式会社1]%text:パートナーテスト株式会社1:partner% Pユーザから所属パートナーへのメンション',
            'deleted' => false,
            'parent_id' => null,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // id=9
        DB::table('chat_posts')->insert([
            'channel_id' => 3,
            'vendor_user_id' => null,
            'partner_user_id' => 1,
            'content' => 'Pユーザからベンダーへのメンション @[テスト株式会社]%text:テスト株式会社:vendor%',
            'deleted' => false,
            'parent_id' => null,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // id=10
        DB::table('chat_posts')->insert([
            'channel_id' => 3,
            'vendor_user_id' => null,
            'partner_user_id' => 1,
            'content' => 'PユーザからVユーザへメンション @[ken.fukumori]%text:ken.fukumori:uuid=v1% @[2ken.fukumori]%text:2ken.fukumori:uuid=v2%',
            'deleted' => false,
            'parent_id' => null,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // 全パートナーchへ投稿
        // id=11
        DB::table('chat_posts')->insert([
            'channel_id' => 4,
            'vendor_user_id' => 1,
            'partner_user_id' => null,
            'content' => '「全てのパートナーCh」への投稿',
            'deleted' => false,
            'parent_id' => null,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // id=12
        DB::table('chat_posts')->insert([
            'channel_id' => 4,
            'vendor_user_id' => 1,
            'partner_user_id' => null,
            'content' => '@[channel]%text:all:channel% 全てのパートナースレッド内でchannelメンション',
            'deleted' => false,
            'parent_id' => 11,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // id=13
        DB::table('chat_posts')->insert([
            'channel_id' => 4,
            'vendor_user_id' => 1,
            'partner_user_id' => null,
            'content' => '@[テスト株式会社]%text:テスト株式会社:vendor% Vユーザ自社メンション',
            'deleted' => false,
            'parent_id' => 11,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // id=14
        DB::table('chat_posts')->insert([
            'channel_id' => 3,
            'vendor_user_id' => 1,
            'partner_user_id' => null,
            'content' => 'Pユーザのスレッドへぶら下げ',
            'deleted' => false,
            'parent_id' => 5,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // id=15
        DB::table('chat_posts')->insert([
            'channel_id' => 3,
            'vendor_user_id' => 1,
            'partner_user_id' => null,
            'content' => 'Vユーザからパートナーテスト(株)1へポスト',
            'deleted' => false,
            'parent_id' => null,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // id=16
        DB::table('chat_posts')->insert([
            'channel_id' => 3,
            'vendor_user_id' => 1,
            'partner_user_id' => null,
            'content' => '@[channel]%text:all:channel% チャンネルメンション',
            'deleted' => false,
            'parent_id' => 15,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // id=17
        DB::table('chat_posts')->insert([
            'channel_id' => 3,
            'vendor_user_id' => 1,
            'partner_user_id' => null,
            'content' => '@[パートナーテスト株式会社1]%text:パートナーテスト株式会社1:partner% パートナー企業へメンション',
            'deleted' => false,
            'parent_id' => 15,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        // id=18
        DB::table('chat_posts')->insert([
            'channel_id' => 3,
            'vendor_user_id' => 1,
            'partner_user_id' => null,
            'content' => '@[ログインパートナー太郎]%text:ログインパートナー太郎:uuid=p1% Pユーザへメンション',
            'deleted' => false,
            'parent_id' => 15,
            'last_reply_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
