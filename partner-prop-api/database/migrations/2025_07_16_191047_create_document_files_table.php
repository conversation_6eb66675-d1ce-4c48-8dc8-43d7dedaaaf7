<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_files', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique()->comment('Unique ID across all systems');

            $table->unsignedBigInteger('vendor_id')->comment('Vendor ID');
            $table->unsignedBigInteger('portal_id')->comment('Portal ID');
            $table->unsignedBigInteger('file_id')->comment('Document ID');
            $table->string('s3_path')->comment('S3 path of the file ');
            $table->string('caption')->nullable()->comment('caption document file');
            $table->enum('type', ['draft', 'publish'])->default('draft')->comment('Status document file');
            $table->timestamps();
            $table->foreign('vendor_id')->references('id')->on('vendors')->onDelete('cascade');
            $table->foreign('portal_id')->references('id')->on('portals')->onDelete('cascade');
            $table->foreign('file_id')->references('id')->on('files')->onDelete('cascade');
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_files');
    }
};
