<?php

namespace App\Services\Partner;

use App\Const\StatusConst;
use App\Models\LinkRequest;
use App\Models\Partner;
use App\Repositories\Accounts\VendorManagedPartnerRepository;
use App\Repositories\Chats\ChannelsRepositoryInterface;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class LinkRequestService
{

    public function __construct(
        private readonly ChannelsRepositoryInterface $channelsRepository
    )
    {}

    public function getList(int $partner_user_id): Collection
    {
        return DB::table('link_requests')
            ->select([
                "vendors.id as vendor_id",
                "link_requests.id as link_request_id",
                "link_requests.message as message",
                "link_requests.is_double_check",
                "vendors.vendor_collaboration_id",
                "vendors.name as vendor_name",
                "vendor_linked_partners.link_status",
                "vendors.vendor_logo"
            ])
            ->join('vendor_linked_partners', 'vendor_linked_partners.id', '=', 'link_requests.vendor_linked_partner_id')
            ->join('vendor_managed_partners', 'vendor_managed_partners.id', '=', 'vendor_linked_partners.managed_partner_id')
            ->join('vendors', 'vendors.id', '=', 'vendor_managed_partners.vendor_id')
            ->where('link_requests.partner_user_id', $partner_user_id)
            ->whereIn('vendor_linked_partners.link_status', [StatusConst::LINK_STATUS['LINK_REQUEST']['id'], StatusConst::LINK_STATUS['LINK_VENDOR_REVIEW']['id']])
            ->orderBy('link_requests.created_at', 'desc')
            ->get();
    }

    public function accept(int $link_request_id, int $partner_id): void
    {
        /** @var  $link_request LinkRequest */
        $link_request = LinkRequest::query()->findOrFail($link_request_id);
        $vendor_linked_partner = $link_request->vendor_linked_partner;

        DB::transaction(function () use ($link_request, $vendor_linked_partner, $partner_id) {
            // 認証したPartnerと対象ベンダーが連携済なら追加連携NG（管理パートナーで判定可能）
            if ($vendor_linked_partner->link_status === StatusConst::LINK_STATUS['LINK_ACTIVE']['id']) {
                throw new \Exception('This vendor is already linked to the partner.');
            }
            $vendor_linked_partner->partner_id = $partner_id;
            if ($link_request->is_double_check) {
                $vendor_linked_partner->changeVendorReview();
            } else {
                $vendor_linked_partner->changeActive();
                /** @var Partner $partner */
                $partner = Partner::query()->find($partner_id);
                $this->channelsRepository->createChannel($vendor_linked_partner->vendor_managed_partner->vendor, $partner);
            }
            $vendor_linked_partner->save();
            $link_request->dummy_partner()->delete();
        });
    }

    public function deny(int $link_request_id): void
    {
        /** @var  $link_request LinkRequest */
        $link_request = LinkRequest::query()->findOrFail($link_request_id);
        $vendor_linked_partner = $link_request->vendor_linked_partner;
        $vendor_linked_partner->link_status = StatusConst::LINK_STATUS['LINK_REJECT']['id'];
        $vendor_linked_partner->save();
    }
}
