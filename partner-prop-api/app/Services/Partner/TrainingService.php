<?php

namespace App\Services\Partner;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Ramsey\Uuid\Uuid;
use App\Repositories\Accounts\VendorLinkedPartnerRepository;
use App\Exceptions\ServiceException;
use App\Const\StatusConst;
use App\Repositories\Accounts\PartnerTeamMemberRepository;
use Exception;
use Aws\S3\S3Client;
use Storage;

class TrainingService
{
  private static function getVendorIdAndPartnerId(int $vendor_linked_partner_id)
  {
    $vendor_linked_partner = VendorLinkedPartnerRepository::fetchByVendorLinkedPartnerId($vendor_linked_partner_id);

    if ($vendor_linked_partner === null) {
      throw new ServiceException('Error: does not link to the vendor', 400);
    }

    $vendor_id = DB::table('vendor_managed_partners')
      ->where('id', $vendor_linked_partner->managed_partner_id)
      ->value('vendor_id');

    if ($vendor_id === null) {
      throw new ServiceException('Error: vendor does not exist', 400);
    }

    return [$vendor_id, $vendor_linked_partner->partner_id];
  }

  public static function getPermissionWithWebTest($user, $web_test_id)
  {
    $training_id = DB::table('training_contents')
      ->where('web_test_id', $web_test_id)
      ->get()
      ->pluck('training_id');

    if ($training_id->isEmpty()) {
      return false;
    }

    $partner_user_permissions = DB::table("shared_training_with_partner_users")
      ->where("partner_user_id", $user->id)
      ->whereIn("training_id", $training_id)
      ->exists();

    if ($partner_user_permissions) {
      return true;
    }

    $partner_user_delete_permissions = DB::table("unshared_training_with_partner_users")
      ->where("partner_user_id", $user->id)
      ->whereIn("training_id", $training_id)
      ->exists();

    if ($partner_user_delete_permissions) {
      return false;
    }

    $partner_permissions = DB::table("shared_training_with_partners")
      ->where("partner_id", $user->partner_id)
      ->whereIn("training_id", $training_id)
      ->exists();

    if ($partner_permissions) {
      return true;
    }

    $all_partner_permissions = DB::table("shared_training_with_all_partners")
      ->whereIn("training_id", $training_id)
      ->exists();

    if ($all_partner_permissions) {
      return true;
    }

    return false;
  }

  private static function isShared($partner_id, $partner_user_id, $training_id)
  {
    // ユーザーがパートナーチームに所属しているか確認
    $partner_team_member = PartnerTeamMemberRepository::fetchByPartnerIdAndPartnerUserId($partner_id, $partner_user_id);
    if ($partner_team_member === null)
      return false;

    $partner_user_delete_permissions = DB::table("unshared_training_with_partner_users")
      ->where("partner_id", $partner_id)
      ->where("partner_user_id", $partner_user_id)
      ->where("training_id", $training_id)
      ->exists();

    // ユーザーへの削除権限がある場合はfalseを返す
    if ($partner_user_delete_permissions)
      return false;

    $partner_user_permissions = DB::table("shared_training_with_partner_users")
      ->where("partner_id", $partner_id)
      ->where("partner_user_id", $partner_user_id)
      ->where("training_id", $training_id)
      ->exists();

    // ユーザーへの共有権限がある場合はtrueを返す
    if ($partner_user_permissions)
      return true;

    $partner_permissions = DB::table("shared_training_with_partners")
      ->where("partner_id", $partner_id)
      ->where("training_id", $training_id)
      ->exists();

    // パートナーへの共有権限がある場合はtrueを返す
    if ($partner_permissions)
      return true;

    $all_partner_permissions = DB::table("shared_training_with_all_partners")
      ->where("training_id", $training_id)
      ->exists();

    // パートナーへの共有権限がある場合はtrueを返す
    if ($all_partner_permissions)
      return true;

    // 全てない場合はfalseを返す
    return false;
  }
  public static function getTrainingListByAttend(int $vendor_linked_partner_id)
  {
    $user = Auth::guard('partner_users')->user();
    [$vendor_id, $partner_id] = self::getVendorIdAndPartnerId($vendor_linked_partner_id);

    // ユーザーがパートナーチームに所属しているか確認
    $partner_team_member = PartnerTeamMemberRepository::fetchByPartnerIdAndPartnerUserId($partner_id, $user->id);
    if ($partner_team_member === null) {
      throw new ServiceException('Error: does not link to this partner', 400);
    }

    $training_ids = array();

    $partner_user_permissions = DB::table("shared_training_with_partner_users")
      ->where("partner_id", $partner_id)
      ->where("partner_user_id", $user->id)
      ->get()
      ->pluck("training_id")
      ->toArray();

    $partner_permissions = DB::table("shared_training_with_partners")
      ->where("partner_id", $partner_id)
      ->get()
      ->pluck("training_id")
      ->toArray();

    $all_partner_permissions = DB::table("shared_training_with_all_partners")
      ->get()
      ->pluck("training_id")
      ->toArray();

    $partner_user_delete_permissions = DB::table("unshared_training_with_partner_users")
      ->where("partner_id", $partner_id)
      ->where("partner_user_id", $user->id)
      ->get()
      ->pluck("training_id")
      ->toArray();

    $training_ids = array_merge($training_ids, $all_partner_permissions);
    $training_ids = array_merge($training_ids, $partner_permissions);
    $training_ids = array_merge($training_ids, $partner_user_permissions);
    $training_ids = array_diff($training_ids, $partner_user_delete_permissions);
    $trainings = DB::table("trainings")
      ->whereIn("id", $training_ids)
      ->where("vendor_id", $vendor_id)
      ->where("publish_status", "published")
      ->select(
        'id',
        'training_record_id',
        'training_title',
        'training_description',
        'training_thumbnail_path',
        'publish_status',
        'created_at',
        'updated_at'
      )
      ->get();

    // 返却用の配列を初期化
    $res = [
      'unattended' => collect(),
      'progress' => collect(),
      'completed' => collect()
    ];


    foreach ($trainings as $training) {
      if ($training->training_thumbnail_path !== null) {
        // S3から一時URLを取得
        $training->training_thumbnail_path = Storage::disk('s3-training')->temporaryUrl($training->training_thumbnail_path, now()->addMinutes(20));
      }

      // 受講状態の確認
      $attendance = DB::table('training_attendances')
        ->where('partner_id', $partner_id)
        ->where('partner_user_id', $user->id)
        ->where('training_id', $training->id)
        ->first();

      if ($attendance === null) {
        $res['unattended']->push($training);
      } else if ($attendance->attendance_status === 0) {

        // コンテンツの進行度を確認
        $contents = DB::table('training_contents')
          ->where('training_id', $training->id)
          ->get();

        // パーセンテージを計算
        $progress = 0;
        $total = count($contents);
        foreach ($contents as $content) {
          $content_attendance = DB::table('training_content_attendances')
            ->where('training_content_id', $content->id)
            ->where("training_attendance_id", $attendance->id)
            ->where('attendance_status', 1)
            ->exists();

          if ($content_attendance) {
            $progress++;
          }
        }

        $training->progress = $total === 0 ? 0 : round($progress / $total * 100);
        $res['progress']->push($training);
      } else if ($attendance->attendance_status === 1) {
        $res['completed']->push($training);
      }
    }

    return $res;
  }


  public static function getTrainingDetailByAttend(int $vendor_linked_partner_id, $training_id)
  {
    $user = Auth::guard('partner_users')->user();
    [$vendor_id, $partner_id] = self::getVendorIdAndPartnerId($vendor_linked_partner_id);

    if (!self::isShared($partner_id, $user->id, $training_id)) {
      throw new ServiceException('Error: the training does not exist', 404);
    }

    // トレーニング情報を取得
    $training = DB::table('trainings')
      ->select('id', 'training_title', 'training_description')
      ->where('vendor_id', $vendor_id)
      ->where('id', $training_id)
      ->first();

    if ($training === null) {
      throw new ServiceException('Error: the training does not exist', 404);
    }

    // トレーニングコンテンツを取得
    $training_contents = DB::table('training_contents')
      ->select('id', 'content_title', 'content_description', 'content_order', 'content_type', 'web_test_id', 'file_id')
      ->where('training_id', $training_id)
      ->orderBy('content_order')
      ->get();

    // トレーニング自体の受講IDを取得
    $training_attendance_id = DB::table('training_attendances')
      ->select('id')
      ->where('partner_id', $partner_id)
      ->where('partner_user_id', $user->id)
      ->where('training_id', $training_id)
      ->first();

    foreach ($training_contents as $content) {
      // 受講状況の確認
      if ($training_attendance_id) {
        $attendance = DB::table('training_content_attendances')
          ->select('attendance_status')
          ->where('training_attendance_id', $training_attendance_id->id)
          ->where('training_content_id', $content->id)
          ->first();

        if ($attendance === null) {
          $content->attendance_status = 'unattended';
        } else if ($attendance->attendance_status === 0) {
          $content->attendance_status = 'progress';
        } else if ($attendance->attendance_status === 1) {
          $content->attendance_status = 'completed';
        }
      } else {
        $content->attendance_status = 'unattended';
      }

      // コンテンツの確認
      if ($content->content_type === 1) { // ポータル形式
        $file = DB::table('files')
          ->where('id', $content->file_id)
          ->first();

        if ($file && $file->type === "normal") {
          $content->is_available = true;
          $content->is_published = true; // ポータルには公開状態はないため常にtrue
        } else {
          $content->is_available = false;
          $content->is_published = false;
        }

      } else if ($content->content_type === 2) { // Webテスト形式
        $web_test = DB::table('web_tests')
          ->where('id', $content->web_test_id)
          ->first();

        $content->is_available = $web_test ? true : false;
        $content->is_published = $web_test->publish_status === 'published' ? true : false;
      }
    }

    $training->contents = $training_contents;

    return $training;
  }

  public static function getContentDetailByAttend(int $vendor_linked_partner_id, $training_id, $content_id)
  {
    $user = Auth::guard('partner_users')->user();
    [$vendor_id, $partner_id] = self::getVendorIdAndPartnerId($vendor_linked_partner_id);

    if (!self::isShared($partner_id, $user->id, $training_id)) {
      throw new ServiceException('Error: the content does not exist', 404);
    }
    // コンテンツの存在確認
    $content_detail = DB::table('training_contents')
      ->where('training_id', $training_id)
      ->where('id', $content_id)
      ->first();

    if ($content_detail === null) {
      throw new ServiceException('Error: the content does not exist', 404);
    }

    // コンテンツの受講状況を取得
    $content_attendance = DB::table('training_content_attendances')
      ->join('training_attendances', 'training_content_attendances.training_attendance_id', 'training_attendances.id')
      ->where('training_id', $training_id)
      ->where('partner_id', $partner_id)
      ->where('partner_user_id', $user->id)
      ->where('training_content_id', $content_id)
      ->select("training_content_attendances.attendance_status as attendance_status")
      ->first();

    // 受講状況を設定
    if ($content_attendance) {
      if ($content_attendance->attendance_status === 0) {
        $content_detail->attendance_status = "progress";
      } else {
        $content_detail->attendance_status = "completed";
      }
    } else {
      $content_detail->attendance_status = "unattended";
    }

    // コンテンツの詳細を取得
    if ($content_detail->content_type === 1) {
      // ポータルの場合
      $file = DB::table('files')
        ->where('id', $content_detail->file_id)
        ->first();

      if ($file === null) {
        throw new ServiceException('Error: does not own this file', 400);
      }

      $presignedUrl = Storage::disk('s3-drive')->temporaryUrl($file->s3_path, now()->addMinutes(20));

      $file->url = $presignedUrl;

      $content_detail->file = $file;
    } else if ($content_detail->content_type === 2) {
      // Webテストの場合
      $web_test = DB::table('web_tests')
        ->where('id', $content_detail->web_test_id)
        ->first();

      if ($web_test === null) {
        throw new ServiceException('Error: does not own this web test', 400);
      }

      $content_detail->web_test = $web_test;
    }
    return $content_detail;
  }

  public static function attendContent(int $vendor_linked_partner_id, $training_id, $content_id, $request)
  {
    $user = Auth::guard('partner_users')->user();
    [$vendor_id, $partner_id] = self::getVendorIdAndPartnerId($vendor_linked_partner_id);

    if (!self::isShared($partner_id, $user->id, $training_id)) {
      throw new ServiceException('Error: does not have permission on the training', 400);
    }

    // コンテンツの存在確認
    $content = DB::table('training_contents')
      ->where('training_id', $training_id)
      ->where('id', $content_id)
      ->first();

    if ($content === null) {
      throw new ServiceException('Error: does not exist the content', 400);
    }


    /* トレーニングコンテンツ受講レコードの更新または新規作成 */

    // 受講状況の確認
    $attendance = DB::table('training_attendances')
      ->where('partner_id', $partner_id)
      ->where('partner_user_id', $user->id)
      ->where('training_id', $training_id)
      ->first();

    if ($attendance === null) {
      $attendance_id = DB::table('training_attendances')
        ->insertGetId([
          'uuid' => (string) Uuid::uuid7(),
          'partner_id' => $partner_id,
          'partner_user_id' => $user->id,
          'training_id' => $training_id,
          'attendance_status' => 0,
          'created_at' => now(),
          'updated_at' => now()
        ]);
    } else {
      $attendance_id = $attendance->id;
    }

    // コンテンツの受講状況の確認
    $content_attendance = DB::table('training_content_attendances')
      ->where('training_attendance_id', $attendance_id)
      ->where('training_content_id', $content_id)
      ->first();

    // 受講状況の更新
    if ($content_attendance === null) {
      // 受講レコードがない場合は新規作成
      DB::table('training_content_attendances')
        ->insert([
          'uuid' => (string) Uuid::uuid7(),
          'training_attendance_id' => $attendance_id,
          'training_content_id' => $content_id,
          'attendance_status' => $request->attendance_status,
          'first_completed_at' => $request->attendance_status === 1 ? now() : null,
          'created_at' => now(),
          'updated_at' => now()
        ]);
    } else if ($content_attendance->attendance_status === 0) {
      // 受講状況が未受講の場合、更新
      DB::table('training_content_attendances')
        ->where('training_attendance_id', $content_attendance->training_attendance_id)
        ->where('training_content_id', $content_id)
        ->update([
          'attendance_status' => $request->attendance_status,
          'first_completed_at' => $request->attendance_status === 1 ? now() : null,
          'updated_at' => now()
        ]);
    }

    /* トレーニング受講レコードの更新 */

    // トレーニングのコンテンツ数を取得
    $contents = DB::table('training_contents')
      ->where('training_id', $training_id)
      ->get();

    // トレーニングのコンテンツ受講数を取得
    $attended_contents = DB::table('training_content_attendances')
      ->rightJoin('training_contents', 'training_content_attendances.training_content_id', 'training_contents.id')
      ->where('training_attendance_id', $attendance_id)
      ->where('training_contents.training_id', $training_id)
      ->where('training_content_attendances.attendance_status', 1)
      ->get();

    // トレーニングのコンテンツ数と受講数が一致している場合、トレーニングを受講完了とする
    if ($contents->count() === $attended_contents->count()) {
      // コンテンツ受講レコードの初回完了日を取得
      $currentFirstCompletedAt = DB::table('training_attendances')
        ->where('id', $attendance_id)
        ->value('first_completed_at');

      if ($currentFirstCompletedAt === null) {
        // 初回完了日がない場合、初回完了日と完了日を設定
        DB::table('training_attendances')
          ->where('id', $attendance_id)
          ->update([
            'attendance_status' => 1,
            'first_completed_at' => now(),
            'completed_at' => now(),
            'updated_at' => now()
          ]);
      } else {
        // 初回完了日がある場合、完了日を更新
        DB::table('training_attendances')
          ->where('id', $attendance_id)
          ->update([
            'attendance_status' => 1,
            'completed_at' => now(),
            'updated_at' => now()
          ]);
      }
    } else {
      // コンテンツ受講にレコードがないコンテンツを取得
      $unattended_contents = $contents->diffUsing($attended_contents, function ($content, $attended_content) {
        return $content->id - $attended_content->id;
      });
      foreach ($unattended_contents as $unattended_content) {
        if ($unattended_content->content_type === 1) {
          // ポータル形式の場合、ファイルが存在するか確認
          $file_exists = DB::table('files')
            ->where('id', $unattended_content->file_id)
            ->first();

          // ファイルが存在する場合、受講可能
          if ($file_exists) {
            $unattended_content->is_available = true;
          } else {
            $unattended_content->is_available = false;
          }
        } else if ($unattended_content->content_type === 2) {
          // Webテスト形式の場合、Webテストが存在するか確認
          $web_test = DB::table('web_tests')
            ->where('id', $unattended_content->web_test_id)
            ->first();

          // Webテストが存在し、かつ公開状態の場合、受講可能
          if ($web_test && $web_test->publish_status === 'published') {
            $unattended_content->is_available = true;
          } else {
            $unattended_content->is_available = false;
          }
        }
      }
      // すべての未受講コンテンツが受講不可の場合、受講不可のためトレーニングを受講完了とする
      if (!collect($unattended_contents)->contains('is_available', true)) {
        $currentFirstCompletedAt = DB::table('training_attendances')
          ->where('id', $attendance_id)
          ->value('first_completed_at');

        if ($currentFirstCompletedAt === null) {
          DB::table('training_attendances')
            ->where('id', $attendance_id)
            ->update([
              'attendance_status' => 1,
              'first_completed_at' => now(),
              'completed_at' => now(),
              'updated_at' => now()
            ]);
        } else {
          DB::table('training_attendances')
            ->where('id', $attendance_id)
            ->update([
              'attendance_status' => 1,
              'completed_at' => now(),
              'updated_at' => now()
            ]);
        }
      }
    }
  }

  public static function downloadFile(int $vendor_linked_partner_id, $training_id, $content_id, int $file_id)
  {
    $user = Auth::guard('partner_users')->user();
    [$vendor_id, $partner_id] = self::getVendorIdAndPartnerId($vendor_linked_partner_id);

    if (!self::isShared($partner_id, $user->id, $training_id)) {
      throw new ServiceException('Error: does not have permission on the training', 400);
    }

    // コンテンツの存在確認
    $content = DB::table('training_contents')
      ->where('training_id', $training_id)
      ->where('id', $content_id)
      ->first();

    if ($content === null) {
      throw new ServiceException('Error: does not exist the content', 400);
    }

    $file = DB::table('files')
      ->where('id', $file_id)
      ->first();

    if ($file === null) {
      throw new ServiceException('Error: the file does not exist', 400);
    }

    if ($file->type === 'directory') {
      throw new ServiceException('Error: this file is not normal file', 400);
    }

    try {
      $encodedName = rawurlencode($file->name);
      $disposition = "attachment; filename*=UTF-8''{$encodedName}";
      $presignedUrl = Storage::disk('s3-drive')->temporaryUrl(
        $file->s3_path,
        now()->addMinutes(20),
        ['ResponseContentDisposition' => $disposition]
      );

      return $presignedUrl;
    } catch (Exception $e) {
      throw $e;
    }
  }
}
