<?php

namespace App\Services\Partner;

use App\Exceptions\ServiceException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Exception;

class WebTestService
{
    public static function getTestList(int $vendor_linked_partner_id)
    {
        $user = Auth::guard('partner_users')->user();
        $vendor_id = self::getVendorId($vendor_linked_partner_id);
        $tests = DB::table('web_tests')
            ->join('web_test_partners', 'web_tests.id', '=', 'web_test_partners.test_id')
            ->where('web_test_partners.partner_id', $user->partner_id)
            ->where("web_tests.vendor_id", $vendor_id)
            ->where('publish_status', 'published')
            ->orderBy("test_record_id")
            ->get()
            ->toArray();

        foreach ($tests as $test) {
            $test->created_at = date('Y-m-d', strtotime($test->created_at));
            $test->updated_at = date('Y-m-d', strtotime($test->updated_at));
        }

        foreach ($tests as $test) {
            $attendances = DB::table('web_test_attendances')
                ->where('test_id', $test->test_id)
                // 選択されている PartnerTeam の partner_id で絞り込む
                ->where('partner_id', $user->partner_id)
                ->where('partner_user_id', $user->id)
                ->get();

            $is_attend = false;
            $is_passing = null;

            if($attendances->isNotEmpty()) {
                $is_attend = true;
                // $attendancesの中でis_passingがtrueのものがあればtrueを返す
                foreach ($attendances as $attendance) {
                    if($attendance->is_passing === true) {
                        $is_passing = true;
                        break;
                    } else if ($attendance->is_passing !== null) {
                        $is_passing = $attendance->is_passing ? true : false;
                    }
                }
            }

            $test->is_attended = $is_attend;
            $test->is_passing = $is_passing;
        }

        return $tests;
    }

    public static function getTestDetail(int $test_id)
    {
        $user = Auth::guard('partner_users')->user();
        $test = DB::table("web_tests")
            ->where("id", $test_id)
            ->first();

        if ($test === null) {
            throw new ServiceException('Error: the test does not exist', 404);
        }

        if (!self::isShared($test, $user)) {
            throw new ServiceException('Error: the test does not exist', 404);
        }

        return $test;
    }

    public static function isShared($test, $user)
    {
        $test_permission = DB::table('web_test_partners')
            ->where('test_id', $test->id)
            ->where('partner_id', $user->partner_id)
            ->exists();

        if($test_permission) {
            return true;
        }
        if (TrainingService::getPermissionWithWebTest(Auth::guard('partner_users')->user(), $test->id)) {
            return true;
        }
        return false;
    }

    public static function isPublished($test)
    {
        $test = DB::table('web_tests')
            ->where('id', $test->id)
            ->where('publish_status', 'published')
            ->first();

        if($test === null) {
            return false;
        }
        return true;
    }

    public static function getQuestionList(int $test_id)
    {
        $questions = DB::table('web_test_questions')
            ->where('test_id', $test_id)
            ->where("is_active", true)
            ->get()
            ->toArray();

        return $questions;
    }

    public static function attendTest(int $test_id)
    {
        $user = Auth::guard('partner_users')->user();

        $test = DB::table('web_tests')
            ->where('web_tests.id', $test_id)
            ->first();

        if (!self::isShared($test, $user)) {
            throw new Exception('Error: dose not shared on this test.');
        }

        $attendanceId = DB::table("web_test_attendances")->insertGetId([
            'test_id' => $test_id,
            'partner_id' => $user->partner_id,
            'partner_user_id' => $user->id,
            'is_passing' => null,
            'total_points' => null,
            'answer_status' => false,
            "passing_mark" => $test->passing_mark,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $questions = self::getQuestionList($test_id);

        foreach ($questions as $question) {
            DB::table("web_test_answers")->insert([
                'attendance_id' => $attendanceId,
                'question_id' => $question->id,
                'partner_user_id' => $user->id,
                'is_correct' => null,
                "question_points" => $question->question_points,
                "question_title" => $question->question_title,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            // web_test_optionsから必要なカラムのみを選択して選択肢を取得
            $question->optionList = DB::table("web_test_question_options")
                ->where("question_id", $question->id)
                ->select(
                    'id',
                    'option_name',
                    'option_order'
                )
                ->get()
                ->toArray();
        }
        $response = [
            'attendance_id' => $attendanceId,
            'questionList' => $questions,
        ];

        return $response;
    }

    public static function answerTest($request, $test_id)
    {
        $user = Auth::guard('partner_users')->user();

        $test = DB::table('web_tests')
            ->where('web_tests.id', $test_id)
            ->first();

        if (!self::isShared($test, $user)) {
            throw new Exception('Error: dose not shared on this test.');
        }

        $attendanceID = $request->attendance_id;
        $answerList = $request->answerList;

        $feedbacks = [];
        $totalPoints = 0;


        foreach ($answerList as $item) {
            $questionId = $item['question_id'];
            $answer = $item['answer'];

            $question = DB::table('web_test_questions')
                ->where('id', $questionId)
                ->first();
            $question_options = DB::table('web_test_question_options')
                ->where('question_id', $questionId)
                ->get();

            if ($question->question_type == 2) {
                // 複数回答式の場合

                // 正解の選択肢を全て取得しIDの配列に変換
                $correctOptions = $question_options
                    ->where('question_id', $questionId)
                    ->where('is_correct', true)
                    ->pluck('id')
                    ->toArray();

                // 回答と正解を比較するためにソートする
                sort($correctOptions);
                sort($answer);

                // 正解かどうかを判定する
                $is_correct = $correctOptions === $answer;

                // レスポンス用に選択肢のorderを取得する
                $correct_answer_orders = [];
                $user_answer_orders = [];

                foreach ($correctOptions as $option_id) {
                    $option = $question_options
                        ->where("id", $option_id)
                        ->first();

                    $correct_answer_orders[] = $option->option_order;
                }
                foreach ($answer as $answerItem) {
                    $user_answer_orders[] = $question_options
                        ->where("id", $answerItem)
                        ->first()
                        ->option_order;
                }
                // 回答をカンマ区切りの文字列に変換する
                $correct_answer = implode(',', $correct_answer_orders);
                $user_answer = implode(',', $user_answer_orders);

                // answerを回してweb_test_answer_optionsに保存する
                foreach ($answer as $optionId) {
                    // $attendanceIdと$answer['question_id']を使ってweb_test_answersからidを取得する
                    $answerId = DB::table('web_test_answers')
                        ->where('attendance_id', $attendanceID)
                        ->where('question_id', $questionId)
                        ->first()->id;
                    // web_test_answer_optionsに保存する
                    DB::table('web_test_answer_options')->insert([
                        'answer_id' => $answerId,
                        'option_id' => $optionId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    // answersのis_correctを更新する
                    DB::table("web_test_answers")
                        ->where('id', $answerId)
                        ->update([
                            'is_correct' => $is_correct,
                            'updated_at' => now(),
                        ]);
                }
            } else {
                // 単一回答式の場合

                // 正解の選択肢を取得する
                $correctOption = $question_options
                    ->where('question_id', $questionId)
                    ->where('is_correct', true)
                    ->first();

                // 正解かどうかを判定する
                $is_correct = $correctOption->id == $answer;

                // レスポンス用に選択肢に対応するorderを取得して格納する
                $user_answer = $question_options
                    ->where('id', $answer)
                    ->first()
                    ->option_order;
                $correct_answer = $question_options
                    ->where('id', $correctOption->id)
                    ->first()
                    ->option_order;

                // $attendanceIdと$answer['question_id']を使ってweb_test_answersからidを取得する
                $answerId = DB::table('web_test_answers')
                    ->where('attendance_id', $attendanceID)
                    ->where('question_id', $questionId)
                    ->first()->id;

                // web_test_answer_optionsに保存する
                DB::table('web_test_answer_options')->insert([
                    'answer_id' => $answerId,
                    'option_id' => $answer,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // answersのis_correctを更新する
                DB::table("web_test_answers")
                    ->where('id', $answerId)
                    ->update([
                        'is_correct' => $is_correct,
                        'updated_at' => now(),
                    ]);
            }

            $feedbacks[] = [
                'question_id' => $questionId,
                'question_title' => $question->question_title,
                'question_description' => $question->question_description,
                'question_explanation' => $question->question_explanation,
                'question_points' => $question->question_points,
                'question_order' => $question->question_order,
                'is_correct' => $is_correct,
                'user_answer' => $user_answer,
                'correct_answer' => $correct_answer,
                'question_options' => $question_options,
                'question_type' => $question->question_type,
            ];
            // 正解の場合は得点を加算する
            $totalPoints += $is_correct ? $question->question_points : 0;
        }

        // 受講レコードを取得
        $attendance = DB::table('web_test_attendances')
            ->where("id", $attendanceID)
            ->first();

        // 合否を判定する
        $isPassing = $totalPoints >= $attendance->passing_mark ? true : false;

        // 受講時間を計算する
        $attendanceTime = now()->diffInSeconds($attendance->created_at);

        // 受講レコードを更新する
        DB::table('web_test_attendances')
            ->where('id', $attendanceID)
            ->update([
                'total_points' => $totalPoints,
                'is_passing' => $isPassing,
                'answer_status' => true,
                'attendance_time' => $attendanceTime,
                'updated_at' => now(),
            ]);

        return [
            'isPassing' => $isPassing,
            'passing_mark' => $test->passing_mark,
            'total_points' => $totalPoints,
            'questions' => $feedbacks,
        ];
    }

    private static function getVendorId(int $vendor_linked_partner_id)
    {
        $vendor_id = DB::table('vendor_linked_partners')
            ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
            ->where('vendor_linked_partners.partner_id', Auth::guard('partner_users')->user()->partner_id)
            ->where('vendor_linked_partners.id', $vendor_linked_partner_id)
            ->value('vendor_id');

        if ($vendor_id === null) {
            throw new Exception('連携していないベンダーです。');
        }

        return $vendor_id;
    }
}
