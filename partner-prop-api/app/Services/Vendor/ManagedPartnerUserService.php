<?php

namespace App\Services\Vendor;

use App\Const\StatusConst;
use App\Helpers\S3Helper;
use App\Models\PartnerUserGiftProfile;
use App\Services\FilteredListService;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use UnexpectedValueException;
use BadMethodCallException;

class ManagedPartnerUserService
{
    /**
     * APIで渡されるFieldを、SQLの条件に入れる際に使うFieldに変換する関数を返す
     * @return callable
     */
    static function makeDefaultFieldResolver(): callable
    {
        return function (string $api_key, string|null $api_value) {
            if (in_array($api_key, [
                'id',
                'partner_name',
                'partner_user_name',
                'division_id',
                'division_detail',
                'position_id',
                'email',
                'tel',
                'created_at',
                'updated_at'
            ])) {
                $key = $api_key;
            } else {
                throw new UnexpectedValueException("Unsupported key, $api_key.");
            }
            if (is_null($api_value)) {
                return ['key' => $key, 'value' => null];
            }
            return ['key' => $key, 'value' => $api_value];
        };
    }

    public static function getPartnerUserIdsByCustomColumnForFilteredList(
        FilteredListService $filterService,
        int $vendorId
    ): array {
        $columnIds = array_keys($filterService->getParamFilters('custom_column'));
        $userIdLists = [];

        foreach ($columnIds as $columnId) {
            // 1) カラム種別を取得
            $type = DB::table('partner_user_custom_columns')
                ->where('vendor_id', $vendorId)
                ->where('id', $columnId)
                ->value('type');
            if (is_null($type)) {
                throw new UnexpectedValueException("Vendor {$vendorId} に column_id {$columnId} は存在しません。");
            }

            // 2) 単純文字列／数値／日付系
            if (in_array($type, [
                StatusConst::CUSTOM_COLUMN_STATUS['STRING']['id'],
                StatusConst::CUSTOM_COLUMN_STATUS['INTEGER']['id'],
                StatusConst::CUSTOM_COLUMN_STATUS['DATE']['id'],
                StatusConst::CUSTOM_COLUMN_STATUS['DATETIME_LOCAL']['id'],
            ], true)) {
                $resolver = match ($type) {
                    StatusConst::CUSTOM_COLUMN_STATUS['INTEGER']['id'] =>
                        fn($k, $v) => ['key'=>'content','value'=> is_numeric($v)?(int)$v:$v],
                        StatusConst::CUSTOM_COLUMN_STATUS['DATE']['id'] => fn($k, $v) => ['key' => 'content', 'value' => $v, 'where' => 'date'],
                        StatusConst::CUSTOM_COLUMN_STATUS['DATETIME_LOCAL']['id'] => fn($k, $v) => ['key' => 'content', 'value' => $v, 'where' => 'date'],
                        default => fn($k, $v) => ['key' => 'content', 'value' => $v]
                    };
                $builder = DB::table('partner_user_custom_contents')
                    ->where('column_id', $columnId);
                $builder = $filterService->applyFilter(
                    $builder,
                    params: [$filterService->getParamFilters("custom_column.{$columnId}.value")],
                    field_resolver: $resolver
                );
                $userIdLists[] = $builder->pluck('partner_user_id')->toArray();
                continue;
            }

            // 3) セレクト型
            if ($type === StatusConst::CUSTOM_COLUMN_STATUS['SELECT']['id']) {
                $defaults = DB::table('partner_user_custom_column_selects')
                    ->where('column_id', $columnId)
                    ->where('is_default', true)
                    ->get(['id','select_value'])
                    ->toArray();
                if (count($defaults) !== 1) {
                    throw new BadMethodCallException("column_id {$columnId} の default が不正です。");
                }

                $sub = function(Builder $q) use($columnId, $defaults) {
                    $q->from('partner_users')
                      ->leftJoin('partner_user_custom_contents', function($j) use($columnId) {
                          $j->on('partner_user_custom_contents.partner_user_id','=','partner_users.id')
                            ->on('partner_user_custom_contents.column_id','=',DB::raw($columnId));
                      })
                      ->selectRaw(
                          "partner_users.id as partner_user_id, ifnull(partner_user_custom_contents.select_id, ?) as select_id, ifnull((select select_value from partner_user_custom_column_selects where id = partner_user_custom_contents.select_id), ?) as select_value",
                          [$defaults[0]->id, $defaults[0]->select_value]
                      );
                };

                $builder = $filterService->applyFilter(
                    DB::table($sub),
                    params: $filterService->getParamFilters("custom_column.{$columnId}"),
                    field_resolver: fn($f,$v) => match($f) {
                        "custom_column.{$columnId}.select_id" => ['key'=>'select_id','value'=>$v],
                        "custom_column.{$columnId}.value"     => ['key'=>'select_value','value'=>$v],
                        default => throw new UnexpectedValueException("Unsupported field {$f}")
                    }
                );
                $userIdLists[] = $builder->pluck('partner_user_id')->toArray();
                continue;
            }

            // 3) ベンダーユーザー参照型
            if ($type === StatusConst::CUSTOM_COLUMN_STATUS['VENDOR_USER']['id']) {
                $builder = DB::table('partner_user_custom_contents')
                    ->join(
                        'partner_user_custom_column_vendor_users',
                        'partner_user_custom_contents.id',
                        '=',
                        'partner_user_custom_column_vendor_users.content_id'
                    )
                    ->join(
                        'vendor_users',
                        'partner_user_custom_column_vendor_users.vendor_user_id',
                        '=',
                        'vendor_users.id'
                    )
                    ->where('partner_user_custom_contents.column_id', $columnId);

                // user_name / user_email で絞り込む
                $builder = $filterService->applyFilter(
                    $builder,
                    params: array_values($filterService->getParamFilters("custom_column.{$columnId}")),
                    field_resolver: fn($field, $value) => match ($field) {
                        "custom_column.{$columnId}.user_name"  => ['key' => 'vendor_users.name',  'value' => $value],
                        "custom_column.{$columnId}.user_email" => ['key' => 'vendor_users.email', 'value' => $value],
                        default => throw new UnexpectedValueException("Unsupported field {$field}"),
                    }
                );

                // このカスタムカラムを持つ“元”パートナーユーザーIDを取得
                $userIdLists[] = $builder->pluck('partner_user_custom_contents.partner_user_id')->toArray();
                continue;
            }

            // 4) パートナーユーザー参照型
            if ($type === StatusConst::CUSTOM_COLUMN_STATUS['PARTNER_USER']['id']) {
                $builder = DB::table('partner_user_custom_contents')
                    ->join(
                        'partner_user_custom_column_partner_users',
                        'partner_user_custom_contents.id',
                        '=',
                        'partner_user_custom_column_partner_users.content_id'
                    )
                    ->join(
                        'partner_users',
                        'partner_user_custom_column_partner_users.partner_user_id',
                        '=',
                        'partner_users.id'
                    )
                    ->where('partner_user_custom_contents.column_id', $columnId);

                // user_name / user_email で絞り込む
                $builder = $filterService->applyFilter(
                    $builder,
                    params: array_values($filterService->getParamFilters("custom_column.{$columnId}")),
                    field_resolver: fn($field, $value) => match ($field) {
                        "custom_column.{$columnId}.user_name"  => ['key' => 'partner_users.name',  'value' => $value],
                        "custom_column.{$columnId}.user_email" => ['key' => 'partner_users.email', 'value' => $value],
                        default => throw new UnexpectedValueException("Unsupported field {$field}"),
                    }
                );

                $userIdLists[] = $builder->pluck('partner_user_custom_contents.partner_user_id')->toArray();
                continue;
            }

            if ($type === StatusConst::CUSTOM_COLUMN_STATUS['FILE']['id']) {
                $builder = DB::table('partner_user_custom_contents')
                    ->join(
                        'partner_user_custom_column_files',
                        'partner_user_custom_contents.id',
                        '=',
                        'partner_user_custom_column_files.content_id'
                    )
                    ->where('partner_user_custom_contents.column_id', $columnId);

                // file_name に対して STARTS などの演算子で絞り込む
                $builder = $filterService->applyFilter(
                    $builder,
                    params: [
                        $filterService->getParamFilters("custom_column.{$columnId}.file_name")
                    ],
                    field_resolver: fn($field, $value) => [
                        'key'   => 'partner_user_custom_column_files.file_name',
                        'value' => $value,
                    ]
                );

                $userIdLists[] = $builder->pluck('partner_user_id')->toArray();
                continue;
            }
        }

        return empty($userIdLists)
            ? []
            : array_intersect(...$userIdLists);
    }

    public static function getFilteredManagedPartnerUserList(FilteredListService $filter_service): array
    {
        $vendor_id = Auth::guard('vendor_users')->user()->vendor_id;
        $subQuery = DB::table('partner_users')
        ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
        ->leftJoin('partner_team_members', 'partner_users.id', '=', 'partner_team_members.partner_user_id')
        ->leftJoin('partners', 'partner_team_members.partner_id', '=', 'partners.id')
        ->leftJoin('vendor_linked_partners', 'partners.id', '=', 'vendor_linked_partners.partner_id')
        ->leftJoin('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', '=', 'vendor_managed_partners.id')
        ->leftJoin('partner_user_profiles', 'partner_users.id', '=', 'partner_user_profiles.partner_user_id')
        ->where('vendor_managed_partners.vendor_id', $vendor_id)
        ->where('vendor_linked_partners.link_status', StatusConst::LINK_STATUS['LINK_ACTIVE']['id'])
        ->distinct()
        ->select([
            'partner_users.id AS id',
            'partner_users.name AS partner_user_name',
            'partner_user_profiles.division_id AS division_id',
            'partner_users.division_detail AS division_detail',
            'partner_user_profiles.position_id AS position_id',
            'partner_users.email AS email',
            'partner_users.tel AS tel',
            'partner_users.updated_at AS updated_at',
            'partner_users.created_at AS created_at',
            'partner_files.name AS file_name',
            'partner_files.s3_path AS s3_path',
            DB::raw('GROUP_CONCAT(DISTINCT vendor_managed_partners.name SEPARATOR "、 ") AS partner_name'),
        ])
        ->groupBy([
            'partner_users.id',
            'partner_users.name',
            'partner_user_profiles.division_id',
            'partner_users.division_detail',
            'partner_user_profiles.position_id',
            'partner_users.email',
            'partner_users.tel',
            'partner_users.updated_at',
            'partner_users.created_at',
            'partner_files.name',
            'partner_files.s3_path',
        ]);

        $builder = DB::query()->fromSub($subQuery, 'sub');

        if ($filter_service->hasFilter($filter_service::DEFAULT_GROUP_IDENTIFIER)) {
            $builder = $filter_service->applyFilter(
                $builder,
                array_values($filter_service->getParamFilters($filter_service::DEFAULT_GROUP_IDENTIFIER)),
                self::makeDefaultFieldResolver()
            );
        }
        if ($filter_service->hasFilter('custom_column')) {
            $partnerUserIds = self::getPartnerUserIdsByCustomColumnForFilteredList($filter_service, $vendor_id);
            if (empty($partnerUserIds)) {
                return ['total' => 0, 'partner_user_list' => []];
            }
            $builder = $builder->whereIn('id', $partnerUserIds);
        }


        $total = $builder->count();
        $builder = $filter_service->applySort($builder, self::makeDefaultFieldResolver());
        $builder = $filter_service->applyPagination($builder);

        $partner_user_list = $builder->get()->toArray();

        foreach ($partner_user_list as $partner_user) {
            $partner_user->partner_user_custom_columns = ManagedPartnerUserCustomColumnService::getPartnerUserColumnContent($partner_user->id);

            $partner_user->division = $partner_user->division_id ? StatusConst::getPartnerUserDivisionName($partner_user->division_id) : "";
            $partner_user->position = $partner_user->position_id ? StatusConst::getPartnerUserPositionName($partner_user->position_id) : "";

            $partner_user->logo_url = ! empty($partner_user->s3_path) ? S3Helper::getS3ImageUrl($partner_user->s3_path) : null;

            $gift_profile = self::getGiftProfile($partner_user->id);
            $partner_user->gift_profile = (object)[
                'address' => $gift_profile['address'] ?? null,
                'postal_code' => $gift_profile['postal_code'] ?? null
            ];
        }

        return [
            "total" => $total,
            "partner_user_list" => $partner_user_list,
        ];
    }

    public static function getPartnerUser($partner_user_id)
    {
        $vendor_id = Auth::guard('vendor_users')->user()->vendor_id;
        // 所属ベンダーか確認
        if (!self::isPartnerUserBelongsToVendor($partner_user_id)) {
            return null;
        }
        $partner_user = DB::table('partner_users')
            ->where('partner_users.id', $partner_user_id)
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->leftJoin('partner_user_profiles', 'partner_users.id', '=', 'partner_user_profiles.partner_user_id')
            ->select(
            'partner_users.id',
            'partner_users.name',
            'partner_users.tel',
            'partner_users.email',
            'partner_user_profiles.division_id AS division_id',
            'partner_users.division_detail',
            'partner_user_profiles.position_id AS position_id',
            'partner_users.created_at',
            'partner_users.updated_at',
            'partner_users.last_login_at',
            'partner_files.name AS file_name',
            'partner_files.s3_path AS s3_path'
        )->first();

        if (!$partner_user) {
            return null;
        }

        $team_memberships = DB::table('partner_team_members')
            ->join('partners', 'partner_team_members.partner_id', '=', 'partners.id')
            ->join('vendor_linked_partners', 'partners.id', '=', 'vendor_linked_partners.partner_id')
            ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', '=', 'vendor_managed_partners.id')
            ->where('partner_team_members.partner_user_id', $partner_user_id)
            ->where('vendor_managed_partners.vendor_id', $vendor_id)
            ->where('vendor_linked_partners.link_status', StatusConst::LINK_STATUS['LINK_ACTIVE']['id'])
            ->select(
                'partner_team_members.partner_id',
                'partners.name as partner_name',
                'vendor_managed_partners.id as managed_partner_id',
                'vendor_managed_partners.name as managed_partner_name',
                'partner_team_members.role'
            )
            ->get();

        foreach ($team_memberships as $membership) {
            $role_key = StatusConst::PARTNER_USER_STATUS_ID[$membership->role];
            $membership->role_name = __(StatusConst::PARTNER_USER_STATUS[$role_key][StatusConst::NAME_KEY]);
        }

        $partner_user->division = $partner_user->division_id ? StatusConst::getPartnerUserDivisionName($partner_user->division_id) : "";
        $partner_user->position = $partner_user->position_id ? StatusConst::getPartnerUserPositionName($partner_user->position_id) : "";
        $partner_user->logo_url = !empty($partner_user->s3_path) ? S3Helper::getS3ImageUrl($partner_user->s3_path) : null;

        $partner_user->partner_teams = $team_memberships;

        return $partner_user;
    }

    public static function getGiftProfile($partner_user_id)
    {
        $vendor_id = Auth::guard('vendor_users')->user()->vendor_id;

        $gift_profile = DB::table('partner_user_gift_profiles')
            ->where('vendor_id', $vendor_id)
            ->where('partner_user_id', $partner_user_id)
            ->select('address', 'postal_code')
            ->first();

        return [
            'address' => $gift_profile?->address ?? null,
            'postal_code' => $gift_profile?->postal_code ?? null,
        ];
    }

    public static function updatePartnerUser($request, $managed_partner_id)
    {
        DB::table('vendor_managed_partners')
            ->where('id', $managed_partner_id)
            ->update([
                'vendor_id' => Auth::guard('vendor_users')->user()->vendor_id,
                'name' => $request->managed_partner_name,
                'prefecture_id' => $request->prefecture_id ?? null,
                'postal_code' => $request->postal_code ?? null,
                'address' => $request->address ?? null,
                'tel' => $request->tel ?? null,
                'url' => $request->url ?? null,
                'memo' => $request->memo ?? null,
                'number_of_employees' => $request->number_of_employees ?? null,
                'contract_status' => StatusConst::CONTRACT_STATUS[$request->contract_status]['id'] ?? StatusConst::CONTRACT_STATUS['CONTRACT_PENDING']['id'],
                'vendor_user_id' => $request->vendor_user_id ?? null,
                'updated_at' => now(),
            ]);

        return response()->json($request);
    }

    public static function updateGiftProfile($gift_profile, $partner_user_id)
    {
        PartnerUserGiftProfile::updateOrCreate(
            [
                'vendor_id' => Auth::guard('vendor_users')->user()->vendor_id,
                'partner_user_id' => $partner_user_id,
            ],
            [
                'address' => empty($gift_profile['address']) ? null : $gift_profile['address'],
                'postal_code' => empty($gift_profile['postal_code']) ? null : $gift_profile['postal_code'],
            ]
        );
    }

    public static function updateParentPartnerUser(?int $parent_partner_id, int $managed_partner_id)
    {
        if (is_null($parent_partner_id)) {
            DB::table('vendor_managed_partner_relations')
                ->where('child_partner_id', $managed_partner_id)
                ->delete();
        } else {
            DB::table('vendor_managed_partner_relations')
                ->updateOrInsert(
                    [
                        'child_partner_id' => $managed_partner_id,
                    ],
                    [
                        'parent_partner_id' => $parent_partner_id,
                        'child_partner_id' => $managed_partner_id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]
                );
        }
    }

    //自社のmanaged_partnerか確認する・返り値はboolean
    public static function isPartnerUserBelongsToVendor($partner_user_id)
    {
        $partner_user = DB::table('partner_users')
            ->join('partner_team_members', 'partner_users.id', '=', 'partner_team_members.partner_user_id')
            ->join('partners', 'partner_team_members.partner_id', '=', 'partners.id')
            ->join('vendor_linked_partners', 'partners.id', '=', 'vendor_linked_partners.partner_id')
            ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', '=', 'vendor_managed_partners.id')
            ->where('vendor_linked_partners.link_status', StatusConst::LINK_STATUS['LINK_ACTIVE']['id'])
            ->where('partner_users.id', $partner_user_id)
            ->where('vendor_managed_partners.vendor_id', Auth::guard('vendor_users')->user()->vendor_id)
            ->first();

        return $partner_user !== null;
    }

    public static function isVendorLinkedPartnerBelongsToVendor($vendor_linked_id)
    {
        $vendor_linked_partner = DB::table('vendor_linked_partners')
            ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
            ->where('vendor_linked_partners.id', $vendor_linked_id)
            ->where('vendor_id', Auth::guard('vendor_users')->user()->vendor_id)
            ->get()
            ->first();

        //$vendor_linked_partnerが存在する場合はtrueを返す
        if (isset($vendor_linked_partner)) {
            return true;
        }
        //$vendor_linked_partnerが存在しない場合はfalseを返す
        return false;
    }
}
