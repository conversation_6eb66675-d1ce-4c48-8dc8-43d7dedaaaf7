<?php

namespace App\Services\Vendor;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use App\Exceptions\ServiceException;
use App\Const\StatusConst;
use App\Repositories\Accounts\PartnerUserRepository;
use App\Repositories\Accounts\PartnerTeamMemberRepository;
use App\Services\FilteredListService;
use Exception;
use Aws\S3\S3Client;
use Storage;
use UnexpectedValueException;

class TrainingService
{
    static function makeDefaultFieldResolver(): callable
    {
        return function (string $api_key, string|null $api_value) {
            if (in_array($api_key, [
                'id',
                'training_title',
                'publish_status',
                'created_at',
                'updated_at'
            ])) {
                $key = $api_key;
            } else {
                throw new UnexpectedValueException("Unsupported key, $api_key.");
            }
            if (is_null($api_value)) {
                return ['key' => $key, 'value' => null];
            }
            return ['key' => $key, 'value' => $api_value];
        };
    }

  public static function isOwner($user, int $training_id)
  {
    return DB::table('trainings')
      ->where('vendor_id', $user->vendor_id)
      ->where('id', $training_id)
      ->exists();
  }

  public static function getPartnerIDByVendorLinkedPartnerID($vendor_linked_partner_id)
  {
    $user = Auth::guard('vendor_users')->user();

    return DB::table('vendor_linked_partners')
      ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
      ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
      ->where('vendor_linked_partners.id', $vendor_linked_partner_id)
      ->value('partner_id');
  }

  public static function isContentBelongsToTraining(int $training_id, int $content_id)
  {
    $content = DB::table('training_contents')
      ->where('id', $content_id)
      ->where('training_id', $training_id)
      ->exists();

    return $content;
  }

  public static function getFilteredTestList(FilteredListService $filter_service): array
  {
    $user = Auth::guard('vendor_users')->user();

    $builder = DB::table('trainings')
        ->where('vendor_id', $user->vendor_id);

    if ($filter_service->hasFilter(($filter_service::DEFAULT_GROUP_IDENTIFIER))) {
        $builder = $filter_service->applyFilter(
            $builder,
            array_values($filter_service->getParamFilters($filter_service::DEFAULT_GROUP_IDENTIFIER)),
            self::makeDefaultFieldResolver()
        );
    }

    $total = $builder->count();
    $builder = $filter_service->applySort($builder, self::makeDefaultFieldResolver());
    $builder = $filter_service->applyPagination($builder);
    $training_result = $builder->get()->toArray();

    return [
        "total" => $total,
        "training_result" => $training_result,
    ];
  }

  public static function getTrainingDetail(int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 404);
    }

    $training = DB::table('trainings')
      ->where('id', $training_id)
      ->first();

    if ($training->training_thumbnail_path) {
      $training->training_thumbnail_path = Storage::disk('s3-training')->temporaryUrl($training->training_thumbnail_path, now()->addMinutes(20));
    }

    return $training;
  }

  public static function createTraining($request)
  {
    $user = Auth::guard('vendor_users')->user();

    $max_record_id = DB::table('trainings')
      ->where('vendor_id', $user->vendor_id)
      ->max('training_record_id');

    $new_training_id = DB::table('trainings')->insertGetId([
      'uuid' => (string) Uuid::uuid7(),
      'vendor_id' => $user->vendor_id,
      'training_record_id' => $max_record_id ? $max_record_id + 1 : 1,
      'training_title' => $request->training_title,
      'created_at' => now(),
      'updated_at' => now()
    ]);

    return $new_training_id;
  }

  public static function updateTraining($request, int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    DB::table('trainings')
      ->where('id', $training_id)
      ->update([
        'training_title' => $request->training_title,
        'training_description' => $request->training_description,
        'updated_at' => now()
      ]);
  }

  public static function deleteTraining(int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    DB::table('trainings')
      ->where('id', $training_id)
      ->delete();
  }

  public static function updateTrainingPublishStatus($request, int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    DB::table('trainings')
      ->where('id', $training_id)
      ->update([
        'publish_status' => $request->publish_status,
        'updated_at' => now()
      ]);
  }

  public static function updateTrainingThumbnail($request, int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    $thumbnail = $request->file('thumbnail');


    $file_name = $thumbnail->getClientOriginalName();
    $s3_dir = 'training/' . $user->vendor_id . '/thumbnail';

    // fileの名前を生成
    $auto_id = date('YmdHis');
    $ext = pathinfo($file_name, PATHINFO_EXTENSION);
    $id_name = $auto_id . '.' . $ext;


    $s3_path = Storage::disk('s3-training')->putFileAs($s3_dir, $thumbnail, $id_name);

    DB::table('trainings')
      ->where('id', $training_id)
      ->update([
        'training_thumbnail_path' => $s3_path,
        'updated_at' => now()
      ]);
  }

  public static function getTrainingResult(int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 404);
    }

    $res = [];

    $training = DB::table('trainings')
      ->where('id', $training_id)
      ->first();
    $res['training_title'] = $training->training_title;

    $vendorUserAttendances = DB::table('training_attendances')
        ->join('vendor_users', 'vendor_users.id', '=', 'training_attendances.vendor_user_id')
        ->join('vendors', 'vendors.id', '=', 'vendor_users.vendor_id')
        ->where('training_attendances.training_id', $training_id)
        ->where('vendors.id', $user->vendor_id)
        ->whereNotNull('training_attendances.vendor_user_id')
        ->orderByDesc('training_attendances.updated_at')
        ->select(
            'training_attendances.id AS id',
            'training_attendances.attendance_status AS attendance_status',
            'training_attendances.created_at',
            'training_attendances.updated_at',
            'training_attendances.first_completed_at',
            'training_attendances.completed_at',
            'training_attendances.vendor_user_id as user_id',
            'vendors.name as company_name',
            'vendor_users.name as user_name',
            'vendor_users.email as user_email',
        )
        ->get()
        ->toArray();

    // ベンダーユーザーの重複をそれぞれ削除
    $uniqueVendorUserAttendances = collect($vendorUserAttendances)->unique(function ($attendance) {
        return $attendance->user_id;
    })->values();

    $partnerUserAttendances = DB::table('training_attendances')
        ->join('partners', 'partners.id', '=', 'training_attendances.partner_id')
        ->join('vendor_linked_partners', 'vendor_linked_partners.partner_id', '=', 'training_attendances.partner_id')
        ->join('vendor_managed_partners', 'vendor_managed_partners.id', '=', 'vendor_linked_partners.managed_partner_id')
        ->join('partner_users', 'partner_users.id', '=', 'training_attendances.partner_user_id')
        ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
        ->where('training_attendances.training_id', $training_id)
        ->whereNotNull('training_attendances.partner_user_id')
        ->orderByDesc('training_attendances.updated_at')
        ->select(
            'training_attendances.id AS id',
            'training_attendances.attendance_status AS attendance_status',
            'training_attendances.created_at',
            'training_attendances.updated_at',
            'training_attendances.first_completed_at',
            'training_attendances.completed_at',
            'training_attendances.partner_user_id as user_id',
            'vendor_managed_partners.name as company_name',
            'partner_users.name as user_name',
            'partner_users.email as user_email',
        )
        ->get()
        ->toArray();

    // パートナーユーザーの重複をそれぞれ削除
    $uniquePartnerUserAttendances = collect($partnerUserAttendances)->unique(function ($attendance) {
        return $attendance->company_name.'_'.$attendance->user_id;
    })->values();

    // ベンダーユーザーとパートナーユーザーの結果をマージ
    $mergedAttendances = $uniqueVendorUserAttendances->merge($uniquePartnerUserAttendances)->values();

    $formattedAttendances = $mergedAttendances->map(function ($attendance) use ($training_id) {

        $attendance->created_at = date('Y-m-d', strtotime($attendance->created_at));
        $attendance->updated_at = date('Y-m-d', strtotime($attendance->updated_at));

        // 初回完了日よりも完了日が古い場合は初回完了日を完了日に設定
        if ($attendance->first_completed_at !== null) {
            if ($attendance->first_completed_at > $attendance->completed_at || $attendance->completed_at === null) {
                $attendance->completed_at = $attendance->first_completed_at;
            }
        }

        $attendance->completed_at = $attendance->completed_at !== null ? date('Y-m-d', strtotime($attendance->completed_at)) : null;

        return $attendance;
    });

    $res['attendances'] = $formattedAttendances;

    return $res;
  }

  public static function isSharedAllPartners(int $training_id)
  {
      $training = DB::table('trainings')->where('id', $training_id)->first();
      if ($training === null) {
          throw new ServiceException('Error: the training does not exist', 400);
      }

      $isSharedWithAllPartners = DB::table('shared_training_with_all_partners')
          ->where('training_id', $training_id)
          ->exists();

      if ($isSharedWithAllPartners) {
          $response = [
              "data" => [
                  "allPartnersAuthorized" => $isSharedWithAllPartners,
              ],
              "message" => "全パートナーへの権限が許可されています。"
          ];
      } else {
          $response = [
              "data" => [
                  "allPartnersAuthorized" => $isSharedWithAllPartners,
              ],
              "message" => "全パートナーへの権限が許可されていません。"
          ];
      }

      return $response;
  }

  public static function shareTrainingWithAllPartners(int $training_id)
  {
    $training = DB::table('trainings')->where('id', $training_id)->first();
        if ($training === null) {
            throw new ServiceException('Error: the training does not exist', 400);
        }

        try {
            DB::table('shared_training_with_all_partners')
                ->insert([
                    'training_id' => $training_id,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        } catch (Exception $e) {
            Log::error($e);
            throw $e;
        }
  }

  public static function unShareTrainingWithAllPartners(int $training_id)
  {
    $training = DB::table('trainings')->where('id', $training_id)->first();
        if ($training === null) {
            throw new ServiceException('Error: the training does not exist', 400);
        }

        try {
            DB::table('shared_training_with_all_partners')
                ->where('training_id', $training_id)
                ->delete();
        } catch (Exception $e) {
            Log::error($e);
            throw $e;
        }
  }

  public static function shareTrainingWithPartners($request, int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    $partner_id = self::getPartnerIDByVendorLinkedPartnerID($request->partner_id);

    $partner = DB::table('vendor_managed_partners')
      ->join(
        'vendor_linked_partners',
        'vendor_managed_partners.id',
        'vendor_linked_partners.managed_partner_id'
      )
      ->where('vendor_id', $user->vendor_id)
      ->where('partner_id', $partner_id)
      ->first();

    if ($partner->link_status !== StatusConst::LINK_STATUS['LINK_ACTIVE']['id']) {
      throw new ServiceException('Error: cannot share the training', 400);
    }

    DB::beginTransaction();
    try {
      // 権限追加処理
      $share_training = DB::table('shared_training_with_partners')
        ->where('partner_id', $partner_id)
        ->where('training_id', $training_id)
        ->first();
      if ($share_training) {
        DB::table('shared_training_with_partners')
          ->where('partner_id', $partner_id)
          ->where('training_id', $training_id)
          ->update([
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
          ]);
      } else {
        DB::table('shared_training_with_partners')
          ->insert([
            'uuid' => (string) Uuid::uuid7(),
            'partner_id' => $partner_id,
            'training_id' => $training_id,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
          ]);
      }
      DB::table('unshared_training_with_partners')
        ->where('partner_id', $partner_id)
        ->where('training_id', $training_id)
        ->delete();
      DB::commit();
    } catch (Exception $e) {
      DB::rollback();
      throw $e;
    }
  }

  public static function shareTrainingWithPartnerUsers($request, int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    DB::beginTransaction();
    try {
      $share_training = DB::table('shared_training_with_partner_users')
        ->where('partner_id', $request->partner_id)
        ->where('partner_user_id', $request->partner_user_id)
        ->where('training_id', $training_id)
        ->first();
      if ($share_training) {
        DB::table('shared_training_with_partner_users')
          ->where('partner_id', $request->partner_id)
          ->where('partner_user_id', $request->partner_user_id)
          ->where('training_id', $training_id)
          ->update([
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
          ]);
      } else {
        DB::table('shared_training_with_partner_users')
          ->insert([
            'uuid' => (string) Uuid::uuid7(),
            'partner_id' => $request->partner_id,
            'partner_user_id' => $request->partner_user_id,
            'training_id' => $training_id,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
          ]);
      }
      DB::table('unshared_training_with_partner_users')
        ->where('partner_id', $request->partner_id)
        ->where('partner_user_id', $request->partner_user_id)
        ->where('training_id', $training_id)
        ->delete();
      DB::commit();
    } catch (Exception $e) {
      DB::rollback();
      throw $e;
    }
  }

  public static function unshareTrainingWithPartners($request, int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();
    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }
    $partner_id = self::getPartnerIDByVendorLinkedPartnerID($request->partner_id);
    $partner = DB::table('vendor_managed_partners')
      ->join('vendor_linked_partners', 'vendor_managed_partners.id', 'vendor_linked_partners.managed_partner_id')
      ->where('vendor_id', $user->vendor_id)
      ->where('partner_id', $partner_id)
      ->first();

    if ($partner->link_status !== StatusConst::LINK_STATUS['LINK_ACTIVE']['id']) {
      throw new ServiceException('Error: cannot unshare the training', 400);
    }

    DB::beginTransaction();
    try {
      // Add unshared training with partners
      $share_training = DB::table('unshared_training_with_partners')
        ->where('partner_id', $partner_id)
        ->where('training_id', $training_id)
        ->first();
      if ($share_training) {
        DB::table('unshared_training_with_partners')
          ->update([
            'partner_id' => $partner_id,
            'training_id' => $training_id,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
          ]);
      } else {
        DB::table('unshared_training_with_partners')
          ->insert([
            'uuid' => (string) Uuid::uuid7(),
            'partner_id' => $partner_id,
            'training_id' => $training_id,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
          ]);
      }
      // Remove shared training with partners
      DB::table('shared_training_with_partners')
        ->where('partner_id', $partner_id)
        ->where('training_id', $training_id)
        ->delete();
      $partner_users = PartnerUserRepository::fetchListByPartnerId($partner_id);
      foreach ($partner_users as $partner_user) {
        DB::table('shared_training_with_partner_users')
          ->where('partner_user_id', $partner_user->id)
          ->where('training_id', $training_id)
          ->delete();
      }
      DB::commit();
    } catch (Exception $e) {
      DB::rollback();
      throw $e;
    }
  }
  public static function unshareTrainingWithPartnerUsers($request, int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();
    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }
    DB::beginTransaction();
    try {
      $share_training = DB::table('unshared_training_with_partner_users')
        ->where('partner_id', $request->partner_id)
        ->where('partner_user_id', $request->partner_user_id)
        ->where('training_id', $training_id)
        ->first();
      if ($share_training) {
        DB::table('unshared_training_with_partner_users')
          ->update([
            'partner_id' => $request->partner_id,
            'partner_user_id' => $request->partner_user_id,
            'training_id' => $training_id,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
          ]);
      } else {
        DB::table('unshared_training_with_partner_users')
          ->insert([
            'uuid' => (string) Uuid::uuid7(),
            'partner_id' => $request->partner_id,
            'partner_user_id' => $request->partner_user_id,
            'training_id' => $training_id,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
          ]);
      }
      DB::table('shared_training_with_partner_users')
        ->where('partner_id', $request->partner_id)
        ->where('partner_user_id', $request->partner_user_id)
        ->where('training_id', $training_id)
        ->delete();
      DB::commit();
    } catch (Exception $e) {
      DB::rollback();
      throw $e;
    }
  }

  public static function getSharedPartnerList(int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();
    $training = DB::table('trainings')
      ->where('id', $training_id)
      ->first();
    if ($training === null) {
      throw new ServiceException('Error: the training does not exist', 400);
    }

    $partners = [];
    // training_idの権限にひもづくpartner_idを取得
    $shared_partners = DB::table('shared_training_with_partners')
      ->where('training_id', $training->id)
      ->pluck('partner_id')
      ->all();

    // training_idの権限にひもづかないpartner_idを取得
    $unshared_partners = DB::table('unshared_training_with_partners')
      ->where('training_id', $training->id)
      ->pluck('partner_id')
      ->all();

    $partners = array_merge($partners, $shared_partners, $unshared_partners);


    // 重複するpartner_idを削除
    $partners = array_unique($partners);

    $partnersData = [];
    // partner_idに対して権限が付与されているか確認
    foreach ($partners as $partner) {
      $is_unshared = DB::table('unshared_training_with_partners')
        ->where('partner_id', $partner)
        ->where('training_id', $training->id)
        ->exists();

      // unsharedの場合は追加しない
      if ($is_unshared) {
        $partnerData = DB::table('partners')
          ->join('vendor_linked_partners', 'partners.id', 'vendor_linked_partners.partner_id')
          ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
          ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
          ->where('partners.id', $partner)
          ->select([
            'vendor_linked_partners.id as id',
            'vendor_managed_partners.name as name',
          ])
          ->first();
        // 追加の属性を設定
        $partnerData->has_access = false;

        $partnersData[] = $partnerData;
        continue;
      }

      $is_shared = DB::table('shared_training_with_partners')
        ->where('partner_id', $partner)
        ->where('training_id', $training->id)
        ->exists();

      if ($is_shared) {
        $partnerData = DB::table('partners')
          ->join('vendor_linked_partners', 'partners.id', 'vendor_linked_partners.partner_id')
          ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
          ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
          ->where('partners.id', $partner)
          ->select([
            'vendor_linked_partners.id as id',
            'vendor_managed_partners.name as name',
          ])
          ->first();
        // 追加の属性を設定
        $partnerData->has_access = true;

        $partnersData[] = $partnerData;
      }

    }

    return $partnersData;
  }


  public static function getSharedPartnerUserList($training_id)
  {
    $user = Auth::guard('vendor_users')->user();
    $training = DB::table('trainings')
      ->where('id', $training_id)
      ->first();
    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: the training does not exist', 400);
    }
    //$root_trainingに紐づくpartner企業を取得
    $partner_ids_with_partner_permissions = DB::table('shared_training_with_partners')
      ->where('training_id', $training->id)
      ->pluck('partner_id')
      ->all();

    // 共有されたパートナーから紐づくpartner_team_membersを取得
    $partner_team_members = PartnerTeamMemberRepository::fetchListByPartnerIds($partner_ids_with_partner_permissions);

    // 個別で共有されたパートナーユーザーを取得して追加
    $shared_partner_users = DB::table('shared_training_with_partner_users')
        ->where('training_id', $training->id)
        ->get();

    foreach ($shared_partner_users as $shared_user) {
      if ($member = PartnerTeamMemberRepository::fetchByPartnerIdAndPartnerUserId(
        $shared_user->partner_id,
        $shared_user->partner_user_id
      )) {
        $partner_team_members->push($member);
      }
    }

    $partner_team_members = $partner_team_members->unique('id');
    $partner_team_members->load('partner_user');
    $partner_team_members->load('partner');

    //partner_usersから閲覧権限を持っているか確認
    foreach ($partner_team_members as $partner_team_member) {
      //rootのユーザー権限を確認して権限がない場合は閲覧権限なし
      $is_user_unshared = DB::table('unshared_training_with_partner_users')
        ->where('partner_id', $partner_team_member->partner_id)
        ->where('partner_user_id', $partner_team_member->partner_user_id)
        ->where('training_id', $training->id)
        ->exists();
      if ($is_user_unshared) {
        $partner_team_member->has_access = false;
        continue;
      }

      //rootのユーザー権限を確認して権限がある場合は閲覧権限あり
      $is_user_shared = DB::table('shared_training_with_partner_users')
        ->where('partner_id', $partner_team_member->partner_id)
        ->where('partner_user_id', $partner_team_member->partner_user_id)
        ->where('training_id', $training->id)
        ->exists();
      if ($is_user_shared) {
        $partner_team_member->has_access = true;
        continue;
      }

      //rootの企業権限を確認して権限がない場合はには閲覧権限なし
      $is_partner_unshared = DB::table('unshared_training_with_partners')
        ->where('partner_id', $partner_team_member->partner_id)
        ->where('training_id', $training->id)
        ->exists();
      if ($is_partner_unshared) {
        $partner_team_member->has_access = false;
        continue;
      }

      //rootの企業権限を確認して権限がある場合はには閲覧権限あり
      $is_partner_shared = DB::table('shared_training_with_partners')
        ->where('partner_id', $partner_team_member->partner_id)
        ->where('training_id', $training->id)
        ->exists();
      if ($is_partner_shared) {
        $partner_team_member->has_access = true;
        continue;
      }
      //どちらもない場合は閲覧権限なし
      $partner_team_member->has_access = false;
    }

    $filtered_partner_team_members = $partner_team_members->map(function ($member) {
      return [
        'email' => $member?->partner_user?->email,
        'has_access' => $member?->has_access ?? false,
        'id' => $member?->partner_user_id,
        'name' => $member?->partner_user?->name,
        'partner_id' => $member?->partner_id,
        'partner_name' => $member?->partner?->name,
      ];
    })->values();

    return $filtered_partner_team_members;
  }

  public static function getContentList(int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    $contents = DB::table('training_contents')
      ->where('training_id', $training_id)
      ->orderBy('content_order', 'asc')
      ->get();

    foreach ($contents as $content) {
      $content_name = null;
      if ($content->content_type === 1 && $content->file_id !== null) {
        $selected_content = DB::table('files')
          ->where('id', $content->file_id)
          ->first();

        $content_name = $selected_content->name;
      } else if ($content->content_type === 2 && $content->web_test_id !== null) {
        $selected_content = DB::table('web_tests')
          ->where('id', $content->web_test_id)
          ->first();

        $content_name = $selected_content->test_title;
      }
      $content->content_name = $content_name;
    }

    return $contents;
  }

  public static function createContent($request, int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    $max_order = DB::table('training_contents')
      ->where('training_id', $training_id)
      ->max('content_order');

    $new_content_id = DB::table('training_contents')->insertGetId([
      'uuid' => (string) Uuid::uuid7(),
      'training_id' => $training_id,
      'content_title' => $request->content_title,
      'content_description' => $request->content_description,
      'content_order' => $max_order !== null ? $max_order + 1 : 0,
      'content_type' => $request->content_type,
      'web_test_id' => $request->web_test_id,
      'file_id' => $request->file_id,
      'created_at' => now(),
      'updated_at' => now()
    ]);

    $new_content = DB::table('training_contents')
      ->where('id', $new_content_id)
      ->first();

    return $new_content;
  }

  public static function updateContent($request, int $training_id, int $content_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    if (!self::isContentBelongsToTraining($training_id, $content_id)) {
      throw new ServiceException('Error: Invalid access', 400);
    }

    DB::table('training_contents')
      ->where('id', $content_id)
      ->update([
        'content_title' => $request->content_title,
        'content_description' => $request->content_description,
        'content_type' => $request->content_type,
        'web_test_id' => $request->web_test_id,
        'file_id' => $request->file_id,
        'updated_at' => now()
      ]);

    $updated_content = DB::table('training_contents')
      ->where('id', $content_id)
      ->first();

    return $updated_content;
  }

  public static function deleteContent(int $training_id, int $content_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    if (!self::isContentBelongsToTraining($training_id, $content_id)) {
      throw new ServiceException('Error: Invalid access', 400);
    }

    DB::table('training_contents')
      ->where('id', $content_id)
      ->delete();
  }

  public static function bulkDeleteContent($request, int $training_id)
  {
    /* MEMO:
    トレーニング受講レコードの受講状態管理
    unattended: 未受講
    progress: 受講中
    completed: 完了

    トレーニングコンテンツ受講レコードの受講状態管理
    unattended: 未受講
    progress: 進行中
    completed: 完了
    */
    DB::beginTransaction();
    try{

    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    $deletedIds = $request->content_ids;

    // トレーニングIDが一致する受講中の受講レコードを取得
    $target_training_attendance_ids = DB::table('training_attendances')
      ->join('training_content_attendances', 'training_attendances.id', '=', 'training_content_attendances.training_attendance_id')
      ->where('training_attendances.training_id', $training_id)
      ->where('training_attendances.attendance_status', 0) // 受講状態が受講中
      ->select(
        'training_attendances.id as training_attendance_id',
        'training_content_attendances.training_content_id',
        'training_content_attendances.attendance_status as content_attendance_status',
      )
      ->get()
      ->groupBy('training_attendance_id');

    // トレーニニングコンテンツを取得
    $training_contents = DB::table('training_contents')
      ->where('training_id', $training_id)
      ->get();

    $is_not_completed_with_delete_ids = false; // 削除IDと一致する進行中の受講レコードが存在するかのフラグ
    $not_exist_progress_or_unattended_content_without_delete_ids = true; // 削除ID以外のコンテンツで完了している受講レコードが存在するかのフラグ

    // 対象となる受講レコード（ユーザー単位）ごとに処理
    foreach ($target_training_attendance_ids as $training_attendance_id => $attendances) {
      foreach ($training_contents as $training_content) {
        // 一つでも削除ID以外のコンテンツで未受講または進行中のレコードがある場合はループを抜ける
        if ($not_exist_progress_or_unattended_content_without_delete_ids === false)
          break;

        // コンテンツIDに紐づく受講レコードを取得
        $attendance = $attendances->firstWhere('training_content_id', $training_content->id);
        // 削除対象のIDかどうか判定
        if (in_array($training_content->id, $deletedIds)) {
          // 削除対象のコンテンツが未受講または進行中である場合はフラグを立てる
          if($attendance === null) {
            $is_not_completed_with_delete_ids = true;
          } else if ($attendance->content_attendance_status === 0) {
            $is_not_completed_with_delete_ids = true;
          }
        } else {
          // 削除ID以外のコンテンツの場合にコンテンツの受講状態が完了しているかを確認
          if($attendance === null) {
            // 未受講のコンテンツが存在する場合はフラグを立てる（ループから抜ける）
            $not_exist_progress_or_unattended_content_without_delete_ids = false;
          } else if ($attendance->content_attendance_status === 0) {
            // 進行中のコンテンツが存在する場合はフラグを立てる（ループから抜ける）
            $not_exist_progress_or_unattended_content_without_delete_ids = false;
          }
        }
      }
      // 削除ID以外のコンテンツで未受講または進行中のレコードが存在する場合はスキップ
      if ($not_exist_progress_or_unattended_content_without_delete_ids) {
        // 削除IDのコンテンツが未受講または進行中だった場合はトレーニングを完了とする
        if ($is_not_completed_with_delete_ids) {
          DB::table('training_attendances')
            ->where('id', $training_attendance_id)
            ->update([
              'attendance_status' => 1,
              "first_completed_at" => now(),
              'updated_at' => now()
            ]);
        }
      }

      // フラグを初期化
      $is_not_completed_with_delete_ids = false;
      $not_exist_progress_or_unattended_content_without_delete_ids = true;
    }

    // コンテンツを削除
    DB::table('training_contents')
      ->where('training_id', $training_id)
      ->whereIn('id', $deletedIds)
      ->delete();

      DB::commit();

    } catch (Exception $e) {
      DB::rollback();
      throw $e;
    }

  }

  public static function updateContentOrder($request, int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    if (!self::isOwner($user, $training_id)) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    $contents = $request->contents;

    foreach ($contents as $content) {
      DB::table('training_contents')
        ->where('training_id', $training_id)
        ->where('id', $content['content_id'])
        ->update([
          'content_order' => $content['content_order'],
          'updated_at' => now()
        ]);
    }
  }

  public static function searchWebTestList($request, int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    $web_tests = DB::table('web_tests')
      ->where('vendor_id', $user->vendor_id)
      ->get();

    return $web_tests;
  }

  public static function searchFileList($request, int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();
    $file_id = $request->file_id;
    $portal_id = $request->portal_id;

    if ($portal_id && $file_id) {
      // ポータルIDとファイルIDに該当するファイル一覧を取得
      $files = DB::table('files')
        ->select('id', 'name', 'type')
        ->where('vendor_id', $user->vendor_id)
        ->where('portal_id', $portal_id)
        ->where('parent', $file_id)
        ->get();
    } else if ($portal_id) {
      // ポータルIDに該当するファイル一覧を取得
      $files = DB::table('files')
        ->select('id', 'name', 'type')
        ->where('vendor_id', $user->vendor_id)
        ->where('portal_id', $portal_id)
        ->get();
    } else {
      // ポータル一覧を取得
      $files = DB::table('portals')
        ->select('id', 'name')
        ->where('vendor_id', $user->vendor_id)
        ->get();
    }

    return $files;
  }

  public static function getTrainingListByAttend()
  {
    $user = Auth::guard('vendor_users')->user();

    $trainings = DB::table('trainings')
      ->select('id', 'training_record_id', 'training_title', 'training_description', 'training_thumbnail_path', 'publish_status', 'created_at', 'updated_at')
      ->where('vendor_id', $user->vendor_id)
      ->where('publish_status', 'published')
      ->get();
    // 返却用の配列を初期化
    $res = [
      'unattended' => collect(),
      'progress' => collect(),
      'completed' => collect()
    ];

    foreach ($trainings as $training) {
      if ($training->training_thumbnail_path !== null) {
        // S3から一時URLを取得
        $training->training_thumbnail_path = Storage::disk('s3-training')->temporaryUrl($training->training_thumbnail_path, now()->addMinutes(20));
      }

      // 受講状態の確認
      $attendance = DB::table('training_attendances')
        ->where('vendor_user_id', $user->id)
        ->where('training_id', $training->id)
        ->first();

      if ($attendance === null) {
        $res['unattended']->push($training);
      } else if ($attendance->attendance_status === 0) {

        // コンテンツの進行度を確認
        $contents = DB::table('training_contents')
          ->where('training_id', $training->id)
          ->get();

        // パーセンテージを計算
        $progress = 0;
        $total = count($contents);
        foreach ($contents as $content) {
          $training_content_attendance = DB::table('training_content_attendances')
            ->where('training_content_id', $content->id)
            ->where("training_attendance_id", $attendance->id)
            ->where('attendance_status', 1)
            ->exists();

          if ($training_content_attendance) {
            $progress++;
          }
        }

        $training->progress = $total === 0 ? 0 : round($progress / $total * 100);
        $res['progress']->push($training);
      } else if ($attendance->attendance_status === 1) {
        $res['completed']->push($training);
      }
    }

    return $res;
  }

  public static function getTrainingDetailByAttend(int $training_id)
  {
    $user = Auth::guard('vendor_users')->user();

    $training = DB::table('trainings')
      ->select('id', 'training_title', 'training_description')
      ->where('vendor_id', $user->vendor_id)
      ->where('id', $training_id)
      ->first();

    if ($training === null) {
      throw new ServiceException('Error: does not own this training', 404);
    }

    $training_contents = DB::table('training_contents')
      ->select('id', 'content_title', 'content_description', 'content_order', 'content_type', 'web_test_id', 'file_id')
      ->where('training_id', $training_id)
      ->orderBy('content_order')
      ->get();

    // トレーニング自体の受講IDを取得
    $training_attendance_id = DB::table('training_attendances')
      ->select('id')
      ->where('vendor_user_id', $user->id)
      ->where('training_id', $training_id)
      ->first();

    foreach ($training_contents as $content) {
      // 受講状況の確認
      if ($training_attendance_id) {
        $attendance = DB::table('training_content_attendances')
          ->select('attendance_status')
          ->where('training_attendance_id', $training_attendance_id->id)
          ->where('training_content_id', $content->id)
          ->first();

        if ($attendance === null) {
          $content->attendance_status = 'unattended';
        } else if ($attendance->attendance_status === 0) {
          $content->attendance_status = 'progress';
        } else if ($attendance->attendance_status === 1) {
          $content->attendance_status = 'completed';
        }
      } else {
        $content->attendance_status = 'unattended';
      }

      // コンテンツの確認
      if ($content->content_type === 1) { // ポータル形式
        $file = DB::table('files')
          ->where('id', $content->file_id)
          ->first();

        if ($file && $file->type === "normal") {
          $content->is_available = true;
          $content->is_published = true; // ポータルには公開状態はないため常にtrue
        } else {
          $content->is_available = false;
          $content->is_published = false;
        }

      } else if ($content->content_type === 2) { // Webテスト形式
        $web_test = DB::table('web_tests')
          ->where('id', $content->web_test_id)
          ->first();

        $content->is_available = $web_test ? true : false;
        $content->is_published = $web_test->publish_status === 'published' ? true : false;
      }
    }

    $training->contents = $training_contents;

    return $training;
  }

  public static function attendContent($request, int $training_id, int $content_id)
  {
    $user = Auth::guard('vendor_users')->user();
    // トレーニングの存在＆所属確認
    $training = DB::table('trainings')
      ->where('vendor_id', $user->vendor_id)
      ->where('id', $training_id)
      ->first();

    if ($training === null) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    // コンテンツの存在＆所属確認
    $content = DB::table('training_contents')
      ->where('training_id', $training_id)
      ->where('id', $content_id)
      ->first();

    if ($content === null) {
      throw new ServiceException('Error: does not own this content', 400);
    }

    // 受講状況の確認
    $attendance_id = DB::table('training_attendances')
      ->where('vendor_user_id', $user->id)
      ->where('training_id', $training_id)
      ->value('id');

    if ($attendance_id === null) {
      // 受講状況がない場合、新規作成
      $attendance_id = DB::table('training_attendances')
        ->insertGetId([
          'uuid' => (string) Uuid::uuid7(),
          'vendor_user_id' => $user->id,
          'training_id' => $training_id,
          'created_at' => now(),
          'updated_at' => now()
        ]);
    }

    // コンテンツの受講状況の確認
    $content_attendance = DB::table('training_content_attendances')
      ->where('training_attendance_id', $attendance_id)
      ->where('training_content_id', $content_id)
      ->first();

    if ($content_attendance === null) {
      // 受講状況がない場合、新規作成
      DB::table('training_content_attendances')
        ->insert([
          'uuid' => (string) Uuid::uuid7(),
          'training_attendance_id' => $attendance_id,
          'training_content_id' => $content_id,
          'attendance_status' => $request->attendance_status,
          'first_completed_at' => $request->attendance_status === 1 ? now() : null,
          'created_at' => now(),
          'updated_at' => now()
        ]);
    } else if ($content_attendance->attendance_status === 0) {
      // 受講状況が未受講の場合、更新
      DB::table('training_content_attendances')
        ->where('training_attendance_id', $content_attendance->training_attendance_id)
        ->where('training_content_id', $content_id)
        ->update([
          'attendance_status' => $request->attendance_status,
          'first_completed_at' => $request->attendance_status === 1 ? now() : null,
          'updated_at' => now()
        ]);
    }

    // トレーニングのコンテンツ数を確認
    $contents = DB::table('training_contents')
      ->where('training_id', $training_id)
      ->get();

    // トレーニングのコンテンツ受講数を確認
    $attended_contents = DB::table('training_content_attendances')
      ->rightJoin('training_contents', 'training_content_attendances.training_content_id', 'training_contents.id')
      ->where('training_attendance_id', $attendance_id)
      ->where('training_contents.training_id', $training_id)
      ->where('training_content_attendances.attendance_status', 1)
      ->get();

    if ($contents->count() === $attended_contents->count()) {
      $currentFirstCompletedAt = DB::table('training_attendances')
        ->where('id', $attendance_id)
        ->value('first_completed_at');

      if ($currentFirstCompletedAt === null) {
        DB::table('training_attendances')
          ->where('id', $attendance_id)
          ->update([
            'attendance_status' => 1,
            'first_completed_at' => now(),
            'completed_at' => now(),
            'updated_at' => now()
          ]);
      } else {
        DB::table('training_attendances')
          ->where('id', $attendance_id)
          ->update([
            'attendance_status' => 1,
            'completed_at' => now(),
            'updated_at' => now()
          ]);
      }
    } else {
      // コンテンツ受講にレコードがないコンテンツを取得
      $unattended_contents = $contents->diffUsing($attended_contents, function ($content, $attended_content) {
        return $content->id - $attended_content->id;
      });
      foreach ($unattended_contents as $unattended_content) {
        if ($unattended_content->content_type === 1) {
          // ポータル形式の場合、ファイルが存在するか確認
          $file_exists = DB::table('files')
            ->where('id', $unattended_content->file_id)
            ->first();

          if ($file_exists) {
            $unattended_content->is_available = true;
          } else {
            $unattended_content->is_available = false;
          }
        } else if ($unattended_content->content_type === 2) {
          // Webテスト形式の場合、Webテストが存在するか確認
          $web_test = DB::table('web_tests')
            ->where('id', $unattended_content->web_test_id)
            ->first();

          if ($web_test && $web_test->publish_status === 'published') {
            $unattended_content->is_available = true;
          } else {
            $unattended_content->is_available = false;
          }
        }
      }
      // すべての$unattended_contentにis_availableプロパティがfalseの場合、受講不可のためトレーニングを受講完了とする
      if (!collect($unattended_contents)->contains('is_available', true)) {
        $currentFirstCompletedAt = DB::table('training_attendances')
          ->where('id', $attendance_id)
          ->value('first_completed_at');

        if ($currentFirstCompletedAt === null) {
          DB::table('training_attendances')
            ->where('id', $attendance_id)
            ->update([
              'attendance_status' => 1,
              'first_completed_at' => now(),
              'completed_at' => now(),
              'updated_at' => now()
            ]);
        } else {
          DB::table('training_attendances')
            ->where('id', $attendance_id)
            ->update([
              'attendance_status' => 1,
              'completed_at' => now(),
              'updated_at' => now()
            ]);
        }
      }
    }
  }

  public static function getContentDetailByAttend(int $training_id, int $content_id)
  {
    $user = Auth::guard('vendor_users')->user();

    // トレーニングの存在＆所属確認
    $training = DB::table('trainings')
      ->where('vendor_id', $user->vendor_id)
      ->where('id', $training_id)
      ->first();

    if ($training === null) {
      throw new ServiceException('Error: does not own this training', 400);
    }

    // コンテンツの存在＆所属確認
    $content_detail = DB::table('training_contents')
      ->where('training_id', $training_id)
      ->where('id', $content_id)
      ->first();

    if ($content_detail === null) {
      throw new ServiceException('Error: does not own this content', 400);
    }

    $content_attendance = DB::table('training_content_attendances')
      ->join('training_attendances', 'training_content_attendances.training_attendance_id', 'training_attendances.id')
      ->where('training_id', $training_id)
      ->where('vendor_user_id', $user->id)
      ->where('training_content_id', $content_id)
      ->select("training_content_attendances.attendance_status as attendance_status")
      ->first();

    if ($content_attendance) {
      if ($content_attendance->attendance_status === 0) {
        $content_detail->attendance_status = "progress";
      } else {
        $content_detail->attendance_status = "completed";
      }
    } else {
      $content_detail->attendance_status = "unattended";
    }

    if ($content_detail->content_type === 1) {
      // ポータルの場合
      $file = DB::table('files')
        ->where('id', $content_detail->file_id)
        ->first();

      if ($file === null) {
        throw new ServiceException('Error: does not own this file', 400);
      }

      $presignedUrl = Storage::disk('s3-drive')->temporaryUrl($file->s3_path, now()->addMinutes(20));

      $file->url = $presignedUrl;

      $content_detail->file = $file;
    } else if ($content_detail->content_type === 2) {
      // Webテストの場合
      $web_test = DB::table('web_tests')
        ->where('id', $content_detail->web_test_id)
        ->first();

      if ($web_test === null) {
        throw new ServiceException('Error: does not own this web test', 400);
      }

      $content_detail->web_test = $web_test;
    }
    return $content_detail;
  }

  public static function isExistRelatedTraining($vendor_user, ?int $file_id, ?int $web_test_id)
  {
    if ($file_id) {
      $is_exist_related_training = DB::table('trainings')
        ->join('training_contents', 'trainings.id', 'training_contents.training_id')
        ->where('vendor_id', $vendor_user->vendor_id)
        ->where('publish_status', '<>', 'draft')
        ->where('content_type', 1)
        ->where('file_id', $file_id)
        ->exists();

    } else if ($web_test_id) {
      $is_exist_related_training = DB::table('trainings')
        ->join('training_contents', 'trainings.id', 'training_contents.training_id')
        ->where('vendor_id', $vendor_user->vendor_id)
        ->where('publish_status', '<>', 'draft')
        ->where('content_type', 2)
        ->where('web_test_id', $web_test_id)
        ->exists();
    }
    return $is_exist_related_training;
  }
}
