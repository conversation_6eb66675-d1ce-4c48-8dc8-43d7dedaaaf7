<?php

namespace App\Services\Vendor;

use App\Const\StatusConst;
use App\Exceptions\ServiceException;
use App\Helpers\S3Helper;
use App\Services\FilteredListService;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use UnexpectedValueException;

class WebTestService
{
    static function makeDefaultFieldResolver(): callable
    {
        return function (string $api_key, string|null $api_value) {
            if (in_array($api_key, [
                'id',
                'test_title',
                'passing_mark',
                'publish_status',
                'created_at',
                'updated_at'
            ])) {
                $key = $api_key;
            } else {
                throw new UnexpectedValueException("Unsupported key, $api_key.");
            }
            if (is_null($api_value)) {
                return ['key' => $key, 'value' => null];
            }
            return ['key' => $key, 'value' => $api_value];
        };
    }

    public static function isOwner($user, int $test_id)
    {
        return DB::table('web_tests')
            ->where('id', $test_id)
            ->where('vendor_id', $user->vendor_id)
            ->exists();
    }

    public static function getFilteredTestList(FilteredListService $filter_service): array
    {
        $user = Auth::guard('vendor_users')->user();

        $builder = DB::table('web_tests')
            ->where('vendor_id', $user->vendor_id);

        if ($filter_service->hasFilter(($filter_service::DEFAULT_GROUP_IDENTIFIER))) {
            $builder = $filter_service->applyFilter(
                $builder,
                array_values($filter_service->getParamFilters($filter_service::DEFAULT_GROUP_IDENTIFIER)),
                self::makeDefaultFieldResolver()
            );
        }

        $total = $builder->count();
        $builder = $filter_service->applySort($builder, self::makeDefaultFieldResolver());
        $builder = $filter_service->applyPagination($builder);
        $webtest_result = $builder->get()->toArray();

        return [
            "total" => $total,
            "webtest_list" => $webtest_result,
        ];
    }

    public static function createTest($request)
    {
        // test_record_idの最大値を取得、もしnullの場合は0を代入
        $max_record_id = DB::table('web_tests')
            ->where('vendor_id', Auth::guard('vendor_users')->user()->vendor_id)
            ->max('test_record_id') ?? 0;

        $newTestID = DB::table('web_tests')->insertGetId([
            'test_title' => $request->test_title,
            'vendor_id' => Auth::guard('vendor_users')->user()->vendor_id,
            'test_record_id' => $max_record_id + 1,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $newTest = DB::table('web_tests')
            ->where('id', $newTestID)
            ->first();

        return $newTest;
    }

    public static function updateTest($request, int $test_id)
    {
        $user = Auth::guard('vendor_users')->user();

        if (! self::isOwner($user, $test_id)) {
            throw new Exception('Error: dose not own this test.');
        }
        DB::table('web_tests')
            ->where('id', $test_id)
            ->update([
                'test_title' => $request->test_title,
                'test_description' => $request->test_description,
                'passing_mark' => $request->passing_mark,
                'updated_at' => now(),
            ]);
    }

    public static function getTestDetail(int $test_id)
    {
        $user = Auth::guard('vendor_users')->user();
        if (! self::isOwner($user, $test_id)) {
            throw new ServiceException('Error: dose not own this test.', 404);
        }
        $test = DB::table('web_tests')
            ->where('id', $test_id)
            ->first();

        return $test;
    }

    public static function deleteTest(int $test_id)
    {
        $user = Auth::guard('vendor_users')->user();
        if (! self::isOwner($user, $test_id)) {
            throw new Exception('Error: dose not own this test.');
        }
        if (TrainingService::isExistRelatedTraining($user, null, $test_id)) {
            throw new ServiceException('Error: cannot delete the test related to training', 400);
        }
        DB::table('web_tests')
            ->where('id', $test_id)
            ->delete();
    }

    public static function getAnswerList(int $test_id)
    {
        $user = Auth::guard('vendor_users')->user();
        if (! self::isOwner($user, $test_id)) {
            throw new ServiceException('Error: dose not own this test.', 404);
        }

        $vendorUserAttendances = DB::table('web_test_attendances')
            ->join('web_tests', 'web_tests.id', '=', 'web_test_attendances.test_id')
            ->join('vendor_users', 'vendor_users.id', '=', 'web_test_attendances.vendor_user_id')
            ->join('vendors', 'vendors.id', '=', 'vendor_users.vendor_id')
            ->where('web_test_attendances.test_id', $test_id)
            ->where('vendors.id', $user->vendor_id)
            ->where('web_test_attendances.answer_status', true)
            ->whereNotNull('web_test_attendances.vendor_user_id')
            ->orderByDesc('web_test_attendances.updated_at')
            ->select(
                'web_tests.passing_mark as passing_mark',
                'web_test_attendances.vendor_user_id as vendor_user_id',
                'web_test_attendances.attendance_time',
                'web_test_attendances.updated_at as latest_attend_at',
                'web_test_attendances.is_passing as latest_result',
                'web_test_attendances.total_points as latest_total_points',
                'vendors.name as company_name',
                'vendor_users.id as vendor_user_id',
                'vendor_users.name as user_name',
                'vendor_users.email as user_email',
            )
            ->get()
            ->toArray();

        // ベンダーユーザーの重複をそれぞれ削除
        $uniqueVendorUserAttendances = collect($vendorUserAttendances)->unique(function ($attendance) {
            return $attendance->vendor_user_id;
        })->values();

        $partnerUserAttendances = DB::table('web_test_attendances')
            ->join('web_tests', 'web_tests.id', '=', 'web_test_attendances.test_id')
            ->join('partners', 'partners.id', '=', 'web_test_attendances.partner_id')
            ->join('vendor_linked_partners', 'vendor_linked_partners.partner_id', '=', 'web_test_attendances.partner_id')
            ->join('vendor_managed_partners', 'vendor_managed_partners.id', '=', 'vendor_linked_partners.managed_partner_id')
            ->join('partner_users', 'partner_users.id', '=', 'web_test_attendances.partner_user_id')
            ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
            ->where('web_test_attendances.test_id', $test_id)
            ->where('web_test_attendances.answer_status', true)
            ->whereNotNull('web_test_attendances.partner_user_id')
            ->orderByDesc('web_test_attendances.updated_at')
            ->select(
                'web_tests.passing_mark as passing_mark',
                'web_test_attendances.vendor_user_id as vendor_user_id',
                'web_test_attendances.attendance_time',
                'web_test_attendances.updated_at as latest_attend_at',
                'web_test_attendances.is_passing as latest_result',
                'web_test_attendances.total_points as latest_total_points',
                'vendor_managed_partners.name as company_name',
                'partner_users.id as partner_user_id',
                'partner_users.name as user_name',
                'partner_users.email as user_email',
            )
            ->get()
            ->toArray();

        // パートナーユーザーの重複をそれぞれ削除
        $uniquePartnerUserAttendances = collect($partnerUserAttendances)->unique(function ($attendance) {
            return $attendance->company_name.'_'.$attendance->partner_user_id;
        })->values();

        // ベンダーユーザーとパートナーユーザーの結果をマージ
        $mergedAttendances = $uniqueVendorUserAttendances->merge($uniquePartnerUserAttendances)->values();

        $formattedAttendances = $mergedAttendances->map(function ($attendance) use ($test_id) {
            if ($attendance->vendor_user_id) {
                // 合計受講回数を取得する
                $total_attendances = DB::table('web_test_attendances')
                    ->where('vendor_user_id', $attendance->vendor_user_id)
                    ->where('answer_status', true)
                    ->where('test_id', $test_id)
                    ->count();

                // レスポンスに追加
                return [
                    'company_name' => $attendance->company_name,
                    'user_name' => $attendance->user_name,
                    'user_email' => $attendance->user_email,
                    'latest_attend_at' => date('Y-m-d', strtotime($attendance->latest_attend_at)),
                    'latest_result' => $attendance->latest_result ? __('message_names.passed') : __('message_names.failure'),
                    'passing_mark' => $attendance->passing_mark,
                    'latest_total_points' => $attendance->latest_total_points,
                    'total_attendances' => $total_attendances,
                    'attendance_time' => gmdate('H:i:s', $attendance->attendance_time),
                ];
            } elseif ($attendance->partner_user_id) {
                // 合計受講回数を取得する
                $total_attendances = DB::table('web_test_attendances')
                    ->where('partner_user_id', $attendance->partner_user_id)
                    ->where('answer_status', true)
                    ->where('test_id', $test_id)
                    ->count();

                // レスポンスに追加
                return [
                    'company_name' => $attendance->company_name,
                    'user_name' => $attendance->user_name,
                    'user_email' => $attendance->user_email,
                    'latest_attend_at' => date('Y-m-d', strtotime($attendance->latest_attend_at)),
                    'latest_result' => $attendance->latest_result ? __('message_names.passed') : __('message_names.failure'),
                    'passing_mark' => $attendance->passing_mark,
                    'latest_total_points' => $attendance->latest_total_points,
                    'total_attendances' => $total_attendances,
                    'attendance_time' => gmdate('H:i:s', $attendance->attendance_time),
                ];
            }
        });

        return $formattedAttendances;
    }

    public static function getQuestionList(int $test_id)
    {
        $questions = DB::table('web_test_questions')
            ->where('test_id', $test_id)
            ->orderBy('question_order')
            ->get()
            ->toArray();

        return $questions;
    }

    public static function createQuestion($request, int $test_id)
    {
        $user = Auth::guard('vendor_users')->user();
        if (! self::isOwner($user, $test_id)) {
            throw new Exception('Error: dose not own this test.');
        }
        $orderMax = DB::table('web_test_questions')
            ->where('test_id', $test_id)
            ->max('question_order');
        $newQuestionId = DB::table('web_test_questions')->insertGetId([
            'test_id' => $test_id,
            'question_title' => $request->question_title,
            'question_points' => $request->question_points,
            'question_order' => $orderMax !== null ? $orderMax + 1 : 0,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $newQuestion = DB::table('web_test_questions')
            ->where('id', $newQuestionId)
            ->first();

        return $newQuestion;
    }

    // 設問が編集できるかどうかをテーブルのcan_editの値からチェック
    public static function canEditQuestion(int $question_id)
    {
        $question = DB::table('web_test_questions')
            ->where('id', $question_id)
            ->select('test_id')
            ->first();

        $test = DB::table('web_tests')
            ->where('id', $question->test_id)
            ->select('publish_status')
            ->first();

        if ($test->publish_status === 'published') {
            return false;
        }

        return true;
    }

    public static function updateQuestion($request, int $question_id)
    {
        if (! self::canEditQuestion($question_id)) {
            throw new Exception('Error: can not edit this question.');
        }
        DB::table('web_test_questions')
            ->where('id', $question_id)
            ->update([
                'question_title' => $request->question_title,
                'question_description' => $request->question_description,
                'question_explanation' => $request->question_explanation,
                'question_points' => $request->question_points,
                'question_type' => $request->question_type,
                'question_order' => $request->question_order,
                'updated_at' => now(),
            ]);
    }

    public static function getQuestionDetail(int $question_id)
    {
        $question = DB::table('web_test_questions')
            ->where('id', $question_id)
            ->first();

        return $question;
    }

    public static function bulkDeleteQuestion($request, int $test_id)
    {
        DB::table('web_test_questions')
            ->where('test_id', $test_id)
            ->whereIn('id', $request->question_ids)
            ->delete();
    }

    public static function updateQuestionPointsOrderAndActive($request, int $test_id)
    {
        // 問題の配点と順番を一括更新する
        $questions = $request->questions;
        foreach ($questions as $question) {
            DB::table('web_test_questions')
                ->where('test_id', $test_id)
                ->where('id', $question['question_id'])
                ->orderBy('question_order')
                ->update([
                    'question_points' => $question['question_points'],
                    'question_order' => $question['question_order'],
                    'is_active' => $question['is_active'],
                    'updated_at' => now(),
                ]);
        }
    }

    public static function getOptionList(int $question_id)
    {
        $options = DB::table('web_test_question_options')
            ->where('question_id', $question_id)
            ->get()
            ->toArray();

        return $options;
    }

    public static function createOption($request, int $question_id)
    {
        $orderMax = DB::table('web_test_question_options')
            ->where('question_id', $question_id)
            ->max('option_order');
        $newOptionId = DB::table('web_test_question_options')->insertGetId([
            'question_id' => $question_id,
            'option_order' => $orderMax !== null ? $orderMax + 1 : 0,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $newOption = DB::table('web_test_question_options')
            ->where('id', $newOptionId)
            ->first();

        return $newOption;
    }

    public static function bulkEditOptions($request, int $question_id)
    {
        $options = $request->options;
        foreach ($options as $option) {
            DB::table('web_test_question_options')
                ->where('id', $option['id'])
                ->update([
                    'option_name' => $option['option_name'],
                    'is_correct' => $option['is_correct'],
                    'option_order' => $option['option_order'],
                    'updated_at' => now(),
                ]);
        }
    }

    public static function bulkDeleteOptions($request, int $question_id)
    {
        DB::table('web_test_question_options')
            ->where('question_id', $question_id)
            ->whereIn('id', $request->option_ids)
            ->delete();
    }

    public static function updatePublishStatus($request, int $test_id)
    {
        if ($request->publish_status === 'published') {
            // 公開する場合は、問題を全て取得して、can_option_editをfalseにする
            $questions = DB::table('web_test_questions')
                ->where('test_id', $test_id)
                ->get()
                ->toArray();
            foreach ($questions as $question) {
                DB::table('web_test_questions')
                    ->where('id', $question->id)
                    ->update([
                        'can_options_edit' => false,
                        'updated_at' => now(),
                    ]);
            }
        }
        if ($request->publish_status === 'unpublished') {
            if (TrainingService::isExistRelatedTraining(Auth::guard('vendor_users')->user(), null, $test_id)) {
                throw new ServiceException('Error: cannot be unpublished the test related to training', 400);
            }
        }
        DB::table('web_tests')
            ->where('id', $test_id)
            ->update([
                'publish_status' => $request->publish_status,
                'updated_at' => now(),
            ]);
    }

    public static function getSharePartners(int $test_id)
    {
        $user = Auth::guard('vendor_users')->user();
        if (! self::isOwner($user, $test_id)) {
            throw new ServiceException('Error: does not own this training', 403);
        }

        $partners = DB::table('web_test_partners')
            ->join('partners', 'partners.id', '=', 'web_test_partners.partner_id')
            ->leftJoin('partner_files', 'partners.file_id', '=', 'partner_files.id')
            ->join('vendor_linked_partners', 'partners.id', 'vendor_linked_partners.partner_id')
            ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
            ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
            ->where('vendor_linked_partners.link_status', '=', StatusConst::LINK_STATUS['LINK_ACTIVE']['id'])
            ->where('web_test_partners.test_id', $test_id)
            ->select('vendor_linked_partners.id AS id',
                'vendor_managed_partners.name AS name',
                'partner_files.name AS file_name',
                'partner_files.s3_path AS s3_path')
            ->get()
            ->toArray();
        foreach ($partners as $partner) {
            $partner->s3_path = ! empty($partner->s3_path) ?
                S3Helper::getS3ImageUrl($partner->s3_path) : null;
            $partner->partner_logo_url = ! empty($partner->s3_path) ?
                S3Helper::getS3ImageUrl($partner->s3_path) : null;
        }

        return $partners;
    }

    public static function getLinkedPartner(int $vendor_linked_partner_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $partner = DB::table('vendor_linked_partners')
            ->where('vendor_linked_partners.id', $vendor_linked_partner_id)
            ->join('vendor_managed_partners', 'vendor_managed_partners.id', '=', 'vendor_linked_partners.managed_partner_id')
            ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
            ->select('vendor_linked_partners.partner_id')
            ->first();

        return $partner;
    }

    public static function shareTest($request, int $test_id)
    {
        // $request->vendor_linked_partner_idでvendor_linked_partnersとvendor_managed_partnersをJoinして、そのvendor_idとAuthのvendor_idを照合
        $partner = self::getLinkedPartner($request->vendor_linked_partner_id);
        if (! $partner) {
            throw new Exception('Error: dose not linked on this partner.');
        }
        DB::table('web_test_partners')->updateOrInsert(
            ['test_id' => $test_id, 'partner_id' => $partner->partner_id],
            [
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );
    }

    public static function unShareTest(int $test_id, int $partner_id)
    {
        // この$partner_idはvendor_linked_partner_idであり、
        // web_test_partnersテーブルのpartner_idとは一致しないため、
        // そのままではweb_test_partners.partner_idとして使用できない。
        $partner = self::getLinkedPartner($partner_id);
        DB::table('web_test_partners')
            ->where('test_id', $test_id)
            ->where('partner_id', $partner->partner_id)
            ->delete();
    }

    public static function getTestListByAttend()
    {
        $user = Auth::guard('vendor_users')->user();

        $tests = DB::table('web_tests')
            ->where('vendor_id', $user->vendor_id)
            ->where('publish_status', 'published')
            ->orderBy('updated_at', 'desc')
            ->get()
            ->toArray();

        foreach ($tests as $test) {
            $test->created_at = date('Y-m-d', strtotime($test->created_at));
            $test->updated_at = date('Y-m-d', strtotime($test->updated_at));
        }

        foreach ($tests as $test) {
            $is_attend = DB::table('web_test_attendances')
                ->where('test_id', $test->id)
                ->where('vendor_user_id', $user->id)
                ->exists();

            // 合否を取得する
            $is_passing = DB::table('web_test_attendances')
                ->where('test_id', $test->id)
                ->where('vendor_user_id', $user->id)
                ->where('is_passing', true)
                ->exists();

            $test->is_attended = $is_attend;
            // is_attendがtrueの場合は、is_passingの値を返して、falseの場合はnullを返す
            $test->is_passing = $is_attend ? $is_passing : null;
        }

        return $tests;
    }

    public static function getQuestionListByAttend(int $test_id)
    {
        $questions = DB::table('web_test_questions')
            ->where('test_id', $test_id)
            ->where('is_active', true)
            ->get()
            ->toArray();

        return $questions;
    }

    public static function attendTest(int $test_id)
    {
        $user = Auth::guard('vendor_users')->user();

        if (! self::isOwner($user, $test_id)) {
            throw new Exception('Error: dose not own this test.');
        }

        $test = DB::table('web_tests')
            ->where('web_tests.id', $test_id)
            ->first();

        $attendanceId = DB::table('web_test_attendances')->insertGetId([
            'test_id' => $test_id,
            'vendor_user_id' => $user->id,
            'is_passing' => null,
            'total_points' => null,
            'answer_status' => false,
            'passing_mark' => $test->passing_mark,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $questions = self::getQuestionListByAttend($test_id);

        foreach ($questions as $question) {
            // 回答を作成
            DB::table('web_test_answers')->insert([
                'attendance_id' => $attendanceId,
                'question_id' => $question->id,
                'vendor_user_id' => $user->id,
                'is_correct' => null,
                'question_points' => $question->question_points,
                'question_title' => $question->question_title,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            // web_test_optionsから必要なカラムのみを選択して選択肢を取得
            $question->optionList = DB::table("web_test_question_options")
                ->where("question_id", $question->id)
                ->select(
                    'id',
                    'option_name',
                    'option_order'
                )
                ->get()
                ->toArray();
        }
        $response = [
            'attendance_id' => $attendanceId,
            'questionList' => $questions,
        ];

        return $response;
    }

    public static function answerTest($request, $test_id)
    {
        $user = Auth::guard('vendor_users')->user();

        if (! self::isOwner($user, $test_id)) {
            throw new Exception('Error: dose not own this test.');
        }

        $test = DB::table('web_tests')
            ->where('id', $test_id)
            ->first();

        $attendanceID = $request->attendance_id;
        $answerList = $request->answerList;

        $feedbacks = [];
        $totalPoints = 0;

        foreach ($answerList as $item) {
            $questionId = $item['question_id'];
            $answer = $item['answer'];

            $question = DB::table('web_test_questions')
                ->where('id', $questionId)
                ->first();
            $question_options = DB::table('web_test_question_options')
                ->where('question_id', $questionId)
                ->get();

            if ($question->question_type == 2) {
                // 複数回答式の場合

                // 正解の選択肢を全て取得しIDの配列に変換
                $correctOptions = $question_options
                    ->where('question_id', $questionId)
                    ->where('is_correct', true)
                    ->pluck('id')
                    ->toArray();

                // 回答と正解を比較するためにソートする
                sort($correctOptions);
                sort($answer);

                // 正解かどうかを判定する
                $is_correct = $correctOptions === $answer;

                $correct_answer_orders = [];
                $user_answer_orders = [];

                foreach ($correctOptions as $option_id) {
                    $option = $question_options
                        ->where('id', $option_id)
                        ->first();

                    $correct_answer_orders[] = $option->option_order;
                }
                foreach ($answer as $answerItem) {
                    $user_answer_orders[] = $question_options
                        ->where('id', $answerItem)
                        ->first()
                        ->option_order;
                }
                // 回答をカンマ区切りの文字列に変換する
                $correct_answer = implode(',', $correct_answer_orders);
                $user_answer = implode(',', $user_answer_orders);

                // answerを回してweb_test_answer_optionsに保存する
                foreach ($answer as $optionId) {
                    // $attendanceIdと$answer['question_id']を使ってweb_test_answersからidを取得する
                    $answerId = DB::table('web_test_answers')
                        ->where('attendance_id', $attendanceID)
                        ->where('question_id', $questionId)
                        ->first()->id;
                    // web_test_answer_optionsに保存する
                    DB::table('web_test_answer_options')->insert([
                        'answer_id' => $answerId,
                        'option_id' => $optionId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    // answersのis_correctを更新する
                    DB::table('web_test_answers')
                        ->where('id', $answerId)
                        ->update([
                            'is_correct' => $is_correct,
                            'updated_at' => now(),
                        ]);
                }
            } else {
                // 単一回答式の場合

                // 正解の選択肢を取得する
                $correctOption = $question_options
                    ->where('question_id', $questionId)
                    ->where('is_correct', true)
                    ->first();

                // 正解かどうかを判定する
                $is_correct = $correctOption->id == $answer;

                // 選択肢に対応するorderを取得して格納する
                $user_answer = $question_options
                    ->where('id', $answer)
                    ->first()
                    ->option_order;
                $correct_answer = $question_options
                    ->where('id', $correctOption->id)
                    ->first()
                    ->option_order;

                // $attendanceIdと$answer['question_id']を使ってweb_test_answersからidを取得する
                $answerId = DB::table('web_test_answers')
                    ->where('attendance_id', $attendanceID)
                    ->where('question_id', $questionId)
                    ->first()->id;

                // web_test_answer_optionsに保存する
                DB::table('web_test_answer_options')->insert([
                    'answer_id' => $answerId,
                    'option_id' => $answer,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // answersのis_correctを更新する
                DB::table('web_test_answers')
                    ->where('id', $answerId)
                    ->update([
                        'is_correct' => $is_correct,
                        'updated_at' => now(),
                    ]);
            }

            $feedbacks[] = [
                'question_id' => $questionId,
                'question_title' => $question->question_title,
                'question_description' => $question->question_description,
                'question_explanation' => $question->question_explanation,
                'question_points' => $question->question_points,
                'question_order' => $question->question_order,
                'is_correct' => $is_correct,
                'user_answer' => $user_answer,
                'correct_answer' => $correct_answer,
                'question_options' => $question_options,
                'question_type' => $question->question_type,
            ];
            // 正解の場合は得点を加算する
            $totalPoints += $is_correct ? $question->question_points : 0;
        }

        $attendance = DB::table('web_test_attendances')
            ->where('id', $attendanceID)
            ->first();

        $isPassing = $totalPoints >= $attendance->passing_mark ? true : false;

        $attendanceTime = now()->diffInSeconds($attendance->created_at);

        DB::table('web_test_attendances')
            ->where('id', $attendanceID)
            ->update([
                'total_points' => $totalPoints,
                'is_passing' => $isPassing,
                'answer_status' => true,
                'attendance_time' => $attendanceTime,
                'updated_at' => now(),
            ]);

        return [
            'isPassing' => $isPassing,
            'passing_mark' => $test->passing_mark,
            'total_points' => $totalPoints,
            'questions' => $feedbacks,
        ];
    }
}
