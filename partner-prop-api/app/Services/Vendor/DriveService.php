<?php

namespace App\Services\Vendor;

use App\Const\StatusConst;
use App\Enum\AuditAction;
use App\Enum\AuditDestTo;
use App\Enum\AuditResult;
use App\Enum\AuditTarget;
use App\Exceptions\ServiceException;
use App\Helpers\S3Helper;
use App\Repositories\Accounts\PartnerTeamMemberRepository;
use App\Services\AuditLog\AuditLogServiceInterface;
use App\Services\AuditLog\Dto\PortalAuditLogDto;
use Aws\S3\S3Client;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use stdClass;
use Storage;

class DriveService
{
    private const DRIVE_MAX_LEVEL = 5;

    public static function getFile($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        $curFile = $file;
        $breadcrumbs = [];
        do {
            array_push($breadcrumbs, [
                'id' => $curFile->id,
                'name' => $curFile->name,
            ]);
            $curFile = DB::table('files')
                ->where('id', $curFile->parent)
                ->first();
        } while ($curFile !== null);

        $file->breadcrumbs = array_reverse($breadcrumbs);

        return $file;
    }

    public static function createFile($request, $file_id)
    {
        $audit_log_service = app(AuditLogServiceInterface::class);
        $user = Auth::guard('vendor_users')->user();
        $vendor_collaboration_id = DB::table('vendors')->where('id', $user->vendor_id)->value('vendor_collaboration_id');
        $parent_file_id = null;
        $x_forwarded_for = $request->header('X-Forwarded-For');
        $user_ip = '';
        $parent_file = null;

        // X-Forwarded-For ヘッダーが存在するか確認
        if (! empty($x_forwarded_for)) {
            $ips = explode(',', $x_forwarded_for);
            $user_ip = trim(end($ips)); // 配列の最後の要素を取得してトリムします
        }

        if ($file_id > 0) {
            $parent_file = DB::table('files')
                            ->where('id', $file_id)
                            ->where('vendor_id', $user->vendor_id)
                            ->first();
        }

        switch ($request->type) {
            case 'normal':
                $file = $request->file('file');
                if (is_null($file) && !$request->has('is_document') ) {
                    throw new ServiceException('Error: normal file is required', 400);
                }

                if ($request->has('content') && empty($request->content)) {
                    throw new ServiceException('Error: normal content document is required', 400);
                }

                if (!$request->has('is_document')) {
                    $file_name = $file->getClientOriginalName();
                    $file_size = $file->getSize();
                }else {
                    $file_name = $request->name;
                    $file_size = 0;
                }

                $thumbnail = $request->file('thumbnail');
                if (! empty($thumbnail)) {
                    $thumbnail_s3_dir = $vendor_collaboration_id.'/drive/thumbnails/'.date('Ymd').'/';
                    $thumbnail_name = Str::random(6).'.'.$thumbnail->getClientOriginalName();
                }
                break;
            case 'directory':
                if (is_null($request->name)) {
                    throw new ServiceException('Error: directory name is required', 400);
                }
                if (isset($parent_file) && $parent_file->depth >= DriveService::DRIVE_MAX_LEVEL - 1) {
                    throw new ServiceException('Error: depth limit exceeds', 400);
                }
                $file_name = $request->name;
                $file_size = 0;
                break;
        }

        // if target is not root directory
        try {
            if ($file_id !== 0) {
                if ($parent_file === null) {
                    throw new ServiceException('Error: cannot create a file from non-directory', 400);
                }
                if ($parent_file->type !== 'directory') {
                    throw new ServiceException('Error: cannot create a file in a normal file', 400);
                }
                if ($user->vendor_id !== $parent_file->vendor_id) {
                    throw new ServiceException('Error: cannot access to the file', 400);
                }
                if ($parent_file->portal_id !== (int) $request->portal_id) {
                    throw new ServiceException('Error: cannot create a file in a different portal tab', 400);
                }
                $parent_file_id = $file_id;
            }
        } catch (ServiceException $e) {
            if ($request->type === 'normal') {
                $audit_log_service->write(
                    new PortalAuditLogDto(
                        $user_ip,
                        AuditTarget::Portal,
                        AuditAction::FileCreate,
                        AuditResult::Failed,
                        AuditDestTo::Vendor,
                        $file_name,
                        $user->email,
                        $user->vendor->vendor_collaboration_id,
                    )
                );
            }
            throw $e;
        }

        DB::beginTransaction();
        try {
            if (isset($parent_file)) {
                $s3_dir = $vendor_collaboration_id.'/drive/'.date('Ymd').'/';
                $depth = $parent_file->depth + 1;
            } else {
                $s3_dir = $vendor_collaboration_id.'/drive/'.date('Ymd').'/';
                $depth = 1;
            }

            $auto_id = DB::table('files')->max('id') + 1;
            $ext = pathinfo($file_name, PATHINFO_EXTENSION);
            $id_name = $auto_id.'.'.$ext;

            DB::table('files')
                ->insert([
                    'id' => $auto_id,
                    'uuid' => Str::uuid(),
                    'vendor_id' => $user->vendor_id,
                    'parent' => $parent_file_id,
                    'type' => $request->type,
                    'name' => $file_name,
                    'size' => $file_size,
                    'depth' => $depth,
                    's3_path' => $s3_dir.$id_name,
                    'thumbnail_path' => empty($thumbnail_s3_dir) ? null : $thumbnail_s3_dir.$thumbnail_name,
                    'portal_id' => $request->portal_id,
                    'content' => $request->content,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);

            if ($request->type === 'normal') {
                if (!$request->has('is_document')) {
                    Storage::disk('s3-drive')->putFileAs($s3_dir, $file, $id_name);
                }
                if (! empty($thumbnail)) {
                    Storage::disk('s3-drive')->putFileAs($thumbnail_s3_dir, $thumbnail, $thumbnail_name);
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            if ($request->type === 'normal') {
                $audit_log_service->write(
                    new PortalAuditLogDto(
                        $user_ip,
                        AuditTarget::Portal,
                        AuditAction::FileCreate,
                        AuditResult::Failed,
                        AuditDestTo::Vendor,
                        $file_name,
                        $user->email,
                        $user->vendor->vendor_collaboration_id,
                    )
                );
            }
            throw $e;
        }
        if ($request->type === 'normal') {
            $audit_log_service->write(
                new PortalAuditLogDto(
                    $user_ip,
                    AuditTarget::Portal,
                    AuditAction::FileCreate,
                    AuditResult::Success,
                    AuditDestTo::Vendor,
                    $file_name,
                    $user->email,
                    $user->vendor->vendor_collaboration_id,
                )
            );
        }
    }

    public static function deleteFile($request, $file_id)
    {
        $audit_log_service = app(AuditLogServiceInterface::class);
        $user = Auth::guard('vendor_users')->user();
        $parent_file_id = null;
        $x_forwarded_for = $request->header('X-Forwarded-For');
        $user_ip = '';
        $is_file = false;
        $file_names = [];

        // X-Forwarded-For ヘッダーが存在するか確認
        if (! empty($x_forwarded_for)) {
            $ips = explode(',', $x_forwarded_for);
            $user_ip = trim(end($ips)); // 配列の最後の要素を取得してトリムします
        }

        // 削除対象がファイルか判定
        $file_type = DB::table('files')->where('id', $file_id)->first()->type;
        if ($file_type === 'normal') {
            $is_file = true;
            $file_names[] = DB::table('files')->where('id', $file_id)->first()->name;
        }

        // if target is not root directory
        try {
            if ($file_id !== 0) {
                $parent_file = DB::table('files')->where('id', $file_id)->first();
                if ($parent_file === null) {
                    throw new ServiceException('Error: cannot delete from non-directory', 400);
                }
                if ($user->vendor_id !== $parent_file->vendor_id) {
                    throw new ServiceException('Error: cannot access to the file', 400);
                }
                $parent_file_id = $file_id;
            }

            if (TrainingService::isExistRelatedTraining($user, $file_id, null)) {
                throw new ServiceException('Error: cannot delete the file related to training', 400);
            }

            $all_children = [];
            // このfile_idが親となるfileを取得
            self::getAllChildren($file_id, $user, $all_children);

            // type=normalのfileのみ残す
            $all_children = array_filter($all_children, function ($child) {
                return $child->type === 'normal';
            });

            foreach ($all_children as $child) {
                if (TrainingService::isExistRelatedTraining($user, $child->id, null)) {
                    throw new ServiceException('Error: cannot delete the file related to training', 400);
                }
            }
        } catch (ServiceException $e) {
            if ($is_file) {
                $audit_log_service->write(
                    new PortalAuditLogDto(
                        $user_ip,
                        AuditTarget::Portal,
                        AuditAction::FileDelete,
                        AuditResult::Failed,
                        AuditDestTo::Vendor,
                        $file_names[0],
                        $user->email,
                        $user->vendor->vendor_collaboration_id,
                    )
                );
            }
            throw $e;
        }

        DB::beginTransaction();
        try {
            if (isset($parent_file_id)) {
                if (! $is_file) {
                    $files = DB::table('files')
                        ->where('parent', $parent_file_id)
                        ->get();
                    foreach ($files as $file) {
                        $file_names[] = $file->name;
                    }
                }
                DB::table('files')
                    ->where('id', $parent_file_id)
                    ->delete();
            } else {
                $query = DB::table('files')
                    ->where('parent', null)
                    ->where('vendor_id', $user->vendor_id)
                    ->select('s3_path');
                $files = $query->get();
                $query->delete();
                foreach ($files as $file) {
                    if ($file->type === 'directory') {
                        Storage::disk('s3')->deleteDirectory($file->s3_path);
                    } else {
                        Storage::disk('s3')->delete($file->s3_path);
                    }
                }
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            foreach ($file_names as $file_name) {
                $audit_log_service->write(
                    new PortalAuditLogDto(
                        $user_ip,
                        AuditTarget::Portal,
                        AuditAction::FileDelete,
                        AuditResult::Failed,
                        AuditDestTo::Vendor,
                        $file_name,
                        $user->email,
                        $user->vendor->vendor_collaboration_id,
                    )
                );
            }
            throw $e;
        }
        foreach ($file_names as $file_name) {
            $audit_log_service->write(
                new PortalAuditLogDto(
                    $user_ip,
                    AuditTarget::Portal,
                    AuditAction::FileDelete,
                    AuditResult::Success,
                    AuditDestTo::Vendor,
                    $file_name,
                    $user->email,
                    $user->vendor->vendor_collaboration_id,
                )
            );
        }
    }

    public static function updateFile($request, $file_id)
    {
        $audit_log_service = app(AuditLogServiceInterface::class);
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        $file_type = $file->type;
        $file_name = $request->name;
        $x_forwarded_for = $request->header('X-Forwarded-For');
        $user_ip = '';

        // X-Forwarded-For ヘッダーが存在するか確認
        if (! empty($x_forwarded_for)) {
            $ips = explode(',', $x_forwarded_for);
            $user_ip = trim(end($ips)); // 配列の最後の要素を取得してトリムします
        }

        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        DB::beginTransaction();
        try {
            DB::table('files')
                ->where('id', $file_id)
                ->update([
                    'name' => $request->name,
                    'content' => $request->content ?? null,
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            if ($file_type === 'normal') {
                $audit_log_service->write(
                    new PortalAuditLogDto(
                        $user_ip,
                        AuditTarget::Portal,
                        AuditAction::FileChangeName,
                        AuditResult::Failed,
                        AuditDestTo::Vendor,
                        $file_name,
                        $user->email,
                        $user->vendor->vendor_collaboration_id,
                    )
                );
            }
            throw $e;
        }
        if ($file_type === 'normal') {
            $audit_log_service->write(
                new PortalAuditLogDto(
                    $user_ip,
                    AuditTarget::Portal,
                    AuditAction::FileChangeName,
                    AuditResult::Success,
                    AuditDestTo::Vendor,
                    $file_name,
                    $user->email,
                    $user->vendor->vendor_collaboration_id,
                )
            );
        }
    }

    public static function updateIsLimitDownloadAll($request, $file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        DB::beginTransaction();
        try {
            DB::table('files')
                ->where('id', $file_id)
                ->update([
                    'is_limit_download_all' => $request->is_limit_download_all,
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public static function getPortalList()
    {
        $user = Auth::guard('vendor_users')->user();
        $portals = DB::table('portals')
            ->where('vendor_id', $user->vendor_id)
            ->orderBy('sort_order')
            ->get()
            ->toArray();

        return $portals;
    }

    public static function createPortal($request)
    {
        $user = Auth::guard('vendor_users')->user();

        $max_sort_order = DB::table('portals')
            ->where('vendor_id', $user->vendor_id)
            ->max('sort_order');

        $sort_order = is_null($max_sort_order) ? 0 : $max_sort_order + 1;
        DB::table('portals')
            ->insert([
                'vendor_id' => $user->vendor_id,
                'name' => $request->name,
                'sort_order' => $sort_order,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
    }

    public static function bulkUpdatePortal($request)
    {
        $user = Auth::guard('vendor_users')->user();
        $new_portal_list = $request->portal_list;
        $current_portal_list = DB::table('portals')
            ->where('vendor_id', $user->vendor_id)
            ->get()
            ->toArray();

        foreach ($current_portal_list as $current_portal) {
            // new_portal_listに存在しないportalを削除
            if (! in_array($current_portal->id, array_column($new_portal_list, 'id'))) {
                DB::table('portals')
                    ->where('id', $current_portal->id)
                    ->delete();
            } else {
                // new_portal_listに存在するportalのsort_orderを更新
                $new_portal_key = array_search($current_portal->id, array_column($new_portal_list, 'id'));
                $new_portal = $new_portal_list[$new_portal_key];
                DB::table('portals')
                    ->where('id', $current_portal->id)
                    ->update([
                        'name' => $new_portal['name'],
                        'sort_order' => $new_portal['sort_order'],
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]);
            }
        }
    }

    public static function getHeader()
    {
        $user = Auth::guard('vendor_users')->user();
        $header = DB::table('portal_headers')
            ->where('vendor_id', $user->vendor_id)
            ->first();

        $res = new stdClass;

        // デフォルトの格納場所を指定
        if ($header === null) {
            $res->url = '';

            return $res;
        }

        // S3クライアントを初期化
        $s3Client = new S3Client([
            'region' => config('filesystems.disks.s3.region'),
            'version' => 'latest',
            'credentials' => [
                'key' => config('filesystems.disks.s3.key'),
                'secret' => config('filesystems.disks.s3.secret'),
            ],
        ]);

        // デフォルトの格納場所を指定
        if ($header === null) {
            $header = collect();
            $header->s3_path = 'portal/images/default/portal_header.png';
        }
        // Presigned URLの生成
        $cmd = $s3Client->getCommand('GetObject', [
            'Bucket' => config('filesystems.disks.s3.bucket'),
            'Key' => $header->s3_path,
        ]);
        $request = $s3Client->createPresignedRequest($cmd, '+1440 minutes'); // 20分後に期限が切れるURLを生成
        $presignedUrl = (string) $request->getUri();

        // Content-Typeの取得
        $objectMetadata = $s3Client->headObject([
            'Bucket' => config('filesystems.disks.s3.bucket'),
            'Key' => $header->s3_path,
        ]);

        $contentType = $objectMetadata['ContentType'];
        $res->url = $presignedUrl; // Presigned URLを設定
        $res->contentType = $contentType; // Content-Typeを設定

        return $res;
    }

    public static function createOrUpdateHeader($request)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = $request->file('header');
        $header = DB::table('portal_headers')
            ->where('vendor_id', $user->vendor_id)
            ->first();

        $file_name = $file->getClientOriginalName();
        $file_size = $file->getSize();
        $s3_dir = 'portal/'.$user->vendor_id.'/';

        $auto_id = DB::table('portal_headers')->max('id') + 1;
        $ext = pathinfo($file_name, PATHINFO_EXTENSION);
        $id_name = $auto_id.'.'.$ext;

        Storage::disk('s3')->putFileAs($s3_dir, $file, $id_name);

        if ($header === null) {
            DB::table('portal_headers')
                ->insert([
                    'vendor_id' => $user->vendor_id,
                    'name' => $file_name,
                    'size' => $file_size,
                    's3_path' => $s3_dir.$id_name,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
        } else {
            DB::table('portal_headers')
                ->where('vendor_id', $user->vendor_id)
                ->update([
                    'name' => $file_name,
                    'size' => $file_size,
                    's3_path' => $s3_dir.$id_name,
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
        }
    }

    public static function getFileList($file_id, $portal_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $parent_file = null;
        $parent_file_id = null;

        // if target is not root directory
        if ($file_id !== 0) {
            $parent_file = DB::table('files')->where('id', $file_id)->first();
            if ($parent_file === null) {
                throw new ServiceException('Error: the directory does not exist', 404);
            }
            if ($user->vendor_id !== $parent_file->vendor_id) {
                throw new ServiceException('Error: cannot access to the file', 403);
            }
            $parent_file_id = $file_id;
        }

        if (isset($parent_file) && $parent_file->type !== 'directory') {
            throw new ServiceException('Error: cannot get list from non-directory', 400);
        }

        $selected_portal = DB::table('portals')
            ->where('vendor_id', $user->vendor_id)
            ->where('id', $portal_id)
            ->first();

        if ($selected_portal === null) {
            // if target portal is not exist
            throw new ServiceException('Error: the portal does not exist', 404);
        } elseif ($selected_portal->vendor_id !== $user->vendor_id) {
            // if target portal is not belong to the vendor
            throw new ServiceException('Error: cannot access to the portal', 403);
        }

        $curFile = $parent_file;
        $breadcrumbs = [];
        while ($curFile !== null) {
            array_push($breadcrumbs, [
                'id' => $curFile->id,
                'name' => $curFile->name,
            ]);
            $curFile = DB::table('files')
                ->where('id', $curFile->parent)
                ->first();
        }

        $files = DB::table('files')
            ->where('parent', $parent_file_id)
            ->where('vendor_id', $user->vendor_id)
            ->where('portal_id', $portal_id)
            ->get()
            ->toArray();

        $res = new stdClass;
        $res->files = $files;
        $res->breadcrumbs = array_reverse($breadcrumbs);

        if ($file_id == 0) {
            $collab_files = DB::table('collaborative_files')
                ->where('parent', $parent_file_id)
                ->where('vendor_id', $user->vendor_id)
                ->where('portal_id', $portal_id)
                ->get()
                ->toArray();
            $res->collab_files = $collab_files;
        }

        return $res;
    }

    public static function getAccessLogs($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        $file_access_logs = DB::table('file_access_logs')
            ->where('file_id', $file_id)
            ->get()
            ->toArray();

        // 関連するpartner_user_idsを取得
        $partner_user_ids = array_column($file_access_logs, 'partner_user_id');

        // 一度のクエリで関連するパートナーユーザーのデータを取得
        $partner_users = DB::table('partner_users')
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->whereIn('partner_users.id', $partner_user_ids)
            ->select(
                'partner_users.id',
                'partner_users.name',
                'partner_users.email',
                'partner_files.name AS file_name',
                'partner_files.s3_path AS s3_path'
            )
            ->get()
            ->keyBy('id')
            ->toArray();

        // 一度のクエリで関連するパートナーの名前を取得
        $partner_ids = array_column($file_access_logs, 'partner_id');
        $partners = DB::table('partners')
            ->whereIn('id', $partner_ids)
            ->pluck('name', 'id')
            ->toArray();

        foreach ($file_access_logs as $log) {
            $log->partner_user_name = $partner_users[$log->partner_user_id]->name;
            $log->email = $partner_users[$log->partner_user_id]->email;
            $log->partner_name = $partners[$log->partner_id];
            $log->partner_avatar_name = $partner_users[$log->partner_user_id]->file_name;
            $log->partner_avatar_url = ! empty($partner_users[$log->partner_user_id]->s3_path) ?
                S3Helper::getS3ImageUrl($partner_users[$log->partner_user_id]->s3_path) : null;
        }

        $groupedData = [];

        foreach ($file_access_logs as $item) {
            $key = $item->partner_name;

            // 会社名でグループ化
            if (! isset($groupedData[$key])) {
                $groupedData[$key] = [];
            }
            $groupedData[$key][] = $item;
        }

        return $groupedData;
    }

    public static function getDownloadLogs($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        $file_download_logs = DB::table('file_download_logs')
            ->where('file_id', $file_id)
            ->get()
            ->toArray();

        // 関連するpartner_user_idsを取得
        $partner_user_ids = array_column($file_download_logs, 'partner_user_id');

        // 一度のクエリで関連するパートナーユーザーのデータを取得
        $partner_users = DB::table('partner_users')
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->whereIn('partner_users.id', $partner_user_ids)
            ->select(
                'partner_users.id',
                'partner_users.name',
                'partner_users.email',
                'partner_files.name AS file_name',
                'partner_files.s3_path AS s3_path'
            )
            ->get()
            ->keyBy('id')
            ->toArray();

        // 一度のクエリで関連するパートナーの名前を取得
        $partner_ids = array_column($file_download_logs, 'partner_id');
        $partners = DB::table('partners')
            ->whereIn('id', $partner_ids)
            ->pluck('name', 'id')
            ->toArray();

        foreach ($file_download_logs as $log) {
            $log->partner_user_name = $partner_users[$log->partner_user_id]->name;
            $log->email = $partner_users[$log->partner_user_id]->email;
            $log->partner_name = $partners[$log->partner_id];
            $log->partner_avatar_name = $partner_users[$log->partner_user_id]->file_name;
            $log->partner_avatar_url = ! empty($partner_users[$log->partner_user_id]->s3_path) ?
                S3Helper::getS3ImageUrl($partner_users[$log->partner_user_id]->s3_path) : null;
        }

        $groupedData = [];

        foreach ($file_download_logs as $item) {
            $key = $item->partner_name;

            // 会社名でグループ化
            if (! isset($groupedData[$key])) {
                $groupedData[$key] = [];
            }
            $groupedData[$key][] = $item;
        }

        return $groupedData;
    }

    public static function viewFile($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        /* $file_path = $file->s3_path; */
        /* $file_name = $file->name; */
        /* $mimeType = Storage::disk('s3')->mimeType($file_path); */
        /* $headers = [['Content-Type' => $mimeType]]; */
        /* return Storage::disk('s3')->response($file_path, $file_name, $headers); */

        $s3Client = Storage::disk('s3-drive');

        $s3_url = $s3Client->temporaryUrl($file->s3_path, now()->addMinutes(1440));
        $contentType = $s3Client->mimeType($file->s3_path);

        $res = new stdClass;
        $res->url = $s3_url; // Presigned URLを設定
        $res->contentType = $contentType; // Content-Typeを設定

        return $res;
    }

    public static function downloadFile($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }

        if ($user->vendor_id !== $file->vendor_id) {
            throw new ServiceException('Error: cannot access to the file', 403);
        }

        $encodedName = rawurlencode($file->name);
        $disposition = "attachment; filename*=UTF-8''{$encodedName}";
        // PROP-5101にてこのコードを実装
        // TODO: リリース後にPresignedURL取得ロジックを共通化する。
        $presignedUrl = Storage::disk('s3-drive')->temporaryUrl(
            $file->s3_path,
            now()->addMinutes(20),
            ['ResponseContentDisposition' => $disposition]
        );

        return $presignedUrl;
    }

    /**
     * ファイルの権限を付与する。（パートナーチーム単位）
     *
     * shared_file_root_with_partners に partner_id を登録し、
     * unshared_file_root_with_partners から partner_id を削除する。
     */
    public static function shareFile($request, $file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        self::validateFileAccess($user, $file);

        $partner_id = DB::table('vendor_linked_partners')
            ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
            ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
            ->where('vendor_linked_partners.id', $request->partner_id) //partner_idはvendor_linked_partnersのid
            ->value('partner_id');
        Log::debug($partner_id);

        $partner = DB::table('vendor_managed_partners')
            ->join(
                'vendor_linked_partners',
                'vendor_managed_partners.id',
                'vendor_linked_partners.managed_partner_id'
            )
            ->where('vendor_id', $user->vendor_id)
            ->where('partner_id', $partner_id)
            ->first();

        if ($partner->link_status !== StatusConst::LINK_STATUS['LINK_ACTIVE']['id']) {
            throw new ServiceException('Error: cannot shared the file', 400);
        }

        //権限付与する1階層目のfileを取得する
        $root_file = self::getRootFileWithFileId($file_id);
        //もしroot_fileのparentが存在する場合はエラーを返す
        if ($root_file->parent !== null) {
            throw new ServiceException('Error: cannot share the file', 400);
        }

        DB::beginTransaction();
        try {
            //権限追加処理
            DB::table('shared_file_root_with_partners')
                ->updateOrInsert(
                    [
                        'partner_id' => $partner_id,
                        'file_id' => $root_file->id,
                    ],
                    [
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]
                );
            //権限削除処理
            DB::table('unshared_file_root_with_partners')
                ->where('partner_id', $partner_id)
                ->where('file_id', $root_file->id)
                ->delete();
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public static function shareFileWithUser($request, $file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();

        $partner_team_member = PartnerTeamMemberRepository::fetchByPartnerIdAndPartnerUserId($request->partner_id, $request->partner_user_id);
        if ($partner_team_member === null) {
            throw new ServiceException('Error: the partner team member does not exist', 404);
        }

        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        //権限付与する1階層目のfileを取得する
        $root_file = self::getRootFileWithFileId($file_id);
        //もしroot_fileのparentが存在する場合はエラーを返す
        if ($root_file->parent !== null) {
            throw new ServiceException('Error: cannot share the file', 400);
        }

        DB::beginTransaction();
        try {
            //権限追加処理
            DB::table('shared_file_roots')
                ->updateOrInsert(
                    [
                        'partner_id' => $partner_team_member->partner_id,
                        'partner_user_id' => $partner_team_member->partner_user_id,
                        'file_id' => $root_file->id,
                    ],
                    [
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]
                );
            //権限削除処理
            DB::table('unshared_file_roots')
                ->where('partner_id', $partner_team_member->partner_id)
                ->where('partner_user_id', $partner_team_member->partner_user_id)
                ->where('file_id', $root_file->id)
                ->delete();
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public static function shareFileWithAllPartner(int $file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        //権限付与する1階層目のfileを取得する
        $root_file = self::getRootFileWithFileId($file_id);

        //もしroot_fileのparentが存在する場合はエラーを返す
        if ($root_file->parent !== null) {
            throw new ServiceException('Error: cannot share the file', 400);
        }

        try {
            //権限追加処理
            DB::table('shared_file_root_with_all_partners')
                ->insert([
                    'file_id' => $root_file->id,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * ファイルの権限を削除する。（パートナーチーム単位）
     *
     * unshared_file_root_with_partners に partner_id を登録し、
     * shared_file_root_with_partners に partner_id を削除し、
     * shared_file_roots の partner_user_id を削除する。
     */
    public static function unShareFile($request, $file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        // $request->partner_id は vendor_linked_partnersのid
        // この$partner_id は正しくpartnersのid
        $partner_id = DB::table('vendor_linked_partners')
            ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
            ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
            ->where('vendor_linked_partners.id', $request->partner_id)
            ->value('partner_id');

        $partner = DB::table('vendor_managed_partners')
            ->join(
                'vendor_linked_partners',
                'vendor_managed_partners.id',
                'vendor_linked_partners.managed_partner_id'
            )
            ->where('vendor_id', $user->vendor_id)
            ->where('partner_id', $partner_id)
            ->sole();

        if ($partner->link_status !== StatusConst::LINK_STATUS['LINK_ACTIVE']['id']) {
            throw new ServiceException('Error: cannot shared the file', 400);
        }

        //権限付与する1階層目のfileを取得する
        $root_file = self::getRootFileWithFileId($file_id);
        //もしroot_fileのparentが存在する場合はエラーを返す
        if ($root_file->parent !== null) {
            throw new ServiceException('Error: cannot unshare the file', 400);
        }

        DB::beginTransaction();
        try {
            //企業の権限を削除
            //権限削除登録
            DB::table('unshared_file_root_with_partners')
                ->updateOrInsert(
                    [
                        'partner_id' => $partner_id,
                        'file_id' => $root_file->id,
                    ],
                    [
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]
                );
            //権限追加削除
            DB::table('shared_file_root_with_partners')
                ->where('partner_id', $partner_id)
                ->where('file_id', $root_file->id)
                ->delete();
            //企業に紐づくユーザーの権限を削除
            DB::table('shared_file_roots')
                ->where('partner_id', $partner_id)
                ->where('file_id', $root_file->id)
                ->whereIn('partner_user_id', function ($query) use ($partner_id) {
                    //企業に紐付くユーザーを取得
                    $query->select('partner_user_id')
                        ->from('partner_team_members')
                        ->where('partner_id', $partner_id);
                })
                ->delete();

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public static function unShareFileWithUser($request, $file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        $partner_team_member = PartnerTeamMemberRepository::fetchByPartnerIdAndPartnerUserId($request->partner_id, $request->partner_user_id);
        if ($partner_team_member === null) {
            throw new ServiceException('Error: the partner team member does not exist', 400);
        }

        //権限付与する1階層目のfileを取得する
        $root_file = self::getRootFileWithFileId($file_id);
        //もしroot_fileのparentが存在する場合はエラーを返す
        if ($root_file->parent !== null) {
            throw new ServiceException('Error: cannot unshare the file', 400);
        }

        DB::beginTransaction();
        try {
            //権限削除登録
            DB::table('unshared_file_roots')
                ->updateOrInsert(
                    [
                        'partner_id' => $partner_team_member->partner_id,
                        'partner_user_id' => $partner_team_member->partner_user_id,
                        'file_id' => $root_file->id,
                    ],
                    [
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ]
                );
            //権限追加削除
            DB::table('shared_file_roots')
                ->where('partner_id', $partner_team_member->partner_id)
                ->where('partner_user_id', $partner_team_member->partner_user_id)
                ->where('file_id', $root_file->id)
                ->delete();
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public static function unShareFileWithAllPartner(int $file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        //権限付与する1階層目のfileを取得する
        $root_file = self::getRootFileWithFileId($file_id);
        //もしroot_fileのparentが存在する場合はエラーを返す
        if ($root_file->parent !== null) {
            throw new ServiceException('Error: cannot unshare the file', 400);
        }

        try {
            //権限削除登録
            DB::table('shared_file_root_with_all_partners')
                ->where('file_id', $root_file->id)
                ->delete();
        } catch (Exception $e) {
            throw $e;
        }
    }

    private static function getAllChildren($parent_id, $vendor_user, &$all_children)
    {
        $children = DB::table('files')
            ->where('parent', $parent_id)
            ->where('vendor_id', $vendor_user->vendor_id)
            ->get();

        foreach ($children as $child) {
            $all_children[] = $child;
            self::getAllChildren($child->id, $vendor_user, $all_children);
        }
    }

    private static function getRootFileWithFileId(int $file_id)
    {
        $file = DB::table('files')->where('id', $file_id)->first();
        //権限付与するfileを確認する
        //file_idから1階層目のfileを取得
        for ($i = 0; $i < 7; $i++) {
            if ($file->parent === null) {
                break;
            }
            //権限付与されるディレクトリまたはファイル
            $file = DB::table('files')->where('id', $file->parent)->first();
        }

        return $file;
    }

    private static function getRootCollabFileWithFileId(int $file_id)
    {
        $file = DB::table('collaborative_files')->where('id', $file_id)->first();
        //権限付与するfileを確認する
        //file_idから1階層目のfileを取得
        for ($i = 0; $i < 7; $i++) {
            if ($file->parent === null) {
                break;
            }
            //権限付与されるディレクトリまたはファイル
            $file = DB::table('collaborative_files')->where('id', $file->parent)->first();
        }

        return $file;
    }

    public static function isSharedAllPartners(int $file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        $root_file = self::getRootFileWithFileId($file_id);

        // 全てのパートナーに共有されているかどうかを確認
        $isSharedWithAllPartners = DB::table('shared_file_root_with_all_partners')
            ->where('file_id', $root_file->id)
            ->exists();

        // 共有状態に基づいてレスポンスを構築
        if ($isSharedWithAllPartners) {
            $response = [
                'data' => [
                    'allPartnersAuthorized' => $isSharedWithAllPartners,
                ],
                'message' => '全パートナーへの権限が許可されています。',
            ];
        } else {
            $response = [
                'data' => [
                    'allPartnersAuthorized' => $isSharedWithAllPartners,
                ],
                'message' => '全パートナーへの権限が許可されていません。',
            ];
        }

        return $response;
    }

    /**
     * ファイルの権限を確認してpartnersを返す。
     *
     * (un)shared_file_root_with_partners を確認してpartnersを返す。
     * shared_file_root_with_all_partners は考慮しない。
     */
    public static function getSharedPartners($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        //file_idの権限にひもづくpartner_idを取得
        $partners = [];
        while (true) {
            $shared_partners = DB::table('shared_file_root_with_partners')
                ->where('file_id', $file->id)
                ->pluck('partner_id')
                ->all();

            $unshared_partners = DB::table('unshared_file_root_with_partners')
                ->where('file_id', $file->id)
                ->pluck('partner_id')
                ->all();

            $partners = array_merge($partners, $shared_partners, $unshared_partners); // 合併された配列

            // $fileがnullであるか、親の情報がnullである場合はループを終了
            if (! $file || $file->parent === null) {
                break;
            }
            // 親ディレクトリの情報を取得
            $file = DB::table('files')->where('id', $file->parent)->first();
        }

        // 重複するpartner_idを削除
        $partners = array_unique($partners);

        $file = DB::table('files')->where('id', $file_id)->sole();

        $partnersData = [];
        //partner_idに対して権限が付与されているか確認
        foreach ($partners as $partner) {
            while (true) {
                $is_unshared = DB::table('unshared_file_root_with_partners')
                    ->where('partner_id', $partner)
                    ->where('file_id', $file->id)
                    ->exists();

                //unsharedの場合は追加しない
                if ($is_unshared) {
                    $partnerData = DB::table('partners')
                        ->leftJoin('partner_files', 'partners.file_id', '=', 'partner_files.id')
                        ->join('vendor_linked_partners', 'partners.id', 'vendor_linked_partners.partner_id')
                        ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
                        ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
                        ->where('partners.id', $partner)
                        ->select([
                            'vendor_linked_partners.id as id',
                            'vendor_managed_partners.name as name',
                            'partner_files.s3_path AS s3_path',
                        ])
                        ->first();
                    $partnerData->partner_avatar_url = ! empty($partnerData->s3_path) ?
                        S3Helper::getS3ImageUrl($partnerData->s3_path) : null;
                    // 追加の属性を設定
                    $partnerData->has_access = false;

                    $partnersData[] = $partnerData;
                    break;
                }

                $is_shared = DB::table('shared_file_root_with_partners')
                    ->where('partner_id', $partner)
                    ->where('file_id', $file->id)
                    ->exists();

                if ($is_shared) {
                    $partnerData = DB::table('partners')
                        ->leftJoin('partner_files', 'partners.file_id', '=', 'partner_files.id')
                        ->join('vendor_linked_partners', 'partners.id', 'vendor_linked_partners.partner_id')
                        ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
                        ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
                        ->where('partners.id', $partner)
                        ->select([
                            'vendor_linked_partners.id as id',
                            'vendor_managed_partners.name as name',
                            'partner_files.s3_path AS s3_path',
                        ])
                        ->first();
                    $partnerData->partner_avatar_url = ! empty($partnerData->s3_path) ?
                        S3Helper::getS3ImageUrl($partnerData->s3_path) : null;
                    // 追加の属性を設定
                    $partnerData->has_access = true;

                    $partnersData[] = $partnerData;
                    break;
                }

                // $fileがnullであるか、親の情報がnullである場合はループを終了
                if (! $file || $file->parent === null) {
                    break;
                }
                // 親ディレクトリの情報を取得
                $file = DB::table('files')->where('id', $file->parent)->first();
            }
        }

        return $partnersData;
    }

    public static function getSharedPartnerUsers($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        self::validateFileAccess($user, $file);

        //file_idから1階層目のfileを取得
        $root_file = self::getRootFileWithFileId($file_id);

        /* partners */
        // 権限テーブル shared_file_root_with_partners からpartner_idの一覧を取得
        $partner_ids = DB::table('shared_file_root_with_partners')
            ->where('file_id', $root_file->id)
            ->pluck('partner_id')
            ->all();
        // partner_idの一覧に所属している全partner_team_membersを取得
        $partner_team_members_in_partners = DB::table('partner_team_members')
            ->whereIn('partner_team_members.partner_id', $partner_ids)
            ->leftjoin('partner_users', 'partner_team_members.partner_user_id', '=', 'partner_users.id')
            ->leftjoin('partners', 'partner_team_members.partner_id', '=', 'partners.id')
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->select('partner_users.id AS id',
                'partner_users.name',
                'partner_team_members.partner_id AS partner_id',
                'partner_users.email',
                'partners.name as partner_name',
                'partner_files.name AS file_name',
                'partner_files.s3_path AS s3_path')
            ->get();

        /* users */
        // 権限テーブル shared_file_roots から partner_userを取得
        $partner_team_members = DB::table('partner_team_members')
            ->join('shared_file_roots', 'partner_team_members.partner_user_id', '=', 'shared_file_roots.partner_user_id')
            ->join('partner_users', 'partner_team_members.partner_user_id', '=', 'partner_users.id')
            ->join('partners', 'partner_team_members.partner_id', '=', 'partners.id')
            ->join('vendor_linked_partners', 'partners.id', '=', 'vendor_linked_partners.partner_id')
            ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', '=', 'vendor_managed_partners.id')
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->where('shared_file_roots.file_id', $root_file->id)
            ->select('partner_users.id AS id',
                'partner_users.name',
                'partner_team_members.partner_id AS partner_id',
                'partner_users.email',
                'vendor_managed_partners.name as partner_name',
                'partner_files.name AS file_name',
                'partner_files.s3_path AS s3_path')
            ->get();

        // 取得したpartner_team_membersをマージしてユニークにする
        $merged_team_members = collect($partner_team_members_in_partners)
            ->concat(collect($partner_team_members))
            ->unique(function ($member) {
                return $member->partner_id.'_'.$member->id;
            })
            ->values();

        // partner_team_member が閲覧権限を持っているか確認
        foreach ($merged_team_members as $partner_team_member) {
            //rootのユーザー権限を確認して権限がない場合は閲覧権限なし
            $is_unshared = DB::table('unshared_file_roots')
                ->where('partner_user_id', $partner_team_member->id)
                ->where('partner_id', $partner_team_member->partner_id)
                ->where('file_id', $root_file->id)
                ->first();
            $partner_team_member->partner_avatar_url = ! empty($partner_user->s3_path) ?
                S3Helper::getS3ImageUrl($partner_team_member->s3_path) : null;
            if ($is_unshared !== null) {
                $partner_team_member->has_access = false;

                continue;
            }
            //rootのユーザー権限を確認して権限がある場合は閲覧権限あり
            $is_shared = DB::table('shared_file_roots')
                ->where('partner_user_id', $partner_team_member->id)
                ->where('partner_id', $partner_team_member->partner_id)
                ->where('file_id', $root_file->id)
                ->first();
            if ($is_shared !== null) {
                $partner_team_member->has_access = true;

                continue;
            }

            //もし権限設定がない場合は企業への権限設定を確認する
            if ($is_unshared === null && $is_shared === null) {
                //rootの企業権限を確認して権限がない場合はには閲覧権限なし
                $is_unshared = DB::table('unshared_file_root_with_partners')
                    ->where('partner_id', $partner_team_member->partner_id)
                    ->where('file_id', $root_file->id)
                    ->first();
                if ($is_unshared !== null) {
                    $partner_team_member->has_access = false;

                    continue;
                }
                //rootの企業権限を確認して権限がある場合はには閲覧権限あり
                $is_shared = DB::table('shared_file_root_with_partners')
                    ->where('partner_id', $partner_team_member->partner_id)
                    ->where('file_id', $root_file->id)
                    ->first();
                if ($is_shared !== null) {
                    $partner_team_member->has_access = true;

                    continue;
                }
                //どちらもない場合は閲覧権限なし
                $partner_team_member->has_access = false;
            }
        }

        return $merged_team_members;
    }

    public static function getDriveInfo()
    {
        $user = Auth::guard('vendor_users')->user();
        $total_file_size = DB::table('files')
            ->where('vendor_id', $user->vendor_id)
            ->sum('size');

        return ['total_file_size' => $total_file_size];
    }

    public static function getFileBreadcrumb(int $file_id): ?string
    {
        $current_file = DB::table('files')
            ->where('id', $file_id)
            ->select('name', 'parent')
            ->first();

        if (! $current_file) {
            return null; // ファイルが見つからない場合
        }

        $path = $current_file->name;
        while ($current_file->parent) {
            $current_file = DB::table('files')
                ->where('id', $current_file->parent)
                ->select('name', 'parent')
                ->first();
            $path = $current_file->name.'/'.$path;
        }

        return $path;
    }

    private static function validateFileAccess($user, $file)
    {
        if ($user->vendor_id !== $file->vendor_id) {
            throw new ServiceException('Error: cannot access to the file', 400);
        }
    }

    private static function validateCollabFileAccess($user, $file)
    {
        if ($user->vendor_id !== $file->vendor_id) {
            throw new ServiceException('Error: cannot access to the file', 400);
        }
    }

    public static function getPermalink($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        $appURL = config('app.front_domain');

        $defaultURL = $appURL.'/drive/s';

        $permalink = $defaultURL.'/'.$file->portal_id.'/'.$file->id.'/'.$file->type;

        return $permalink;
    }

    public static function getSearchedFiles(string $query, string $sort_by = '', string $order_by = 'desc')
    {
        $user = Auth::guard('vendor_users')->user();

        $file_query = DB::table('files as target_file')
            ->leftJoin('files as parent_file', 'target_file.parent', '=', 'parent_file.id')
            ->leftJoin('portals as portals', 'target_file.portal_id', '=', 'portals.id')
            ->select(
                'target_file.id as id',
                'target_file.uuid as uuid',
                'target_file.vendor_id as vendor_id',
                'target_file.type as type',
                'target_file.parent as parent',
                'target_file.name as name',
                'target_file.size as size',
                'target_file.s3_path as s3_path',
                'target_file.portal_id as portal_id',
                'target_file.created_at as created_at',
                'target_file.updated_at as updated_at',
                'target_file.depth as depth',
                'portals.name as portal_name',
                'parent_file.name as parent_name',
                DB::raw("'standard' as category"),
            )
            ->where('target_file.vendor_id', $user->vendor_id)
            ->where('target_file.name', 'LIKE', "%{$query}%");

        $collab_file_query = DB::table('collaborative_files as target_file')
            ->leftJoin('collaborative_files as parent_file', 'target_file.parent', '=', 'parent_file.id')
            ->leftJoin('portals as portals', 'target_file.portal_id', '=', 'portals.id')
            ->select(
                'target_file.id as id',
                'target_file.uuid as uuid',
                'target_file.vendor_id as vendor_id',
                'target_file.type as type',
                'target_file.parent as parent',
                'target_file.name as name',
                'target_file.size as size',
                'target_file.s3_path as s3_path',
                'target_file.portal_id as portal_id',
                'target_file.created_at as created_at',
                'target_file.updated_at as updated_at',
                'target_file.depth as depth',
                'portals.name as portal_name',
                'parent_file.name as parent_name',
                DB::raw("'collaborative' as category"),
            )
            ->where('target_file.vendor_id', $user->vendor_id)
            ->where('target_file.name', 'LIKE', "%{$query}%");

        $merged_query = $file_query->unionAll($collab_file_query);

        // 'asc'以外は漏れなくdescにする
        $order_direction = ($order_by === 'asc') ? 'asc' : 'desc';

        $searched_files = [];

        // ソート条件の分岐
        switch ($sort_by) {
            case 'created_at':
                $searched_files = $merged_query->orderBy('created_at', $order_direction)
                    ->get();
                break;
            case 'updated_at':
                $searched_files = $merged_query->orderBy('updated_at', $order_direction)
                    ->get();
                break;
            case 'relevance_score':
            default:
                // 完全一致 > 前方一致 > 部分一致
                $searched_files = $merged_query->orderByRaw("CASE
                    WHEN name LIKE ? THEN 4
                    WHEN name LIKE ? THEN 3
                    WHEN name LIKE ? THEN 2
                    ELSE 1 END $order_direction", ["{$query}", "{$query}%", "%{$query}%"])
                    ->get();
                break;
        }

        // コレクションを分割
        [$files, $folders] = $searched_files->partition(fn ($file) => $file->type === 'normal');

        // 分割した結果を配列に格納
        $files_by_type = [
            'files' => $files->values()->all(),
            'folders' => $folders->values()->all(),
        ];

        return $files_by_type;
    }

    public static function registerFileThumbnail($request, int $file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('files')->where('id', $file_id)->where('vendor_id', $user->vendor_id)->first();
        $vendor_collaboration_id = DB::table('vendors')->where('id', $user->vendor_id)->value('vendor_collaboration_id');

        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 400);
        }
        // 新しいサムネ画像をアップできたら既存のサムネ画像を削除したい。そのためにpathを保持する
        if (! empty($file->thumbnail_path) && Storage::disk('s3-drive')->exists($file->thumbnail_path)) {
            $old_thumbnail_path = $file->thumbnail_path;
        }
        $thumbnail = $request->file('thumbnail');
        $file_name = $thumbnail->getClientOriginalName();
        $s3_dir = $vendor_collaboration_id.'/drive/thumbnails/'.date('Ymd');
        // fileの名前を生成
        $id_name = Str::random(6).'.'.$file_name;
        $s3_path = Storage::disk('s3-drive')->putFileAs($s3_dir, $thumbnail, $id_name);
        DB::table('files')
            ->where('id', $file_id)
            ->update([
                'thumbnail_path' => $s3_path,
                'updated_at' => now(),
            ]);
        // 古いサムネを削除
        if (isset($old_thumbnail_path)) {
            Storage::disk('s3-drive')->delete($old_thumbnail_path);
        }
    }

    public static function getFileThumbnail(int $file_id)
    {
        $user = Auth::guard('vendor_users')->user();

        $file = DB::table('files')->where('id', $file_id)->where('vendor_id', $user->vendor_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }

        return DriveService::getThumbnailPresignedUrl($file);
    }

    private static function getThumbnailPresignedUrl($file)
    {
        if ($file->thumbnail_path) {
            // S3から一時URLを取得
            return Storage::disk('s3-drive')->temporaryUrl($file->thumbnail_path, now()->addMinutes(1440));
        } else {
            throw new ServiceException('Error: Thumbnail not found', 404);
        }
    }

    public static function getCollabFile($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('collaborative_files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        $curFile = $file;
        $breadcrumbs = [];
        do {
            array_push($breadcrumbs, [
                'id' => $curFile->id,
                'name' => $curFile->name,
            ]);
            $curFile = DB::table('collaborative_files')
                ->where('id', $curFile->parent)
                ->first();
        } while ($curFile !== null);

        $file->breadcrumbs = array_reverse($breadcrumbs);

        return $file;
    }

    public static function createCollabFile($request, $file_id)
    {
        $audit_log_service = app(AuditLogServiceInterface::class);
        $user = Auth::guard('vendor_users')->user();
        $vendor_collaboration_id = DB::table('vendors')->where('id', $user->vendor_id)->value('vendor_collaboration_id');
        $parent_file_id = null;
        $x_forwarded_for = $request->header('X-Forwarded-For');
        $user_ip = '';
        $partner_id = null;
        $parent_file = null;

        // X-Forwarded-For ヘッダーが存在するか確認
        if (! empty($x_forwarded_for)) {
            $ips = explode(',', $x_forwarded_for);
            $user_ip = trim(end($ips)); // 配列の最後の要素を取得してトリムします
        }

        // ルートにtype=normalを作ろうとした場合、エラーを返す
        if ($file_id === 0 && $request->type === 'normal') {
            throw new ServiceException('Error: cannot create a collaborative file in root directory', 400);
        }

        // リクエストにvlp_idがある場合、そのパートナーと連携中でなければエラーを返す
        // FEからpartner_idで受け取った値をCreateCollabFileRequest内でvlp_idに定義し直している
        if (isset($request->vlp_id)) {
            $partner_id = DB::table('vendor_linked_partners')
                ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
                ->where('vendor_linked_partners.id', $request->vlp_id)
                ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
                ->where('vendor_linked_partners.link_status', StatusConst::LINK_STATUS['LINK_ACTIVE']['id'])
                ->value('partner_id');
            if (is_null($partner_id)) {
                throw new ServiceException('Error: file creation not allowed for this partner', 403);
            }
        }

        // ルートにtype=directoryを作ろうとした場合、$request->partner_idがなければエラーを返す
        if ($file_id === 0 && $request->type === 'directory' && ! isset($request->vlp_id)) {
            throw new ServiceException('Error: cannot create a collaborative file in root directory without partner_id', 400);
        }

        if ($file_id > 0) {
            $parent_file = DB::table('collaborative_files')
                            ->where('id', $file_id)
                            ->where('vendor_id', $user->vendor_id)
                            ->first();
        }

        switch ($request->type) {
            case 'normal':
                $file = $request->file('file');
                if (is_null($file)) {
                    throw new ServiceException('Error: normal file is required', 400);
                }
                $file_name = $file->getClientOriginalName();
                $file_size = $file->getSize();
                $thumbnail = $request->file('thumbnail');
                if (! empty($thumbnail)) {
                    $thumbnail_s3_dir = $vendor_collaboration_id.'/drive/thumbnails/'.date('Ymd').'/';
                    $thumbnail_name = Str::random(6).'.'.$thumbnail->getClientOriginalName();
                }
                break;
            case 'directory':
                if (is_null($request->name)) {
                    throw new ServiceException('Error: directory name is required', 400);
                }
                if (isset($parent_file) && $parent_file->depth >= DriveService::DRIVE_MAX_LEVEL - 1) {
                    throw new ServiceException('Error: depth limit exceeds', 400);
                }
                $file_name = $request->name;
                $file_size = 0;
                break;
        }

        // if target is not root directory
        try {
            if ($file_id !== 0) {
                if ($parent_file === null) {
                    throw new ServiceException('Error: cannot create a file from non-directory', 400);
                }
                if ($parent_file->type !== 'directory') {
                    throw new ServiceException('Error: cannot create a file in a normal file', 400);
                }
                if ($user->vendor_id !== $parent_file->vendor_id) {
                    throw new ServiceException('Error: cannot access to the file', 403);
                }
                if ($parent_file->portal_id !== (int) $request->portal_id) {
                    throw new ServiceException('Error: cannot create a file in a different portal tab', 400);
                }
                $parent_file_id = $file_id;
            }
        } catch (ServiceException $e) {
            if ($request->type === 'normal') {
                $audit_log_service->write(
                    new PortalAuditLogDto(
                        $user_ip,
                        AuditTarget::Portal,
                        AuditAction::CollabFileCreate,
                        AuditResult::Failed,
                        AuditDestTo::Vendor,
                        $file_name,
                        $user->email,
                        $user->vendor->vendor_collaboration_id,
                    )
                );
            }
            throw $e;
        }

        DB::beginTransaction();
        try {
            if (isset($parent_file)) {
                $s3_dir = $vendor_collaboration_id.'/drive/collab/'.date('Ymd').'/';
                $depth = $parent_file->depth + 1;
            } else {
                $s3_dir = $vendor_collaboration_id.'/drive/collab/'.date('Ymd').'/';
                $depth = 1;
            }

            $auto_id = DB::table('collaborative_files')->max('id') + 1;
            $ext = pathinfo($file_name, PATHINFO_EXTENSION);
            $id_name = $auto_id.'.'.$ext;

            DB::table('collaborative_files')
                ->insert([
                    'id' => $auto_id,
                    'uuid' => Str::uuid(),
                    'vendor_id' => $user->vendor_id,
                    'parent' => $parent_file_id,
                    'type' => $request->type,
                    'name' => $file_name,
                    'size' => $file_size,
                    'depth' => $depth,
                    's3_path' => $s3_dir.$id_name,
                    'portal_id' => $request->portal_id,
                    'thumbnail_path' => empty($thumbnail_s3_dir) ? null : $thumbnail_s3_dir.$thumbnail_name,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);

            if ($request->type === 'normal') {
                Storage::disk('s3-drive')->putFileAs($s3_dir, $file, $id_name);
                if (! empty($thumbnail)) {
                    Storage::disk('s3-drive')->putFileAs($thumbnail_s3_dir, $thumbnail, $thumbnail_name);
                }
            }

            // 権限追加処理
            // 全てのファイルはルートディレクトリの権限を確認するため、depth=0での作成時のみ権限追加処理する
            if ($depth === 1) {
                DB::table('shared_collaborative_file_root_with_partners')
                    ->updateOrInsert(
                        [
                            'partner_id' => $partner_id,
                            'file_id' => $auto_id,
                        ],
                        [
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s'),
                        ]
                    );
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            if ($request->type === 'normal') {
                $audit_log_service->write(
                    new PortalAuditLogDto(
                        $user_ip,
                        AuditTarget::Portal,
                        AuditAction::CollabFileCreate,
                        AuditResult::Failed,
                        AuditDestTo::Vendor,
                        $file_name,
                        $user->email,
                        $user->vendor->vendor_collaboration_id,
                    )
                );
            }
            throw $e;
        }
        if ($request->type === 'normal') {
            $audit_log_service->write(
                new PortalAuditLogDto(
                    $user_ip,
                    AuditTarget::Portal,
                    AuditAction::CollabFileCreate,
                    AuditResult::Success,
                    AuditDestTo::Vendor,
                    $file_name,
                    $user->email,
                    $user->vendor->vendor_collaboration_id,
                )
            );
        }
    }

    public static function deleteCollabFile($request, $file_id)
    {
        $audit_log_service = app(AuditLogServiceInterface::class);
        $user = Auth::guard('vendor_users')->user();
        $parent_file_id = null;
        $x_forwarded_for = $request->header('X-Forwarded-For');
        $user_ip = '';
        $is_file = false;
        $file_names = [];

        // X-Forwarded-For ヘッダーが存在するか確認
        if (! empty($x_forwarded_for)) {
            $ips = explode(',', $x_forwarded_for);
            $user_ip = trim(end($ips)); // 配列の最後の要素を取得してトリムします
        }

        // 削除対象がファイルか判定
        $file_type = DB::table('collaborative_files')->where('id', $file_id)->first()->type;
        if ($file_type === 'normal') {
            $is_file = true;
            $file_names[] = DB::table('collaborative_files')->where('id', $file_id)->first()->name;
        }

        // if target is not root directory
        try {
            if ($file_id !== 0) {
                $parent_file = DB::table('collaborative_files')->where('id', $file_id)->first();
                if ($parent_file === null) {
                    throw new ServiceException('Error: cannot delete from non-directory', 400);
                }
                if ($user->vendor_id !== $parent_file->vendor_id) {
                    throw new ServiceException('Error: cannot access to the file', 400);
                }
                $parent_file_id = $file_id;
            }

            if (TrainingService::isExistRelatedTraining($user, $file_id, null)) {
                throw new ServiceException('Error: cannot delete the file related to training', 400);
            }

            $all_children = [];
            // このfile_idが親となるfileを取得
            self::getAllChildren($file_id, $user, $all_children);

            // type=normalのfileのみ残す
            $all_children = array_filter($all_children, function ($child) {
                return $child->type === 'normal';
            });

            foreach ($all_children as $child) {
                if (TrainingService::isExistRelatedTraining($user, $child->id, null)) {
                    throw new ServiceException('Error: cannot delete the file related to training', 400);
                }
            }
        } catch (ServiceException $e) {
            if ($is_file) {
                $audit_log_service->write(
                    new PortalAuditLogDto(
                        $user_ip,
                        AuditTarget::Portal,
                        AuditAction::CollabFileDelete,
                        AuditResult::Failed,
                        AuditDestTo::Vendor,
                        $file_names[0],
                        $user->email,
                        $user->vendor->vendor_collaboration_id,
                    )
                );
            }
            throw $e;
        }

        DB::beginTransaction();
        try {
            if (isset($parent_file_id)) {
                if (! $is_file) {
                    $files = DB::table('collaborative_files')
                        ->where('parent', $parent_file_id)
                        ->get();
                    foreach ($files as $file) {
                        $file_names[] = $file->name;
                    }
                }
                DB::table('collaborative_files')
                    ->where('id', $parent_file_id)
                    ->delete();
            } else {
                $query = DB::table('collaborative_files')
                    ->where('parent', null)
                    ->where('vendor_id', $user->vendor_id)
                    ->select('s3_path');
                $files = $query->get();
                $query->delete();
                foreach ($files as $file) {
                    if ($file->type === 'directory') {
                        Storage::disk('s3')->deleteDirectory($file->s3_path);
                    } else {
                        Storage::disk('s3')->delete($file->s3_path);
                    }
                }
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            foreach ($file_names as $file_name) {
                $audit_log_service->write(
                    new PortalAuditLogDto(
                        $user_ip,
                        AuditTarget::Portal,
                        AuditAction::CollabFileDelete,
                        AuditResult::Failed,
                        AuditDestTo::Vendor,
                        $file_name,
                        $user->email,
                        $user->vendor->vendor_collaboration_id,
                    )
                );
            }
            throw $e;
        }
        foreach ($file_names as $file_name) {
            $audit_log_service->write(
                new PortalAuditLogDto(
                    $user_ip,
                    AuditTarget::Portal,
                    AuditAction::CollabFileDelete,
                    AuditResult::Success,
                    AuditDestTo::Vendor,
                    $file_names[0],
                    $user->email,
                    $user->vendor->vendor_collaboration_id,
                )
            );
        }
    }

    public static function updateCollabFile($request, $file_id)
    {
        $audit_log_service = app(AuditLogServiceInterface::class);
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('collaborative_files')->where('id', $file_id)->first();
        $file_type = $file->type;
        $file_name = $request->name;
        $x_forwarded_for = $request->header('X-Forwarded-For');
        $user_ip = '';

        // X-Forwarded-For ヘッダーが存在するか確認
        if (! empty($x_forwarded_for)) {
            $ips = explode(',', $x_forwarded_for);
            $user_ip = trim(end($ips)); // 配列の最後の要素を取得してトリムします
        }

        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        DB::beginTransaction();
        try {
            DB::table('collaborative_files')
                ->where('id', $file_id)
                ->update([
                    'name' => $request->name,
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            if ($file_type === 'normal') {
                $audit_log_service->write(
                    new PortalAuditLogDto(
                        $user_ip,
                        AuditTarget::Portal,
                        AuditAction::CollabFileChangeName,
                        AuditResult::Failed,
                        AuditDestTo::Vendor,
                        $file_name,
                        $user->email,
                        $user->vendor->vendor_collaboration_id,
                    )
                );
            }
            throw $e;
        }
        if ($file_type === 'normal') {
            $audit_log_service->write(
                new PortalAuditLogDto(
                    $user_ip,
                    AuditTarget::Portal,
                    AuditAction::CollabFileDelete,
                    AuditResult::Success,
                    AuditDestTo::Vendor,
                    $file_name,
                    $user->email,
                    $user->vendor->vendor_collaboration_id,
                )
            );
        }
    }

    public static function registerCollabFileThumbnail($request, int $file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('collaborative_files')->where('id', $file_id)->where('vendor_id', $user->vendor_id)->first();
        $vendor_collaboration_id = DB::table('vendors')->where('id', $user->vendor_id)->value('vendor_collaboration_id');

        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        // 新しいサムネ画像をアップできたら既存のサムネ画像を削除したい。そのためにpathを保持する
        if (! empty($file->thumbnail_path) && Storage::disk('s3-drive')->exists($file->thumbnail_path)) {
            $old_thumbnail_path = $file->thumbnail_path;
        }
        $thumbnail = $request->file('thumbnail');
        $file_name = $thumbnail->getClientOriginalName();
        $s3_dir = $vendor_collaboration_id.'/drive/thumbnails/'.date('Ymd');
        // fileの名前を生成
        $id_name = Str::random(6).'.'.$file_name;
        $s3_path = Storage::disk('s3-drive')->putFileAs($s3_dir, $thumbnail, $id_name);
        DB::table('collaborative_files')
            ->where('id', $file_id)
            ->update([
                'thumbnail_path' => $s3_path,
                'updated_at' => now(),
            ]);
        // 古いサムネを削除
        if (isset($old_thumbnail_path)) {
            Storage::disk('s3-drive')->delete($old_thumbnail_path);
        }
    }

    public static function getCollabFileList($file_id, $portal_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $parent_file = null;
        $parent_file_id = null;

        // if target is not root directory
        if ($file_id !== 0) {
            $parent_file = DB::table('collaborative_files')->where('id', $file_id)->first();
            if ($parent_file === null) {
                throw new ServiceException('Error: the directory does not exist', 404);
            }
            if ($user->vendor_id !== $parent_file->vendor_id) {
                throw new ServiceException('Error: cannot access to the file', 403);
            }
            $parent_file_id = $file_id;
        }

        if (isset($parent_file) && $parent_file->type !== 'directory') {
            throw new ServiceException('Error: cannot get list from non-directory', 400);
        }

        $selected_portal = DB::table('portals')
            ->where('vendor_id', $user->vendor_id)
            ->where('id', $portal_id)
            ->first();

        if ($selected_portal === null) {
            // if target portal is not exist
            throw new ServiceException('Error: the portal does not exist', 404);
        } elseif ($selected_portal->vendor_id !== $user->vendor_id) {
            // if target portal is not belong to the vendor
            throw new ServiceException('Error: cannot access to the portal', 403);
        }

        $curFile = $parent_file;
        $breadcrumbs = [];
        while ($curFile !== null) {
            array_push($breadcrumbs, [
                'id' => $curFile->id,
                'name' => $curFile->name,
            ]);
            $curFile = DB::table('collaborative_files')
                ->where('id', $curFile->parent)
                ->first();
        }

        $files = DB::table('collaborative_files')
            ->where('parent', $parent_file_id)
            ->where('vendor_id', $user->vendor_id)
            ->where('portal_id', $portal_id)
            ->get()
            ->toArray();

        $res = new stdClass;
        $res->collab_files = $files;
        $res->breadcrumbs = array_reverse($breadcrumbs);

        return $res;
    }

    public static function viewCollabFile($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('collaborative_files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        /* $file_path = $file->s3_path; */
        /* $file_name = $file->name; */
        /* $mimeType = Storage::disk('s3')->mimeType($file_path); */
        /* $headers = [['Content-Type' => $mimeType]]; */
        /* return Storage::disk('s3')->response($file_path, $file_name, $headers); */

        $s3Client = Storage::disk('s3-drive');

        $s3_url = $s3Client->temporaryUrl($file->s3_path, now()->addMinutes(1440));
        $contentType = $s3Client->mimeType($file->s3_path);

        $res = new stdClass;
        $res->url = $s3_url; // Presigned URLを設定
        $res->contentType = $contentType; // Content-Typeを設定

        return $res;
    }

    public static function downloadCollabFile($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('collaborative_files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }

        if ($user->vendor_id !== $file->vendor_id) {
            throw new ServiceException('Error: cannot access to the file', 400);
        }

        $encodedName = rawurlencode($file->name);
        $disposition = "attachment; filename*=UTF-8''{$encodedName}";
        // PROP-5101にてこのコードを実装
        // TODO: リリース後にPresignedURL取得ロジックを共通化する。
        $presignedUrl = Storage::disk('s3-drive')->temporaryUrl(
            $file->s3_path,
            now()->addMinutes(20),
            ['ResponseContentDisposition' => $disposition]
        );

        return $presignedUrl;
    }

    public static function getSharedCollabPartners($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('collaborative_files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        // 親ディレクトリの情報を取得
        $root_file = self::getRootCollabFileWithFileId($file_id);

        // 親ディレクトリの権限にひもづくpartner_idを取得
        $shared_partner_id = DB::table('shared_collaborative_file_root_with_partners')
            ->where('file_id', $root_file->id)
            ->value('partner_id');

        $partnerData = DB::table('partners')
            ->leftJoin('partner_files', 'partners.file_id', '=', 'partner_files.id')
            ->join('vendor_linked_partners', 'partners.id', 'vendor_linked_partners.partner_id')
            ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
            ->where('vendor_managed_partners.vendor_id', $user->vendor_id)
            ->where('partners.id', $shared_partner_id)
            ->select([
                'vendor_linked_partners.id as id',
                'partners.name as name',
                'partner_files.s3_path AS s3_path',
            ])
            ->first();
        $partnerData->partner_avatar_url = ! empty($partnerData->s3_path) ?
            S3Helper::getS3ImageUrl($partnerData->s3_path) : null;
        // 追加の属性を設定
        $partnerData->has_access = true;

        // getSharedPartners()と同じ形式で返却する
        $partnersData[] = $partnerData;

        return $partnersData;
    }

    public static function getCollabFilePermalink($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('collaborative_files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateCollabFileAccess($user, $file);

        $appURL = config('app.front_domain');

        $defaultURL = $appURL.'/drive/c';

        $permalink = $defaultURL.'/'.$file->portal_id.'/'.$file->id.'/'.$file->type;

        return $permalink;
    }

    public static function getCollabFileThumbnail($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('collaborative_files')->where('id', $file_id)->where('vendor_id', $user->vendor_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }

        return DriveService::getThumbnailPresignedUrl($file);
    }

    public static function getCollabFileAccessLogs($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('collaborative_files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        $file_access_logs = DB::table('collaborative_file_access_logs')
            ->where('collaborative_file_id', $file_id)
            ->get()
            ->toArray();

        // 関連するpartner_user_idsを取得
        $partner_user_ids = array_column($file_access_logs, 'partner_user_id');

        // 一度のクエリで関連するパートナーユーザーのデータを取得
        $partner_users = DB::table('partner_users')
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->whereIn('partner_users.id', $partner_user_ids)
            ->select(
                'partner_users.id',
                'partner_users.name',
                'partner_users.email',
                'partner_files.name AS file_name',
                'partner_files.s3_path AS s3_path'
            )
            ->get()
            ->keyBy('id')
            ->toArray();

        // 一度のクエリで関連するパートナーの名前を取得
        $partner_ids = array_column($file_access_logs, 'partner_id');
        $partners = DB::table('partners')
            ->whereIn('id', $partner_ids)
            ->pluck('name', 'id')
            ->toArray();

        foreach ($file_access_logs as $log) {
            $log->partner_user_name = $partner_users[$log->partner_user_id]->name;
            $log->email = $partner_users[$log->partner_user_id]->email;
            $log->partner_name = $partners[$log->partner_id];
            $log->partner_avatar_name = $partner_users[$log->partner_user_id]->file_name;
            $log->partner_avatar_url = ! empty($partner_users[$log->partner_user_id]->s3_path) ?
                S3Helper::getS3ImageUrl($partner_users[$log->partner_user_id]->s3_path) : null;
        }

        $groupedData = [];

        foreach ($file_access_logs as $item) {
            $key = $item->partner_name;

            // 会社名でグループ化
            if (! isset($groupedData[$key])) {
                $groupedData[$key] = [];
            }
            $groupedData[$key][] = $item;
        }

        return $groupedData;
    }

    public static function getCollabFileDownloadLogs($file_id)
    {
        $user = Auth::guard('vendor_users')->user();
        $file = DB::table('collaborative_files')->where('id', $file_id)->first();
        if ($file === null) {
            throw new ServiceException('Error: the file does not exist', 404);
        }
        self::validateFileAccess($user, $file);

        $file_download_logs = DB::table('collaborative_file_download_logs')
            ->where('collaborative_file_id', $file_id)
            ->get()
            ->toArray();

        // 関連するpartner_user_idsを取得
        $partner_user_ids = array_column($file_download_logs, 'partner_user_id');

        // 一度のクエリで関連するパートナーユーザーのデータを取得
        $partner_users = DB::table('partner_users')
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->whereIn('partner_users.id', $partner_user_ids)
            ->select(
                'partner_users.id',
                'partner_users.name',
                'partner_users.email',
                'partner_files.name AS file_name',
                'partner_files.s3_path AS s3_path'
            )
            ->get()
            ->keyBy('id')
            ->toArray();

        // 一度のクエリで関連するパートナーの名前を取得
        $partner_ids = array_column($file_download_logs, 'partner_id');
        $partners = DB::table('partners')
            ->whereIn('id', $partner_ids)
            ->pluck('name', 'id')
            ->toArray();

        foreach ($file_download_logs as $log) {
            $log->partner_user_name = $partner_users[$log->partner_user_id]->name;
            $log->email = $partner_users[$log->partner_user_id]->email;
            $log->partner_name = $partners[$log->partner_id];
            $log->partner_avatar_name = $partner_users[$log->partner_user_id]->file_name;
            $log->partner_avatar_url = ! empty($partner_users[$log->partner_user_id]->s3_path) ?
                S3Helper::getS3ImageUrl($partner_users[$log->partner_user_id]->s3_path) : null;
        }

        $groupedData = [];

        foreach ($file_download_logs as $item) {
            $key = $item->partner_name;

            // 会社名でグループ化
            if (! isset($groupedData[$key])) {
                $groupedData[$key] = [];
            }
            $groupedData[$key][] = $item;
        }

        return $groupedData;
    }
}
