<?php

namespace App\Services\Vendor;

use App\Components\ChatMessage;
use App\Events\ChatMessageCreated;
use App\Events\ChatMessageDeleted;
use App\Events\ChatMessageUpdated;
use App\Events\UpdateBadgeCountChannel;
use App\Events\Vendor\ReceiveMentionChat;
use App\Events\Vendor\ReceiveReplyChat;
use App\Exceptions\ServiceException;
use App\Helpers\S3Helper;
use App\Jobs\UpdateChatCountUnreadJob;
use App\Jobs\VendorChatMentionJobs;
use Aws\S3\S3Client;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Pusher\Pusher;
use stdClass;
use Storage;

class ChatService
{
    private const PERMISSION_READ = 1;

    private const PERMISSION_WRITE = 2;

    private const PERMISSION_MEMBER = 4;

    private const PERMISSION_ALL = self::PERMISSION_READ + self::PERMISSION_WRITE + self::PERMISSION_MEMBER; // 7

    private const PER_PAGE = 20;

    private const CHANNEL_PER_PAGE = 15;

    // improve get s3 img
    private static $avatar_cache = [];

    private static function isOwner(&$user, $channel_id)
    {
        return DB::table('channels')
            ->where('vendor_id', $user->vendor_id)
            ->where('id', $channel_id)
            ->exists();
    }

    private static function getS3Client()
    {
        return new S3Client([
            'region' => config('filesystems.disks.s3-chat.region'),
            'version' => 'latest',
            'credentials' => [
                'key' => config('filesystems.disks.s3-chat.key'),
                'secret' => config('filesystems.disks.s3-chat.secret'),
            ],
            'endpoint' => config('filesystems.disks.s3-chat.endpoint'),
            'use_path_style_endpoint' => config('filesystems.disks.s3-chat.use_path_style_endpoint'),
        ]);
    }

    private static function getS3Url(&$s3Client, $s3_path, &$contentType)
    {
        $cmd = $s3Client->getCommand('GetObject', [
            'Bucket' => config('filesystems.disks.s3-chat.bucket'),
            'Key' => $s3_path,
        ]);
        $request = $s3Client->createPresignedRequest($cmd, '+1440 minutes');
        $presignedUrl = (string) $request->getUri();

        $objectMetadata = $s3Client->headObject([
            'Bucket' => config('filesystems.disks.s3-chat.bucket'),
            'Key' => $s3_path,
        ]);

        $contentType = $objectMetadata['ContentType'];

        return $presignedUrl;
    }

    private static function isAllPartnerChannel(int $channel_id)
    {
        //自動チャンネル作成のpermissionがREADの場合はALLチャンネルのためtrueを返す
        return DB::table('auto_channel_authorizers')
            ->where('channel_id', $channel_id)
            ->where('permission', self::PERMISSION_READ)
            ->exists();
    }

    public static function getAttachedFileArrays($attached_files)
    {
        $file_dict = [];
        try {
            $s3Client = self::getS3Client();

            foreach ($attached_files as $attached_file) {
                $key = $attached_file->chat_post_id;
                if (! array_key_exists($key, $file_dict)) {
                    $file_dict[$key] = [];
                }

                try {
                    $presignedUrl = self::getS3Url(
                        $s3Client,
                        $attached_file->s3_path,
                        $contentType
                    );
                } catch (Exception $e) {
                    Log::error($e);
                    $presignedUrl = '';
                    $contentType = '';
                }

                array_push($file_dict[$key], [
                    'id' => $attached_file->id,
                    'size' => $attached_file->size,
                    'name' => $attached_file->name,
                    'url' => $presignedUrl,
                    'contentType' => $contentType,
                ]);
            }

            return $file_dict;
        } catch (Exception $e) {
            Log::error($e);

            return [];
        }
    }

    public static function formatPostData($record, $user)
    {
        if ($user->vendor_id) {
            $record->channel_name = self::getChannelName($record->channel_id, $user->vendor_id);
        }

        self::restructureUser($record, false);

        $record->timestamp = new stdClass;
        $record->timestamp->created_at = $record->created_at;
        $record->timestamp->updated_at = $record->updated_at;
        unset($record->created_at);
        unset($record->updated_at);

        if ($record->user->vendor_user_id === $user->id) {
            $record->editable = true;
            $record->deletable = true;
        } else {
            $record->editable = false;
            $record->deletable = false;
        }
    }

    public static function authChannel($request, $is_mobile)
    {
        $channel_name = $request->channel_name;
        $socket_id = $request->socket_id;
        if ($is_mobile) {
            $user = Auth::guard('vendor_users')->user();

            $regex = '/private-channels.([0-9]+)/';
            $result = preg_match($regex, $channel_name, $matches);
            if (! $result) {
                throw new Exception('Error: cannot find the channel');
            }

            $channel_id = $matches[1];

            if (! ChatService::isOwner($user, $channel_id)) {
                throw new Exception('Error: cannot access to the channel');
            }
        } else {
            if ($channel_name != 'private-channels.chat-event') {
                throw new Exception('Error: cannot access');
            }
        }
        $pusher = new Pusher(
            env('PUSHER_APP_KEY'),
            env('PUSHER_APP_SECRET'),
            env('PUSHER_APP_ID'),
            ['cluster' => env('PUSHER_APP_CLUSTER')]
        );

        $signature = $pusher->authorizeChannel($channel_name, $socket_id);

        return json_decode($signature);
    }

    public static function getChannels($request)
    {
        $last_channel_id = $request['last_channel_id'];
        $channel_name = $request['channel_name'];
        $limit = $request['limit'];

        if (! $limit) {
            $limit = self::PER_PAGE;
        }

        $user = Auth::guard('vendor_users')->user();

        $sub_query = DB::table('channels')
            ->leftJoin('chat_posts', 'chat_posts.channel_id', '=', 'channels.id')
            ->leftJoin('chat_count_unread', function ($join) use ($user) {
                $join->on('channels.id', '=', 'chat_count_unread.channel_id')
                    ->where('chat_count_unread.vendor_user_id', '=', $user->id);
            })
            ->where('channels.vendor_id', $user->vendor_id)
            ->select(
                'channels.id as channel_id',
                'channels.name as name',
                DB::raw('ROW_NUMBER() OVER (ORDER BY
                    CASE
                        WHEN channels.name = "ALL" THEN 0
                        WHEN chat_count_unread.unread_mention_list_ids IS NOT NULL THEN 1
                        ELSE 2
                    END,
                    MAX(chat_posts.created_at) DESC) as row_num')
            )
            ->groupBy('channels.id', 'channels.name', 'chat_count_unread.unread_mention_list_ids');

        $total_channel = $sub_query->get()->count();

        // find row_num of target channel if isset $channel_id
        if ($last_channel_id) {
            $row_num = DB::table(DB::raw("({$sub_query->toSql()}) as sub"))
                ->mergeBindings($sub_query)
                ->where('channel_id', $last_channel_id)
                ->value('row_num');
        } else {
            $row_num = 0;
        }

        // Fetch channels based on $channel_name
        $channels = DB::table(DB::raw("({$sub_query->toSql()}) as sub"))
            ->mergeBindings($sub_query)
            ->where('row_num', '>', $row_num)
            ->orderBy('row_num');

        // If $channel_name is provided, filter channels after fetching
        if (isset($channel_name)) {
            // Get all channels for further processing
            $channels = $channels->get()->toArray();
            $channels = array_filter($channels, function ($record) use ($user, $channel_name) {
                $record->name = self::getChannelName($record->channel_id, $user->vendor_id);

                return stripos($record->name, $channel_name) !== false;
            });
        } else {
            // Otherwise, limit results for pagination
            $channels = $channels->limit($limit)->get()->toArray();
        }

        foreach ($channels as &$record) {
            if (! $channel_name) {
                $record->name = self::getChannelName($record->channel_id, $user->vendor_id);
            }
            $record->badge_count = self::getChannelBadgeCount($record->channel_id, $user->id)['mention_badge_count'];
            $record->unread = self::getChannelBadgeCount($record->channel_id, $user->id)['unread'];
            unset($record->row_num);
        }

        usort($channels, function ($first_channel, $second_channel) {
            return $first_channel->name === '全てのパートナー' ? -1
                : ($second_channel->name === '全てのパートナー' ? 1 : 0);
        });

        $mentions_count = DB::table('chat_count_unread')
            ->where('vendor_user_id', $user->id)
            ->selectRaw('SUM(IFNULL(JSON_LENGTH(unread_mention_list_ids), 0)) as count')
            ->first();
        $replies_count = DB::table('chat_count_unread')
            ->where('vendor_user_id', $user->id)
            ->selectRaw('SUM(IFNULL(JSON_LENGTH(unread_reply_list_ids), 0)) as count')
            ->first();

        $responseData = [
            'mention' => ['badge_count' => (int) $mentions_count->count],
            'post' => ['badge_count' => (int) $replies_count->count],
            'channels' => array_values($channels),
            'total_channel' => $total_channel,
        ];

        return $responseData;
    }

    public static function getChannelById($channel_id)
    {
        // Get the currently logged-in user.
        $user = Auth::guard('vendor_users')->user();

        // Get the channel from channel_id.
        $channel = DB::table('channels')
            ->select('id as channel_id')
            ->where('vendor_id', $user->vendor_id)
            ->where('id', $channel_id)
            ->first();

        if ($channel) {
            $channel->name = self::getChannelName($channel_id, $user->vendor_id);

            // Retrieve the unread mention badge count in the channel identified by channel_id for the logged-in partner user.
            $channel->badge_count = self::getChannelBadgeCount($channel_id, $user->id)['mention_badge_count'];

            // Retrieve the total unread mention badge count from all channels of the logged-in partner user.
            $mentions_count = DB::table('chat_count_unread')
                ->where('vendor_user_id', $user->id)
                ->selectRaw('SUM(IFNULL(JSON_LENGTH(unread_mention_list_ids), 0)) as count')
                ->first();

            // Retrieve the total unread replies badge count from all channels of the logged-in partner user.
            $replies_count = DB::table('chat_count_unread')
                ->where('vendor_user_id', $user->id)
                ->selectRaw('SUM(IFNULL(JSON_LENGTH(unread_reply_list_ids), 0)) as count')
                ->first();

            // Check for unread messages in the channel by channel_id for the logged-in partner user.
            $channel->unread = self::getChannelBadgeCount($channel->channel_id, $user->id)['unread'];

            return [
                'mention' => ['badge_count' => (int) $mentions_count->count],
                'post' => ['badge_count' => (int) $replies_count->count],
                'channel_id' => $channel->channel_id,
                'name' => $channel->name,
                'badge_count' => $channel->badge_count,
                'unread' => $channel->unread,
            ];
        }

        return null;
    }

    private static function getChannelBadgeCount($channel_id, $user_id)
    {
        $chat_count_unread = DB::table('chat_count_unread')
            ->where('channel_id', $channel_id)
            ->where('vendor_user_id', $user_id)
            ->first();

        if ($chat_count_unread != null) {
            $unread_mention_list_ids = json_decode($chat_count_unread->unread_mention_list_ids) ?? [];
            $unread_reply_ids = json_decode($chat_count_unread->unread_reply_ids) ?? [];
            $unread_message_ids = json_decode($chat_count_unread->unread_message_ids) ?? [];

            return [
                'mention_badge_count' => count($unread_mention_list_ids),
                'unread' => ! empty($unread_reply_ids) || ! empty($unread_message_ids),
            ];
        }
        DB::table('chat_count_unread')->insert([
            'channel_id' => $channel_id,
            'vendor_user_id' => $user_id,
        ]);

        return [
            'mention_badge_count' => 0,
            'unread' => false,
        ];
    }

    //チャンネル名を動的に生成するための関数
    private static function getChannelName(int $channel_id, int $vendor_id)
    {
        //チャンネル許可情報を取得する
        $channel_allows = DB::table('channels')
            ->join('channel_allows', 'channels.id', 'channel_allows.channel_id')
            ->where('channels.id', $channel_id)
            ->first();

        //チャンネル自動生成条件
        $channel_auto_channel_authorizer = DB::table('auto_channel_authorizers')
            ->where('channel_id', $channel_id)
            ->first();

        //ベンダーのみのチャンネル名生成条件
        if (is_null($channel_allows) && is_null($channel_auto_channel_authorizer)) {
            return DB::table('vendors')
                ->where('id', $vendor_id)
                ->value('name');
        }

        //ALLベンダーのチャンネル名生成条件
        if (isset($channel_auto_channel_authorizer) || $channel_allows->permission === self::PERMISSION_READ) {
            return '全てのパートナー';
        }

        //ベンダー企業とパートナー企業のチャンネル名生成条件
        if (isset($channel_allows) && $channel_allows->permission === self::PERMISSION_ALL) {
            //もし複数のパートナーが存在する場合はエラーログを出してnullを返す
            if (
                DB::table('channel_allows')
                    ->where('channel_id', $channel_id)
                    ->where('permission', self::PERMISSION_ALL)
                    ->count() > 1
            ) {
                Log::error('Error: multiple partners in the channel');

                return null;
            }

            $partner_name = DB::table('partners')
                ->join('channel_allows', 'partners.id', 'channel_allows.partner_id')
                ->join('vendor_linked_partners', 'partners.id', 'vendor_linked_partners.partner_id')
                ->join('vendor_managed_partners', 'vendor_linked_partners.managed_partner_id', 'vendor_managed_partners.id')
                ->where('channel_allows.channel_id', $channel_id)
                ->value('vendor_managed_partners.name'); //2818: 共有先パートナー名を自社で管理しているパートナー名にする

            return $partner_name;
        }

        return null;
    }

    // internal function
    public static function createChannel($request)
    {
        $user = Auth::guard('vendor_users')->user();

        DB::table('channels')
            ->insert([
                'vendor_id' => $user->vendor_id,
                'name' => $request->name,
            ]);
    }

    public static function getMessages($channel_id)
    {
        $user = Auth::guard('vendor_users')->user();

        // TODO: Limit the number of messages got

        if (! ChatService::isOwner($user, $channel_id)) {
            throw new ServiceException('Error: channel does not exist', 404);
        }

        $res = DB::table('chat_posts')
            ->leftJoin(
                'vendor_users',
                'chat_posts.vendor_user_id',
                'vendor_users.id'
            )
            ->leftJoin('vendor_files', 'vendor_users.file_id', '=', 'vendor_files.id')
            ->leftJoin(
                'partner_users',
                'chat_posts.partner_user_id',
                'partner_users.id'
            )
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->where('channel_id', $channel_id)
            ->select(
                'chat_posts.id as chat_post_id',
                'chat_posts.channel_id',
                'chat_posts.vendor_user_id',
                'chat_posts.partner_user_id',
                'vendor_users.name as vendor_user_name',
                'partner_users.name as partner_user_name',
                'chat_posts.content',
                'chat_posts.deleted',
                'chat_posts.parent_id',
                'chat_posts.created_at',
                'chat_posts.updated_at',
                'vendor_files.name AS vendor_file_name',
                'vendor_files.s3_path AS vendor_s3_path',
                'partner_files.name AS partner_file_name',
                'partner_files.s3_path AS partner_s3_path'
            )
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();

        $attached_files = DB::table('attached_files')
            ->join(
                'chat_posts',
                'attached_files.chat_post_id',
                'chat_posts.id'
            )
            ->where('channel_id', $channel_id)
            ->where('parent_id', null)
            ->select(
                'chat_posts.id as chat_post_id',
                'attached_files.id as id',
                'attached_files.filesize as size',
                'attached_files.filename as name',
                'attached_files.s3_path as s3_path'
            )
            ->get();

        $file_dict = self::getAttachedFileArrays($attached_files);

        $parent_ids = [];
        foreach ($res as &$record) {
            if (array_key_exists($record->chat_post_id, $file_dict)) {
                $record->attached_files = $file_dict[$record->chat_post_id];
            } else {
                $record->attached_files = [];
            }

            // restructure
            $record->user = new stdClass;
            $record->user->name =
                $record->vendor_user_name !== null ?
                $record->vendor_user_name :
                $record->partner_user_name;
            $vendor_user_logo_url = ! empty($record->vendor_s3_path) ?
                S3Helper::getS3ImageUrl($record->vendor_s3_path) : null;
            $partner_user_logo_url = ! empty($record->partner_s3_path) ?
                S3Helper::getS3ImageUrl($record->partner_s3_path) : null;
            $vendor_user_file_name = $record->vendor_file_name ?? null;
            $partner_user_file_name = $record->partner_file_name ?? null;
            $record->user->logo_url =
                $record->vendor_user_name !== null ? $vendor_user_logo_url : $partner_user_logo_url;
            $record->user->file_name =
                $record->vendor_user_name !== null ? $vendor_user_file_name : $partner_user_file_name;
            //vendor_user_idとpartner_user_idがNULLの場合はnameを「削除されたユーザーです」に変更
            if (is_null($record->vendor_user_id) && is_null($record->partner_user_id)) {
                $record->user->name = __('message_names.deleted_user');
            }
            unset($record->vendor_user_name);
            unset($record->partner_user_name);
            $record->user->vendor_user_id = $record->vendor_user_id;
            $record->user->partner_user_id = $record->partner_user_id;

            if (! empty($record->parent_id)) {
                $parent_ids[$record->parent_id][$record->vendor_user_id.'_'.$record->partner_user_id] =
                    $record->user;
            }
            unset($record->vendor_user_id);
            unset($record->partner_user_id);

            $record->timestamp = new stdClass;
            $record->timestamp->created_at = $record->created_at;
            $record->timestamp->updated_at = $record->updated_at;
            unset($record->created_at);
            unset($record->updated_at);

            if ($record->user->vendor_user_id === $user->id) {
                $record->editable = true;
                $record->deletable = true;
            } else {
                $record->editable = false;
                $record->deletable = false;
            }
        }
        foreach ($res as &$record) {
            $record->thread_users = [];
            if (isset($parent_ids[$record->chat_post_id])) {
                $record->thread_users = array_values($parent_ids[$record->chat_post_id]);
            }
        }

        return $res;
    }

    public static function getMessagesPerPage(int $channel_id, ?int $chat_post_id = null, $chat_post_end = null)
    {
        $limit = self::PER_PAGE;
        $user = Auth::guard('vendor_users')->user();

        if (! ChatService::isOwner($user, $channel_id)) {
            throw new ServiceException('Error: channel does not exist', 404);
        }

        $sqlQuery = DB::table('chat_posts')
            ->leftJoin(
                'vendor_users',
                'chat_posts.vendor_user_id',
                'vendor_users.id'
            )
            ->leftJoin('vendor_files', 'vendor_users.file_id', '=', 'vendor_files.id')
            ->leftJoin(
                'partner_users',
                'chat_posts.partner_user_id',
                'partner_users.id'
            )
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->where('channel_id', $channel_id)
            ->whereNull('parent_id');

        $total = $sqlQuery->count();

        if ($chat_post_id) {
            $post = DB::table('chat_posts')->find($chat_post_id);
            $created_at = $post->created_at;

            if ($created_at) {
                $sqlQuery->where(function ($query) use ($created_at, $chat_post_id) {
                    $query->where('chat_posts.created_at', '<', $created_at)
                        ->orWhere(function ($query) use ($created_at, $chat_post_id) {
                            $query->where('chat_posts.created_at', '=', $created_at)
                                ->where('chat_posts.id', '<', $chat_post_id);
                        });
                });
            }
        }

        $posts = $sqlQuery->select(
            'chat_posts.id as chat_post_id',
            'chat_posts.channel_id',
            'chat_posts.vendor_user_id',
            'chat_posts.partner_user_id',
            'vendor_users.name as vendor_user_name',
            'partner_users.name as partner_user_name',
            'chat_posts.content',
            'chat_posts.deleted',
            'chat_posts.parent_id',
            'chat_posts.created_at',
            'chat_posts.updated_at',
            'vendor_files.name AS vendor_file_name',
            'vendor_files.s3_path AS vendor_s3_path',
            'partner_files.name AS partner_file_name',
            'partner_files.s3_path AS partner_s3_path'
        );
        // chat_post_end is the id of the most recent unread message
        if ($chat_post_end) {
            $posts->where('chat_posts.id', '>=', $chat_post_end);
        }
        $posts = $posts
            ->orderBy('created_at', 'desc')
            ->orderBy('chat_post_id', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
        $post_ids = array_map(function ($post) {
            return $post->chat_post_id;
        }, $posts);

        $attached_files = DB::table('attached_files')
            ->join(
                'chat_posts',
                'attached_files.chat_post_id',
                'chat_posts.id'
            )
            ->whereIn('chat_post_id', $post_ids)
            ->select(
                'chat_posts.id as chat_post_id',
                'attached_files.id as id',
                'attached_files.filesize as size',
                'attached_files.filename as name',
                'attached_files.s3_path as s3_path'
            )
            ->get();

        $file_dict = self::getAttachedFileArrays($attached_files);

        $parent_ids = [];
        foreach ($posts as &$record) {
            if (array_key_exists($record->chat_post_id, $file_dict)) {
                $record->attached_files = $file_dict[$record->chat_post_id];
            } else {
                $record->attached_files = [];
            }

            // restructure
            self::formatPostData($record, $user);
            $chat_count_unread = DB::table('chat_count_unread')
                ->where('vendor_user_id', $user->id)
                ->where('channel_id', $channel_id)
                ->first();
            $record->unread = ($chat_count_unread->unread_message_ids != null && in_array($record->chat_post_id, json_decode($chat_count_unread->unread_message_ids))) ? 1 : 0;
            unset($record->vendor_user_id);
            unset($record->partner_user_id);
            unset($record->channel_name);

            $record->total_replies = 0;

            $query = DB::table('chat_posts')
                ->leftJoin(
                    'vendor_users',
                    'chat_posts.vendor_user_id',
                    'vendor_users.id'
                )
                ->leftJoin(
                    'vendor_files',
                    'vendor_users.file_id',
                    'vendor_files.id'
                )
                ->leftJoin(
                    'partner_users',
                    'chat_posts.partner_user_id',
                    'partner_users.id'
                )
                ->leftJoin(
                    'partner_files',
                    'partner_users.file_id',
                    'partner_files.id'
                )
                ->where('parent_id', $record->chat_post_id)
                ->select(
                    'chat_posts.id as chat_post_id',
                    'chat_posts.channel_id',
                    'chat_posts.vendor_user_id',
                    'chat_posts.partner_user_id',
                    'vendor_users.name as vendor_user_name',
                    'partner_users.name as partner_user_name',
                    'chat_posts.content',
                    'chat_posts.deleted',
                    'chat_posts.parent_id',
                    'chat_posts.created_at',
                    'chat_posts.updated_at',
                    'vendor_files.name AS vendor_file_name',
                    'vendor_files.s3_path AS vendor_s3_path',
                    'partner_files.name AS partner_file_name',
                    'partner_files.s3_path AS partner_s3_path'
                );

            $all_reply = $query
                ->orderBy('created_at')
                ->get()
                ->toArray();
            $record->is_replies_unreads = false;
            if (count($all_reply) > 0) {
                foreach ($all_reply as &$reply) {
                    if (! $record->is_replies_unreads) {
                        if ($chat_count_unread->unread_reply_ids != null && in_array($reply->chat_post_id, json_decode($chat_count_unread->unread_reply_ids))) {
                            $record->is_replies_unreads = true;
                        }
                    }
                    self::formatPostData($reply, $user);

                    if (! empty($reply->parent_id)) {
                        $parent_ids[$reply->parent_id][$reply->vendor_user_id.'_'.$reply->partner_user_id] =
                            $reply->user;
                    }
                }
            }
            $record->total_replies = $query->where('deleted', 0)->count();
        }
        foreach ($posts as &$record) {
            $record->thread_users = [];
            if (isset($parent_ids[$record->chat_post_id])) {
                $record->thread_users = array_values($parent_ids[$record->chat_post_id]);
            }
        }

        return [
            'data' => $posts,
            'total_count' => $total,
        ];
    }

    public static function getThread($parent_id)
    {
        $user = Auth::guard('vendor_users')->user();

        $channel_id = DB::table('chat_posts')
            ->where('id', $parent_id)
            ->sole()
            ->channel_id;

        if (! ChatService::isOwner($user, $channel_id)) {
            throw new Exception('Error: cannot access to the channel');
        }
        $res_parent_post = DB::table('chat_posts')
            ->leftJoin(
                'vendor_users',
                'chat_posts.vendor_user_id',
                'vendor_users.id'
            )
            ->leftJoin('vendor_files', 'vendor_users.file_id', '=', 'vendor_files.id')
            ->leftJoin(
                'partner_users',
                'chat_posts.partner_user_id',
                'partner_users.id'
            )
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->where('channel_id', $channel_id)
            ->where('chat_posts.id', $parent_id)
            ->select(
                'chat_posts.id as chat_post_id',
                'chat_posts.channel_id',
                'chat_posts.vendor_user_id',
                'chat_posts.partner_user_id',
                'vendor_users.name as vendor_user_name',
                'partner_users.name as partner_user_name',
                'chat_posts.content',
                'chat_posts.deleted',
                'chat_posts.parent_id',
                'chat_posts.created_at',
                'chat_posts.updated_at',
                'vendor_files.name AS vendor_file_name',
                'vendor_files.s3_path AS vendor_s3_path',
                'partner_files.name AS partner_file_name',
                'partner_files.s3_path AS partner_s3_path'
            )
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();

        $res = DB::table('chat_posts')
            ->leftJoin(
                'vendor_users',
                'chat_posts.vendor_user_id',
                'vendor_users.id'
            )
            ->leftJoin('vendor_files', 'vendor_users.file_id', '=', 'vendor_files.id')
            ->leftJoin(
                'partner_users',
                'chat_posts.partner_user_id',
                'partner_users.id'
            )
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->where('channel_id', $channel_id)
            ->where('parent_id', $parent_id)
            ->select(
                'chat_posts.id as chat_post_id',
                'chat_posts.channel_id',
                'chat_posts.vendor_user_id',
                'chat_posts.partner_user_id',
                'vendor_users.name as vendor_user_name',
                'partner_users.name as partner_user_name',
                'chat_posts.content',
                'chat_posts.deleted',
                'chat_posts.parent_id',
                'chat_posts.created_at',
                'chat_posts.updated_at',
                'vendor_files.name AS vendor_file_name',
                'vendor_files.s3_path AS vendor_s3_path',
                'partner_files.name AS partner_file_name',
                'partner_files.s3_path AS partner_s3_path'
            )
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();
        //親の投稿と子の投稿を結合
        $res = array_merge($res, $res_parent_post);

        $message_ids = array_map(function ($thread) {
            return $thread->chat_post_id;
        }, $res);
        self::readMessagesInThread($user->id, $channel_id, $message_ids);

        //vendor_user_idとpartner_user_idがNULLの場合はnameを「削除されたユーザーです」に変更
        foreach ($res as &$record) {
            if (is_null($record->vendor_user_id) && is_null($record->partner_user_id)) {
                $record->vendor_user_name = __('message_names.deleted_user');
            }
        }

        $parent_attached_files = $attached_files = DB::table('attached_files')
            ->join(
                'chat_posts',
                'attached_files.chat_post_id',
                'chat_posts.id'
            )
            ->where('channel_id', $channel_id)
            ->where('chat_posts.id', $parent_id)
            ->select(
                'chat_posts.id as chat_post_id',
                'attached_files.id as id',
                'attached_files.filesize as size',
                'attached_files.filename as name',
                'attached_files.s3_path as s3_path'
            )
            ->get()
            ->toArray();

        $attached_files = DB::table('attached_files')
            ->join(
                'chat_posts',
                'attached_files.chat_post_id',
                'chat_posts.id'
            )
            ->where('channel_id', $channel_id)
            ->where('parent_id', $parent_id)
            ->select(
                'chat_posts.id as chat_post_id',
                'attached_files.id as id',
                'attached_files.filesize as size',
                'attached_files.filename as name',
                'attached_files.s3_path as s3_path'
            )
            ->get()
            ->toArray();

        //親の投稿と子の投稿の添付ファイルを結合
        $attached_files = array_merge($parent_attached_files, $attached_files);

        $file_dict = self::getAttachedFileArrays($attached_files);

        foreach ($res as &$record) {
            if (array_key_exists($record->chat_post_id, $file_dict)) {
                $record->attached_files = $file_dict[$record->chat_post_id];
            } else {
                $record->attached_files = [];
            }

            self::restructureUser($record);

            $record->timestamp = new stdClass;
            $record->timestamp->created_at = $record->created_at;
            $record->timestamp->updated_at = $record->updated_at;
            unset($record->created_at);
            unset($record->updated_at);
        }

        return $res;
    }

    public static function getThreadsPerPage(int $parent_id, ?int $chat_post_id = null, int $per_page = self::PER_PAGE, $chat_post_end = null)
    {
        $user = Auth::guard('vendor_users')->user();

        $channel_id = DB::table('chat_posts')
            ->where('id', $parent_id)
            ->sole()
            ->channel_id;

        if (! ChatService::isOwner($user, $channel_id)) {
            throw new Exception('Error: cannot access to the channel');
        }

        $parent_post = DB::table('chat_posts')
            ->leftJoin(
                'vendor_users',
                'chat_posts.vendor_user_id',
                'vendor_users.id'
            )
            ->leftJoin('vendor_files', 'vendor_users.file_id', '=', 'vendor_files.id')
            ->leftJoin(
                'partner_users',
                'chat_posts.partner_user_id',
                'partner_users.id'
            )
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->where('channel_id', $channel_id)
            ->where('chat_posts.id', $parent_id)
            ->select(
                'chat_posts.id as chat_post_id',
                'chat_posts.channel_id',
                'chat_posts.vendor_user_id',
                'chat_posts.partner_user_id',
                'vendor_users.name as vendor_user_name',
                'partner_users.name as partner_user_name',
                'chat_posts.content',
                'chat_posts.deleted',
                'chat_posts.parent_id',
                'chat_posts.created_at',
                'chat_posts.updated_at',
                'vendor_files.name AS vendor_file_name',
                'vendor_files.s3_path AS vendor_s3_path',
                'partner_files.name AS partner_file_name',
                'partner_files.s3_path AS partner_s3_path'
            )
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();

        $sql_query = DB::table('chat_posts')
            ->leftJoin(
                'vendor_users',
                'chat_posts.vendor_user_id',
                'vendor_users.id'
            )
            ->leftJoin('vendor_files', 'vendor_users.file_id', '=', 'vendor_files.id')
            ->leftJoin(
                'partner_users',
                'chat_posts.partner_user_id',
                'partner_users.id'
            )
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->where('channel_id', $channel_id)
            ->where('deleted', 0)
            ->where('parent_id', $parent_id);

        $total = $sql_query->count();

        if ($chat_post_id) {
            $sql_query->where('chat_posts.id', '<', $chat_post_id);
        }

        $messages = $sql_query->select(
            'chat_posts.id as chat_post_id',
            'chat_posts.channel_id',
            'chat_posts.vendor_user_id',
            'chat_posts.partner_user_id',
            'vendor_users.name as vendor_user_name',
            'partner_users.name as partner_user_name',
            'chat_posts.content',
            'chat_posts.deleted',
            'chat_posts.parent_id',
            'chat_posts.created_at',
            'chat_posts.updated_at',
            'vendor_files.name AS vendor_file_name',
            'vendor_files.s3_path AS vendor_s3_path',
            'partner_files.name AS partner_file_name',
            'partner_files.s3_path AS partner_s3_path'
        );
        //        if( $chat_post_end) {
        //            $messages->where('chat_posts.id', '>=', $chat_post_end);
        //        }
        $messages = $messages->orderBy('created_at', 'desc')
            ->limit($per_page)
            ->get()
            ->toArray();

        $messages = array_merge($messages, $parent_post);
        $message_ids = array_map(function ($thread) {
            return $thread->chat_post_id;
        }, $messages);

        //        self::readMessagesInThread($user->id, $channel_id, $message_ids);
        $attached_files = DB::table('attached_files')
            ->join(
                'chat_posts',
                'attached_files.chat_post_id',
                'chat_posts.id'
            )
            ->whereIn('chat_post_id', $message_ids)
            ->select(
                'chat_posts.id as chat_post_id',
                'attached_files.id as id',
                'attached_files.filesize as size',
                'attached_files.filename as name',
                'attached_files.s3_path as s3_path'
            )
            ->get();

        $file_dict = self::getAttachedFileArrays($attached_files);

        if (count($messages) >= 2 && $chat_post_id) {
            $first_messages_id = $messages[count($messages) - 2]->chat_post_id;
            $last_messages_id = $chat_post_id;

            $deleted_messages = DB::table('chat_posts')
                ->leftJoin(
                    'vendor_users',
                    'chat_posts.vendor_user_id',
                    'vendor_users.id'
                )
                ->leftJoin('vendor_files', 'vendor_users.file_id', '=', 'vendor_files.id')
                ->leftJoin(
                    'partner_users',
                    'chat_posts.partner_user_id',
                    'partner_users.id'
                )
                ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
                ->where('channel_id', $channel_id)
                ->where('deleted', 1)
                ->where('parent_id', $parent_id)
                ->whereBetween('chat_posts.id', [$first_messages_id, $last_messages_id])
                ->select(
                    'chat_posts.id as chat_post_id',
                    'chat_posts.channel_id',
                    'chat_posts.vendor_user_id',
                    'chat_posts.partner_user_id',
                    'vendor_users.name as vendor_user_name',
                    'partner_users.name as partner_user_name',
                    'chat_posts.content',
                    'chat_posts.deleted',
                    'chat_posts.parent_id',
                    'chat_posts.created_at',
                    'chat_posts.updated_at',
                    'vendor_files.name AS vendor_file_name',
                    'vendor_files.s3_path AS vendor_s3_path',
                    'partner_files.name AS partner_file_name',
                    'partner_files.s3_path AS partner_s3_path'
                )
                ->orderBy('created_at', 'desc')
                ->get()
                ->toArray();

            $messages = array_merge($deleted_messages, $messages);

            usort($messages, function ($firstMessage, $secondMessage) {
                return $secondMessage->chat_post_id - $firstMessage->chat_post_id;
            });
        }
        $chat_count_unread = DB::table('chat_count_unread')
            ->where('channel_id', $channel_id)
            ->where('vendor_user_id', $user->id)
            ->select('unread_reply_ids')
            ->first();
        $listIdReplyUnread = empty($chat_count_unread) ? [] : json_decode($chat_count_unread->unread_reply_ids);
        foreach ($messages as &$record) {
            $record->unread = empty($listIdReplyUnread) ? 0 : (int) in_array($record->chat_post_id, $listIdReplyUnread);
            if (array_key_exists($record->chat_post_id, $file_dict)) {
                $record->attached_files = $file_dict[$record->chat_post_id];
            } else {
                $record->attached_files = [];
            }

            if (is_null($record->vendor_user_id) && is_null($record->partner_user_id)) {
                $record->vendor_user_name = __('message_names.deleted_user');
            }

            self::formatPostData($record, $user);
            unset($record->vendor_user_id);
            unset($record->partner_user_id);
            unset($record->channel_name);
        }

        return [
            'data' => $messages,
            'total_count' => $total,
        ];
    }

    private static function readMessagesInThread($user_id, $channel_id, $chat_post_ids)
    {
        DB::beginTransaction();
        try {
            DB::table('chat_mentions')
                ->join(
                    'chat_posts',
                    'chat_mentions.chat_post_id',
                    'chat_posts.id'
                )
                ->whereIn('chat_post_id', $chat_post_ids)
                ->where('chat_mentions.vendor_user_id', $user_id)
                ->update([
                    'chat_mentions.unread' => false,
                    'chat_mentions.updated_at' => date('Y-m-d H:i:s'),
                ]);

            $chat_count_unread = DB::table('chat_count_unread')
                ->where('channel_id', $channel_id)
                ->where('vendor_user_id', $user_id)
                ->first();
            if ($chat_count_unread != null) {
                self::updateCountChatUnread($chat_count_unread, $chat_post_ids);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    // public static function checkChannelFromThread($parent_id)
    // {
    //     $user = Auth::guard('vendor_users')->user();

    //     DB::beginTransaction();
    //     try {

    //         DB::table('chat_mentions')
    //             ->join(
    //                 'chat_posts',
    //                 'chat_mentions.chat_post_id',
    //                 'chat_posts.id'
    //             )
    //             ->where('parent_id', $parent_id)
    //             ->where('chat_mentions.vendor_user_id', $user->id)
    //             ->update([
    //                 'chat_mentions.unread' => false,
    //                 'chat_mentions.updated_at' => date('Y-m-d H:i:s'),
    //             ]);

    //         DB::table('chat_posts')
    //         ->where('parent_id', $parent_id)
    //         ->whereNull('vendor_user_id')
    //         ->update([
    //             'unread' => false,
    //             'updated_at' => date('Y-m-d H:i:s'),
    //         ]);

    //         DB::commit();
    //     } catch (Exception $e) {
    //         DB::rollback();
    //         throw $e;
    //     }
    // }

    public static function getReplies(?int $chat_post_id = null)
    {
        $limit = self::PER_PAGE;

        $user = Auth::guard('vendor_users')->user();

        $sqlQuery = DB::table('chat_posts')
            ->join(
                'channels',
                'channels.id',
                'chat_posts.channel_id'
            )
            ->leftJoin(
                'vendor_users',
                'chat_posts.vendor_user_id',
                'vendor_users.id'
            )
            ->leftJoin(
                'vendor_files',
                'vendor_users.file_id',
                'vendor_files.id'
            )
            ->leftJoin(
                'partner_users',
                'chat_posts.partner_user_id',
                'partner_users.id'
            )
            ->leftJoin(
                'partner_files',
                'partner_users.file_id',
                'partner_files.id'
            )
            ->where('channels.name', '<>', 'ALL')   // ignore channel All Partner (channels.name = ALL)
            ->where('deleted', 0)
            ->whereNull('chat_posts.parent_id')
            ->whereExists(function ($query) use ($user) {
                $query->select(DB::raw(1))
                    ->from('chat_posts as replies')
                    ->whereColumn('replies.parent_id', 'chat_posts.id')
                    ->where('replies.deleted', 0)
                    ->where(function ($subQuery) use ($user) {
                        $subQuery->where('chat_posts.vendor_user_id', $user->id)
                            ->orWhere('replies.vendor_user_id', $user->id)
                            ->orWhereExists(function ($mentionsQuery) use ($user) {
                                $mentionsQuery->select(DB::raw(1))
                                    ->from('chat_mentions')
                                    ->whereColumn('chat_mentions.chat_post_id', 'chat_posts.id')
                                    ->where('chat_mentions.vendor_user_id', $user->id);
                            })
                            ->orWhereExists(function ($mentionsQuery) use ($user) {
                                $mentionsQuery->select(DB::raw(1))
                                    ->from('chat_mentions')
                                    ->whereColumn('chat_mentions.chat_post_id', 'replies.id')
                                    ->where('chat_mentions.vendor_user_id', $user->id);
                            });
                    });
            });

        $total = $sqlQuery->count();

        if ($chat_post_id) {
            $post = DB::table('chat_posts')->find($chat_post_id);
            $last_reply_at = $post->last_reply_at;

            if ($last_reply_at) {
                $sqlQuery->where(function ($query) use ($last_reply_at, $chat_post_id) {
                    $query->where('last_reply_at', '<', $last_reply_at)
                        ->orWhere(function ($query) use ($last_reply_at, $chat_post_id) {
                            $query->where('last_reply_at', '=', $last_reply_at)
                                ->where('chat_posts.id', '<', $chat_post_id);
                        });
                });
            }
        }

        $posts = $sqlQuery->select(
            'chat_posts.id as chat_post_id',
            'chat_posts.channel_id',
            'chat_posts.vendor_user_id',
            'chat_posts.partner_user_id',
            'vendor_users.name as vendor_user_name',
            'partner_users.name as partner_user_name',
            'chat_posts.content',
            'chat_posts.deleted',
            'chat_posts.parent_id',
            'chat_posts.last_reply_at',
            'chat_posts.created_at',
            'chat_posts.updated_at',
            'vendor_files.name AS vendor_file_name',
            'vendor_files.s3_path AS vendor_s3_path',
            'partner_files.name AS partner_file_name',
            'partner_files.s3_path AS partner_s3_path'
        )
            ->orderBy('last_reply_at', 'desc')
            ->orderBy('chat_post_id', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();

        $post_ids = collect($posts)->pluck('chat_post_id');

        $attached_files = DB::table('attached_files')
            ->join(
                'chat_posts',
                'attached_files.chat_post_id',
                'chat_posts.id'
            )
            ->whereIn('chat_post_id', $post_ids)
            ->where('parent_id', null)
            ->select(
                'chat_posts.id as chat_post_id',
                'attached_files.id as id',
                'attached_files.filesize as size',
                'attached_files.filename as name',
                'attached_files.s3_path as s3_path'
            )
            ->get();

        $file_dict = self::getAttachedFileArrays($attached_files);

        $parent_ids = [];
        foreach ($posts as &$post) {
            if (array_key_exists($post->chat_post_id, $file_dict)) {
                $post->attached_files = $file_dict[$post->chat_post_id];
            } else {
                $post->attached_files = [];
            }

            self::formatPostData($post, $user);
            $chat_count_unread = DB::table('chat_count_unread')
                ->where('vendor_user_id', $user->id)
                ->where('channel_id', $post->channel_id)
                ->whereJsonContains('unread_message_ids', $post->chat_post_id)
                ->first();
            $post->unread = $chat_count_unread != null ? 1 : 0;

            $post->total_replies = 0;
            $post->replies = [];

            $query = DB::table('chat_posts')
                ->leftJoin(
                    'vendor_users',
                    'chat_posts.vendor_user_id',
                    'vendor_users.id'
                )
                ->leftJoin(
                    'vendor_files',
                    'vendor_users.file_id',
                    'vendor_files.id'
                )
                ->leftJoin(
                    'partner_users',
                    'chat_posts.partner_user_id',
                    'partner_users.id'
                )
                ->leftJoin(
                    'partner_files',
                    'partner_users.file_id',
                    'partner_files.id'
                )
                ->where('parent_id', $post->chat_post_id)
                ->select(
                    'chat_posts.id as chat_post_id',
                    'chat_posts.channel_id',
                    'chat_posts.vendor_user_id',
                    'chat_posts.partner_user_id',
                    'vendor_users.name as vendor_user_name',
                    'partner_users.name as partner_user_name',
                    'chat_posts.content',
                    'chat_posts.deleted',
                    'chat_posts.parent_id',
                    'chat_posts.created_at',
                    'chat_posts.updated_at',
                    'vendor_files.name AS vendor_file_name',
                    'vendor_files.s3_path AS vendor_s3_path',
                    'partner_files.name AS partner_file_name',
                    'partner_files.s3_path AS partner_s3_path'
                );

            $all_reply = $query
                ->orderBy('created_at')
                ->get()
                ->toArray();

            if (count($all_reply) > 0) {
                foreach ($all_reply as &$reply) {
                    self::formatPostData($reply, $user);
                    $chat_count_unread = DB::table('chat_count_unread')
                        ->where('vendor_user_id', $user->id)
                        ->where('channel_id', $reply->channel_id)
                        ->whereJsonContains('unread_reply_ids', $reply->chat_post_id)
                        ->first();
                    $reply->unread = $chat_count_unread != null ? 1 : 0;

                    if (! empty($reply->parent_id)) {
                        $parent_ids[$reply->parent_id][$reply->vendor_user_id.'_'.$reply->partner_user_id] =
                            $reply->user;
                    }
                }

                // get unread replies
                $replies = collect($all_reply)->filter(function ($reply) {
                    return $reply->unread == 1;
                })->values()->toArray();

                // if unread replies is empty, get 2 latest replies
                if (count($replies) == 0) {
                    $replies = array_slice($all_reply, -2);
                }

                $reply_ids = collect($replies)->pluck('chat_post_id')->toArray();

                $attachedFileReplies = DB::table('attached_files')
                    ->join(
                        'chat_posts',
                        'attached_files.chat_post_id',
                        'chat_posts.id'
                    )
                    ->whereIn('chat_post_id', $reply_ids)
                    ->select(
                        'chat_posts.id as chat_post_id',
                        'attached_files.id as id',
                        'attached_files.filesize as size',
                        'attached_files.filename as name',
                        'attached_files.s3_path as s3_path'
                    )
                    ->get();

                $fileDictReplies = self::getAttachedFileArrays($attachedFileReplies);

                foreach ($replies as &$reply) {
                    if (array_key_exists($reply->chat_post_id, $fileDictReplies)) {
                        $reply->attached_files = $fileDictReplies[$reply->chat_post_id];
                    } else {
                        $reply->attached_files = [];
                    }
                }

                $post->total_replies = count(array_filter($all_reply, function ($reply) {
                    return $reply->deleted === 0;
                }));
                $post->replies = $replies;
            }
        }

        foreach ($posts as &$post) {
            $post->thread_users = [];
            if (isset($parent_ids[$post->chat_post_id])) {
                $post->thread_users = array_values($parent_ids[$post->chat_post_id]);
            }
        }

        return [
            'data' => $posts,
            'total_count' => $total,
        ];
    }

    // public static function checkChannel($channel_id)
    // {
    //     $user = Auth::guard('vendor_users')->user();

    //     if (!ChatService::isOwner($user, $channel_id)) {
    //         throw new Exception('Error: cannot access to the channel');
    //     }

    //     DB::beginTransaction();
    //     try {
    //         DB::table('chat_mentions')
    //             ->join(
    //                 'chat_posts',
    //                 'chat_mentions.chat_post_id',
    //                 'chat_posts.id'
    //             )
    //             ->where('channel_id', $channel_id)
    //             ->where('chat_mentions.vendor_user_id', $user->id)
    //             ->whereNull('chat_posts.parent_id')
    //             ->update([
    //                 'chat_mentions.unread' => false,
    //                 'chat_mentions.updated_at' => date('Y-m-d H:i:s'),
    //             ]);

    //         DB::table('chat_posts')
    //         ->where('channel_id', $channel_id)
    //         ->whereNull('vendor_user_id')
    //         ->whereNull('parent_id')
    //         ->update([
    //             'unread' => false,
    //             'updated_at' => date('Y-m-d H:i:s'),
    //         ]);

    //         DB::commit();
    //     } catch (Exception $e) {
    //         DB::rollback();
    //         throw $e;
    //     }
    // }

    public static function readMessagesInChannel($channel_id, $chatPostIds)
    {
        $user = Auth::guard('vendor_users')->user();

        if (! ChatService::isOwner($user, $channel_id)) {
            throw new Exception('Error: cannot access to the channel');
        }

        DB::beginTransaction();
        // Update and clear read messages in the channel using channel_id and the $chatPostIds array from the chat_count_unread table.
        try {
            $chat_count_unread = DB::table('chat_count_unread')
                ->where('channel_id', $channel_id)
                ->where('vendor_user_id', $user->id)
                ->first();
            if (! empty($chatPostIds)) {
                DB::table('chat_mentions')
                    ->join(
                        'chat_posts',
                        'chat_mentions.chat_post_id',
                        'chat_posts.id'
                    )
                    ->where('channel_id', $channel_id)
                    ->where('chat_mentions.vendor_user_id', $user->id)
                    ->whereNull('chat_posts.parent_id')
                    ->whereIn('chat_posts.id', $chatPostIds)
                    ->update([
                        'chat_mentions.unread' => false,
                        'chat_mentions.updated_at' => date('Y-m-d H:i:s'),
                    ]);
                self::updateCountChatUnreadByListPostId($chat_count_unread, $chatPostIds);
            }
            DB::commit();

            return self::getCountUnread($channel_id, $user->id);
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public static function readAllMessagesInChannel($channel_id)
    {
        $user = Auth::guard('vendor_users')->user();

        if (! ChatService::isOwner($user, $channel_id)) {
            throw new Exception('Error: cannot access to the channel');
        }

        DB::beginTransaction();
        // Update read all messages in the channel by channel_id
        try {
            DB::table('chat_mentions')
                ->join(
                    'chat_posts',
                    'chat_mentions.chat_post_id',
                    'chat_posts.id'
                )
                ->where('channel_id', $channel_id)
                ->where('chat_mentions.vendor_user_id', $user->id)
                ->where('chat_mentions.unread', (int) true)
                ->update([
                    'chat_mentions.unread' => (int) false,
                    'chat_mentions.updated_at' => date('Y-m-d H:i:s'),
                ]);

            $chat_count_unread = DB::table('chat_count_unread')
                ->where('channel_id', $channel_id)
                ->where('vendor_user_id', $user->id)
                ->first();

            if ($chat_count_unread != null) {
                DB::table('chat_count_unread')
                    ->where('id', $chat_count_unread->id)
                    ->update([
                        'unread_mention_list_ids' => null,
                        'unread_reply_list_ids' => null,
                        'unread_message_ids' => null,
                        'unread_reply_ids' => null,
                    ]);
            }

            DB::commit();

            return self::getChannelBadgeCount($channel_id, $user->id);
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    private static function handleMentions($request, $user, $channel_id, $post_id)
    {
        // delete old mentions
        DB::table('chat_mentions')
            ->where('chat_post_id', $post_id)
            ->delete();

        $mentions = [];

        // TODO: replace with uuid
        /* $regex = '/@([^@:]+):(vendor|partner|uuid=)([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})?/u'; */
        $regex = '/@\[([^@:\[\]]+)\]%text:([^@:\[\]]+):(channel|vendor|partner|uuid=)([vp][0-9]+)?%/u';

        $result = preg_match_all(
            $regex,
            $request->content,
            $matches,
            PREG_SET_ORDER
        );

        $mention_name = '';
        foreach ($matches as $match) {
            $name = $mention_name = $match[2];
            $type = $match[3];
            switch ($type) {
                case 'channel':
                    $vendor_user_ids = DB::table('vendor_users')
                        ->join(
                            'vendors',
                            'vendor_users.vendor_id',
                            'vendors.id'
                        )
                        ->join(
                            'channels',
                            'vendors.id',
                            'channels.vendor_id'
                        )
                        ->where('channels.id', $channel_id)
                        ->where('channels.vendor_id', $user->vendor_id)
                        ->pluck('vendor_users.id');

                    foreach ($vendor_user_ids as $vendor_user_id) {
                        if ($vendor_user_id == $user->id) {
                            continue;
                        }
                        array_push($mentions, [
                            'vendor_user_id' => $vendor_user_id,
                            'partner_user_id' => null,
                        ]);
                    }

                    $partner_user_ids = DB::table('channel_allows')
                        ->join(
                            'partner_team_members',
                            'channel_allows.partner_id',
                            'partner_team_members.partner_id'
                        )
                        ->where('channel_id', $channel_id)
                        ->where('permission', '&', self::PERMISSION_READ)
                        ->pluck('partner_team_members.partner_user_id');

                    foreach ($partner_user_ids as $partner_user_id) {
                        array_push($mentions, [
                            'vendor_user_id' => null,
                            'partner_user_id' => $partner_user_id,
                        ]);
                    }
                    break;
                case 'vendor':
                    $vendor_user_ids = DB::table('vendors')
                        ->join(
                            'vendor_users',
                            'vendors.id',
                            'vendor_users.vendor_id'
                        )
                        ->join(
                            'channels',
                            'vendors.id',
                            'channels.vendor_id'
                        )
                        ->where('channels.id', $channel_id)
                        ->where('vendors.name', $name)
                        ->where('vendors.id', $user->vendor_id)
                        ->pluck('vendor_users.id');

                    foreach ($vendor_user_ids as $vendor_user_id) {
                        if ($vendor_user_id == $user->id) {
                            continue;
                        }
                        array_push($mentions, [
                            'vendor_user_id' => $vendor_user_id,
                            'partner_user_id' => null,
                        ]);
                    }
                    break;
                case 'partner':
                    $partner_user_ids = DB::table('channel_allows')
                        ->join(
                            'partners',
                            'channel_allows.partner_id',
                            'partners.id'
                        )
                        ->join(
                            'vendor_linked_partners',
                            'partners.id',
                            'vendor_linked_partners.partner_id'
                        )->join(
                            'vendor_managed_partners',
                            'vendor_linked_partners.managed_partner_id',
                            'vendor_managed_partners.id'
                        )
                        ->join(
                            'partner_team_members',
                            'partners.id',
                            'partner_team_members.partner_id'
                        )
                        // Perhaps operator '&' is unuseable
                        ->where('channel_id', $channel_id)
                        ->where('permission', '&', self::PERMISSION_READ)
                        ->where('vendor_managed_partners.name', $name)
                        ->pluck('partner_team_members.partner_user_id');

                    foreach ($partner_user_ids as $partner_user_id) {
                        array_push($mentions, [
                            'vendor_user_id' => null,
                            'partner_user_id' => $partner_user_id,
                        ]);
                    }
                    break;
                case 'uuid=':
                    // 一人しかメンションされない。
                    $uuid = $match[4];
                    if (strlen($uuid) < 2) {
                        break;
                    }
                    if (substr($uuid, 0, 1) == 'v') {
                        $vendor_user_id = intval(substr($uuid, 1));

                        $existUser = DB::table('vendor_users')
                            ->join(
                                'channels',
                                'vendor_users.vendor_id',
                                'channels.vendor_id'
                            )
                            ->where('channels.id', $channel_id)
                            ->where('vendor_users.id', $vendor_user_id)
                            ->where('vendor_users.vendor_id', $user->vendor_id)
                            ->exists();
                        if ($existUser) {
                            array_push($mentions, [
                                'vendor_user_id' => $vendor_user_id,
                                'partner_user_id' => null,
                            ]);
                        }
                    }
                    if (substr($uuid, 0, 1) == 'p') {
                        $partner_user_id = intval(substr($uuid, 1));

                        $existUser = DB::table('channel_allows')
                            ->join(
                                'partner_team_members',
                                'channel_allows.partner_id',
                                'partner_team_members.partner_id'
                            )
                            // Perhaps operator '&' is unusable
                            ->where('channel_id', $channel_id)
                            ->where('permission', '&', self::PERMISSION_READ)
                            ->where('partner_team_members.partner_user_id', $partner_user_id)
                            ->exists();
                        if ($existUser) {
                            array_push($mentions, [
                                'vendor_user_id' => null,
                                'partner_user_id' => $partner_user_id,
                            ]);
                        }
                    }
                    break;
            }
        }

        // Creating mentions
        $mentions = array_unique($mentions, SORT_REGULAR);
        $chat_mention_ids = [];
        foreach ($mentions as &$mention) {
            $chat_mention_id = DB::table('chat_mentions')
                ->insertGetId([
                    'vendor_user_id' => $mention['vendor_user_id'],
                    'partner_user_id' => $mention['partner_user_id'],
                    'chat_post_id' => $post_id,
                    'unread' => $user->id == $mention['vendor_user_id'] ? 0 : 1,
                ]);
            $chat_mention_ids[] = $chat_mention_id;
        }
        unset($mention);

        return [
            'mentions' => $mentions,
            'matches' => $matches,
            'chat_mention_ids' => $chat_mention_ids,
        ];
    }

    public static function createMessage($request, $channel_id)
    {
        $user = Auth::guard('vendor_users')->user();

        if (! ChatService::isOwner($user, $channel_id)) {
            throw new Exception('Error: cannot access to the channel');
        }

        if ($request->content == null && $request->attached_files == null) {
            throw new Exception('Error: empty request');
        }

        if ($request->parent_id !== null) {
            $parent_parent_id = DB::table('chat_posts')
                ->where('id', $request->parent_id)
                ->sole()
                ->parent_id;
            if ($parent_parent_id !== null) {
                throw new Exception('Error: cannot make nested thread');
            }
        }

        DB::beginTransaction();
        try {
            $now = date('Y-m-d H:i:s');
            $post_id = DB::table('chat_posts')
                ->insertGetId([
                    'channel_id' => $channel_id,
                    'vendor_user_id' => $user->id,
                    'content' => $request->content,
                    'parent_id' => $request->parent_id,
                    'last_reply_at' => $now,
                    'created_at' => $now,
                    'updated_at' => $now,
                ]);

            if ($request->parent_id) {
                DB::table('chat_posts')
                    ->where('id', $request->parent_id)
                    ->update([
                        'last_reply_at' => $now,
                    ]);
            }

            if ($request->attached_files == null) {
                $request->attached_files = [];
            }

            $attached_files = [];
            $s3Client = self::getS3Client();
            foreach ($request->attached_files as $attached_file) {
                $file = base64_decode($attached_file['base64_string']);
                $uuid = uuid_create();
                $filename = $attached_file['name'];
                $s3_path = $uuid.'/'.$attached_file['name'];

                $attached_file_id = DB::table('attached_files')
                    ->insertGetId([
                        'chat_post_id' => $post_id,
                        'filename' => $filename,
                        'filesize' => strlen($file),
                        's3_path' => $s3_path,
                    ]);

                Storage::disk('s3-chat')->put($s3_path, $file);

                $presignedUrl = self::getS3Url(
                    $s3Client,
                    $s3_path,
                    $contentType
                );

                array_push($attached_files, [
                    'id' => $attached_file_id,
                    'size' => strlen($file),
                    'name' => $filename,
                    'url' => $presignedUrl,
                    'contentType' => $contentType,
                ]);
            }

            [
                'mentions' => $mentions,
                'matches' => $matches,
                'chat_mention_ids' => $chat_mention_ids,
            ] = self::handleMentions($request, $user, $channel_id, $post_id);

            $vendor_user = DB::table('vendor_users')
                ->leftJoin('vendor_files', 'vendor_users.file_id', '=', 'vendor_files.id')
                ->where('vendor_users.id', $user->id)
                ->select(
                    'vendor_files.name AS file_name',
                    'vendor_files.s3_path AS s3_path',
                )
                ->first();

            // Broadcasting
            $msg = new ChatMessage;

            $msg->channel_id = $channel_id;
            $msg->chat_post_id = $post_id;
            $msg->parent_id = (int) $request->parent_id;
            $msg->content = $request->content;

            $msg->attached_files = $attached_files;

            $msg->user_name = $user->name;
            $msg->vendor_user_id = $user->id;
            $msg->partner_user_id = (int) null;
            $msg->user_s3_path = ! empty($vendor_user->s3_path) ?
                S3Helper::getS3ImageUrl($vendor_user->s3_path) : null;
            $msg->user_file_name = $vendor_user->file_name ?? null;

            $msg->last_reply_at = $now;
            $msg->created_at = $now;
            $msg->updated_at = $now;
            $msg->deleted = (int) false;
            $msg->unread = (int) true;

            $msg->mentions = $mentions;

            ChatMessageCreated::dispatch($msg);
            UpdateBadgeCountChannel::dispatch($msg, $mentions);
            UpdateChatCountUnreadJob::dispatch($user, (int) $request->parent_id, $msg, $mentions);

            $parent_id = (int) $request->parent_id;

            if ($parent_id != 0) {

                ReceiveReplyChat::dispatch($user, $parent_id, $msg);
                ReceiveMentionChat::dispatch($user, $parent_id, $msg, $mentions);
            } elseif (count($mentions) > 0) {
                ReceiveMentionChat::dispatch($user, $parent_id, $msg, $mentions);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }

        $channel_name = self::getChannelName($channel_id, $user->vendor_id);

        // '10分後にメール送信するジョブ'を作成
        VendorChatMentionJobs::dispatch(
            $post_id,
            $chat_mention_ids,
            $channel_name,
            $matches,
        )->delay(now()->addMinutes(10));
    }

    public static function updateMessage($request, $post_id)
    {
        $user = Auth::guard('vendor_users')->user();

        $post = DB::table('chat_posts')
            ->where('id', $post_id)
            ->sole();

        if ($post->vendor_user_id !== $user->id) {
            throw new Exception('Error: cannot access to the channel');
        }

        DB::beginTransaction();
        try {
            $now = date('Y-m-d H:i:s');

            DB::table('chat_posts')
                ->where('id', $post_id)
                ->update([
                    'content' => $request->content,
                    'updated_at' => $now,
                ]);

            [
                'mentions' => $mentions,
                'matches' => $matches,
                'chat_mention_ids' => $chat_mention_ids,
            ] = self::handleMentions($request, $user, $post->channel_id, $post_id);

            // Broadcasting
            $msg = new ChatMessage;

            $msg->channel_id = $post->channel_id;
            $msg->chat_post_id = $post_id;
            $msg->parent_id = (int) $post->parent_id;
            $msg->content = $request->content;

            $msg->created_at = $post->created_at;
            $msg->updated_at = $now;

            ChatMessageUpdated::dispatch($msg);

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }

        $channel_name = self::getChannelName($post->channel_id, $user->vendor_id);

        // '10分後にメール送信するジョブ'を作成
        VendorChatMentionJobs::dispatch(
            $post_id,
            $chat_mention_ids,
            $channel_name,
            $matches,
        )->delay(now()->addMinutes(10));
    }

    private static function checkIfMessageIsDeletedFromMentionListAndReplyList($post)
    {
        $user = Auth::guard('vendor_users')->user();

        if ($post->parent_id == null) {
            return [
                'is_deleted_from_mention_list' => true,
                'is_deleted_from_reply_list' => true,
            ];
        }

        $is_deleted_from_mention_list = false;
        $is_deleted_from_reply_list = false;

        $parent_post = DB::table('chat_posts')
            ->where('id', $post->parent_id)
            ->sole();

        $other_replies = DB::table('chat_posts')
            ->where('parent_id', $parent_post->id)
            ->where('deleted', 0)
            ->where('id', '!=', $post->id);

        $is_mention = DB::table('chat_mentions')
            ->where('chat_post_id', $parent_post->id)
            ->where('vendor_user_id', $user->id)
            ->exists();

        if ($other_replies->count() == 0) {
            $is_deleted_from_reply_list = true;
            $is_deleted_from_mention_list = ! $is_mention;
        } elseif ($other_replies->count() > 0) {
            if ($parent_post->vendor_user_id == $user->id || in_array($user->id, $other_replies->pluck('vendor_user_id')->toArray())) {
                $is_deleted_from_reply_list = false;
            } else {
                $is_deleted_from_reply_list = true;
            }

            if ($is_mention) {
                $is_deleted_from_mention_list = false;
            } else {
                $is_mention_in_other_replies = DB::table('chat_mentions')
                    ->whereIn('chat_post_id', $other_replies->pluck('id')->toArray())
                    ->where('vendor_user_id', $user->id)
                    ->exists();
                $is_deleted_from_mention_list = ! $is_mention_in_other_replies;
            }
        }

        return [
            'is_deleted_from_mention_list' => $is_deleted_from_mention_list,
            'is_deleted_from_reply_list' => $is_deleted_from_reply_list,
        ];
    }

    public static function deleteMessage($post_id)
    {
        $user = Auth::guard('vendor_users')->user();

        $post = DB::table('chat_posts')
            ->where('id', $post_id)
            ->sole();

        if ($post->vendor_user_id !== $user->id) {
            throw new Exception('Error: cannot access to the channel');
        }

        $check_result = self::checkIfMessageIsDeletedFromMentionListAndReplyList($post);

        DB::beginTransaction();
        try {
            $now = date('Y-m-d H:i:s');

            DB::table('chat_posts')
                ->where('id', $post_id)
                ->orWhere('parent_id', $post_id)
                ->update([
                    'content' => '',
                    // Perhaps false -> 0
                    'deleted' => true,
                    'updated_at' => $now,
                ]);

            $query = DB::table('attached_files')
                ->where('chat_post_id', $post_id);
            $attached_files = $query->get();
            $query->delete();
            foreach ($attached_files as $attached_file) {
                Storage::disk('s3-chat')->delete($attached_file->s3_path);
            }

            // Broadcasting
            $msg = new ChatMessage;

            $msg->channel_id = $post->channel_id;
            $msg->chat_post_id = $post_id;
            $msg->parent_id = (int) $post->parent_id;
            $msg->vendor_user_id = $post->vendor_user_id;
            $msg->partner_user_id = 0;
            $msg->deleted = (int) true;
            $msg->unread = (int) false;

            $msg->updated_at = $now;

            ChatMessageDeleted::dispatch($msg);
            UpdateBadgeCountChannel::dispatch($msg);

            DB::commit();

            return $check_result;
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public static function getAttachedFile($attached_file_id)
    {
        $user = Auth::guard('vendor_users')->user();

        $attached_file = DB::table('attached_files')
            ->join(
                'chat_posts',
                'attached_files.chat_post_id',
                'chat_posts.id'
            )
            ->where('attached_files.id', $attached_file_id)
            ->select(
                'attached_files.id as id',
                'attached_files.filename as name',
                'attached_files.filesize as size',
                'attached_files.s3_path as s3_path',
                'chat_posts.channel_id as channel_id'
            )
            ->sole();

        $channel_id = $attached_file->channel_id;

        if (! ChatService::isOwner($user, $channel_id)) {
            throw new Exception('Error: cannot access to the channel');
        }

        $s3Client = new S3Client([
            'region' => config('filesystems.disks.s3-chat.region'),
            'version' => 'latest',
            'credentials' => [
                'key' => config('filesystems.disks.s3-chat.key'),
                'secret' => config('filesystems.disks.s3-chat.secret'),
            ],
            'endpoint' => config('filesystems.disks.s3-chat.endpoint'),
            'use_path_style_endpoint' => config('filesystems.disks.s3-chat.use_path_style_endpoint'),
        ]);

        $cmd = $s3Client->getCommand('GetObject', [
            'Bucket' => config('filesystems.disks.s3-chat.bucket'),
            'Key' => $attached_file->s3_path,
        ]);
        $request = $s3Client->createPresignedRequest($cmd, '+1440 minutes');
        $presignedUrl = (string) $request->getUri();

        $objectMetadata = $s3Client->headObject([
            'Bucket' => config('filesystems.disks.s3-chat.bucket'),
            'Key' => $attached_file->s3_path,
        ]);

        $contentType = $objectMetadata['ContentType'];

        return [
            'id' => $attached_file->id,
            'size' => $attached_file->size,
            'name' => $attached_file->name,
            'url' => $presignedUrl,
            'contentType' => $contentType,
        ];
    }

    public static function downloadAttachedFile($attached_file_id)
    {
        $user = Auth::guard('vendor_users')->user();

        $attached_file = DB::table('attached_files')
            ->join(
                'chat_posts',
                'attached_files.chat_post_id',
                'chat_posts.id'
            )
            ->where('attached_files.id', $attached_file_id)
            ->select(
                'chat_posts.channel_id as channel_id',
                'attached_files.filename as filename',
                'attached_files.s3_path as s3_path',
            )
            ->sole();
        //自社のファイルのみダウンロード可能にする
        if (! ChatService::isOwner($user, $attached_file->channel_id)) {
            throw new Exception('Error: cannot access to the channel');
        }

        // Storageファサードを使用してS3からファイルの情報を取得
        $mimeType = Storage::disk('s3-chat')->mimeType($attached_file->s3_path);
        $content = Storage::disk('s3-chat')->get($attached_file->s3_path);
        $file_name = $attached_file->filename;

        return [
            'content' => $content,
            'mimeType' => $mimeType,
            'fileName' => $file_name,
        ];
    }

    public static function deleteAttachedFile($attached_file_id)
    {
        $user = Auth::guard('vendor_users')->user();

        $attached_file = DB::table('attached_files')
            ->join(
                'chat_posts',
                'attached_files.chat_post_id',
                'chat_posts.id'
            )
            ->where('attached_files.id', $attached_file_id)
            ->select(
                'attached_files.s3_path as s3_path',
                'chat_posts.vendor_user_id as vendor_user_id'
            )
            ->sole();

        if ($attached_file->vendor_user_id !== $user->id) {
            throw new Exception('Error: cannot access to the channel');
        }

        DB::beginTransaction();
        try {
            $query = DB::table('attached_files')
                ->where('id', $attached_file_id);
            $query->delete();
            Storage::disk('s3-chat')->delete($attached_file->s3_path);

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    // internal function
    public static function updatePermissions($request, $channel_id)
    {
        $user = Auth::guard('vendor_users')->user();

        if (! ChatService::isOwner($user, $channel_id)) {
            throw new Exception('Error: cannot access to the channel');
        }

        DB::beginTransaction();
        try {
            foreach ($request->partners as $partner_id) {
                DB::table('channel_allows')
                    ->updateOrInsert(
                        [
                            'channel_id' => $channel_id,
                            'partner_id' => intval($partner_id),
                        ],
                        [
                            'permission' => $request->permission,
                        ]
                    );
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    private const USER_VENDOR_USER = 0;

    private const USER_PARTNER_USER = 1;

    public static function getMembers($channel_id)
    {
        $user = Auth::guard('vendor_users')->user();

        if (! ChatService::isOwner($user, $channel_id)) {
            throw new Exception('Error: cannot access to the channel');
        }

        $vendor_users = DB::table('vendor_users')
            ->leftJoin('vendor_files', 'vendor_users.file_id', '=', 'vendor_files.id')
            ->join(
                'vendors',
                'vendor_users.vendor_id',
                'vendors.id'
            )
            ->where('vendor_users.vendor_id', $user->vendor_id)
            ->select(
                'vendor_users.role as role',
                'vendor_users.name as name',
                'vendor_users.email as email',
                'vendor_users.id as vendor_user_id',
                'vendors.name as company_name',
                'vendor_files.name AS file_name',
                'vendor_files.s3_path AS s3_path'
            )
            ->get();

        $partner_users = DB::table('channel_allows')
            ->join(
                'partner_team_members',
                'channel_allows.partner_id',
                'partner_team_members.partner_id'
            )
            ->join(
                'partner_users',
                'partner_team_members.partner_user_id',
                'partner_users.id'
            )
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->join(
                'partners',
                'partner_team_members.partner_id',
                'partners.id'
            )
            ->where('channel_id', $channel_id)
            // Perhaps operator '&' is unusable
            ->where('permission', '&', self::PERMISSION_READ)
            ->select(
                'partner_team_members.role as role',
                'partner_users.name as name',
                'partner_users.email as email',
                'partner_users.id as partner_user_id',
                'partners.name as company_name',
                'partner_files.name AS file_name',
                'partner_files.s3_path AS s3_path'
            )
            ->get();

        $res = [];

        foreach ($vendor_users as $vendor_user) {
            array_push($res, [
                'type' => ChatService::USER_VENDOR_USER,
                'role' => $vendor_user->role,
                'name' => $vendor_user->name,
                'email' => $vendor_user->email,
                'vendor_user_id' => $vendor_user->vendor_user_id,
                'company_name' => $vendor_user->company_name,
                'file_name' => $vendor_user->file_name,
                'logo_url' => ! empty($vendor_user->s3_path) ? S3Helper::getS3ImageUrl($vendor_user->s3_path) : null,
            ]);
        }

        foreach ($partner_users as $partner_user) {
            array_push($res, [
                'type' => ChatService::USER_PARTNER_USER,
                'role' => $partner_user->role,
                'name' => $partner_user->name,
                'email' => $partner_user->email,
                'partner_user_id' => $partner_user->partner_user_id,
                'company_name' => $partner_user->company_name,
                'file_name' => $partner_user->file_name,
                'logo_url' => ! empty($partner_user->s3_path) ? S3Helper::getS3ImageUrl($partner_user->s3_path) : null,
            ]);
        }

        return $res;
    }

    private const MENTION_VENDOR = 0;

    private const MENTION_PARTNER = 1;

    private const MENTION_VENDOR_USER = 2;

    private const MENTION_PARTNER_USER = 3;

    private const MENTION_CHANNEL = 4;

    public static function getMentionables($channel_id)
    {
        $user = Auth::guard('vendor_users')->user();

        if (! ChatService::isOwner($user, $channel_id)) {
            throw new Exception('Error: cannot access to the channel');
        }

        //ALL_PARTNERチャンネルの場合はvendorとvendor_usersを取得しない
        if (! ChatService::isAllPartnerChannel($channel_id)) {
            $vendor = DB::table('vendors')
                ->where('id', $user->vendor_id)
                ->sole();
            $vendor_users = DB::table('vendor_users')
                ->leftJoin('vendor_files', 'vendor_users.file_id', '=', 'vendor_files.id')
                ->where('vendor_users.vendor_id', $user->vendor_id)
                ->select(
                    'vendor_users.*',
                    'vendor_files.name AS file_name',
                    'vendor_files.s3_path AS s3_path',
                )
                ->get();
        } else {
            $vendor = null;
            $vendor_users = [];
        }

        $partners = DB::table('channel_allows')
            ->join(
                'partners',
                'channel_allows.partner_id',
                'partners.id'
            )
            ->join(
                'vendor_linked_partners',
                'partners.id',
                'vendor_linked_partners.partner_id'
            )
            ->join(
                'vendor_managed_partners',
                'vendor_linked_partners.managed_partner_id',
                'vendor_managed_partners.id'
            )
            // Perhaps operator '&' is unusable
            ->where('channel_allows.permission', '<>', self::PERMISSION_READ)
            ->where('channel_allows.channel_id', $channel_id)
            ->select(
                'vendor_managed_partners.name as name',
                'partners.id as id'
            )
            ->get();

        $partner_users = DB::table('channel_allows')
            ->join(
                'partner_team_members',
                'channel_allows.partner_id',
                'partner_team_members.partner_id'
            )
            ->join(
                'partner_users',
                'partner_team_members.partner_user_id',
                'partner_users.id'
            )
            ->where('channel_id', $channel_id)
            // Perhaps operator '&' is unusable
            ->where('channel_allows.permission', '<>', self::PERMISSION_READ)
            ->select(
                'partner_users.name as name',
                'partner_users.email as email',
                'partner_users.id as id'
            )
            ->get();

        $res = [];

        array_push($res, [
            'type' => ChatService::MENTION_CHANNEL,
            'label' => 'channel',
            'text' => 'all:channel',
        ]);

        if (! ChatService::isAllPartnerChannel($channel_id)) {
            array_push($res, [
                'type' => ChatService::MENTION_VENDOR,
                'label' => $vendor->name,
                'text' => $vendor->name.':vendor',
                'vendor_id' => $vendor->id,
            ]);
        }

        foreach ($partners as $partner) {
            array_push($res, [
                'type' => ChatService::MENTION_PARTNER,
                'label' => $partner->name,
                'text' => $partner->name.':partner',
                'partner_id' => $partner->id,
            ]);
        }

        foreach ($vendor_users as $vendor_user) {
            array_push($res, [
                'type' => ChatService::MENTION_VENDOR_USER,
                'label' => $vendor_user->name,
                // TODO: replace email with uuid
                'text' => $vendor_user->name.':uuid=v'.$vendor_user->id,
                'vendor_user_id' => $vendor_user->id,
                'file_name' => $vendor_user->file_name,
                'logo_url' => ! empty($vendor_user->s3_path) ? S3Helper::getS3ImageUrl($vendor_user->s3_path) : null,
            ]);
        }

        foreach ($partner_users as $partner_user) {
            array_push($res, [
                'type' => ChatService::MENTION_PARTNER_USER,
                'label' => $partner_user->name,
                // TODO: replace email with uuid
                'text' => $partner_user->name.':uuid=p'.$partner_user->id,
                'partner_user_id' => $partner_user->id,
                'file_name' => null,
                'logo_url' => null,
            ]);
        }

        return $res;
    }

    public static function getBadges()
    {
        $user = Auth::guard('vendor_users')->user();

        $channel_ids = DB::table('chat_mentions')
            ->join('chat_posts', 'chat_post_id', 'chat_posts.id')
            ->where('chat_mentions.vendor_user_id', $user->id)
            ->where('unread', true)
            ->pluck('chat_posts.channel_id');

        $histgram = [];
        foreach ($channel_ids as $channel_id) {
            if (array_key_exists($channel_id, $histgram)) {
                $histgram[$channel_id]++;
            } else {
                $histgram[$channel_id] = 1;
            }
        }

        $res = [];
        foreach ($histgram as $channel_id => $count) {
            $badge = [
                'channel_id' => $channel_id,
                'count' => $count,
            ];
            array_push($res, $badge);
        }

        return $res;
    }

    public static function getUnreads($channel_id)
    {
        $user = Auth::guard('vendor_users')->user();

        if (! ChatService::isOwner($user, $channel_id)) {
            throw new Exception('Error: cannot access to the channel');
        }

        $posts = DB::table('chat_mentions')
            ->join('chat_posts', 'chat_post_id', 'chat_posts.id')
            ->where('chat_mentions.vendor_user_id', $user->id)
            ->where('unread', true)
            ->select(
                'chat_posts.id',
                'chat_posts.channel_id',
                'chat_posts.vendor_user_id',
                'chat_posts.partner_user_id',
                'content',
                'deleted',
                'parrent_id',
                'chat_posts.created_at',
                'chat_posts.updated_at',
            )
            ->get()
            ->toArray();

        return ['count' => count($posts), 'posts' => $posts];
    }

    public static function getAvatarChat($request)
    {
        $thread_users = [];
        if (! empty($request->parent_id)) {
            $thread_users = self::getByMessageId($request->parent_id);
        }
        if (! empty($request->vendor_user_id)) {
            $vendor_user = DB::table('vendor_users')
                ->leftJoin('vendor_files', 'vendor_users.file_id', '=', 'vendor_files.id')
                ->select(
                    'vendor_files.name AS file_name',
                    'vendor_files.s3_path AS s3_path',
                )
                ->where('vendor_users.id', $request->vendor_user_id)
                ->first();
            $file_name = $vendor_user->file_name ?? null;
            $avatar_url = ! empty($vendor_user->s3_path) ? S3Helper::getS3ImageUrl($vendor_user->s3_path) : null;
        }
        if (! empty($request->partner_user_id)) {
            $partner_user = DB::table('partner_users')
                ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
                ->select(
                    'partner_files.name AS file_name',
                    'partner_files.s3_path AS s3_path',
                )
                ->where('partner_users.id', $request->partner_user_id)
                ->first();
            $file_name = $partner_user->file_name ?? null;
            $avatar_url = ! empty($partner_user->s3_path) ? S3Helper::getS3ImageUrl($partner_user->s3_path) : null;
        }

        return [
            'file_name' => $file_name ?? null,
            'avatar_url' => $avatar_url ?? null,
            'thread_users' => $thread_users,
        ];
    }

    public static function getByMessageId($chat_post_id)
    {
        $res = DB::table('chat_posts')
            ->leftJoin(
                'vendor_users',
                'chat_posts.vendor_user_id',
                'vendor_users.id'
            )
            ->leftJoin('vendor_files', 'vendor_users.file_id', '=', 'vendor_files.id')
            ->leftJoin(
                'partner_users',
                'chat_posts.partner_user_id',
                'partner_users.id'
            )
            ->leftJoin('partner_files', 'partner_users.file_id', '=', 'partner_files.id')
            ->where('chat_posts.parent_id', $chat_post_id)
            ->select(
                'chat_posts.vendor_user_id',
                'chat_posts.partner_user_id',
                'vendor_users.name as vendor_user_name',
                'partner_users.name as partner_user_name',
                'vendor_files.name AS vendor_file_name',
                'vendor_files.s3_path AS vendor_s3_path',
                'partner_files.name AS partner_file_name',
                'partner_files.s3_path AS partner_s3_path'
            )
            ->orderBy('chat_posts.created_at', 'desc')
            ->get()
            ->toArray();

        $result = [];
        foreach ($res as $record) {
            $record->user = new stdClass;
            $record->user->name =
                $record->vendor_user_name !== null ?
                    $record->vendor_user_name :
                    $record->partner_user_name;
            $partner_user_logo_url = ! empty($record->partner_s3_path) ?
                S3Helper::getS3ImageUrl($record->partner_s3_path) : null;
            $partner_user_file_name = $record->partner_file_name ?? null;
            $record->user->logo_url =
                $record->partner_user_name !== null ? $partner_user_logo_url : null;
            $record->user->file_name =
                $record->partner_user_name !== null ? $partner_user_file_name : null;
            //vendor_user_idとpartner_user_idがNULLの場合はnameを「削除されたユーザーです」に変更
            if (is_null($record->vendor_user_id) && is_null($record->partner_user_id)) {
                $record->user->name = __('message_names.deleted_user');
            }
            unset($record->vendor_user_name);
            unset($record->partner_user_name);
            $record->user->vendor_user_id = $record->vendor_user_id;
            $record->user->partner_user_id = $record->partner_user_id;
            $vendor_user_logo_url = ! empty($record->vendor_s3_path) ?
                S3Helper::getS3ImageUrl($record->vendor_s3_path) : null;
            $partner_user_logo_url = ! empty($record->partner_s3_path) ?
                S3Helper::getS3ImageUrl($record->partner_s3_path) : null;
            $vendor_user_file_name = $record->vendor_file_name ?? null;
            $partner_user_file_name = $record->partner_file_name ?? null;
            $record->user->logo_url =
                $record->vendor_user_id !== null ? $vendor_user_logo_url : $partner_user_logo_url;
            $record->user->file_name =
                $record->vendor_user_id !== null ? $vendor_user_file_name : $partner_user_file_name;

            $result[$record->vendor_user_id.'_'.$record->partner_user_id] = $record->user;
            $result[$record->vendor_user_id.'_1'.$record->partner_user_id] = $record->user;
            $result[$record->vendor_user_id.'_2'.$record->partner_user_id] = $record->user;
        }

        return array_values($result);
    }

    public static function getMentions(?int $chat_post_id = null)
    {
        $mentions_grouped = self::fetchMentions();
        $mentions_paginated = self::paginateData($mentions_grouped, $chat_post_id);

        $thread_ids = [];
        foreach ($mentions_paginated as $parent_id => $child_ids) {
            $thread_ids[] = $parent_id;
        }

        $threads = [];
        foreach ($thread_ids as $thread_id) {
            // get thread data
            $thread = self::getPostData($thread_id);

            // get mention replies
            [
                'total_replies' => $total_replies,
                'mention_replies' => $mention_replies,
                'thread_users' => $thread_users,
            ] = self::getMentionReplies($thread_id);

            $thread->total_replies = $total_replies;
            $thread->replies = $mention_replies;
            $thread->thread_users = array_values($thread_users);

            if ($thread->attached_files->count() > 0) {
                $file_dict = self::getAttachedFileArrays($thread->attached_files);
                if (array_key_exists($thread->chat_post_id, $file_dict)) {
                    $thread->attached_files = $file_dict[$thread->chat_post_id];
                }
            }

            $threads[] = $thread;
        }

        return [
            'data' => $threads,
            'total_count' => count($mentions_grouped),
        ];
    }

    private static function fetchMentions()
    {
        $user = Auth::guard('vendor_users')->user();
        $user_id = $user->id;

        $mentions = DB::table('chat_posts')
            ->select('chat_posts.id', 'chat_posts.parent_id')
            ->join('channels', 'channels.id', 'chat_posts.channel_id')
            ->join('chat_mentions', 'chat_posts.id', 'chat_mentions.chat_post_id')
            ->leftJoin('chat_posts as parent_posts', 'chat_posts.parent_id', 'parent_posts.id')
            ->where('channels.name', '<>', 'ALL')   // ignore channel All Partner (channels.name = ALL)
            ->where('chat_posts.deleted', 0)
            ->where(function ($query) {
                $query->whereNull('parent_posts.deleted')
                    ->orWhere('parent_posts.deleted', 0);
            })
            ->where('chat_mentions.vendor_user_id', $user_id)
            ->orderBy('chat_posts.last_reply_at', 'desc')
            ->orderBy('chat_posts.id', 'desc')
            ->get();

        // Group data mentions
        $mentions_grouped = [];
        foreach ($mentions as $post) {
            if ($post->parent_id === null) {
                // is thread posts
                if (! isset($mentions_grouped[$post->id])) {
                    $mentions_grouped[$post->id] = [];
                }
            } else {
                // is reply posts
                if (! isset($mentions_grouped[$post->parent_id])) {
                    $mentions_grouped[$post->parent_id] = [];
                }
                $mentions_grouped[$post->parent_id][] = $post->id;
            }
        }

        return $mentions_grouped;
    }

    private static function paginateData($data, ?int $chat_post_id = null)
    {
        if ($chat_post_id) {
            $index = array_search($chat_post_id, array_keys($data));
            $filter_mentions = array_slice($data, $index + 1, count($data) - 1, true);

            return array_slice($filter_mentions, 0, self::PER_PAGE, true);
        }

        return array_slice($data, 0, self::PER_PAGE, true);
    }

    private static function getPostData($id)
    {
        $post = DB::table('chat_posts')
            ->leftJoin(
                'vendor_users',
                'chat_posts.vendor_user_id',
                'vendor_users.id'
            )
            ->leftJoin(
                'vendor_files',
                'vendor_users.file_id',
                'vendor_files.id'
            )
            ->leftJoin(
                'partner_users',
                'chat_posts.partner_user_id',
                'partner_users.id'
            )
            ->leftJoin(
                'partner_files',
                'partner_users.file_id',
                'partner_files.id'
            )
            ->leftJoin(
                'channels',
                'chat_posts.channel_id',
                'channels.id'
            )
            ->where('chat_posts.id', $id)
            ->select(
                'chat_posts.id as chat_post_id',
                'chat_posts.channel_id',
                'chat_posts.vendor_user_id',
                'chat_posts.partner_user_id',
                'vendor_users.name as vendor_user_name',
                'partner_users.name as partner_user_name',
                'chat_posts.content',
                'chat_posts.deleted',
                'chat_posts.parent_id',
                'chat_posts.created_at',
                'chat_posts.updated_at',
                'vendor_files.name AS vendor_file_name',
                'vendor_files.s3_path AS vendor_s3_path',
                'partner_files.name AS partner_file_name',
                'partner_files.s3_path AS partner_s3_path'
            )
            ->first();

        return self::restructurePostData($post);
    }

    private static function restructurePostData($post)
    {
        // restructure
        self::restructureUser($post);

        $post->timestamp = new stdClass;
        $post->timestamp->created_at = $post->created_at;
        $post->timestamp->updated_at = $post->updated_at;
        unset($post->created_at);
        unset($post->updated_at);

        $user = Auth::guard('vendor_users')->user();
        if ($post->user->vendor_user_id === $user->id) {
            $post->editable = true;
            $post->deletable = true;
        } else {
            $post->editable = false;
            $post->deletable = false;
        }

        $chat_count_unread = DB::table('chat_count_unread')
            ->where('vendor_user_id', $user->id)
            ->where('channel_id', $post->channel_id)
            ->first();

        if ($chat_count_unread != null) {
            $unread_mention_list_ids = json_decode($chat_count_unread->unread_mention_list_ids) ?? [];
            $post->unread = (int) in_array($post->chat_post_id, $unread_mention_list_ids);
        } else {
            $post->unread = (int) false;
        }

        $post->channel_name = self::getChannelName($post->channel_id, $user->vendor_id);

        return self::attachFilePostData($post);
    }

    private static function restructureUser(&$post, $is_unset_user_id = true)
    {
        $user_key = $post->vendor_user_id ?? $post->partner_user_id;
        $is_vendor_user = $post->vendor_user_name !== null;

        $post->user = new stdClass;
        $post->user->name = $is_vendor_user ? $post->vendor_user_name : $post->partner_user_name;

        $cache_key = $is_vendor_user ? "vendor_user_{$user_key}" : "partner_user_{$user_key}";

        if (isset(self::$avatar_cache[$cache_key])) {
            $post->user->logo_url = self::$avatar_cache[$cache_key]['logo_url'];
            $post->user->file_name = self::$avatar_cache[$cache_key]['file_name'];
        } else {
            if ($is_vendor_user) {
                $logo_url = ! empty($post->vendor_s3_path) ? S3Helper::getS3ImageUrl($post->vendor_s3_path) : null;
                $file_name = $post->vendor_file_name ?? null;
            } else {
                $logo_url = ! empty($post->partner_s3_path) ? S3Helper::getS3ImageUrl($post->partner_s3_path) : null;
                $file_name = $post->partner_file_name ?? null;
            }

            self::$avatar_cache[$cache_key] = [
                'logo_url' => $logo_url,
                'file_name' => $file_name,
            ];

            $post->user->logo_url = $logo_url;
            $post->user->file_name = $file_name;
        }

        //vendor_user_idとpartner_user_idがNULLの場合はnameを「削除されたユーザーです」に変更
        if (is_null($post->vendor_user_id) && is_null($post->partner_user_id)) {
            $post->user->name = __('message_names.deleted_user');
        }
        unset($post->vendor_user_name);
        unset($post->partner_user_name);
        $post->user->vendor_user_id = $post->vendor_user_id;
        $post->user->partner_user_id = $post->partner_user_id;

        if ($is_unset_user_id) {
            unset($post->vendor_user_id);
            unset($post->partner_user_id);
        }
    }

    private static function attachFilePostData($post)
    {
        $attached_files = DB::table('attached_files')
            ->join(
                'chat_posts',
                'attached_files.chat_post_id',
                'chat_posts.id'
            )
            ->where('chat_post_id', $post->chat_post_id)
            ->select(
                'chat_posts.id as chat_post_id',
                'attached_files.id as id',
                'attached_files.filesize as size',
                'attached_files.filename as name',
                'attached_files.s3_path as s3_path'
            )
            ->get();

        $post->attached_files = $attached_files;

        return $post;
    }

    private static function getMentionReplies($thread_id)
    {
        $user = Auth::guard('vendor_users')->user();

        $query = DB::table('chat_posts')
            ->leftJoin(
                'vendor_users',
                'chat_posts.vendor_user_id',
                'vendor_users.id'
            )
            ->leftJoin(
                'vendor_files',
                'vendor_users.file_id',
                'vendor_files.id'
            )
            ->leftJoin(
                'partner_users',
                'chat_posts.partner_user_id',
                'partner_users.id'
            )
            ->leftJoin(
                'partner_files',
                'partner_users.file_id',
                'partner_files.id'
            )
            ->leftJoin(
                'channels',
                'chat_posts.channel_id',
                'channels.id'
            )
            ->where('chat_posts.parent_id', $thread_id)
            ->where('deleted', 0)
            ->select(
                'chat_posts.id as chat_post_id',
                'chat_posts.channel_id',
                'chat_posts.vendor_user_id',
                'chat_posts.partner_user_id',
                'vendor_users.name as vendor_user_name',
                'partner_users.name as partner_user_name',
                'chat_posts.content',
                'chat_posts.deleted',
                'chat_posts.parent_id',
                'chat_posts.created_at',
                'chat_posts.updated_at',
                'vendor_files.name AS vendor_file_name',
                'vendor_files.s3_path AS vendor_s3_path',
                'partner_files.name AS partner_file_name',
                'partner_files.s3_path AS partner_s3_path'
            );

        $all_reply = $query
            ->orderBy('chat_posts.created_at')
            ->get()
            ->toArray();

        $total_replies = count($all_reply);
        $thread_users = self::getThreadUsers($all_reply);

        // get unread mentions
        $mention_replies = $query
            ->join('chat_mentions', 'chat_posts.id', 'chat_mentions.chat_post_id')
            ->where('chat_mentions.vendor_user_id', $user->id)
            ->where('chat_mentions.unread', 1)
            ->orderBy('chat_posts.created_at')
            ->get()
            ->toArray();

        // if unread mentions is empty, get 2 latest replies
        if (count($mention_replies) == 0) {
            $mention_replies = array_slice($all_reply, -2);
        }

        $processed_mention_replies = array_map(function ($reply) {
            $formatted_reply = self::restructurePostData($reply);
            if ($formatted_reply->attached_files->count() > 0) {
                $file_dict = self::getAttachedFileArrays($formatted_reply->attached_files);

                if (array_key_exists($reply->chat_post_id, $file_dict)) {
                    $reply->attached_files = $file_dict[$reply->chat_post_id];
                }
            }

            return $formatted_reply;
        }, $mention_replies);

        return [
            'total_replies' => $total_replies,
            'mention_replies' => $processed_mention_replies,
            'thread_users' => $thread_users,
        ];
    }

    private static function getThreadUsers($replies)
    {
        // cloning to avoid reference issues.
        $replies_clone = array_map(function ($reply) {
            return clone $reply;
        }, $replies);

        $thread_users = [];

        foreach ($replies_clone as $reply) {
            self::restructureUser($reply);
            $thread_users[$reply->user->vendor_user_id.'_'.$reply->user->partner_user_id] = $reply->user;
        }

        return $thread_users;
    }

    public static function readMessage($message_id, $type)
    {
        $user = Auth::guard('vendor_users')->user();

        DB::beginTransaction();
        try {
            $message = DB::table('chat_posts')
                ->where('id', $message_id)
                ->sole();

            $unread_mention_query = DB::table('chat_mentions')
                ->join('chat_posts', 'chat_posts.id', 'chat_mentions.chat_post_id')
                ->where('chat_mentions.vendor_user_id', $user->id)
                ->where(function ($q) use ($message_id) {
                    $q->where('chat_mentions.chat_post_id', $message_id)
                        ->orWhere('chat_posts.parent_id', $message_id);
                });

            $chat_count_unread = DB::table('chat_count_unread')
                ->where('channel_id', $message->channel_id)
                ->where('vendor_user_id', $user->id)
                ->first();

            if ($chat_count_unread != null) {
                if ($type == 'mention') {
                    $unread_mention_ids = (clone $unread_mention_query)->pluck('chat_posts.id')->toArray();
                    self::updateCountChatUnread($chat_count_unread, $unread_mention_ids);
                } else {
                    $reply_ids = DB::table('chat_posts')
                        ->where('parent_id', $message_id)
                        ->orWhere('id', $message_id)
                        ->pluck('id')
                        ->toArray();
                    self::updateCountChatUnread($chat_count_unread, $reply_ids);
                }
            }

            // update chat_mentions data
            $unread_mention_query
                ->where('chat_mentions.unread', 1)
                ->update([
                    'chat_mentions.unread' => 0,
                    'chat_mentions.updated_at' => date('Y-m-d H:i:s'),
                ]);

            DB::commit();

            return true;
        } catch (Exception $e) {
            DB::rollback();

            return false;
        }
    }

    public static function updateCountChatUnread($chat_count_unread, $list_unread_message_ids)
    {
        $unread_lists = [
            'unread_mention_list_ids' => json_decode($chat_count_unread->unread_mention_list_ids, true) ?? [],
            'unread_reply_list_ids' => json_decode($chat_count_unread->unread_reply_list_ids, true) ?? [],
            'unread_message_ids' => json_decode($chat_count_unread->unread_message_ids, true) ?? [],
            'unread_reply_ids' => json_decode($chat_count_unread->unread_reply_ids, true) ?? [],
        ];

        foreach ($unread_lists as $key => $unread_ids) {
            $unread_lists[$key] = array_values(array_diff($unread_ids, array_intersect($list_unread_message_ids, $unread_ids)));
        }

        $update_data = array_map(fn ($ids) => count($ids) > 0 ? json_encode($ids) : null, $unread_lists);
        DB::table('chat_count_unread')
            ->where('id', $chat_count_unread->id)
            ->update($update_data);
    }

    public static function updateCountChatUnreadByListPostId($chat_count_unread, $list_unread_message_ids)
    {
        // Update all unread  message counts based on $list_unread_message_ids in the chat_count_unread table.
        $unread_lists = [
            'unread_mention_list_ids' => json_decode($chat_count_unread->unread_mention_list_ids, true) ?? [],
            'unread_reply_list_ids' => json_decode($chat_count_unread->unread_reply_list_ids, true) ?? [],
            'unread_message_ids' => json_decode($chat_count_unread->unread_message_ids, true) ?? [],
            'unread_reply_ids' => json_decode($chat_count_unread->unread_reply_ids, true) ?? [],
        ];

        foreach ($unread_lists as $key => $unread_ids) {
            $unread_lists[$key] = array_values(array_diff($unread_ids, array_intersect($list_unread_message_ids, $unread_ids)));
        }
        $update_data = array_map(fn ($ids) => count($ids) > 0 ? json_encode($ids) : null, $unread_lists);
        DB::table('chat_count_unread')
            ->where('id', $chat_count_unread->id)
            ->update($update_data);
    }

    public static function checkUnread($params): bool
    {
        // Check if the threadsIds array exists in the unread messages array.
        $unreads = DB::table('chat_count_unread')
            ->where('channel_id', $params['id'])
            ->where('vendor_user_id', Auth::guard('vendor_users')->user()->id)
            ->first();
        if (empty($unreads)) {
            return false;
        }
        if (empty($unreads->unread_reply_list_ids)) {
            return false;
        }
        $listChatUnreads = json_decode($unreads->unread_reply_list_ids);
        $isUnread = false;
        foreach ($listChatUnreads as $id) {
            if (in_array($id, $params['threadsIds'])) {
                $isUnread = true;
                break;
            }
        }

        return $isUnread;
    }

    public static function readReply($params)
    {
        $user = Auth::guard('vendor_users')->user();

        if (! ChatService::isOwner($user, $params['channelId'])) {
            throw new Exception('Error: cannot access to the channel');
        }

        DB::beginTransaction();
        try {
            // Get the list of unread reply messages in the channel.
            $chat_count_unread = DB::table('chat_count_unread')
                ->where('channel_id', $params['channelId'])
                ->where('vendor_user_id', $user->id)
                ->first();
            $listReplyId = DB::table('chat_posts')
                ->orWhere('parent_id', $params['thread'])
                ->pluck('id')
                ->toArray();
            $chatPostReplyIds = $params['listReplyIds'];
            // If there are unread reply messages, update the chat_mentions and chat_count_unread tables.
            if (! empty($chatPostReplyIds)) {
                DB::table('chat_mentions')
                    ->join(
                        'chat_posts',
                        'chat_mentions.chat_post_id',
                        'chat_posts.id'
                    )
                    ->where('channel_id', $params['channelId'])
                    ->where('chat_mentions.vendor_user_id', $user->id)
                    ->whereNull('chat_posts.parent_id')
                    ->whereIn('chat_posts.id', $chatPostReplyIds)
                    ->update([
                        'chat_mentions.unread' => false,
                        'chat_mentions.updated_at' => date('Y-m-d H:i:s'),
                    ]);
                self::updateCountChatUnreadByListPostId($chat_count_unread, $chatPostReplyIds);
            }
            DB::commit();

            // Get unread reply message IDs, the unread reply count, and the latest unread message ID.
            $chat_count_unread = DB::table('chat_count_unread')
                ->where('channel_id', $params['channelId'])
                ->where('vendor_user_id', $user->id)
                ->first();
            $unreadReplyIds = empty($chat_count_unread->unread_reply_ids) ? [] : json_decode($chat_count_unread->unread_reply_ids);
            $listReplyUnreadId = array_values(array_intersect($unreadReplyIds, $listReplyId));

            return [
                'listReplyUnreadId' => $chat_count_unread->unread_reply_ids,
                'count' => empty($listReplyUnreadId) ? 0 : count($listReplyUnreadId),
                'lastPostIdUnread' => empty($listReplyUnreadId) ? null : array_slice($listReplyUnreadId, -1)[0],
                'requestReadId' => $params['listReplyIds'],
            ];

        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    private static function getCountUnread($channel_id, $user_id)
    {
        // Get the count of unread messages and the id of the most recent unread message in the channel by channel_id
        $chat_count_unread = DB::table('chat_count_unread')
            ->where('channel_id', $channel_id)
            ->where('vendor_user_id', $user_id)
            ->select('unread_reply_ids', 'unread_message_ids')
            ->first();
        $listParentIds = [];
        $listPostId = [];

        if ($chat_count_unread != null) {
            if (! empty(json_decode($chat_count_unread->unread_reply_ids))) {
                $listParentIds = DB::table('chat_posts')
                    ->where('channel_id', $channel_id)
                    ->whereIn('id', json_decode($chat_count_unread->unread_reply_ids))
                    ->pluck('parent_id')->unique()->toArray();
            }
            $unread_message_ids = json_decode($chat_count_unread->unread_message_ids);
            if (! empty($unread_message_ids)) {
                $listPostId = $unread_message_ids;
            }
            $lastPostIdUnread = array_unique(array_merge($listParentIds, $listPostId));
            sort($lastPostIdUnread);

            return [
                'count' => count($lastPostIdUnread),
                'lastPostIdUnread' => count($lastPostIdUnread) != 0 ? array_slice($lastPostIdUnread, -1)[0] : null,
            ];
        }
        DB::table('chat_count_unread')->insert([
            'channel_id' => $channel_id,
            'vendor_user_id' => $user_id,
        ]);

        return [
            'count' => 0,
            'lastPostIdUnread' => null,
        ];
    }
}
