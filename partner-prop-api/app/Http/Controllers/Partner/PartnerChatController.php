<?php

namespace App\Http\Controllers\Partner;

use App\Const\StatusConst;
use App\Enum\AuditAction;
use App\Enum\AuditDestTo;
use App\Enum\AuditResult;
use App\Enum\AuditTarget;
use App\Http\Controllers\Controller;
use App\Http\Requests\Partner\CheckReplyMessageRequest;
use App\Services\AuditLog\AuditLogServiceInterface;
use App\Services\AuditLog\Dto\AuditLogDto;
use App\Services\Partner\ChatService;
use App\Http\Requests\Partner\CreateMessageRequest;
use App\Http\Requests\Partner\UpdateMessageRequest;
use App\Http\Requests\Partner\AuthChannelRequest;
use App\Traits\GetClientIp;
use App\Exceptions\ServiceException;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class PartnerChatController extends Controller
{

    use GetClientIp;

    public function __construct(
        private readonly AuditLogServiceInterface $auditLogService
    )
    {}

    const PER_PAGE = 20;
    public function authChannel(AuthChannelRequest $request)
    {
        try {
            $is_mobile = isset($request['is_mobile']) ? (bool) $request['is_mobile'] : true;
            $token = ChatService::authChannel($request, $is_mobile);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($token);
    }

    public function channelList(int $vendor_linked_partner_id)
    {
        try {
            $channelList = ChatService::getChannels($vendor_linked_partner_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($channelList);
    }

    /**
     * Retrieve a channel along with its badge count, message count,
     * mention badge count, and reply badge count, unread mention badge count, information channel based on channel_id.
     *
     * @param Request $request The request containing 'channel_id' and int $vendor_linked_partner_id .
     * @return json The channel data in JSON format
     */
    public function getChannelById(int $vendor_linked_partner_id, Request $request)
    {
        try {
            $channel_id = $request['channel_id'];
            $channel = ChatService::getChannelById($vendor_linked_partner_id, $channel_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($channel);
    }

    public function messageList(int $channel_id, Request $request)
    {
        try {
            $chat_post_id = $request['chat_post_id'];
            $chat_post_end = $request['chat_post_end'];
            // Include the chat_post_end parameter with the message ID to fetch the latest unread message on the next page.
            $messageList = ChatService::getMessagesPerPage($channel_id, $chat_post_id, $chat_post_end);

        } catch (ServiceException $e) {
            $e->report();
            return $e->render();
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($messageList);
    }

    public function getThread(int $parent_id, Request $request)
    {
        try {
        $chat_post_id = $request['chat_post_id'];
        $chat_post_end = $request['chat_post_end'];
        $per_page = $request['per_page'] ?? self::PER_PAGE;
        $thread = ChatService::getThreadsPerPage($parent_id, $chat_post_id, $per_page, $chat_post_end);

        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($thread);
    }

    public function checkChannel(int $channel_id, Request $request)
    {
        try {
            # Add the `chatPostIds` request to get message IDs visible in the chat window.
            $total = ChatService::readMessagesInChannel($channel_id, $request->chatPostIds);
            return response()->json(['total_unread' => $total], Response::HTTP_OK  );
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Read all messages in the channel, including mentions, non-mentions, and replies.
     *
     * @param int $channel_id The ID of the channel to retrieve messages from.
     * @return json The result data in JSON format.
     */
    public function readAllMessagesChannel(int $channel_id)
    {
        try {
            ChatService::readAllMessagesInChannel($channel_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function createMessage(CreateMessageRequest $request, int $channel_id)
    {

        $login_user = Auth::guard('partner_users')->user();

        try {
            ChatService::createMessage($request, $channel_id);
            $this->auditLogService->write(
                new AuditLogDto(
                    $this->getClientIp(),
                    AuditTarget::Chat,
                    AuditAction::CreateMessage,
                    AuditResult::Success,
                    AuditDestTo::Partner,
                    $login_user->email,
                    $login_user->partner->partner_collaboration_id,
                )
            );
        } catch (Exception $e) {
            Log::error($e->getMessage());
            $this->auditLogService->write(
                new AuditLogDto(
                    $this->getClientIp(),
                    AuditTarget::Chat,
                    AuditAction::CreateMessage,
                    AuditResult::Failed,
                    AuditDestTo::Partner,
                    $login_user->email,
                    $login_user->partner->partner_collaboration_id,
                )
            );
            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function updateMessage(UpdateMessageRequest $request, int $post_id)
    {
        try {
            ChatService::updateMessage($request, $post_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function deleteMessage(int $post_id)
    {
        try {
            $check_result = ChatService::deleteMessage($post_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return response()->json($check_result);
    }

    public function getAttachedFile(int $attached_file_id)
    {
        try {
            $attached_file = ChatService::getAttachedFile($attached_file_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($attached_file);
    }

    public function downloadAttachedFile(int $attached_file_id)
    {

        $login_user = Auth::guard('partner_users')->user();

        try {
            $attached_file = ChatService::downloadAttachedFile($attached_file_id);
            $this->auditLogService->write(
                new AuditLogDto(
                    $this->getClientIp(),
                    AuditTarget::Chat,
                    AuditAction::FileDownload,
                    AuditResult::Success,
                    AuditDestTo::Partner,
                    $login_user->email,
                    $login_user->partner->partner_collaboration_id,
                )
            );
        } catch (Exception $e) {
            Log::error($e->getMessage());
            $this->auditLogService->write(
                new AuditLogDto(
                    $this->getClientIp(),
                    AuditTarget::Chat,
                    AuditAction::FileDownload,
                    AuditResult::Failed,
                    AuditDestTo::Partner,
                    $login_user->email,
                    $login_user->partner->partner_collaboration_id,
                )
            );
            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return response($attached_file['content'])
                ->header('Content-Type', $attached_file['mimeType'])
                ->header('Content-Disposition', 'attachment; filename="' . $attached_file['fileName'] . '"');
    }

    public function deleteAttachedFile(int $attached_file_id)
    {
        try {
            ChatService::deleteAttachedFile($attached_file_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function memberList(int $channel_id)
    {
        try {
            $memberList = ChatService::getMembers($channel_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($memberList);
    }

    public function mentionableList(int $channel_id)
    {
        try {
            $mentionableList = ChatService::getMentionables($channel_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($mentionableList);
    }

    public function getBadges(int $vendor_linked_partner_id)
    {
        try {
            $badges = ChatService::getBadges($vendor_linked_partner_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($badges);
    }

    public function replyList(Request $request)
    {
        try {
            $vendor_linked_partner_id = $request['vendor_linked_partner_id'];
            $chat_post_id = $request['chat_post_id'];
            $replyList = ChatService::getReplies($vendor_linked_partner_id, $chat_post_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($replyList);
    }

    public function mentionList(Request $request)
    {
        try {
            $vendor_linked_partner_id = $request['vendor_linked_partner_id'];
            $chat_post_id = $request['chat_post_id'];
            $mentionList = ChatService::getMentions($vendor_linked_partner_id, $chat_post_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($mentionList);
    }

    public function readMessage(Request $request, $message_id)
    {
        $type = $request->input('type');

        try {
            $result = ChatService::readMessage($message_id, $type);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($result);
    }

    /**
     * Check the read status of reply messages in the channel.
     *
     * @param CheckReplyMessageRequest $request The request containing necessary parameters.
     * @return json The result data in JSON format
     */
    public function checkReplyRead(CheckReplyMessageRequest $request)
    {
        $isUnread = ChatService::checkUnread($request->all());
        return response()->json($isUnread);
    }

    /**
     * Read reply messages in the channel.
     *
     * @param Request $request The request containing necessary parameters such as 'channel_id'.
     * @return json The result data in JSON format
     */
    public function readReply(Request $request)
    {
        $replyUnread = ChatService::readReply($request->all());
        return response()->json($replyUnread);
    }
}
