<?php

namespace App\Http\Controllers\Partner;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use App\Services\Partner\WebTestService;
use App\Exceptions\ServiceException;

class PartnerWebTestController extends Controller
{
    public function testList(int $vendor_linked_partner_id)
    {
        try {
            $tests = WebTestService::getTestList($vendor_linked_partner_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($tests);
    }

    public function testDetail(int $test_id)
    {
        try {
            $test = WebTestService::getTestDetail($test_id);
        } catch (ServiceException $e) {
            $e->report();
            return $e->render();
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($test);
    }

    public function questionList(int $test_id)
    {
        try {
            $questions = WebTestService::getQuestionList($test_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($questions);
    }

    public function attendTest(int $test_id)
    {
        try {
            $questionsAndAttendanceID = WebTestService::attendTest($test_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($questionsAndAttendanceID);

    }

    public function answerTest(Request $request, $test_id)
    {
        try {
            $feedbacks = WebTestService::answerTest($request, $test_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return response()->json($feedbacks);
    }
}
