<?php

namespace App\Http\Controllers\Vendor;

use App\Enum\AuditAction;
use App\Enum\AuditDestTo;
use App\Enum\AuditResult;
use App\Enum\AuditTarget;
use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\CheckReplyMessageRequest;
use App\Http\Requests\Vendor\CreateChannelRequest;
use App\Http\Requests\Vendor\CreateMessageRequest;
use App\Http\Requests\Vendor\UpdateMessageRequest;
use App\Http\Requests\Vendor\UpdatePermissionRequest;
use App\Http\Requests\Vendor\AuthChannelRequest;
use App\Services\AuditLog\AuditLogServiceInterface;
use App\Services\AuditLog\Dto\AuditLogDto;
use App\Services\Vendor\ChatService;
use App\Exceptions\ServiceException;
use App\Traits\GetClientIp;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Carbon\Carbon;

class VendorChatController extends Controller
{

    use GetClientIp;

    public function __construct(
        private readonly AuditLogServiceInterface $auditLogService
    )
    {}

    const PER_PAGE = 20;
    public function authChannel(AuthChannelRequest $request)
    {
        try {
            $is_mobile = isset($request['is_mobile']) ? (bool) $request['is_mobile'] : true;
            $token = ChatService::authChannel($request, $is_mobile);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($token);
    }

    public function channelList(Request $request)
    {
        try {
            $channelList = ChatService::getChannels($request);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($channelList);
    }

    /**
     * Retrieve a channel along with its badge count, message count,
     * mention badge count, and reply badge count, unread mention badge count, information channel  based on channel_id.
     *
     * @param Request $request The request containing 'channel_id'.
     * @return json The channel data in JSON format
     */
    public function getChannelById(Request $request)
    {
        try {
            $channel_id = $request['channel_id'];
            $channel = ChatService::getChannelById($channel_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($channel);
    }

    // internal function
    public function createChannel(CreateChannelRequest $request)
    {
        try {
            ChatService::createChannel($request);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function messageList(int $channel_id, Request $request)
    {
        try {
            $is_mobile = isset($request['is_mobile']) ? (bool) $request['is_mobile'] : true;

            if ($is_mobile) {
                $messageList = ChatService::getMessages($channel_id);
            } else {
                $chat_post_id = $request['chat_post_id'];
                $chat_post_end = $request['chat_post_end'];
                # Include the chat_post_end parameter with the message ID to fetch the latest unread message on the next page.
                $messageList = ChatService::getMessagesPerPage($channel_id, $chat_post_id,$chat_post_end);
            }
        } catch (ServiceException $e) {
            $e->report();
            return $e->render();
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($messageList);
    }

    public function getThread(int $parent_id, Request $request)
    {
        try {
            $is_mobile = isset($request['is_mobile']) ? (bool) $request['is_mobile'] : true;

            if ($is_mobile) {
                $thread = ChatService::getThread($parent_id);
            } else {
                $chat_post_id = $request['chat_post_id'];
                $chat_post_end = $request['chat_post_end'];
                $per_page = $request['per_page'] ?? self::PER_PAGE;
                $thread = ChatService::getThreadsPerPage($parent_id, $chat_post_id, $per_page,$chat_post_end);
            }

        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($thread);
    }

    public function checkChannel(int $channel_id, Request $request)
    {
        try {
          # Add the `chatPostIds` request to get message IDs visible in the chat window.
          $total = ChatService::readMessagesInChannel($channel_id, $request->chatPostIds);
            return response()->json(['total_unread' => $total], Response::HTTP_OK  );
        } catch(Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Read all messages in the channel, including mentions, non-mentions, and replies.
     *
     * @param int $channel_id The ID of the channel to retrieve messages from.
     * @return json The result data in JSON format.
     */
    public function readAllMessagesChannel(int $channel_id)
    {
        try {
            ChatService::readAllMessagesInChannel($channel_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function createMessage(CreateMessageRequest $request, int $channel_id)
    {

        $login_user = Auth::guard('vendor_users')->user();

        try {
            ChatService::createMessage($request, $channel_id);
            $this->auditLogService->write(
                new AuditLogDto(
                    $this->getClientIp(),
                    AuditTarget::Chat,
                    AuditAction::CreateMessage,
                    AuditResult::Success,
                    AuditDestTo::Vendor,
                    $login_user->email,
                    $login_user->vendor->vendor_collaboration_id,
                )
            );
        } catch(Exception $e) {
            Log::error($e->getMessage());
            $this->auditLogService->write(
                new AuditLogDto(
                    $this->getClientIp(),
                    AuditTarget::Chat,
                    AuditAction::CreateMessage,
                    AuditResult::Failed,
                    AuditDestTo::Vendor,
                    $login_user->email,
                    $login_user->vendor->vendor_collaboration_id,
                )
            );
            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function updateMessage(UpdateMessageRequest $request, int $post_id)
    {
        try {
            ChatService::updateMessage($request, $post_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function deleteMessage(int $post_id)
    {
        try {
            $check_result = ChatService::deleteMessage($post_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return response()->json($check_result);
    }

    public function getAttachedFile(int $attached_file_id)
    {
        try {
            $attachedFile = ChatService::getAttachedFile($attached_file_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($attachedFile);
    }

    public function downloadAttachedFile(int $attached_file_id)
    {
        try {
            $attachedFile = ChatService::downloadAttachedFile($attached_file_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response($attachedFile['content'])
                ->header('Content-Type', $attachedFile['mimeType'])
                ->header('Content-Disposition', 'attachment; filename="' . $attachedFile['fileName'] . '"');
    }

    public function deleteAttachedFile(int $attached_file_id)
    {
        try {
            ChatService::deleteAttachedFile($attached_file_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function replyList(Request $request)
    {
        try {
            $chat_post_id = $request['chat_post_id'];
            $replyList = ChatService::getReplies($chat_post_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($replyList);
    }

    // internal function
    public function updatePermissions(UpdatePermissionRequest $request, int $channel_id)
    {
        try {
            ChatService::updatePermissions($request, $channel_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function memberList(int $channel_id)
    {
        try {
            $memberList = ChatService::getMembers($channel_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($memberList);
    }

    public function mentionableList(int $channel_id)
    {
        try {
            $mentionableList = ChatService::getMentionables($channel_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($mentionableList);
    }

    public function badgeList()
    {
        try {
            $badgeList = ChatService::getBadges();
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($badgeList);
    }

    public function mentionList(Request $request)
    {
        try {
            $chat_post_id = $request['chat_post_id'];
            $mentionList = ChatService::getMentions($chat_post_id);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($mentionList);
    }

    public function readMessage(Request $request, $message_id)
    {
        $type = $request->input('type');

        try {
            $result = ChatService::readMessage($message_id, $type);
        } catch(Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($result);
    }
    /**
     * Check the read status of reply messages in the channel.
     *
     * @param CheckReplyMessageRequest $request The request containing necessary parameters.
     * @return json The result data in JSON format
     */
    public function checkReplyRead(CheckReplyMessageRequest $request)
    {
        $isUnread = ChatService::checkUnread($request->all());
        return response()->json($isUnread);
    }

    /**
     * Read reply messages in the channel.
     *
     * @param Request $request The request containing necessary parameters such as 'channel_id'.
     * @return json The result data in JSON format
     */
    public function readReply(Request $request)
    {
        $replyUnread = ChatService::readReply($request->all());
        return response()->json($replyUnread);
    }
    /* public function list() */
    /* { */
    /*     try { */
    /*         $vendorUserList = VendorUserService::getVendorUserList(); */
    /*     } catch(Exception $e) { */
    /*         Log::error($e->getMessage()); */

    /*         return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR); */
    /*     } */

    /*     return response()->json($vendorUserList); */
    /* } */

    /* public function create(CreateVendorUserRequest $request) */
    /* { */
    /*     try { */
    /*         VendorUserService::createVendorUser($request); */
    /*     } catch(Exception $e) { */
    /*         Log::error($e->getMessage()); */

    /*         return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR); */
    /*     } */
    /* } */

    /* public function detail(int $vendor_user_id) */
    /* { */
    /*     try { */
    /*         $vendor_user = VendorUserService::getVendorUser($vendor_user_id); */
    /*     } catch(Exception $e) { */
    /*         Log::error($e->getMessage()); */

    /*         return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR); */
    /*     } */

    /*     return response()->json($vendor_user); */
    /* } */

    /* public function update(UpdateVendorUserRequest $request, int $vendor_user_id) */
    /* { */
    /*     try { */
    /*         VendorUserService::updateVendorUser($request, $vendor_user_id); */
    /*     } catch(Exception $e) { */
    /*         Log::error($e->getMessage()); */

    /*         return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR); */
    /*     } */
    /* } */

    /* public function delete(int $vendor_user_id) */
    /* { */
    /*     try { */
    /*         VendorUserService::deleteVendorUser($vendor_user_id); */
    /*     } catch(Exception $e) { */
    /*         Log::error($e->getMessage()); */

    /*         return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR); */
    /*     } */
    /* } */

    /* public static function resetPassword(Request $request, int $vendor_user_id) */
    /* { */
    /*     try { */
    /*         VendorUserService::resetVendorUserPassword($request->password, $vendor_user_id); */
    /*     } catch(Exception $e) { */
    /*         Log::error($e->getMessage()); */

    /*         return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR); */
    /*     } */
    /* } */

    /* //ログインユーザーの詳細情報を取得 */
    /* public function getLoginUserDetail() */
    /* { */
    /*     try { */
    /*         $vendor_user = VendorUserService::getLoginUserDetail(); */
    /*     } catch(Exception $e) { */
    /*         Log::error($e->getMessage()); */

    /*         return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR); */
    /*     } */

    /*     return response()->json($vendor_user); */
    /* } */

    /* //ログインユーザーの詳細情報を更新 */
    /* public function updateLoginUserDetail(Request $request) */
    /* { */
    /*     try { */
    /*         VendorUserService::updateLoginUserDetail($request); */
    /*     } catch(Exception $e) { */
    /*         Log::error($e->getMessage()); */

    /*         return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR); */
    /*     } */
    /* } */

    /* public function searchVendorUser(Request $request) */
    /* { */
    /*     $validated_data = $request->validate([ */
    /*         'name' => 'required|max:255', */
    /*     ]); */

    /*     //$validated_dataがnullの場合はバリデーションエラー */
    /*     if (is_null($validated_data)) { */
    /*         return response()->json(Response::HTTP_BAD_REQUEST); */
    /*     } */

    /*     try { */
    /*         $vendor_user_list = VendorUserService::searchVendorUser($request); */
    /*     } catch(Exception $e) { */
    /*         Log::error($e->getMessage()); */

    /*         return response()->json(['message' => 'Internal Server Error.'], Response::HTTP_INTERNAL_SERVER_ERROR); */
    /*     } */

    /*     return response()->json($vendor_user_list); */
    /* } */
}
