<?php

namespace App\Http\Controllers\Vendor;

use App\Exceptions\ExceedFilterSizeLimitException;
use App\Exceptions\FilterOperationFailureException;
use App\Exports\Vendor\WebTestExport;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use App\Services\FilteredListService;
use App\Services\Vendor\WebTestService;
use Maatwebsite\Excel\Facades\Excel;
use App\Exceptions\ServiceException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class VendorWebTestController extends Controller
{
    public function filteredTestList(Request $request): JsonResponse
    {
        try {
            $filter_service = FilteredListService::make($request, default_sorts: [['sort_by' => 'lead_id', 'order_by' => 'ASC']]);
            $result = WebTestService::getFilteredTestList($filter_service);

            return response()->json([
                'total_count' => $result['total'],
                'current_page' => $filter_service->page(),
                'total_pages' => ceil($result['total'] / $filter_service->pageSize()),
                'data' => $result['webtest_list'],
            ]);
        } catch (ExceedFilterSizeLimitException $e) {
            Log::debug($e->getMessage());

            return response()->json([
                'label' => 'ERROR:EXCEED_FILTER_SIZE_LIMIT',
                'reason' => 'The number of search results exceeds the limit. Please narrow down your search criteria.',
            ], Response::HTTP_BAD_REQUEST);
        } catch (FilterOperationFailureException|ValidationException $e) {
            Log::debug($e->getMessage());

            return response()->json([
                'label' => 'ERROR:INVALID_FILTER_REQUEST',
                'reason' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        } catch (Exception $e) {
            Log::error($e);

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function createTest(Request $request)
    {
        try {
            $newTest = WebTestService::createTest($request);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($newTest);
    }

    public function updateTest(Request $request, int $test_id)
    {
        try {
            WebTestService::updateTest($request, $test_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function testDetail(int $test_id)
    {
        try {
            $test = WebTestService::getTestDetail($test_id);
        } catch (ServiceException $e) {
            $e->report();
            return $e->render();
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($test);
    }

    public function deleteTest(int $test_id)
    {
        try {
            WebTestService::deleteTest($test_id);
        } catch (ServiceException $e) {
            return $e->render();
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function answerList(int $test_id)
    {
        try {
            $answers = WebTestService::getAnswerList($test_id);
        } catch (ServiceException $e) {
            $e->report();
            return $e->render();
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($answers);
    }

    public function questionList(int $test_id)
    {
        try {
            $questions = WebTestService::getQuestionList($test_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($questions);
    }

    public function createQuestion(Request $request, int $test_id)
    {
        try {
            $newQuestion = WebTestService::createQuestion($request, $test_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($newQuestion);
    }

    public function updateQuestion(Request $request, int $question_id)
    {
        try {
            WebTestService::updateQuestion($request, $question_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function questionDetail(int $question_id)
    {
        try {
            $question = WebTestService::getQuestionDetail($question_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($question);
    }

    public function bulkDeleteQuestion(Request $request, int $question_id)
    {
        try {
            WebTestService::bulkDeleteQuestion($request, $question_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function updateQuestionPointsOrderAndActive(Request $request, int $test_id)
    {
        try {
            WebTestService::updateQuestionPointsOrderAndActive($request, $test_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function optionList(int $question_id)
    {
        try {
            $options = WebTestService::getOptionList($question_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($options);
    }

    public function createOption(Request $request, int $question_id)
    {
        try {
            $newOption = WebTestService::createOption($request, $question_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($newOption);
    }

    public function bulkEditOptions(Request $request, int $question_id)
    {
        try {
            WebTestService::bulkEditOptions($request, $question_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function bulkDeleteOptions(Request $request, int $question_id)
    {
        try {
            WebTestService::bulkDeleteOptions($request, $question_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function updatePublishStatus(Request $request, int $test_id)
    {
        try {
            WebTestService::updatePublishStatus($request, $test_id);
        } catch (ServiceException $e) {
            return $e->render();
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getSharePartners(int $test_id)
    {
        try {
            $partners = WebTestService::getSharePartners($test_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($partners);
    }

    public function shareTest(Request $request, int $test_id)
    {
        try {
            WebTestService::shareTest($request, $test_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function unShareTest(int $test_id, int $partner_id)
    {
        // この引数$partner_idはvendor_linked_partner_id
        try {
            WebTestService::unShareTest($test_id, $partner_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    public function getTestListByAttend()
    {
        try {
            $tests = WebTestService::getTestListByAttend();
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($tests);
    }

    public function attendTest(int $test_id)
    {
        try {
            $questionsAndAttendanceID = WebTestService::attendTest($test_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        return response()->json($questionsAndAttendanceID);
    }

    public function answerTest(Request $request, $test_id)
    {
        try {
            $feedbacks = WebTestService::answerTest($request, $test_id);
        } catch (Exception $e) {
            Log::error($e->getMessage());

            return response()->json("Internal Server Error", Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        return response()->json($feedbacks);
    }

    public function answerListCsvExport()
    {
        return Excel::download(new WebTestExport(), 'web_test_list.xlsx');
    }
}
