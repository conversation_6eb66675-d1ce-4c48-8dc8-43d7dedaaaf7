<?php

namespace App\Http\Resources\Partner\Contact;

use App\Models\ContactForm;
use App\Traits\ContactBucketTrait;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ContactFormListResource extends ResourceCollection
{
    use ContactBucketTrait;

    public static $wrap = 'contact_forms';

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        /**
         * @var Collection<ContactForm> $this
         */

        return $this->collection->map(function (ContactForm $contact_form) {
            return [
                'uuid' => $contact_form->uuid,
                'title' => $contact_form->title,
                'description' => $contact_form->is_description_visible ? $contact_form->description : null,
                'logo_path' => $this->getPresignedUrl($contact_form->logo_path),
                'thumbnail_path' => $this->getPresignedUrl($contact_form->thumbnail_path),
            ];
        });
    }
}
