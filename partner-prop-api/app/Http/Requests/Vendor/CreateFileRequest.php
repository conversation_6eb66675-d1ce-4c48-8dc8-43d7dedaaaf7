<?php

namespace App\Http\Requests\Vendor;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class CreateFileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return auth()->check(); // ログインしているかどうかのチェック
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'type' => ['required', 'string', 'regex:/^(normal|directory)$/'],
            'name' => 'nullable|string|max:255',
            'file' => 'nullable|file|max:500000',
            'portal_id' => 'required|integer|exists:portals,id',
            'thumbnail' => 'nullable|file|image|mimes:jpeg,png,jpg|max:3072',
        ];

        if (request('type') === 'normal' && !request()->has('is_document') ) {
            $rules['file'] = 'required|file|max:500000';
        }

        if (request('type') === 'normal' && request()->has('is_document') ) {
            $rules['content'] = 'required|string';
        }

        return $rules;
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     */
    public function withValidator(Validator $validator)
    {
        // ルートパラメータ 'file_id' を取得します。'0' は最上位階層を示し、null に対応します。
        $parent_file_id = $this->route('file_id') === '0' ? null : $this->route('file_id');

        $this->validatePortal($validator, request('portal_id'));

        switch (request('type')) {
            case 'normal':
                if (!request()->has('is_document')) {
                    $this->validateNormalFile($validator, $parent_file_id);
                }
                break;
            case 'directory':
                $this->validateDirectory($validator, $parent_file_id);
                break;
            default:
                break;
        }
    }

    /**
     * ポータルのバリデーション
     * ベンダーに紐づいたか確認する
     *
     * @param  \Illuminate\Validation\Validator  $validator
     */
    private function validatePortal(Validator $validator, ?int $portal_id)
    {
        $has_access_to_portal = DB::table('portals')
            ->where('id', $portal_id)
            ->where('vendor_id', auth()->user()->vendor_id)
            ->exists();
        if (! $has_access_to_portal) {
            $validator->errors()->add('portal_id', 'Error: cannot access to the portal');
        }
    }

    /**
     * ファイルのバリデーション
     * files.id, vendor_id, portal_idが一致するか確認
     * 名前が重複していないか確認
     *
     * @param  \Illuminate\Validation\Validator  $validator
     */
    private function validateNormalFile(Validator $validator, ?int $parent_file_id)
    {
        if ($validator->errors()->has('file')) {
            return;
        }
        $file_name = request()->file('file')->getClientOriginalName();
        $validator->after(function ($validator) use ($parent_file_id, $file_name) {
            //同じ階層に同じファイル名または同じフォルダ名が存在するかどうかのチェック
            if ($parent_file_id === null) {
                // file_idがnullの場合は一番上の階層なので、vendor_id, name, portal_idが一致するファイルを探す
                $same_name_file = DB::table('files')
                    ->where('vendor_id', auth()->user()->vendor_id)
                    ->where('parent', null) // 明示的にnullを指定
                    ->where('portal_id', request('portal_id'))
                    ->where('name', $file_name)
                    ->first();
            } else {
                // file_idがnullでない場合は特定のparentでファイルを探す
                $same_name_file = DB::table('files')
                    ->where('vendor_id', auth()->user()->vendor_id)
                    ->where('parent', $parent_file_id)
                    ->where('portal_id', request('portal_id'))
                    ->where('name', $file_name)
                    ->first();
            }

            if (isset($same_name_file)) {
                $validator->errors()->add('file', 'Error: The file or directory name already exists.');
            }
        });
    }

    /**
     * ディレクトリのバリデーション
     * files.id, vendor_id, portal_idが一致するか確認
     * 名前が重複していないか確認
     *
     * @param  \Illuminate\Validation\Validator  $validator
     */
    private function validateDirectory(Validator $validator, ?int $parent_file_id)
    {
        //同じ階層に同じファイル名または同じフォルダ名が存在するかどうかのチェック
        $validator->after(function ($validator) use ($parent_file_id) {
            if ($parent_file_id === null) {
                // file_idがnullの場合は一番上の階層なので、vendor_id, name, portal_idが一致するファイルを探す
                $same_name_file = DB::table('files')
                    ->where('vendor_id', auth()->user()->vendor_id)
                    ->where('parent', null) // 明示的にnullを指定
                    ->where('portal_id', request('portal_id'))
                    ->where('name', request('name'))
                    ->first();
            } else {
                // file_idがnullでない場合は特定のparentでファイルを探す
                $same_name_file = DB::table('files')
                    ->where('vendor_id', auth()->user()->vendor_id)
                    ->where('parent', $parent_file_id)
                    ->where('portal_id', request('portal_id'))
                    ->where('name', request('name'))
                    ->first();
            }

            if (isset($same_name_file)) {
                $validator->errors()->add('name', 'Error: The file or directory name already exists.');
            }
        });
    }
}
