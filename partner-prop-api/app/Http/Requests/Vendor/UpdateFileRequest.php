<?php

namespace App\Http\Requests\Vendor;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateFileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return auth()->check(); // ログインしているかどうかのチェック
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'name' => 'required|string|max:255',
        ];

        if (request('type') === 'normal' && request()->has('is_document') ) {
            $rules['content'] = 'required|string';
        }

        return $rules;
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     */
    public function withValidator(Validator $validator)
    {
        //$parent_file_idを取得して、nullの場合とそれ以外の場合で処理を分ける
        try {
            $current_file = DB::table('files')
                ->where('vendor_id', auth()->user()->vendor_id)
                ->where('id', $this->route('file_id'))
                ->first() ?? null;
        } catch (\Exception $e) {
            Log::error($e->getMessage());

            return response()->json(['message' => 'Internal Server Error'], 500);
        }

        //nullの場合は、最上位階層のファイルとして扱うのでポータルIDで検索する
        if ($current_file->parent === null) {
            $same_name_file = DB::table('files')
                ->where('vendor_id', auth()->user()->vendor_id)
                ->where('id', '!=', $current_file->id) // 現在のファイルを除く
                ->where('portal_id', $current_file->portal_id)
                ->where('name', $this->name)
                ->where('parent', null)
                ->first();
        } else {
            $same_name_file = DB::table('files')
                ->where('vendor_id', auth()->user()->vendor_id)
                ->where('id', '!=', $current_file->id) // 現在のファイルを除く
                ->where('name', $this->name)
                ->where('parent', $current_file->parent)
                ->first();
        }
        $validator->after(function ($validator) use ($same_name_file) {
            if (isset($same_name_file)) {
                $validator->errors()->add('file', 'Error: The file or directory name already exists.');
            }
        });
    }
}
