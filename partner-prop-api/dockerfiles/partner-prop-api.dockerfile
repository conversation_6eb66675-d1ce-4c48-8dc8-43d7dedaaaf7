FROM php:8.3-fpm-bookworm

LABEL maintainer="PartnerProp inc."

WORKDIR /var/www/html
RUN php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');" && \
    php composer-setup.php && \
    php -r "unlink('composer-setup.php');" && \
    mv composer.phar /usr/local/bin/composer

RUN apt update \
    && apt install -y libonig-dev libxml2-dev zlib1g-dev libpng-dev libzip-dev autoconf libbz2-dev libxslt-dev \
    && apt clean \
    && rm -rf /var/lib/apt/lists/*

# Memo:
# 一緒にやるとconfigureまわりでこける謎の組み合わせがあったので、それの回避
RUN docker-php-ext-install mbstring dom gd zip \
    && docker-php-ext-install pdo \
    && docker-php-ext-install pdo_mysql mysqli \
    && docker-php-ext-install bz2 calendar exif gettext pcntl sockets xsl

COPY . /var/www/html

RUN chown -R www-data:www-data /var/www/html \
    && rm -rf /var/www/html/deploy

USER www-data
# composer updateはlockファイルを更新するので install を使う. ビルドなので開発用パッケージはインストールしない
RUN composer install --no-dev --no-cache --working-dir /var/www/html
