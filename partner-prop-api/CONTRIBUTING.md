# CONTRIBUTING.md

## 概要
本規約は、PHP Laravelで構築されるWebアプリケーションのバックエンド開発において、チーム全体が統一された品質でコードを作成するためのガイドラインです。

**原文**: [Notionページ - [BE]コード規約](https://www.notion.so/BE-22414b5f1eae80b2b12fd293fb9a1671)

## コーディング規約

### PSR-12準拠
PSR-12に準拠したコーディングを必ず行うこと。

### 命名規則
以下の命名規則を厳守すること：

| 対象 | 規則 | 例 |
|------|------|-----|
| 変数名 | スネークケース | `$user_name` |
| メソッド名 | キャメルケース | `getUserName()` |
| クラス名 | パスカルケース | `UserController` |
| テーブル名 | 複数形のスネークケース | `user_profiles` |
| ディレクトリ名 | パスカルケース | `Controllers` |
| ファイル名（クラス） | パスカルケース | `UserController.php` |
| ファイル名（その他） | スネークケース | `database.php` |

### ディレクトリ名規則

#### 判断基準
- **複数形**: 同種のクラスが複数格納される「クラス群の集合」
- **単数形**: 特定の機能領域を表現する「概念的なまとまり」

#### 複数形パスカルケース（クラス群）
- `Components/` - コンポーネントクラス群
- `Events/` - イベントクラス群
- `Exceptions/` - 例外クラス群
- `Exports/` - エクスポートクラス群
- `Helpers/` - ヘルパークラス群
- `Imports/` - インポートクラス群
- `Jobs/` - ジョブクラス群
- `Models/` - モデルクラス群
- `Modules/` - モジュールクラス群
- `Notifications/` - 通知クラス群
- `Observers/` - オブザーバークラス群
- `Policies/` - ポリシークラス群
- `Providers/` - プロバイダクラス群
- `Repositories/` - リポジトリクラス群
- `Services/` - サービスクラス群
- `Traits/` - トレイトクラス群
- `Constants/` - 定数クラス群
- `Enums/` - 列挙型クラス群

#### 複数形パスカルケース（Laravel HTTP関連）
- `Controllers/` - コントローラークラス群
- `Requests/` - リクエストクラス群
- `Resources/` - リソースクラス群
- `Middleware/` - ミドルウェアクラス群

#### 単数形パスカルケース（機能領域）
- `Console/` - コンソール機能領域
- `Http/` - HTTP機能領域
- `Mail/` - メール機能領域

### エンドポイント規約
Google における URL 構造のベスト プラクティスに準拠する。

### モデル自動生成ツール
モデル名・モデルメソッド名はモデル自動生成ツールの規則に従うこと。

## 開発ツール

### コード品質ツール

| ツール | 用途 | 状態 |
|--------|------|------|
| Pint | コード整形 | 導入済 |
| larastan | 型チェック | デファクトスタンダード |
| PHP_CodeSniffer | 静的解析 | デファクトスタンダード |

### デバッグツール

#### Laravel Telescope
- アクセス：dev-api_serve 環境で http://localhost/telescope
- パフォーマンス監視、クエリ分析、リクエスト追跡に活用

#### N+1クエリ対策
- Laravel純正のN+1検出機能を活用してパフォーマンスを最適化すること

## コードレビュー観点

### 必須チェック項目
- [ ] PSR-12準拠
- [ ] 命名規則の遵守
- [ ] エンドポイント規約の準拠
- [ ] N+1クエリの有無
- [ ] 適切なエラーハンドリング
- [ ] セキュリティ考慮（SQLインジェクション、XSS等）
- [ ] テストコードの品質

### パフォーマンス観点
- [ ] クエリの最適化
- [ ] キャッシュの適切な利用
- [ ] 不要なループ処理の除去
- [ ] メモリ使用量の最適化

### 保守性観点
- [ ] コードの可読性
- [ ] 適切なコメント
- [ ] DRY原則の遵守
- [ ] SOLID原則の考慮