<?php

return [
    'lead_table_export_name' => 'Lead management list',
    'lead_table_export_headers' => [
        'vendor' => [
            'Record ID',
            'Creation Date(JST)',
            'Customer Name',
            'Shared Partner',
            'Partner Collaboration ID',
            'Partner Contact Name',
            'Partner Contact ID',
            'Case Status',
            'Postal Code',
            'Address',
            'Phone Number',
            'URL',
            'Memo',
            'Number of Employees',
        ],
        'partner' => [
            'Record ID',
            'Creation Date(JST)',
            'Customer Name',
            'Shared Vendor',
            'Shared Partner',
            'Partner Collaboration ID',
            'Partner Contact Name',
            'Partner Contact ID',
            'Case Status',
            'Postal Code',
            'Address',
            'Phone Number',
            'URL',
            'Memo',
            'Number of Employees',
        ],

    ],
    'opportunity_status_header' => 'Opportunity Status',
    'approval_status_header' => 'Approval Status',
    'sub_table_export_headers' => ['Record ID', 'No'],
    'drive_access_log_export_name' => 'Viewing  Log',
    'drive_access_log_export_headers' => [
        'Viewed Date(JST)', //file_access_logs.updated_at
        'File ID', //files.id
        'File Name', //files.name
        'Partner Company ID', //partners.partner_collaboration_id
        'Partner Company Name', //partners.name
        'Partner User ID', //partner_users.partner_user_id
        'User Name' //partner_users.name
    ],
    'drive_download_log_export_name' => 'Download Log',
    'drive_download_log_export_headers' => [
        'Download date(JST)', //file_download_logs.updated_at
        'File ID', //files.id
        'File Name', //files.name
        'Partner Company ID', //partners.partner_collaboration_id
        'Partner Company Name', //partners.name
        'Partner User ID', //partner_users.partner_user_id
        'User Name' //partner_users.name
    ],
    'managed_partner_collaborated_table_export_name' => 'List of Collaborative Users',
    'managed_partner_collaborated_table_export_headers' => [
        'Partner Company ID',
        'Partner Company Name',
        'Name',
        'Email Address',
        'Phone Number',
        'Role',
        'Division',
        'Division (Detail)',
        'Position',
    ],
    'managed_partner_table_export_name' => 'Partner Management List',
    'managed_partner_table_export_headers' => [
        'Record ID',
        'Created Date(JST)',
        'Partner Name',
        'Cooperation ID',
        'Vendor Contact Name',
        'Vendor Contact User ID',
        'Cooperation Status',
        'Contract Status',
        'Postal Code',
        'Address',
        'Phone Number',
        'URL',
        'Memo',
        'Number Of Employees',
    ],
    'gift_history_table_export_name' => 'Gift Sending History List',
    'gift_history_table_export_headers' => [
        'Partner ID',
        'Partner User ID',
        'Partner User Name',
        'Subject',
        'Description',
        'Sent Date(JST)',
    ],
    'web_test_answer_partner_detail_name' => 'Test Answer Details - Partner',
    'web_test_answer_partner_detail_export' => [
        'Test ID',
        'Test Title',
        "Question ID",
        "Question Text",
        "Question Format",
        "Question Correct Answer",
        "Questions And Answers",
        "True Or False",
        'Partner User Email Address',
        'Partner User Name',
        'Partner ID',
        'Partner Company Name',
        'Point Allocation',
    ],
    'correct' => 'Correct',
    'incorrect' => 'Incorrect',
    'web_test_answer_partner_table_export_name' => 'Test Answer List - Partner',
    'web_test_answer_partner_table_export_headers' => [
        'Test ID',
        'Test Title',
        'Partner ID',
        'Partner Company Name',
        'Partner Collaboration ID',
        'Partner User Email Address',
        'Partner User Name',
        'Latest Course Date(JST)',
        'Passing Score',
        'Latest Score',
        'Latest Pass/Fail Status',
        'Number Of Attendance',
        'Test Time',
    ],
    'web_test_answer_vendor_detail_export_name' => 'Test Answer Details - Vendor',
    'web_test_answer_vendor_detail_export_headers' => [
        'Test ID',
        'Test Title',
        "Question ID",
        "Question Text",
        "Question Format",
        "Question Correct Answer",
        "Questions And Answers",
        "True Or False",
        'Vendor User Email Address',
        'Vendor Username',
        'Vendor ID',
        'Vendor Company Name',
        'Point Allocation',
    ],
    'web_test_answer_vendor_table_export_name' => 'Test Answer List - Vendor',
    'web_test_answer_vendor_table_export_headers' => [
        'Test ID',
        'Test Title',
        'Vendor ID',
        'Vendor Company Name',
        'Vendor User Email Address',
        'Vendor User Name',
        'Latest Course Date(JST)',
        'Passing Score',
        'Latest Score',
        'Latest Pass/Fail Status',
        'Number Of Attendance',
        'Test Time',
    ],
    'passed' => "Passed",
    'failure' => "Failure",
    'contact_ticket_export_headers' => [
        'Answer Ticket ID',
        'Form Name',
        'Partner Company Name',
        'Partner Company ID',
        'Form Sender Email Address',
        'Form Submission Date',
        'Comment',
    ],
];
