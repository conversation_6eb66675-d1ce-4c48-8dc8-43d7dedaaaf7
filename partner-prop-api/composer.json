{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.3.0", "awobaz/compoships": "^2.4", "aws/aws-sdk-php": "^3.279", "doctrine/dbal": "^3.6", "ezyang/htmlpurifier": "^4.17", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.7", "league/flysystem-aws-s3-v3": "^3.15", "maatwebsite/excel": "^3.1", "mews/purifier": "*", "phpnexus/cwh": "^3.1", "pusher/pusher-php-server": "^7.2", "sentry/sentry-laravel": "^4.0", "tymon/jwt-auth": "^2.1"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.3", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "nunomaduro/larastan": "^3.0", "phpstan/phpstan": "^2.0", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/GlobalHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}