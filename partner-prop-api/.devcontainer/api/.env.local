# === App meta
TZ=Asia/Tokyo
APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:84BxjBTty6lBwYzjCcwBxF+vNOouTPVyDQrym4b9rjk=
APP_DEBUG=true
APP_URL=http://partner-prop.com
APP_DOMAIN=local.partner-prop.com
SANCTUM_STATEFUL_DOMAINS=localhost,localhost:3000,vendor.local.partner-prop.com:6200,partner.local.partner-prop.com:6100,vendor.local.partner-prop.com,partner.local.partner-prop.com

# === Partner Prop
VENDOR_FRONT_DOMAIN=http://vendor.local.partner-prop.com:6200
PARTNER_FRONT_DOMAIN=http://partner.local.partner-prop.com:6100
LEAD_SUB_TABLE_BUCKET_DIR=lead_sub_table
# === Partner Prop - S3
AWS_BUCKET=drive-files
AWS_BUCKET_MANAGED_PARTNER_CUSTOM_COLUMN=managed-partner-custom-column-files
AWS_BUCKET_MANAGED_PARTNER_SUB_TABLE=managed-partner-sub-table-files
AWS_BUCKET_LEAD_CUSTOM_COLUMN=lead-custom-column-files
AWS_BUCKET_LEAD_SUB_TABLE=lead-sub-table-files
AWS_BUCKET_CHAT=p48-proptech-dev
AWS_BUCKET_AUDIT=audit-files
AWS_BUCKET_STG_AREA=stg-area-files
AWS_BUCKET_STG_AREA_ASYNC_IMPORT_DIR=async-import
# === Partner Prop - AWS SNS
AWS_SNS_VERSION=latest
AWS_SNS_TOPIC_ARN_ASYNC_IMPORT=arn:aws:sns:ap-northeast-1:000000000000:async-import-topic.fifo
AWS_SNS_MESSAGE_GROUP_ID_ASYNC_IMPORT=async-import-group

# === AWS
AWS_USE_PATH_STYLE_ENDPOINT=true
AWS_BUCKET=p48-proptech-dev
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=ct4aIJZuUJ/gWh9ls7vQwVdgBVqzBhrjDb5fOTzQ
AWS_DEFAULT_REGION=ap-northeast-1

# === Session
SESSION_DOMAIN=".local.partner-prop.com"
SESSION_DRIVER=database
SESSION_LIFETIME_PARTNER=30240
SESSION_LIFETIME_VENDOR=30240
# TODO: Secure flag must be true in production
SESSION_SECURE_COOKIE=false

# === Logging - Cloudwatch
LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# === Database - MySQL
DB_CONNECTION=mysql
DB_HOST=partner-prop-db
DB_PORT=3306
DB_DATABASE=partner-prop
DB_USERNAME=username
DB_PASSWORD=userpass

# === Cache
CACHE_DRIVER=file
MEMCACHED_HOST=127.0.0.1
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# === File system
FILESYSTEM_DISK=local

# === Queue
QUEUE_CONNECTION=database
QUEUE_DRIVER=database

# === Mailer
MAIL_MAILER=smtp
# MAIL_HOST=mailpit
# MAIL_PORT=1025
# MAIL_USERNAME=null
# MAIL_PASSWORD=null
MAIL_HOST=sandbox.smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=90e1b01249b9b1
MAIL_PASSWORD=752f0007abeca0
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# === Broadcast
# BROADCAST_DRIVER=pusher
# PUSHER_APP_ID=*******
# PUSHER_APP_KEY=c6bb95a0666b5913d3e8
# PUSHER_APP_SECRET=8ec038c34158d9bf735b
# PUSHER_HOST=
# PUSHER_PORT=443
# PUSHER_SCHEME=https
# PUSHER_APP_CLUSTER=ap3

BROADCAST_DRIVER=pusher
PUSHER_APP_ID=*******
PUSHER_APP_KEY=4663422f54958aa2b865
PUSHER_APP_SECRET=ebacbc11d54e7e7bae48
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=ap3

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

VENDOR_USER_S3_PATH=vendor
VENDOR_S3_PATH=vendor
PARTNER_USER_S3_PATH=partner
PARTNER_S3_PATH=partner


<<<<<<< Updated upstream
# === QUICKSIGHT
AWS_ACCOUNT_ID=************
AWS_QUICKSIGHT_SESSION_LIFETIME=30

# === Sentry
SENTRY_LARAVEL_DSN=
=======
>>>>>>> Stashed changes
