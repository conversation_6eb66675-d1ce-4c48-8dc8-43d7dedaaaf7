AWS_DEFAULT_REGION=ap-northeast-1
#AWS_ACCESS_KEY_ID=
#AWS_SECRET_ACCESS_KEY=

# インポート用データが格納されるBucket（現状、接続チェックにのみ利用）
#AWS_S3_BUCKET_NAME=
# インポートジョブを受け取るQueue
#AWS_SQS_QUEUE_URL=

# ログ出力設定
# - ログにTracebackを出すかどうか
LOGGING_TRACEBACK=true
# - ログの出力先 選択肢：{stream|cloudwatch}
LOGGER=cloudwatch
# - ログの出力先がcloudwatchの場合の設定
AWS_CLOUDWATCH_LOG_GROUP_NAME=dev3-cloudwatch-group
AWS_CLOUDWATCH_LOG_STREAM_NAME=dev3-async-import-stream

# データベース接続情報
#DB_HOST=
#DB_PORT=
#DB_USER=
#DB_PASSWORD=
#DB_DATABASE=

# メール（SMTP）設定
MAIL_HOST=email-smtp.ap-northeast-1.amazonaws.com
MAIL_PORT=587
MAIL_FROM_ADDRESS=<EMAIL>
# - メール送信認証情報
#   - 未設定だと認証なしにSMTPに接続に行く
# MAIL_USERNAME=
# MAIL_PASSWORD=
#   - 未設定だとTLSなしにSMTPに接続に行く
MAIL_ENCRYPTION=tls
