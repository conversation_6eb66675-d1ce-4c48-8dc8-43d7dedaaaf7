server_tokens off;

upstream php-fpm {
    server ${FAST_CGI_PASS};
}

server {
    listen      80;
    server_name ${SERVER_NAME};

    root /usr/share/nginx/html/partner-prop-api/public;
    index index.php index.html index.htm;

    client_max_body_size 500M;

    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }
    location ~ \.php$ {
        fastcgi_intercept_errors on;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_pass php-fpm;
    }
}
