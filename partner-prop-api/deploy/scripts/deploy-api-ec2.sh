set -e

environment=$1
branch=$2

if [[ -z "$environment" || -z "$branch" ]]; then
  echo "Environment and branch name are required"
  exit 1
fi

source deploy/scripts/_util.sh

# stop API
systemctl stop nginx.service
systemctl stop php-fpm.service

# Re-deploy
git_checkout "$branch"
# set vars
set_vars "$environment"

if [[ -f .env ]]; then
  mv .env .env.bak
fi
cp "${DEPLOY_DIR}/env/partner-prop-api.env" .env
echo "# Secrets" >> .env && generate_secrets "./deploy/api-secrets.list" >> .env

rm -rf vendor/
composer install --no-dev --no-cache

php artisan cache:clear
php artisan config:clear
php artisan route:clear

if [[ -f "${DEPLOY_DIR}/etc/php.d/zzz-api.ini" ]]; then
    cp "${DEPLOY_DIR}/etc/php.d/zzz-api.ini" "/etc/php.d/"
else
    cp "deploy/base/etc/php.d/zzz-api.ini" "/etc/php.d/"
fi
copy_config "php-fpm.d"

if [[ -f "${DEPLOY_DIR}/env/nginx.env" ]]; then
    process_template "deploy/base/etc/nginx/default.conf.template" "${DEPLOY_DIR}/env/nginx.env" "/etc/nginx/conf.d/partner-prop.conf"
else
    copy_config "nginx"
fi


if [[ -n "${PP_DEPLOY_WITH_MIGRATE}" ]]; then
    php artisan migrate
fi

systemctl start php-fpm.service
systemctl start nginx.service

echo "Deployment done successfully!!"
