set -e

environment=$1
branch=$2

if [[ -z "$environment" || -z "$branch" ]]; then
    echo "Environment and branch name are required"
    exit 1
fi

source deploy/scripts/_util.sh
set_vars "$environment"
git_checkout "$branch"

# Deploy Worker
if [[ -n "${PP_DEPLOY_WORKER}" ]]; then
    systemctl stop supervisord.service

    if [[ -f .env ]]; then
        mv .env .env.bak
    fi
    cp "${DEPLOY_DIR}/env/partner-prop-api.env" .env
    echo "# Secrets" >> .env
    generate_secrets "./deploy/api-secrets.list" >> .env

    rm -rf vendor/
    composer install --no-dev --no-cache

    if [[ -f "${DEPLOY_DIR}/etc/php.d/zzz-api.ini" ]]; then
        cp "${DEPLOY_DIR}/etc/php.d/zzz-worker.ini" "/etc/php.d/"
    else
        cp "deploy/base/etc/php.d/zzz-worker.ini" "/etc/php.d/"
    fi
    copy_config "supervisord.d"
    systemctl start supervisord.service

    echo "Done worker deployment"
fi

# Deploy scheduler
if [[ -n "${PP_DEPLOY_SCHEDULER}" ]]; then
    if [[ -f "${DEPLOY_DIR}/etc/cron.d/partner-prop" ]]; then
        cp "${DEPLOY_DIR}/etc/cron.d/partner-prop" "/etc/cron.d/"
    else
        cp "deploy/base/etc/cron.d/partner-prop" "/etc/cron.d/"
    fi
fi

# Deploy ImportWorker
if [[ -n "${PP_DEPLOY_IMPORT_WORKER}" ]]; then
    cd ext-services/async-import
    source scripts/deploy-ec2-util.sh

    generate_secrets "../../deploy/import-secrets.list" "import" > ".secrets"

    ENV_FILE=".env.$environment"
    build_and_restart

    cd -

    echo "Done import worker deployment"
fi
