function git_checkout() {
    branch=$1
    if [[ -z "$branch" ]]; then
        echo "Branch name is required"
        exit 1
    fi
    git fetch origin -p
    git switch "$branch"
    git pull origin "$branch"
}

function set_vars() {
    echo "environment: $1"

    # IMDSv2対応
    local token=$(curl -s -X PUT "http://169.254.169.254/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")
    [[ -z "$token" ]] && { echo "Failed to get IMDSv2 token"; exit 1; }

    local region=$(curl -s -H "X-aws-ec2-metadata-token: $token" http://169.254.169.254/latest/meta-data/placement/region)
    [[ -n "$region" ]] && export AWS_DEFAULT_REGION=$region || { echo "Failed to get region"; exit 1; }

    export DEPLOY_DIR="./deploy/overlays/$1"
    export SSM_PARAM_PATH_PREFIX_API="/partner-prop-api/$1/api/"
    export SSM_PARAM_PATH_PREFIX_IMPORT="/partner-prop-api/$1/import/"

    case "$1" in
        test | production )
            # FIXME: these envs not supported yet
            exit 1
            ;;
        haposoft-2)
            # Note: Secretは現状共有しているので、一方に寄せておく
            export SSM_PARAM_PATH_PREFIX_API="/partner-prop-api/haposoft-1/api/"
            export SSM_PARAM_PATH_PREFIX_IMPORT="/partner-prop-api/haposoft-1/import/"
            ;;
    esac
    echo "done to set vars"
}

function generate_secrets() {
    src=$1
    if [[ -z "$src" ]]; then
        echo "Secret name list file is required"
        exit 1
    fi
    if [[ "$2" == "import" ]]; then
        prefix="${SSM_PARAM_PATH_PREFIX_IMPORT}"
    else
        prefix="${SSM_PARAM_PATH_PREFIX_API}"
    fi

    names=()
    while IFS= read -r key
    do
        names+=("${prefix}${key}")
    done < "$src"

    chunk_size=10
    for ((i=0; i<${#names[@]}; i+=chunk_size)); do
        chunk=("${names[@]:i:chunk_size}")
        aws ssm get-parameters --with-decryption --names "${chunk[@]}" --query "Parameters[*].{Name:Name,Value:Value}" --output text \
        | while read -r name value
        do
            key=$(basename "$name")
            echo "${key}=${value}"
        done
    done
}

# 設定ファイル/ディレクトリをコピー
copy_config() {
    local path="$1"
    local name=$(basename "$path")
    local env_path="${DEPLOY_DIR}/etc/${path}"
    local base_path="deploy/base/etc/${path}"

    if [[ -d "/etc/${path}" ]]; then
        echo "Backing up existing /etc/${path} files"
        for f in /etc/${path}/*; do
            [[ "$f" == *~ ]] && continue
            [ -f "$f" ] && mv "$f" "$f~"
        done
    fi

    if [[ -d "$env_path" ]]; then
        echo "Environment-specific ${name} found, using $env_path"
        cp -r "${env_path}/." "/etc/${path}"
    elif [[ -f "$env_path" ]]; then
        echo "Environment-specific ${name} found, using $env_path"
        cp "$env_path" "/etc/${path}"
    else
        echo "Environment-specific ${name} not found, using $base_path"
        if [[ -d "$base_path" ]]; then
            cp -r "${base_path}/." "/etc/${path}"
        else
            cp "$base_path" "/etc/${path}"
        fi
    fi
}

# テンプレート処理
process_template() {
    local template_file="$1" env_file="$2" output_file="$3"
    local nginx_conf_dir="/etc/nginx/conf.d"

    [[ ! -f "$template_file" ]] && { echo "Template file not found: $template_file"; return 1; }

    if [[ "$output_file" == "$nginx_conf_dir/"* ]]; then
        echo "Backing up existing $nginx_conf_dir files"
        for f in $nginx_conf_dir/*; do
            [[ "$f" == *~ ]] && continue
            [ -f "$f" ] && mv "$f" "$f~"
        done
    fi
    if [[ -f "$env_file" ]]; then
        echo "Processing template: $template_file with env: $env_file"
        set -a
        . "$env_file"
        set +a
        VARS='$SERVER_NAME $FAST_CGI_PASS'
        envsubst "$VARS" < "$template_file" > "$output_file"
        echo "Template processed successfully: $output_file" || \
        { echo "Template processing failed"; return 1; }
    else
        echo "Error: Environment file not found: $env_file"
        return 1
    fi
}