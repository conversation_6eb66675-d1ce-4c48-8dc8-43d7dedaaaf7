[www]
user = nginx
group = nginx
listen = /run/php-fpm/www.sock

pm = dynamic
pm.max_children = 40
pm.start_servers = 10
pm.min_spare_servers = 10
pm.max_spare_servers = 30

listen.acl_users = apache,nginx
listen.allowed_clients = 127.0.0.1

slowlog = /var/log/php-fpm/www-slow.log

catch_workers_output = yes

php_admin_value[error_log] = /var/log/php-fpm/www-error.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 128M

php_value[session.save_handler] = files
php_value[session.save_path]    = /var/lib/php/session
php_value[soap.wsdl_cache_dir]  = /var/lib/php/wsdlcache
