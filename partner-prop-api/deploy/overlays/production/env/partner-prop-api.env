# === App meta
APP_NAME=Partner-Prop
APP_ENV=production
# APP_KEY=*****
APP_DEBUG=false
APP_URL=https://app-prop.com
APP_DOMAIN=app-prop.com
APIS_URL=https://apis.app-prop.com
# === Partner Prop
VENDOR_FRONT_DOMAIN=https://vendor.app-prop.com
PARTNER_FRONT_DOMAIN=https://partner.app-prop.com
FRONT_DOMAIN=https://app-prop.com
# LEAD_SUB_TABLE_BUCKET_DIR=not used
# === Partner Prop - S3
AWS_BUCKET=prod-prop-files
AWS_BUCKET_MANAGED_PARTNER_CUSTOM_COLUMN=prod-prop-managed-partner-custom-column-files
AWS_BUCKET_MANAGED_PARTNER_SUB_TABLE=prod-prop-managed-partner-sub-table-files
AWS_BUCKET_LEAD_CUSTOM_COLUMN=prod-prop-lead-custom-column-files
AWS_BUCKET_LEAD_SUB_TABLE=prod-prop-lead-sub-table-files
AWS_BUCKET_CHAT=prod-prop-chat-files
AWS_BUCKET_EXPORTED_FILE=prod-prop-export-files
AWS_BUCKET_AUDIT=prod-prop-audit-files
AWS_BUCKET_TRAINING=prod-prop-training-files
AWS_BUCKET_STG_AREA=prod-prop-stg-area-files
AWS_BUCKET_CONTACT=prod-prop-contact-files

AWS_BUCKET_LEAD_CREATION_FORM=prod-lead-custom-url-files

VENDOR_USER_S3_PATH=vendor
VENDOR_S3_PATH=vendor
PARTNER_USER_S3_PATH=partner
PARTNER_S3_PATH=partner
# === prop.async-import
AWS_BUCKET_STG_AREA_ASYNC_IMPORT_DIR=async-import
# AWS_SNS_TOPIC_ARN_ASYNC_IMPORT=*****
AWS_SNS_MESSAGE_GROUP_ID_ASYNC_IMPORT=async-import-group

AWS_SNS_VERSION=latest

# === AWS
# AWS_ACCESS_KEY_ID=*****
# AWS_SECRET_ACCESS_KEY=*****
AWS_DEFAULT_REGION=ap-northeast-1

# === Session
SESSION_DOMAIN=".app-prop.com"
SESSION_DRIVER=database
SESSION_LIFETIME_PARTNER=30240
SESSION_LIFETIME_VENDOR=30240
SESSION_SECURE_COOKIE=true

# === Logging - Cloudwatch
LOG_CHANNEL=cloudwatch
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=info
AWS_CLOUDWATCH_LOG_GROUP_NAME=prod-cloudwatch-group
AWS_CLOUDWATCH_LOG_STREAM_NAME=prod1-cloudwatch-stream
AWS_CLOUDWATCH_LOG_RETENTION=1

# === Database - MySQL(RDS)
DB_CONNECTION=mysql
DB_HOST=prod-prop-cluster.cluster-crqg36fuzuoc.ap-northeast-1.rds.amazonaws.com
DB_PORT=3306
DB_DATABASE=partner_prop
# DB_USERNAME=*****
# DB_PASSWORD=*****

# === Cache
CACHE_DRIVER=file

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# === File system
FILESYSTEM_DISK=local

# === Queue
QUEUE_CONNECTION=database
QUEUE_DRIVER=database

# === Mailer
# MAIL_FROM_NAME=not used
MAIL_MAILER=smtp
MAIL_HOST=email-smtp.ap-northeast-1.amazonaws.com
MAIL_PORT=587
# MAIL_USERNAME=*****
# MAIL_PASSWORD=*****
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>

# === Broadcast - Pusher
BROADCAST_DRIVER=pusher
# PUSHER_APP_ID=*****
# PUSHER_APP_KEY=*****
# PUSHER_APP_SECRET=*****
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=ap3

# === Anyflow JWT Setting
JWT_PUBLIC_KEY=file:///usr/share/nginx/html/partner-prop-api/storage/app/keys/public.key
JWT_PRIVATE_KEY=file:///usr/share/nginx/html/partner-prop-api/storage/app/keys/private.key
JWT_ALGO=RS256
JWT_TTL=60000
JWT_REFRESH_TTL=20160
APP_ISSUER_URL=a4c92fd8-ad62-45bf-9f6e-77f46b395c8d

# === QUICKSIGHT
AWS_QUICKSIGHT_SESSION_LIFETIME=30

# === Sentry
SENTRY_LARAVEL_DSN=https://<EMAIL>/4509592304877568
SENTRY_SAMPLE_RATE=1.0              # 全エラーを送信
SENTRY_TRACES_SAMPLE_RATE=0.1       # 10%のトランザクション監視
SENTRY_PROFILES_SAMPLE_RATE=0.1     # 10%のプロファイリング
