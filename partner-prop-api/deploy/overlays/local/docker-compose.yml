# Nginx - PHP-FPM を使ったテスト向けのマニフェスト

services:
  nginx:
    depends_on:
      init-api:
        condition: service_completed_successfully
    image: nginx:stable-bullseye
    pull_policy: always
    container_name: partner-prop-nginx
    ports:
      - "9000:80"
      - "9080:8080"
      - "9443:443"
    restart: on-failure
    environment:
      # defaukt.conf.template をカスタムする
      LISTEN_PORT: 80
      SERVER_NAME: "vendorapis.local.partner-prop.com partnerapis.local.partner-prop.com apis.local.partner-prop.com"
      # fastcgi_passは nginx upstream で指定
      FAST_CGI_PASS: partner-prop-api
      DOCUMENT_ROOT: /var/www/html/public
      CLIENT_MAX_BODY_SIZE: 500M
      # Local専用
      HTTPS_LISTEN_PORT: 443
      SSL_CERTIFICATE: /partner-prop-api-keys/https/server.crt
      SSL_CERTIFICATE_KEY: /partner-prop-api-keys/https/server.key
    volumes:
      # Upstreamなど、ローカル用の設定類
      - type: bind
        source: deploy/overlays/local/etc/nginx/servers.conf.template
        target: /etc/nginx/templates/servers.conf.template
      # HTTP側のデフォルト設定類
      - type: bind
        source: deploy/overlays/local/etc/nginx/default.conf.template
        target: /etc/nginx/templates/default.conf.template
      # Init containerで生成した秘密鍵をマウント
      - partner-prop-api-keys-volume:/partner-prop-api-keys
    networks:
      - partner-prop-network

  init-api:
    container_name: partner-prop-init-api
    build:
      context: .
      dockerfile: dockerfiles/partner-prop-api.dockerfile
    volumes:
      - type: bind
        source: .
        target: /var/www/html
      - partner-prop-api-keys-volume:/data
      - partner-prop-local-vendor:/var/www/html/vendor
    env_file:
      # リポジトリルートに .env がある場合はその内容が優先される
      - path: .devcontainer/api/.env.local
        required: true
      - path: .env
        required: false
    user: root
    command: >
      sh -c '
        composer install --no-dev --no-cache --working-dir /var/www/html ;

        php artisan cache:clear ;
        php artisan config:clear ;
        php artisan route:clear ;

        mkdir -p /data/jwt ;
        openssl genpkey -algorithm RSA -out /data/jwt/private.key -pkeyopt rsa_keygen_bits:2048 ;
        openssl rsa -pubout -in /data/jwt/private.key -out /data/jwt/public.key ;

        mkdir -p /data/https ;
        openssl genrsa -out /data/https/server.key 2048 ;
        openssl req -new -key /data/https/server.key -out /data/https/server.csr -subj "/C=JP/ST=Tokyo/L=/O=/OU=/CN=local.partner-prop.com"
        openssl x509 -in /data/https/server.csr -days 365 -req -signkey /data/https/server.key > /data/https/server.crt ;
      '
    restart: "no"

  api:
    depends_on:
      init-api:
        condition: service_completed_successfully
    build:
      context: .
      dockerfile: dockerfiles/partner-prop-api.dockerfile
#    container_name: partner-prop-api
    deploy:
      mode: replicated
      # 複数サーバーでも実験できるようにReplicas設定。増減させた場合、etc/nginx/servers.confをいじること
      # 本番はこのようにやらないのであくまで実験用。本番は、ALB -> (Nginx+php-fpm) x N = Nginx-php-fpm は1:1の関係
      replicas: 2
      endpoint_mode: vip
      resources:
        limits:
          cpus: "1"
          memory: "1G"
      restart_policy:
        condition: on-failure
    env_file:
      # リポジトリルートに .env がある場合はその内容が優先される
      - path: .devcontainer/api/.env.local
        required: true
      - path: .env
        required: false
    environment:
      # コンテナなのでログ書き出し先を標準エラーに固定
      - LOG_CHANNEL=stderr
    networks:
      - partner-prop-network
    volumes:
      # PHP, PHP-FPM で本番等で利用するのと同等の設定
      - type: bind
        source: deploy/base/etc/php.d/zzz-api.ini
        target: /usr/local/etc/php/conf.d/zzz-api.ini
      - type: bind
        source: deploy/overlays/local/etc/php-fpm.d/zzz-api.conf
        target: /usr/local/etc/php-fpm.d/zzz-api.conf
      # リポジトリルートを一括でマウントすると、場合によってPermissionエラーが起きるので注意
      - type: bind
        source: .
        target: /var/www/html
      # 自動生成した鍵のマウント
      - partner-prop-api-keys-volume:/var/www/html/storage/app/keys
      # 依存解決結果のマウント
      - partner-prop-local-vendor:/var/www/html/vendor

  api-worker:
    depends_on:
      init-api:
        condition: service_completed_successfully
    build:
      context: .
      dockerfile: dockerfiles/partner-prop-api.dockerfile
    container_name: partner-prop-api-worker
    env_file:
      - path: .devcontainer/api/.env.local
        required: true
      - path: .env
        required: false
    environment:
      - LOG_CHANNEL=stderr
    networks:
      - partner-prop-network
    volumes:
      - type: bind
        source: deploy/base/etc/php.d/zzz-worker.ini
        target: /usr/local/etc/php/conf.d/zzz-worker.ini
      - type: bind
        source: .
        target: /var/www/html
      - partner-prop-api-keys-volume:/var/www/html/storage/app/keys
      - partner-prop-local-vendor:/var/www/html/vendor
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600

volumes:
  partner-prop-local-vendor:
    name: partner-prop-local-vendor
    driver: local
  partner-prop-api-keys-volume: {}

networks:
  partner-prop-network:
    name: partner-prop-network
    driver: bridge
