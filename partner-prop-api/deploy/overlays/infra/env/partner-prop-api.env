# === App meta
APP_NAME=Partner-Prop
APP_ENV=infra
# APP_KEY=*****
APP_DEBUG=true
APP_URL=http://infra.app-prop-test.com
APP_DOMAIN=infra.app-prop-test.com
APIS_URL=https://apis.infra.app-prop-test.com

# === Partner Prop
VENDOR_FRONT_DOMAIN=https://vendor.infra.app-prop-test.com
PARTNER_FRONT_DOMAIN=https://partner.infra.app-prop-test.com
FRONT_DOMAIN=https://infra.app-prop-test.com
LEAD_SUB_TABLE_BUCKET_DIR=lead_sub_table
# === Partner Prop - S3
#AWS_BUCKET=
#AWS_BUCKET_MANAGED_PARTNER_CUSTOM_COLUMN=
#AWS_BUCKET_MANAGED_PARTNER_SUB_TABLE=
#AWS_BUCKET_LEAD_CUSTOM_COLUMN=
#AWS_BUCKET_LEAD_SUB_TABLE=
#AWS_BUCKET_CHAT=
#AWS_BUCKET_AUDIT=
#AWS_BUCKET_EXPORTED_FILE=
#AWS_BUCKET_TRAINING=
#AWS_BUCKET_STG_AREA=
#AWS_BUCKET_CONTACT=
#AWS_BUCKET_LEAD_CREATION_FORM=

VENDOR_USER_S3_PATH=vendor
VENDOR_S3_PATH=vendor
PARTNER_USER_S3_PATH=partner
PARTNER_S3_PATH=partner
# === prop.async-import
AWS_BUCKET_STG_AREA_ASYNC_IMPORT_DIR=async-import
# AWS_SNS_TOPIC_ARN_ASYNC_IMPORT=*****
AWS_SNS_MESSAGE_GROUP_ID_ASYNC_IMPORT=async-import-group

AWS_SNS_VERSION=latest

# === AWS
# AWS_ACCESS_KEY_ID=*****
# AWS_SECRET_ACCESS_KEY=*****
AWS_DEFAULT_REGION=ap-northeast-1

# === Session
SESSION_DOMAIN=".app-prop-test.com"
SESSION_DRIVER=database
SESSION_LIFETIME_PARTNER=30240
SESSION_LIFETIME_VENDOR=30240
SESSION_SECURE_COOKIE=true
SANCTUM_STATEFUL_DOMAINS=localhost,vendor.infra.app-prop-test.com,partner.infra.app-prop-test.com

# === Logging - Cloudwatch
LOG_CHANNEL=cloudwatch
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug
AWS_CLOUDWATCH_LOG_GROUP_NAME=infra-cloudwatch-group
AWS_CLOUDWATCH_LOG_STREAM_NAME=infra-cloudwatch-stream
# TODO: インフラ側で設定するか検討
AWS_CLOUDWATCH_LOG_RETENTION=1

# === Database - MySQL(RDS)
DB_CONNECTION=mysql
# DB_HOST=****
# DB_PORT=****
# DB_DATABASE=****
# DB_USERNAME=*****
# DB_PASSWORD=*****

# === Cache
CACHE_DRIVER=file

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# === File system
FILESYSTEM_DISK=local

# === Queue
QUEUE_CONNECTION=database
QUEUE_DRIVER=database

# === Mailer
MAIL_FROM_NAME="Test Prop"
MAIL_MAILER=smtp
MAIL_HOST=email-smtp.ap-northeast-1.amazonaws.com
MAIL_PORT=587
# MAIL_USERNAME=*****
# MAIL_PASSWORD=*****
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>

# === Broadcast - Pusher
BROADCAST_DRIVER=pusher
# PUSHER_APP_ID=*****
# PUSHER_APP_KEY=*****
# PUSHER_APP_SECRET=*****
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=ap3

# === Anyflow JWT Setting
JWT_PUBLIC_KEY=file:///usr/share/nginx/html/partner-prop-api/storage/app/keys/public.key
JWT_PRIVATE_KEY=file:///usr/share/nginx/html/partner-prop-api/storage/app/keys/private.key
JWT_ALGO=RS256
JWT_TTL=60000
JWT_REFRESH_TTL=20160
APP_ISSUER_URL=a4c92fd8-ad62-45bf-9f6e-77f46b395c8d

# === QUICKSIGHT
#AWS_ACCOUNT_ID=
AWS_QUICKSIGHT_SESSION_LIFETIME=30

# === Sentry
SENTRY_LARAVEL_DSN=
