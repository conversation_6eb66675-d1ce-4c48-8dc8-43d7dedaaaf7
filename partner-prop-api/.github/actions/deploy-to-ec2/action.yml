name: Deploy to EC2 via SSM Run Command
description: ""

inputs:
  environment:
    description: 'Environment of deploy target'
    required: true
  branch:
    description: 'Branch to deploy'
    required: true
  ssm-document-name-api:
    description: 'SSM Document Name for API deployment'
    required: true
  ssm-document-name-worker:
    description: 'SSM Document Name for Worker deployment'
    required: true
  slack-notify:
    description: 'Whether to send Slack notification'
    required: false
    default: 'false'
  slack-notify-channel-id:
    description: 'Slack channel ID for notifications'
    required: false
  slack-bot-token:
    description: 'Slack bot token for sending notifications'
    required: false

runs:
  using: 'composite'
  steps:
    - name: Deploy API to EC2 with SSM Run Command
      shell: bash
      env:
        AWS_PAGER: ""
      run: |
        aws ssm send-command \
          --document-name "${{ inputs.ssm-document-name-api }}" \
          --timeout-seconds 300 \
          --targets "Key=tag:Env,Values=${{ inputs.environment }}" "Key=tag:AppApi,Values=true" \
          --parameters "Env=${{ inputs.environment }},Branch=${{ inputs.branch }}" \
          --output json | tee ssm-out.json

        cmd_id=$(jq -r '.Command.CommandId' ssm-out.json)
        sleep 3
        aws ssm list-command-invocations --command-id $cmd_id --output json | tee ssm-out.json
        for ins_id in $(jq -c -r '.CommandInvocations[].InstanceId' ssm-out.json); do
          echo "Waiting for command $cmd_id to finish on instance $ins_id"
          aws ssm wait command-executed --command-id "$cmd_id" --instance-id "$ins_id" --output json
        done

    - name: Deploy Worker to EC2 with SSM Run Command
      shell: bash
      env:
        AWS_PAGER: ""
      run: |
        aws ssm send-command \
          --document-name "${{ inputs.ssm-document-name-worker }}" \
          --timeout-seconds 300 \
          --targets "Key=tag:Env,Values=${{ inputs.environment }}" "Key=tag:AppWorker,Values=true" \
          --parameters "Env=${{ inputs.environment }},Branch=${{ inputs.branch }},DeployWorker=1,DeployScheduler=1,DeployImportWorker=1" \
          --output json | tee ssm-out.json

        cmd_id=$(jq -r '.Command.CommandId' ssm-out.json)
        sleep 3
        aws ssm list-command-invocations --command-id $cmd_id --output json | tee ssm-out.json
        for ins_id in $(jq -c -r '.CommandInvocations[].InstanceId' ssm-out.json); do
          echo "Waiting for command $cmd_id to finish on instance $ins_id"
          aws ssm wait command-executed --command-id "$cmd_id" --instance-id "$ins_id" --output json
        done

    - if: ${{ success() && inputs.slack-notify == 'true' }}
      name: Post success message to Slack 
      uses: slackapi/slack-github-action@v2
      with:
        method: chat.postMessage
        token: ${{ inputs.slack-bot-token }}
        payload: |
          channel: ${{ inputs.slack-notify-channel-id }}
          text: ":rocket: *Backend Deploy Succeeded*\nEnv: ${{ inputs.environment }}, Ref: ${{ inputs.branch }}, Commit: ${{ github.sha }}"

    - if: ${{ failure() && inputs.slack-notify == 'true' }}
      name: Post failure message to Slack
      uses: slackapi/slack-github-action@v2
      with:
        method: chat.postMessage
        token: ${{ inputs.slack-bot-token }}
        payload: |
          channel: ${{ inputs.slack-notify-channel-id }}
          text: ":x: *Backend Deploy Failed*\nEnv: ${{ inputs.environment }}, Ref: ${{ inputs.branch }}, Commit: ${{ github.sha }}"
