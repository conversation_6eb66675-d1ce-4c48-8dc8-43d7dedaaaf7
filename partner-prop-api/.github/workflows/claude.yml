name: <PERSON> Assistant

on:
  issue_comment:                # イシュー／PR コメント
    types: [created]
  pull_request_review_comment:  # PR 行内コメント
    types: [created]
  issues:                       # イシュー本文
    types: [opened, assigned]
  pull_request_review:          # PR レビュー本文
    types: [submitted]

jobs:
  claude-code-action:
    # コメント／本文に「@claude」で始まる文字列が含まれる場合のみ実行
    if: |
      (github.event_name == 'issue_comment'               && contains(github.event.comment.body, '@claude')) ||
      (github.event_name == 'pull_request_review_comment' && contains(github.event.comment.body, '@claude')) ||
      (github.event_name == 'pull_request_review'         && contains(github.event.review.body,  '@claude')) ||
      (github.event_name == 'issues'                      && contains(github.event.issue.body,   '@claude'))

    runs-on: ubuntu-latest

    permissions:
      contents:      read
      pull-requests: read
      issues:        read
      id-token:      write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 

      - name: Run Claude PR Action
        uses: anthropics/claude-code-action@beta
        with:
          allowed_tools: "Ba<PERSON>(gh:*),<PERSON><PERSON>(git:*),<PERSON><PERSON>(php:*)"
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          timeout_minutes: "60"
          direct_prompt: |
            Reply in Japanese.
