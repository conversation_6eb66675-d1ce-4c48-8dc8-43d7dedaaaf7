# IssueOps workflow for EC2 API deployments
# This workflow enables deployment management through PR comments, allowing teams to
# deploy API to EC2 environments using AWS SSM for non-production environments.
# 
# Trigger: Comment '.deploy to <environment>' on pull requests
#
# Required secrets:
#   AWS_CI_ROLE_ARN_DEV    - AWS IAM Role ARN for DEV environments (dev1-dev4)
#   AWS_CI_ROLE_ARN_HAPO   - AWS IAM Role ARN for HAPO environments (haposoft-1,2)  
#   AWS_CI_ROLE_ARN_STG    - AWS IAM Role ARN for STG environment
#   SLACK_NOTIFY_CHANNEL_ID       - Slack channel ID for notifications
#
# Required variables:
#   DOCUMENT_NAME_DEPLOY_API      - SSM document name for API deployment
#   DOCUMENT_NAME_DEPLOY_WORKER   - SSM document name for Worker deployment
#   AWS_REGION                    - AWS region for deployment
#   SLACK_BOT_TOKEN               - Slack bot token for sending notifications

name: Deploy API to EC2 by IssueOps

defaults:
  run:
    shell: bash

on:
  issue_comment:
    types: [created]

permissions:
  id-token: write
  issues: write
  pull-requests: write
  deployments: write
  contents: write
  checks: read
  statuses: read

jobs:
  deploy:
    if: ${{ github.event.issue.pull_request && (startsWith(github.event.comment.body, '.deploy')) }}
    runs-on: ubuntu-latest
    steps:
      - id: comment-cmd
        uses: github/branch-deploy@v10
        with:
          trigger: ".deploy"
          help_trigger: ".deploy_help"
          lock_trigger: ".deploy_lock"
          unlock_trigger: ".deploy_unlock"
          lock_info_alias: ".deploy_wcid"
          environment: "dev"
          environment_targets: ${{ vars.ISSUEOPS_DEPLOY_ENVIRONMENT_TARGETS }}
          skip_reviews: ${{ vars.ISSUEOPS_DEPLOY_SKIP_REVIEWS || vars.ISSUEOPS_DEPLOY_ENVIRONMENT_TARGETS }}
          skip_ci: ${{ vars.ISSUEOPS_DEPLOY_SKIP_CI || vars.ISSUEOPS_DEPLOY_ENVIRONMENT_TARGETS }}
          update_branch: ${{ vars.ISSUEOPS_DEPLOY_UPDATE_BRANCH }}
          outdated_mode: ${{ vars.ISSUEOPS_DEPLOY_OUTDATED_MODE }}
          allow_non_default_target_branch_deployments: true  # main向きPRでもQA環境にデプロイ可能にするため

      - if: ${{ steps.comment-cmd.outputs.continue == 'true' }}
        # Note:
        #  GitHub ActionsのEnvironmentでやるべきだが、environmentはジョブレベルで指定が必要で、このレベルで2jobに分けるのは無駄な消費になりうるのでMappingで対応
        name: Environment Mapping
        id: config
        run: |
          case "${{ steps.comment-cmd.outputs.environment }}" in
            dev1|dev2|dev3|dev4)
              echo "ci-role-arn=${{ secrets.AWS_CI_ROLE_ARN_DEV }}" >> $GITHUB_OUTPUT
              ;;
            haposoft-1|haposoft-2)
              echo "ci-role-arn=${{ secrets.AWS_CI_ROLE_ARN_HAPO }}" >> $GITHUB_OUTPUT
              ;;
            stg)
              echo "ci-role-arn=${{ secrets.AWS_CI_ROLE_ARN_STG }}" >> $GITHUB_OUTPUT
              ;;
          esac

      - if: ${{ steps.comment-cmd.outputs.continue == 'true' }}
        name: Configure AWS credentials (OIDC)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ steps.config.outputs.ci-role-arn }}
          aws-region: ${{ vars.AWS_REGION }}

      - if: ${{ steps.comment-cmd.outputs.continue == 'true' }}
        uses: actions/checkout@v4

      - if: ${{ steps.comment-cmd.outputs.continue == 'true' }}
        id: deploy
        uses: ./.github/actions/deploy-to-ec2
        with:
          environment: "${{ steps.comment-cmd.outputs.environment }}"
          branch: "${{ steps.comment-cmd.outputs.ref }}"
          ssm-document-name-api: "${{ vars.DOCUMENT_NAME_DEPLOY_API }}"
          ssm-document-name-worker: "${{ vars.DOCUMENT_NAME_DEPLOY_WORKER }}"
          slack-notify: "true"
          slack-notify-channel-id: "${{ vars.SLACK_NOTIFY_CHANNEL_ID }}"
          slack-bot-token: "${{ secrets.SLACK_BOT_TOKEN }}"
