name: Create Release Pull Request

on:
  workflow_dispatch:

env:
  TZ: 'Asia/Tokyo'

jobs:
  build:
    name: Create Release Pull Request
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.PAT_TOKEN }}
          fetch-depth: 0

      - name: Get Latest Version
        id: version
        run: |
          # Get latest tag
          LATEST_TAG=$(git tag -l "v*.*.*" --sort=-version:refname | head -1)
          
          if [ -z "$LATEST_TAG" ]; then
            echo "No previous version found, starting with v1.0.0"
            VERSION="0.0.0"
            MAJOR=0
            MINOR=0
            PATCH=0
          else
            echo "Latest tag: $LATEST_TAG"
            
            # Extract version numbers
            VERSION="${LATEST_TAG#v}"
            MAJOR=$(echo $VERSION | cut -d. -f1)
            MINOR=$(echo $VERSION | cut -d. -f2)
            PATCH=$(echo $VERSION | cut -d. -f3)
          fi
          
          echo "LATEST_VERSION=${VERSION}" >> $GITHUB_ENV
          echo "VERSION_MAJOR=${MAJOR}" >> $GITHUB_ENV
          echo "VERSION_MINOR=${MINOR}" >> $GITHUB_ENV
          echo "VERSION_PATCH=${PATCH}" >> $GITHUB_ENV
          echo "OUTPUT_BRANCH=releases" >> $GITHUB_ENV

      - name: Get Repository Name
        id: get-repository-name
        run: echo "REPOSITORY_NAME=${GITHUB_REPOSITORY#${GITHUB_REPOSITORY_OWNER}/}" >> $GITHUB_ENV

      - name: Check if release branch exists
        run: |
          git fetch
          if git show-ref --verify --quiet refs/remotes/origin/${{ env.OUTPUT_BRANCH }}; then
            echo "❌ Error: ${{ env.OUTPUT_BRANCH }} branch already exists!"
            echo "Please delete the existing ${{ env.OUTPUT_BRANCH }} branch before creating a new one:"
            echo "  git push origin --delete ${{ env.OUTPUT_BRANCH }}"
            echo "  git branch -D ${{ env.OUTPUT_BRANCH }}"
            exit 1
          fi

      - name: Create and Checkout branch
        run: |
          git checkout develop
          git checkout -b ${{ env.OUTPUT_BRANCH }}
          git push -u origin ${{ env.OUTPUT_BRANCH }}

      - name: Get Merged Pull Request Numbers
        id: pr
        run: |
          ids=$(git log --merges --pretty=format:'%s' origin/main..${{ env.OUTPUT_BRANCH }} | grep -o -E ' #[0-9]{1,}' | tr -d ' #' | tr '\n #' ',' | sed s'|,$||g')
          echo "ids=${ids}" >> $GITHUB_OUTPUT

      - name: Generate Release Pull Request Body and Determine Version Type
        uses: actions/github-script@v7
        id: generate-release-pr-body
        env:
          GITHUB_ACCESS_TOKEN: ${{ secrets.PAT_TOKEN }}
          PR_NUMBER_LIST: ${{ steps.pr.outputs.ids }}
          VERSION_MAJOR: ${{ env.VERSION_MAJOR }}
          VERSION_MINOR: ${{ env.VERSION_MINOR }}
          VERSION_PATCH: ${{ env.VERSION_PATCH }}
        with:
          github-token: ${{ secrets.PAT_TOKEN }}
          result-encoding: string
          script: |
            const prIds = process.env.PR_NUMBER_LIST;
            let prList = [];

            const promise = new Promise((resolve) => {
              (async () => {
                await Promise.all(prIds.split(',').map(async (prId) => {
                  if (!prId.trim()) return;
                  
                  const pull = await github.rest.pulls.get({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    pull_number: prId
                  });

                  // PR情報をリストに追加
                  prList.push(`- #${prId} ${pull['data']['title']}`);
                }));
                resolve();
              })();
            });

            return promise.then(() => {
              // Always increment minor version and reset patch to 0
              let major = parseInt(process.env.VERSION_MAJOR || '1');
              let minor = parseInt(process.env.VERSION_MINOR || '0');
              
              minor++;
              const nextVersion = `v${major}.${minor}.0`;
              
              // Set outputs
              core.setOutput('next_version', nextVersion);
              core.setOutput('version_type', 'minor');
              
              console.log(`Next version: ${nextVersion}`);
              let releaseBody = `# Release ${nextVersion}

            ## 📋 リリース作業チェックリスト

            ### 事前準備
            - [ ] ステージング環境での動作確認完了
            - [ ] データベースマイグレーション確認
            - [ ] 設定ファイルの更新確認
            - [ ] 関連ドキュメントの更新

            ### リリース実行
            - [ ] 本番環境へのデプロイ実行
            - [ ] データベースマイグレーション実行（必要な場合）
            - [ ] キャッシュクリア実行
            - [ ] 基本動作確認

            ### リリース後
            - [ ] 監視ダッシュボードでエラー確認
            - [ ] ログ確認
            - [ ] パフォーマンス確認
            - [ ] 関係者への完了報告

            ## 📊 今回のリリース概要
            
            **バージョン**: ${nextVersion}  
            **含まれるPR数**: ${prIds.split(',').filter(id => id.trim()).length}件

            ## 📝 変更内容
            ${prList.join('\r\n')}
            `;

              return releaseBody;
            });

      - name: Set Final Version
        run: |
          echo "NEXT_VERSION=${{ steps.generate-release-pr-body.outputs.next_version }}" >> $GITHUB_ENV

      - name: Create Release Pull Request
        id: create_pr
        run: |
          response=$(curl -X POST https://api.github.com/repos/partner-prop/${{ env.REPOSITORY_NAME }}/pulls \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Authorization: token ${{ secrets.PAT_TOKEN }}" \
            -d '{"base":"main","head":"${{ env.OUTPUT_BRANCH }}","title":"Release ${{ env.NEXT_VERSION }}"}')
          number=$(echo $response | jq -r .number)
          echo "NUMBER=${number}" >> $GITHUB_ENV

      - name: Get Release Pull Request Number When Already Exists
        id: get_pr_number
        run: |
          if [ "${{ env.NUMBER }}" != "null" ]; then
            exit 0
          fi
          response=$(curl -X GET https://api.github.com/repos/partner-prop/${{ env.REPOSITORY_NAME }}/pulls?branch=${{ env.OUTPUT_BRANCH }} \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Authorization: token ${{ secrets.PAT_TOKEN }}")
          number=$(echo $response | jq -r .[0].number)
          echo "NUMBER=${number}" >> $GITHUB_ENV

      - name: Update Release Pull Request
        uses: actions/github-script@v7
        env:
          PULL_NUMBER: ${{ env.NUMBER }}
          BODY: ${{ steps.generate-release-pr-body.outputs.result }}
        with:
          github-token: ${{ secrets.PAT_TOKEN }}
          script: |
            console.log(process.env.PULL_NUMBER)

            if (!process.env.PULL_NUMBER) {
              return 1
            }

            github.rest.pulls.update({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: process.env.PULL_NUMBER,
              body: process.env.BODY
            });
