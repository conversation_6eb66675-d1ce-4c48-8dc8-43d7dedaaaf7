name: Deploy to EC2 as manual(dispatch workflow)

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch (e.g. main, feature/XXX)'
        required: true
      environment:
        description: 'Environment of deploy target'
        required: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Configure AWS credentials (OIDC)
        if: ${{ !env.ACT }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_CI_ROLE_ARN }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Configure AWS credentials (Local/Act)
        if: ${{ env.ACT }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION }}

      - uses: actions/checkout@v4

      - name: Validate branch
        if: ${{ !env.ACT }}
        run: |
          if git ls-remote --exit-code "origin" "${{ inputs.branch }}" > /dev/null 2>&1; then
            echo "Branch exists"
          else
            echo "Branch does not exist"
            exit 1
          fi

      - id: deploy
        uses: ./.github/actions/deploy-to-ec2
        with:
          environment: "${{ inputs.environment }}"
          branch: "${{ inputs.branch }}"
          ssm-document-name-api: "${{ vars.DOCUMENT_NAME_DEPLOY_API }}"
          ssm-document-name-worker: "${{ vars.DOCUMENT_NAME_DEPLOY_WORKER }}"
          slack-notify: "true"
          slack-notify-channel-id: "${{ vars.SLACK_NOTIFY_CHANNEL_ID }}"
          slack-bot-token: "${{ secrets.SLACK_BOT_TOKEN }}"
