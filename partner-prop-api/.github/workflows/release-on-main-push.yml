name: Release on Main Push

on:
  push:
    branches: [main]

env:
  TZ: 'Asia/Tokyo'

jobs:
  create-release:
    name: Create Release and Tag
    runs-on: ubuntu-latest
    timeout-minutes: 15
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.PAT_TOKEN }}
          fetch-depth: 0

      - name: Get Latest Version
        id: version
        run: |
          # Get latest tag
          LATEST_TAG=$(git tag -l "v*.*.*" --sort=-version:refname | head -1)
          
          if [ -z "$LATEST_TAG" ]; then
            echo "No previous version found, starting with v1.0.0"
            VERSION="0.0.0"
            MAJOR=0
            MINOR=0
            PATCH=0
          else
            echo "Latest tag: $LATEST_TAG"
            
            # Extract version numbers
            VERSION="${LATEST_TAG#v}"
            MAJOR=$(echo $VERSION | cut -d. -f1)
            MINOR=$(echo $VERSION | cut -d. -f2)
            PATCH=$(echo $VERSION | cut -d. -f3)
          fi
          
          echo "LATEST_VERSION=${VERSION}" >> $GITHUB_ENV
          echo "VERSION_MAJOR=${MAJOR}" >> $GITHUB_ENV
          echo "VERSION_MINOR=${MINOR}" >> $GITHUB_ENV
          echo "VERSION_PATCH=${PATCH}" >> $GITHUB_ENV

      - name: Get Merged Pull Requests Since Last Release
        id: pr_list
        run: |
          # Get the latest tag or start from first commit if no tags
          LATEST_TAG=$(git tag -l "v*.*.*" --sort=-version:refname | head -1)
          
          if [ -z "$LATEST_TAG" ]; then
            # No previous tags, get all merged PRs
            COMMIT_RANGE="$(git rev-list --max-parents=0 HEAD)..HEAD"
          else
            # Get commits since last tag
            COMMIT_RANGE="${LATEST_TAG}..HEAD"
          fi
          
          echo "Checking commit range: $COMMIT_RANGE"
          
          # Get merge commit messages and extract PR numbers
          PR_NUMBERS=$(git log --merges --pretty=format:'%s' $COMMIT_RANGE | grep -o -E '#[0-9]+' | tr -d '#' | sort -u | tr '\n' ',' | sed 's/,$//')
          
          echo "Found PR numbers: $PR_NUMBERS"
          echo "pr_numbers=${PR_NUMBERS}" >> $GITHUB_OUTPUT

      - name: Generate Version and Release Notes
        uses: actions/github-script@v7
        id: generate_release
        env:
          PR_NUMBERS: ${{ steps.pr_list.outputs.pr_numbers }}
          VERSION_MAJOR: ${{ env.VERSION_MAJOR }}
          VERSION_MINOR: ${{ env.VERSION_MINOR }}
          VERSION_PATCH: ${{ env.VERSION_PATCH }}
        with:
          github-token: ${{ secrets.PAT_TOKEN }}
          script: |
            const prNumbers = process.env.PR_NUMBERS;
            let prTitles = [];
            
            if (prNumbers) {
              const prList = prNumbers.split(',').filter(num => num.trim());
              
              for (const prNumber of prList) {
                if (!prNumber.trim()) continue;
                
                try {
                  const pull = await github.rest.pulls.get({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    pull_number: parseInt(prNumber)
                  });
                  
                  prTitles.push(`- #${prNumber} ${pull.data.title}`);
                } catch (error) {
                  console.log(`Failed to get PR #${prNumber}: ${error.message}`);
                }
              }
            }
            
            // Increment minor version and reset patch to 0
            let major = parseInt(process.env.VERSION_MAJOR || '1');
            let minor = parseInt(process.env.VERSION_MINOR || '0');
            
            minor++;
            const nextVersion = `v${major}.${minor}.0`;
            
            // Generate release notes
            let releaseNotes = `# Release ${nextVersion}\n\n`;
            
            if (prTitles.length > 0) {
              releaseNotes += `## 変更内容\n\n${prTitles.join('\n')}\n\n`;
            } else {
              releaseNotes += `## 変更内容\n\n- マイナーアップデートまたは内部的な変更\n\n`;
            }
            
            releaseNotes += `**リリース日時**: ${new Date().toLocaleString('ja-JP', { timeZone: 'Asia/Tokyo' })}\n`;
            
            core.setOutput('version', nextVersion);
            core.setOutput('release_notes', releaseNotes);
            
            console.log(`Next version: ${nextVersion}`);
            console.log('Release notes generated');

      - name: Create and Push Tag
        run: |
          VERSION="${{ steps.generate_release.outputs.version }}"
          
          # Check if tag already exists
          if git rev-parse "${VERSION}" >/dev/null 2>&1; then
            echo "Tag ${VERSION} already exists, skipping..."
            exit 0
          fi
          
          # Create annotated tag
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          
          git tag -a "${VERSION}" -m "Release ${VERSION}"
          git push origin "${VERSION}"
          
          echo "Created and pushed tag: ${VERSION}"

      - name: Create GitHub Release
        uses: actions/github-script@v7
        env:
          VERSION: ${{ steps.generate_release.outputs.version }}
          RELEASE_NOTES: ${{ steps.generate_release.outputs.release_notes }}
        with:
          github-token: ${{ secrets.PAT_TOKEN }}
          script: |
            const version = process.env.VERSION;
            const releaseNotes = process.env.RELEASE_NOTES;
            
            try {
              const release = await github.rest.repos.createRelease({
                owner: context.repo.owner,
                repo: context.repo.repo,
                tag_name: version,
                name: version,
                body: releaseNotes,
                draft: false,
                prerelease: false
              });
              
              console.log(`Created GitHub release: ${release.data.html_url}`);
              core.setOutput('release_url', release.data.html_url);
            } catch (error) {
              console.error('Error creating release:', error);
              throw error;
            }

      - name: Create Pull Request to Develop
        uses: actions/github-script@v7
        env:
          VERSION: ${{ steps.generate_release.outputs.version }}
        with:
          github-token: ${{ secrets.PAT_TOKEN }}
          script: |
            const version = process.env.VERSION;
            
            try {
              // Check if develop branch exists
              try {
                await github.rest.repos.getBranch({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  branch: 'develop'
                });
              } catch (error) {
                console.log('Develop branch does not exist, skipping PR creation');
                return;
              }
              
              // Create PR to develop
              const pr = await github.rest.pulls.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: `sync: Merge ${version} to develop`,
                head: 'main',
                base: 'develop',
                body: `## 概要
            ### 対応内容
            - ${version} のリリースに伴う develop ブランチへの同期
            
            ### 対応チケット
            - リリース同期作業
            
            ### その他
            このPRは自動生成されました。mainブランチからdevelopブランチへの同期を行います。
            
            ### セルフレビュー
            - [x] 全てのテストコードが正常完了していること
            - [x] 改修範囲で、テストコードの追加がされていること（必要な場合）
            - [x] OpenAPI仕様書が追加されていること
            - [x] 設計・製造の技術標準に従っていること
            - [x] デプロイに修正が必要かチェックし、必要なら手順の特定・デプロイ設定の最新化を行っていること
            
            🤖 Generated with [Claude Code](https://claude.ai/code)
            
            Co-Authored-By: Claude <<EMAIL>>`
              });
              
              console.log(`Created PR to develop: ${pr.data.html_url}`);
            } catch (error) {
              if (error.status === 422 && error.message.includes('No commits between')) {
                console.log('No differences between main and develop, skipping PR creation');
              } else {
                console.error('Error creating PR to develop:', error);
                // Don't fail the workflow for PR creation errors
              }
            }