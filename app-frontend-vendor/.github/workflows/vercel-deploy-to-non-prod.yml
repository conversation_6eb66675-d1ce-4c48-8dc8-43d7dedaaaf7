# IssueOps workflow for Vercel preview deployments
# This workflow enables deployment management through PR comments, allowing teams to
# update preview deployments on Vercel for non-production environments using IssueOps.
# 
# Trigger: Comment '.deploy to <environment>' on pull requests

name: Vercel Preview Deploy

env:
  TENANT: vendor
  GH_ORG: ${{ github.repository_owner }}
  GH_REPO: ${{ github.event.repository.name }}
  SLACK_NOTIFY_CHANNEL_ID: ${{ vars.SLACK_NOTIFY_CHANNEL_ID }}
  SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
  VERCEL_TEAM_SLUG: partner-prop
  VERCEL_PROJECT_NAME: ${{ vars.VERCEL_PROJECT_NAME }}
  VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}

defaults:
  run:
    shell: bash

on:
  issue_comment:
    types: [created]

permissions:
  pull-requests: write
  deployments: write
  contents: write
  checks: read
  statuses: read

jobs:
  update_preview_deploy:
    if: ${{ github.event.issue.pull_request && (startsWith(github.event.comment.body, '.deploy')) }}
    runs-on: ubuntu-latest
    steps:
      - id: comment-cmd
        uses: github/branch-deploy@v10
        with:
          trigger: ".deploy"
          help_trigger: ".deploy_help"
          lock_trigger: ".deploy_lock"
          unlock_trigger: ".deploy_unlock"
          lock_info_alias: ".deploy_wcid"
          environment: "stg"
          environment_targets: "stg,dev1,dev2,haposoft-1,haposoft-2"
          skip_reviews: "stg,dev1,dev2,haposoft-1,haposoft-2"  # todo stgは除外予定。
          skip_ci: "stg,dev1,dev2,haposoft-1,haposoft-2"  # todo stgは除外予定。
          update_branch: warn
          allow_non_default_target_branch_deployments: true  # todo 非推奨オプション。ブランチルール改修後に削除

      - name: Install required tools
        if: ${{ steps.comment-cmd.outputs.continue == 'true' }}
        run: |
          if which http > /dev/null 2>&1; then
            echo "httpie is already installed."
          else
            python -m pip install httpie
          fi

      - name: Update preview deployment on Vercel
        if: ${{ steps.comment-cmd.outputs.continue == 'true' }}
        run: |
          domain="${TENANT}.${{ steps.comment-cmd.outputs.environment }}.app-prop-test.com"

          # get binded branch from domain
          echo "Get Preview Branch for Domain: ${domain}"
          response=$(http --check-status get "https://api.vercel.com/v9/projects/${VERCEL_PROJECT_NAME}/domains/${domain}" \
            "Authorization:Bearer ${VERCEL_TOKEN}" \
            "slug==${VERCEL_TEAM_SLUG}")
          prev_branch=$(echo "${response}" | jq -r '.gitBranch')
          next_branch="${{ steps.comment-cmd.outputs.ref }}"

          echo "DEPLOY_ENV=${{ steps.comment-cmd.outputs.environment }}" >> $GITHUB_ENV
          echo "PREV_BRANCH=${prev_branch}" >> $GITHUB_ENV
          echo "NEXT_BRANCH=${next_branch}" >> $GITHUB_ENV

          # get envs
          echo "Get Environment Variables for Preview Branch: ${prev_branch}"
          prev_env_vars=$(http --check-status get "https://api.vercel.com/v10/projects/${VERCEL_PROJECT_NAME}/env" \
            "Authorization:Bearer ${VERCEL_TOKEN}" \
            "slug==${VERCEL_TEAM_SLUG}" \
            "gitBranch==${prev_branch}")

          echo "Change Preview Branch: ${prev_branch} -> ${next_branch}"

          # update assigned branch of env vars in Preview
          for env_id in $(echo "${prev_env_vars}" | jq -c -r '.envs[].id'); do
            http --check-status --ignore-stdin patch "https://api.vercel.com/v10/projects/${VERCEL_PROJECT_NAME}/env/${env_id}" \
              "Authorization:Bearer ${VERCEL_TOKEN}" \
              "slug==${VERCEL_TEAM_SLUG}" \
              "gitBranch=${next_branch}"
          done

          # update assigned branch of domain in Preview
          http --check-status --ignore-stdin patch "https://api.vercel.com/v9/projects/${VERCEL_PROJECT_NAME}/domains/${domain}" \
            "Authorization:Bearer ${VERCEL_TOKEN}" \
            "slug==${VERCEL_TEAM_SLUG}" \
            "gitBranch=${next_branch}"

          # deploy Preview branch
          http --check-status --ignore-stdin post https://api.vercel.com/v13/deployments \
            "Authorization:Bearer ${VERCEL_TOKEN}" \
            "slug==${VERCEL_TEAM_SLUG}" \
            "name=${VERCEL_PROJECT_NAME}" \
            "gitSource[type]=github" \
            "gitSource[org]=${GH_ORG}" \
            "gitSource[repo]=${GH_REPO}" \
            "gitSource[ref]=${next_branch}"

      - name: Notify environments via Slack
        if: ${{ steps.comment-cmd.outputs.continue == 'true' && success() }}
        run: |
          response=$(http get "https://api.vercel.com/v9/projects/${VERCEL_PROJECT_NAME}/domains" \
            "Authorization:Bearer ${VERCEL_TOKEN}" \
            "slug==${VERCEL_TEAM_SLUG}")

          # construct message
          message=$':rocket: *Vercel Deploy Status*\n```'
          while read -r line; do
            message="${message}"$'\n'"- ${line}"
          done < <(echo "$response" | jq -r '.domains[] | "\(.name) -> \(.gitBranch)"')
          message="${message}"$'\n```\n'":arrows_counterclockwise: *Diff* [${DEPLOY_ENV}] ${PREV_BRANCH} -> ${NEXT_BRANCH}"

          http --ignore-stdin post "https://slack.com/api/chat.postMessage" \
            "Authorization:Bearer ${SLACK_BOT_TOKEN}" \
            "channel=${SLACK_NOTIFY_CHANNEL_ID}" \
            "text=${message}"

      - name: Notify failure via Slack
        if: ${{ steps.comment-cmd.outputs.continue == 'true' && failure() }}
        run: |
          message=":x: *Vercel Deploy Failed* [${DEPLOY_ENV}] ${PREV_BRANCH} -> ${NEXT_BRANCH}"

          http --ignore-stdin post "https://slack.com/api/chat.postMessage" \
            "Authorization:Bearer ${SLACK_BOT_TOKEN}" \
            "channel=${SLACK_NOTIFY_CHANNEL_ID}" \
            "text=${message}"
