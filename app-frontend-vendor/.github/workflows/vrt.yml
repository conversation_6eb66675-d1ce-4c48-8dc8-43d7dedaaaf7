name: Visual Regression Test
on:
  push:
    branches:
      - main
  pull_request:

concurrency:
  group: '${{ github.workflow }}-${{ github.event_name }}-${{ github.ref }}'
  cancel-in-progress: true

jobs:
  vrt:
    runs-on: ubuntu-latest
    env:
      outdated-comment-action: minimize
      matching-threshold: 0.2
      threshold-rate: 0
      threshold-pixel: 0
      target-hash: ''
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Ignore reg-suit params when push
        if: github.event_name == 'push'
        run: |
          echo "outdated-comment-action=" >> $GITHUB_ENV

      - name: Set the commit hash of the branch point as the comparison target
        run: |
          git fetch origin ${{ github.base_ref }}
          echo "target-hash=$(git merge-base --fork-point origin/${{ github.base_ref }})" >> $GITHUB_ENV

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: "package.json"

      - name: Install dependencies
        run: npm install

      - name: Japanese Font Install
        run: sudo apt install fonts-noto-cjk

      - name: Install Chrome for Puppeteer
        run: npx puppeteer browsers install chrome

      - name: Build Storybook
        run: npm run build-storybook

      - name: Run visual regression tests
        run: npm run vrt:screenshot

      - name: Visual regression test
        uses: reg-viz/reg-actions@v2
        with:
          github-token: '${{ secrets.GITHUB_TOKEN }}'
          image-directory-path: '__screenshots__'
          outdated-comment-action: ${{ env.outdated-comment-action }}
          matching-threshold: ${{ env.matching-threshold }}
          threshold-rate: ${{ env.threshold-rate }}
          threshold-pixel: ${{ env.threshold-pixel }}
          target-hash: ${{ env.target-hash }}
