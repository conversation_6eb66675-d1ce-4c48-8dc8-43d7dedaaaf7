name: Expression Counter

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  count-expressions:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup base commit
        run: |
          git fetch origin ${{ github.base_ref }}
          BASE_COMMIT=$(git merge-base --fork-point origin/${{ github.base_ref }})
          echo "BASE_COMMIT=$BASE_COMMIT" >> $GITHUB_ENV
          echo "HEAD_COMMIT=${{ github.sha }}" >> $GITHUB_ENV

      - name: Count and Comment
        uses: actions/github-script@v7
        env:
          BASE_COMMIT: ${{ env.BASE_COMMIT }}
          HEAD_COMMIT: ${{ env.HEAD_COMMIT }}
        with:
          script: |
            const script = require('./scripts/count-expressions/index.js');
            await script({github, context, exec, core});