# Vercelのビルドを、手動で実行します
# 本番環境、test1、test2ごとに
# (1) Vercelへのデプロイメント
# (2) その結果をPRにコメント
name: Vercel Build on PR

on:
  workflow_dispatch: # 手動でのビルドトリガー
    inputs:
      pr_number:
        description: 'Pull Request Number'
        required: true
      branch_name:
        description: 'Branch Name'
        required: true
      deployment_environment: # デプロイ環境の選択
        type: choice
        description: 'Select Deployment Environment'
        required: true
        default: 'test1-app-frontend-vendor' # デフォルト値を設定
        options: # プルダウンの選択肢
          - test1-app-frontend-vendor
          - test2-app-frontend-vendor
          - vendor.haposoft-1.app-prop-test.com
          - vendor.haposoft-2.app-prop-test.com

jobs:
  check_pr_status_is_open:
    runs-on: ubuntu-latest
    outputs:
      pr_status: ${{ steps.check_pr.outputs.status }}
    steps:
      - name: Check PR Status
        id: check_pr
        run: |
          pr_status=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
          "https://api.github.com/repos/${{ github.repository }}/pulls/${{ github.event.inputs.pr_number }}")
          status=$(echo $pr_status | jq -r '.state')
          echo "status=$status" >> $GITHUB_OUTPUT
          echo "PR status: $status"

  deploy:
    runs-on: ubuntu-latest
    needs: check_pr_status_is_open
    if: needs.check_pr_status_is_open.outputs.pr_status == 'open'
    steps:
      - name: Check out code
        uses: actions/checkout@v2

      # ビルドを実行し、デプロイする
      - name: Trigger Vercel Build
        id: deploy
        run: |
          set -x  # デバッグ用に詳細なコマンド出力を有効化

          echo "Starting deployment for ${{ github.event.inputs.deployment_environment }} on branch: ${{ github.event.inputs.branch_name }}"

          response=$(curl -X POST "https://api.vercel.com/v13/deployments?teamId=${{ secrets.TEAM_ID }}" \
          -H "Authorization: Bearer ${{ secrets.VERCEL_TOKEN }}" \
          -H "Content-Type: application/json" \
          -d "{
            \"name\": \"${{ github.event.inputs.deployment_environment }}\",
            \"gitSource\": {
              \"type\": \"github\",
              \"repoId\": \"${{ secrets.REPO_ID }}\",
              \"ref\": \"${{ github.event.inputs.branch_name }}\"
            },
            \"target\": \"production\"
          }")

          # レスポンスの内容をデバッグ表示（必要に応じて）
          echo "Deployment response: $response"

          # deployment_id を抽出
          deployment_id=$(echo "$response" | jq -r '.id')

          # deployment_id を出力変数として設定
          echo "deployment_id=$deployment_id" >> $GITHUB_OUTPUT

          # エラーチェック
          if ! echo "$response" | jq -e . > /dev/null; then
              echo "Invalid JSON response: $response"
              exit 1
          fi

          if [[ "$(echo $response | jq -r '.error')" != "null" ]]; then
              echo "Error deploying to Vercel: $(echo $response | jq -r '.error.message')"
              exit 1
          fi



