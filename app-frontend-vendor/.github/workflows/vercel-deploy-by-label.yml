name: Vercel Build on PR labeled

# 定数を環境変数として定義
env:
  LABEL_TEST1: "test1にデプロイ"
  LABEL_TEST2: "test2にデプロイ"
  LABEL_HAPOSOFT1: "haposoft-1にデプロイ"
  LABEL_HAPOSOFT2: "haposoft-2にデプロイ"
  LABEL_DEV2: "dev2にデプロイ"

  ENV_TEST1: "test1-app-frontend-vendor"
  ENV_TEST2: "test2-app-frontend-vendor"
  ENV_HAPOSOFT1: "vendor.haposoft-1.app-prop-test.com"
  ENV_HAPOSOFT2: "vendor.haposoft-2.app-prop-test.com"
  ENV_DEV2: "develop2-app-frontend-vendor"
on:
  pull_request:
    types: [labeled]

jobs:
  # 1 PRイベントから必要な変数（pr_number, branch_name, deployment_environment）を抽出
  set-vars:
    runs-on: ubuntu-latest
    outputs:
      pr_number: ${{ steps.set_vars.outputs.pr_number }}
      branch_name: ${{ steps.set_vars.outputs.branch_name }}
      deployment_environment: ${{ steps.set_vars.outputs.deployment_environment }}
    steps:
      - name: Set variables from PR event and label
        id: set_vars
        shell: bash
        run: |
          label="${{ github.event.label.name }}"
          echo "Label received: $label"
          if [ "$label" == "${{ env.LABEL_TEST1 }}" ]; then
            env_name="${{ env.ENV_TEST1 }}"
          elif [ "$label" == "${{ env.LABEL_TEST2 }}" ]; then
            env_name="${{ env.ENV_TEST2 }}"
          elif [ "$label" == "${{ env.LABEL_HAPOSOFT1 }}" ]; then
            env_name="${{ env.ENV_HAPOSOFT1 }}"
          elif [ "$label" == "${{ env.LABEL_HAPOSOFT2 }}" ]; then
            env_name="${{ env.ENV_HAPOSOFT2 }}"
          elif [ "$label" == "${{ env.LABEL_DEV2 }}" ]; then
            env_name="${{ env.ENV_DEV2 }}"
          else
            echo "Unrecognized label: $label"
            exit 1
          fi
          pr_number="${{ github.event.pull_request.number }}"
          branch_name="${{ github.event.pull_request.head.ref }}"
          echo "pr_number=$pr_number" >> $GITHUB_OUTPUT
          echo "branch_name=$branch_name" >> $GITHUB_OUTPUT
          echo "deployment_environment=$env_name" >> $GITHUB_OUTPUT

  # 2 Vercelへのビルド・デプロイ実行
  deploy:
    needs: set-vars
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v2
        with:
          ref: ${{ needs.set-vars.outputs.branch_name }}
      - name: Trigger Vercel Build
        id: deploy
        shell: bash
        run: |
          echo "environment: ${{ needs.set-vars.outputs.deployment_environment }}"
          echo "branch: ${{ needs.set-vars.outputs.branch_name }}"
          set -x
          echo "Starting deployment for ${{ needs.set-vars.outputs.deployment_environment }} on branch: ${{ needs.set-vars.outputs.branch_name }}"
          response=$(curl -X POST "https://api.vercel.com/v13/deployments?teamId=${{ secrets.TEAM_ID }}" \
            -H "Authorization: Bearer ${{ secrets.VERCEL_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d "{
              \"name\": \"${{ needs.set-vars.outputs.deployment_environment }}\",
              \"gitSource\": {
                \"type\": \"github\",
                \"repoId\": \"${{ secrets.REPO_ID }}\",
                \"ref\": \"${{ needs.set-vars.outputs.branch_name }}\"
              },
              \"target\": \"production\"
            }")

          # レスポンスの内容をデバッグ表示
          echo "Deployment response: $response"
          deployment_id=$(echo "$response" | jq -r '.id')
          echo "deployment_id=$deployment_id" >> $GITHUB_OUTPUT

          if ! echo "$response" | jq -e . > /dev/null; then
              echo "Invalid JSON response: $response"
              exit 1
          fi

          if [[ "$(echo $response | jq -r '.error')" != "null" ]]; then
              echo "Error deploying to Vercel: $(echo $response | jq -r '.error.message')"
              exit 1
          fi
