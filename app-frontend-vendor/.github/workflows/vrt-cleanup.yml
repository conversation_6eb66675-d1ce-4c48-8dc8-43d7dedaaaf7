name: Cleanup VRT Result
permissions:
  contents: write
  pull-requests: write

on:
  pull_request:
    types:
      - closed
jobs:
  cleanup:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout gh-pages branch
        uses: actions/checkout@v4
        with:
          ref: gh-pages

      - name: Remove Preview Build
        run: |
          rm -rf ./vrt/${{ github.event.pull_request.number }}
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git add -A
          git commit -m "Remove Preview Build for PR #${{ github.event.pull_request.number }}" --allow-empty
          git push

      - name: Leave PR comment
        uses: actions/github-script@v7
        with:
          script: |
            const issue_number = context.issue.number;
            const owner = context.repo.owner;
            const repo = context.repo.repo;
            const mark = "<!-- preview deployment comment -->";
            const message = `:robot: VRTの結果は削除されました\n\n${mark}`;
            const comments = await github.rest.issues.listComments({
              owner,
              repo,
              issue_number
            });
            const comment = comments.data.find(comment => comment.body.includes(mark));
            if (comment) {
              await github.rest.issues.updateComment({
                owner,
                repo,
                comment_id: comment.id,
                body: message
              });
            }
