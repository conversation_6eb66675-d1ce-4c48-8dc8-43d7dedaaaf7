name: Slack Notification on PR Merge

on:
  pull_request:
    types: [closed]

jobs:
  notify-slack:
    if: github.event.pull_request.merged == true && github.event.pull_request.base.ref == 'main'
    runs-on: ubuntu-latest

    steps:
      - name: Send Slack notification
        env:
          SLACK_BACK_MERGE_NOTIFIER_TOKEN: ${{ secrets.SLACK_BACK_MERGE_NOTIFIER_TOKEN }}
        run: |
          CHANNEL_ID="C02JE6HBED7"  # 通知先は「開発」チャンネル
          curl -X POST -H "Authorization: Bearer $SLACK_BACK_MERGE_NOTIFIER_TOKEN" -H "Content-Type: application/json" \
          --data "{
            \"channel\": \"$CHANNEL_ID\",
            \"text\": \"mainブランチが更新されました。\n開発者は自身のブランチにmainブランチをバックマージし、必要に応じてコンフリクトの解消を行なってください。\"
          }" https://slack.com/api/chat.postMessage
