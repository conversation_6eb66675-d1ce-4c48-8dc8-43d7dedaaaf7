name: CI

on: pull_request

jobs:
  ci:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # フルヒストリーを取得してPR差分の比較ができるようにする
      
      - name: Get changed files
        id: changed-files
        run: |
          # PRのベースブランチを取得
          BASE_REF=${{ github.base_ref || 'main' }}
          
          # 変更されたすべてのファイルのリストを取得してスペース区切りにする
          CHANGED_FILES=$(git diff --name-only --diff-filter=ACMRT origin/$BASE_REF HEAD | tr '\n' ' ')
          
          # 変更がなければ何もしない
          if [ -z "$CHANGED_FILES" ]; then
            echo "No files changed, will skip lint"
            CHANGED_FILES=""
          else
            echo "Changed files: $CHANGED_FILES"
          fi
          
          # 環境変数にセット
          echo "files=$CHANGED_FILES" >> $GITHUB_OUTPUT
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: "package.json"
          cache: "npm"
          
      - name: Install dependencies
        run: npm install
        
      - name: <PERSON><PERSON> changed files
        run: |
          if [ -z "${{ steps.changed-files.outputs.files }}" ]; then
            echo "No files changed, skipping lint"
          else
            echo "Lin<PERSON> changed files only"
            npx @biomejs/biome check --no-errors-on-unmatched ${{ steps.changed-files.outputs.files }}
          fi
          
      - name: Type check
        run: npm run type-check
        
      - name: Test
        run: npm run test
