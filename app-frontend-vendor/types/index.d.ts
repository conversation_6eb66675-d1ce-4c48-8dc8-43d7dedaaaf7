declare module "*.svg" {
  const content: React.FC<React.SVGProps<SVGElement>>;
  export default content;
}

interface DriveExplorerData {
  partner_id: number;
  id: number;
  vendor_id: number;
  type: string;
  parent: number | null;
  name: string;
  size: number;
  s3_path: string;
  thumbnail_path: string | null;
  created_at: string;
  updated_at: string;
  depth: number;
  is_limit_download_all: boolean;
  partner?: {
    name: string;
  };
  content?: string;
}

type DriveExplorerDataList = {
  files: DriveExplorerData[];
  Breadcrumbs: DriveBreadcrumbItem[];
  collab_files: DriveExplorerData[];
};

type productStatusList =
  | "未対応"
  | "案件化"
  | "アポ獲得"
  | "商談中"
  | "口頭合意"
  | "受注"
  | "保留"
  | "失注";

type Product = {
  product_id: number;
  vendor_id: number;
  product_name: string;
  product_status: string;
  product_status_name: string;
  sales_status: string;
  sales_status_name: string;
  created_at: string | null;
  updated_at: string | null;
};

type LeadProduct = {
  approval_status: string;
  approval_status_name: string;
  negotiation_status: string;
  negotiation_status_name: string;
  vendor_linked_partner_id: number;
} & Product;

type Lead = {
  [key: string]: any;
  lead_id: number;
  lead_record_id: number;
  hex_record_id: string;
  lead_name: string;
  prefecture_id: number | null;
  postal_code: string | null;
  address: string | null;
  tel: string | null;
  url: string | null;
  memo: string | null;
  number_of_employees: number | null;
  lead_status: number;
  lead_status_name: string;
  partner_id: number;
  partner_name: string;
  partner_user_id: number | null;
  partner_user_name: string;
  created_at: string;
  updated_at: string;
  managed_partner_id: number;
  vendor_id: number;
  vendor_linked_partner_id: number;
  custom: CustomColumn[];
  products: LeadProduct[];
  partner_collaboration_id: string;
  partner_avatar_url: string | null;
  partner_logo_url: string | null;
};

type LeadProfile = { [key: string]: string };

type Partner = {
  managed_partner_id: number;
  managed_partner_record_id: number;
  partner_collaboration_id: string;
  managed_partner_hex_id: string;
  contract_status: number;
  contract_status_name: string;
  link_status: string;
  link_status_name: string;
  vendor_id: number;
  vendor_user_id: number | null;
  vendor_user_name: string | null;
  vendor_avatar_url: string | null;
  managed_partner_name: string;
  managed_partner_name_kana: string | null;
  prefecture_id: number | null;
  postal_code: string | null;
  address: string | null;
  number_of_employees: number | null;
  tel: string | null;
  url: string | null;
  memo: string | null;
  created_at: string;
  updated_at: string;
  company_logo: string | null;
  last_login_at: string | null;
  managed_partner_custom_columns: CustomColumn[];
  custom: CustomColumn[];
  products: Product[];
  is_parent: number;
  parent_partner_id: number | null;
  parent_partner_name: string | null;
  request_sent_email: string | null;
  register_expire_date: string | null;
  partner_name: string | null;
};

type ParentPartnerCandidate = {
  managed_partner_id: number;
  managed_partner_name: string;
};

type ParentPartnerUserCandidate = {
  partner_user_id: number;
  partner_user_name: string;
};

type ChildPartners = {
  child_partners: ChildPartnerInfo[];
};

type ChildParterInfo = {
  managed_partner_id: number;
  managed_partner_record_id: number;
  managed_partner_hex_id: string;
  managed_partner_name: string;
  prefecture_id: number;
  vendor_user_name: string;
};

type LinkActivePartner = {
  vendor_linked_partner_id: number;
  partner_name: string;
  partner_id: number;
};

type newPartner = {
  name: string;
  prefecture_id: number;
  postal_code: string;
  address: string;
  tel: string;
  url: string;
  memo: string;
  number_of_employees: number;
  contract_id: number;
  vendor_user_id: number;
};

type newUser = {
  name: string;
  name_kana: string;
  email: string;
  password: string;
  tel: string;
  role: number;
  division: string;
};

type Vendor = {
  id: number;
  vendor_collaboration_id: string;
  name: string;
  name_kana: string;
  tel: string;
  prefecture_id: string;
  postal_code: string;
  address: string;
  vendor_logo: string | null;
  file_name: string | null;
  created_at: string | null;
  updated_at: string | null;
};

type SessionUser = {
  id: number;
  user_id: number;
  user_name: string;
  user_role: number;
  user_email: string;
  user_division: string;
  vendor_id: number;
  vendor_name: string;
  vendor_collaboration_id: string;
  vendor_logo: string;
  avatar_url: string | null;
  dashboard_view_permission: number; // デモダッシュボード表示用の権限
  dashboard_permission: number; // ダッシュボード表示権限
  is_api_integration_allowed: number;
};

type VendorUser = {
  password: string;
  last_login_at: string;
  role_name: string;
} & User;

type PartnerUser = {
  custom: CustomColumn[];
  partner_user_custom_columns: CustomColumn[];
  division: string;
  division_name: string;
  division_detail: string;
  position: string;
  position_name: string;
  partner_name: string;
  partner_user_name: string;
  partner_id: number;
  partner_user_id: number;
  email: string;
  tel: string;
  created_at: string;
  updated_at: string;
  password: string;
  register_expire_date: string | null;
  contract_status: number;
  contract_status_name: string;
  link_status: string;
  link_status_name: string;
  vendor_id: number;
  vendor_user_id: number | null;
  vendor_user_name: string | null;
  vendor_avatar_url: string | null;
  last_login_at: string;
  is_parent: number;
  parent_partner_id: number;
  parent_partner_name: string;
  partner_team_member_id: number | null;
  partner_teams?: LinkedTeam[];
  address: string | null;
  postal_code: string | null;
  gift_profile: {
    address: string | null;
    postal_code: string | null;
  };
} & User;

type LinkedTeam = {
  id: number;
  partner_id: number;
  partner_name: string;
  managed_partner_id: number;
  managed_partner_name: string;
  role_name?: string;
};

type User = {
  id: number;
  vendor_id: number;
  partner_name: string;
  name: string;
  position_id: string;
  employment_type: string;
  has_access: boolean;
  file_name: string;
  s3_path: string | null;
  logo_url: string | null;
  name_kana: string;
  email: string;
  tel: string;
  role_id: number;
  role: string;
  division: string;
  created_at: string | null;
  updated_at: string | null;
};

type ManagedUser = {
  password: string;
  last_login_at: string;
  two_factor_enabled: boolean;
} & User;

type Prefecture = {
  label: string;
  value: number;
};

type NegotiationStatus = {
  id: number;
  name: string;
  code: string;
};

type NegotiationStatusList = {
  [key: string]: NegotiationStatus;
};

type ApprovalStatus = {
  id: number;
  name: string;
};

type ApprovalStatusList = {
  [key: string]: ApprovalStatus;
};

type ContractStatus = {
  name: string;
  id: number;
};
type ContractStatusList = {
  [key: string]: ContractStatus;
};

type linkStatus = {
  name: string;
  id: number;
};

type LinkStatusList = {
  [key: string]: linkStatus;
};

type leadStatus = {
  name: string;
  id: number;
};

type LeadStatusList = {
  [key: string]: leadStatus;
};

type columnPermissionStatus = {
  name: string;
  id: number;
};

type ColumnPermissionStatusList = {
  [key: string]: columnPermissionStatus;
};

type ColumnPermissionStatusListCompare = {
  [key: string]: number[];
};

type DataMasters = {
  prefectures: Prefecture[];
  negotiation: NegotiationStatusList;
  contract: ContractStatusList;
  link: LinkStatusList;
  lead: LeadStatusList;
  column_permissions: ColumnPermissionStatusList;
};

type CustomColumn = {
  column_id: number;

  /** 大文字・小文字で区別されないです。 */
  column_name: string;

  column_label: string;
  type_status: string;
  type_name: string;
  default: number;
  current: string | number;
  select_id: number;
  updated_at: string;
  created_at: string;
  select_contents: SelectItem[];
  isModified: boolean;
  id: number;
  vendor_user_id: number;
  partner_user_id: number;
  sort_order: number;
  file_id: number;
  can_edit: boolean;
  can_read: boolean;
  write_permission: string;
  read_permission: string;
};

type CustomUserColumn = {
  column_id: number;
  column_name: string;
  column_label: string;
  type_status: string;
  type_name: string;
  default: number;
  current: string | number;
  select_id: number;
  updated_at: string;
  created_at: string;
  select_contents: SelectItem[];
  isModified: boolean;
  id: number;
  vendor_user_id: number;
  partner_user_id: number;
  sort_order: number;
  file_id: number;
  can_edit: boolean;
  write_permission: string;
  can_read: boolean; // 全てのベンダーユーザが閲覧可能なため、利用しない項目
  read_permission: string; // 全てのベンダーユーザが閲覧可能なため、利用しない項目
};

type SelectItem = {
  id: number;
  column_id: number;
  select_id: number;
  select_value: string;
  // ドロップダウンの選択肢の表示順
  sort_order: number;
};

type ModalTypes =
  | "customColumn"
  | "customUserColumn"
  | "newProduct"
  | "editProduct"
  | "subtable_make"
  | "subtable_edit"
  | "subtable_column_edit"
  | "subtable_column_make"
  | "editCustomColumn"
  | "editCustomUserColumn"
  | "newWebTest"
  | "newQuestion"
  | "shareTest"
  | "newTraining"
  | null;

type EditProductType = {
  product_id: number;
  name: string;
  status: string;
};

type driveDirectoryItem = {
  id: number;
  vendor_id: number;
  type: "directory" | "normal";
  parent: null | number;
  name: string;
  size: number;
  s3_path: string;
  created_at: string;
  updated_at: string;
  depth: number;
};

type driveDirectory = {
  files: driveDirectoryItem[];
  breadcrumbs: DriveBreadcrumbItem[];
  collab_files: driveDirectoryItem[];
};

type DriveBreadcrumbItem = {
  id: number;
  name: string;
};

type DriveModifierMode =
  | "renameFile"
  | "renameCollabFile"
  | "renameFolder"
  | "renameCollabFolder"
  | "newFolder"
  | "newCollabRootFolder"
  | "newCollabSubFolder"
  | "newFile"
  | "share"
  | "sharePortal"
  | "deleteFolder"
  | "deleteCollabFolder"
  | "deleteFile"
  | "newPortal";

type DriveModifier = {
  fileID: number | undefined;
  mode: DriveModifierMode | undefined;
  filename: string | undefined;
  isLimitDownloadAll?: boolean;
  isLimitDownloadPartner?: boolean;
  close: () => void;
  opened: boolean;
  mutate: () => void;
  mutatePortal?: () => void;
  type?: "directory" | "normal";
};

type DriveViewData = {
  url: string;
};

type DriveItemData = string | null;

type DriveItemInfo = {
  id: number;
  vendor_id: number;
  type: "directory" | "normal";
  parent: null | number;
  name: string;
  size: number;
  s3_path: string;
  created_at: string;
  updated_at: string;
  depth: number;
  breadcrumbs: DriveBreadcrumbItem[];
  is_limit_download_all: boolean;
  thumbnail_path?: string;
  content?: string;
};

type ModalInnerProps = {
  mode: DriveModifierMode;
  filename?: string;
  fileID?: number;
  opened: boolean;
  isLimitDownloadAll?: boolean;
  isLimitDownloadPartner?: boolean;
  type?: "directory" | "normal";
};

type DrivePermissionPartner = {
  partner_id: number;
  id: number;
  partner_collaboration_id: string;
  name: string;
  name_kana: string;
  tel: string;
  address: string;
  partner_avatar_url: string;
  partner_logo: string;
  partner_name: string;
  has_access: boolean;
};

type DriveUser = {
  id: number;
  partner_id: number;
  partner_name: string;
  partner_user_id: number;
  partner_user_name: string;
  partner_avatar_url: string;
  file_id: number;
  email: string;
  created_at: string;
  updated_at: string;
};

type DriveUserLog = {
  [companyName: string]: DriveUser[];
};

type DrivePortal = {
  id: number;
  name: string;
  sort_order: number;
  vendor_id: number;
  created_at: string;
  updated_at: string;
};

type Subtable = {
  sub_table_id: number;
  sub_table_name: string;
  read_permission: string;
  vendor_id: number;
  created_at: string;
  updated_at: string;
};

type SubtableSelectContent = {
  select_id: number;
  column_id: number;
  select_value: string;
  sort_order: number;
  created_at: string;
  updated_at: string;
  is_default: number;
};

type SubtableRecordType =
  | "SELECT"
  | "STRING"
  | "INTEGER"
  | "VENDOR_USER"
  | "DATE"
  | "PARTNER_USER"
  | "FILE"
  | "LONG_TEXT";

type SubtableColumn = {
  column_id: number;
  sub_table_id: number;
  column_label: string;
  column_name: string;
  type: number;
  sort_order: number;
  created_at: string;
  updated_at: string;
  default: string;
  type_name: SubtableRecordType;
  select_contents?: SubtableSelectContent[];
  isModified: boolean; // 変更検知用
  can_edit: boolean;
  can_read: boolean;
  write_permission: string;
  read_permission: string;
};

type SubtableRecord = {
  sub_table_id: number;
  sub_table_name: string;
  vendor_id: number;
  created_at: string;
  updated_at: string;
  columns: SubtableColumn[];
  records: SubtableRecordData[];
};

type SubtableRecordData = {
  record_id: number;
  managed_partner_id: number;
  sub_table_id: number;
  order: number;
  created_at: string;
  updated_at: string;
  values: SubtableRecordValue[];
};

type SubtableRecordValue = {
  id: number;
  sub_table_column_id: number;
  sub_table_record_id: number;
  value: string | null;
  select_id: number | null;
  created_at: string;
  updated_at: string;
  vendor_user_id?: number;
  partner_user_id?: number;
  file_id: number;
  type: number;
};

type chatChannel = {
  channel_id: number;
  name: string;
  badge_count: number;
  channel_pusher: Channel | null;
  unread?: boolean;
};

type chatMessage = {
  chat_post_id: number;
  is_replies_unreads?: boolean;
  channel_id: number;
  channel_name?: string;
  content: string;
  deleted: number;
  parent_id: number | null;
  thread_users: thread_users[];
  attached_files: attached_files[];
  child_count: number;
  user: {
    name: string;
    vendor_user_id: number | null;
    partner_user_id: number | null;
    logo_url: string | null;
    file_name: string;
  };
  timestamp: {
    created_at: string;
    updated_at: string;
  };
  isUpdated: boolean;
  isDeleted: boolean;
  isFirstPage?: boolean;
  last_reply_at?: string;
  replies?: chatMessage[];
  total_replies?: number | string;
  unread: number;
  vendor_user_ids: number[];
  partner_user_ids: number[];
  mentions?: {
    vendor_user_id: number | string | null;
    partner_user_id: number | string | null;
  }[];
};

type chatMessagesListResponse = {
  data: chatMessage[];
  is_first_page: boolean;
  total_count: number;
};

type attached_files = {
  id: number;
  size: number;
  name: string;
  url: string;
  contentType: string;
};

type thread_users = {
  name: string;
  vendor_user_id: number | null;
  partner_user_id: number | null;
  logo_url: string | null;
  file_name: string;
};

type chatMentionableUser = {
  type: number;
  label: string;
  text: string;
  vendor_user_id?: number;
  partner_user_id?: number;
};

type chatUser = {
  type: number;
  role: number;
  name: string;
  vendor_user_id: number;
  partner_user_id: number;
  company_name: string;
};

type WebTest = {
  id: number;
  test_title: string;
  test_description: string | null;
  passing_mark: number | null;
  publish_status: string | null;
  created_at: string;
  updated_at: string;
  test_record_id: number; // ベンダー毎のテストID
};

type WebTestQuestion = {
  id: number;
  test_id: number;
  question_title: string;
  question_description: string;
  question_explanation: string;
  question_points: number;
  question_type: number;
  question_order: number;
  can_options_edit: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
};

type WebTestOption = {
  id: number;
  question_id: number;
  option_name: string;
  is_correct: number; // 0: 不正解, 1: 正解
  option_order: number;
  created_at: string;
  updated_at: string;
};

type WebTestAnswer = {
  partner_name: string;
  partner_user_name: string;
  partner_email: string;
  latest_attend_at: string;
  latest_result: boolean;
  passing_mark: number;
  latest_total_points: number;
  total_attendances: number;
};

type WebTestAttendanceQuestionList = {
  optionList: WebTestOption[];
} & WebTestQuestion;

type WebTestAnswerResultQuestion = {
  question_id: number;
  question_title: string;
  question_description: string;
  question_explanation: string;
  question_points: number;
  question_order: number;
  is_correct: boolean;
  user_answer: string;
  correct_answer: string;
  question_options: WebTestOption[];
  question_type: number;
};

type WebTestAnswerResult = {
  isPassing: boolean;
  passing_mark: number;
  total_points: number;
  questions: WebTestAnswerResultQuestion[];
};

type WebTestAnswerList = {
  question_id: number;
  answer: number[];
  question_type: number;
}[];

type JobStatus = {
  uuid: string;
  status: string;
  data_type: string;
  presigned_url: string | null; // 結果ファイルのダウンロードリンク。
  requested_at: string;
  exported_at: string | null;
};

type ColumnType = "FIXED_COLUMN" | "CUSTOM_COLUMN";

type ColumnValueInfo = {
  value: string;
  columnType: ColumnType;
  valueType: ValueType;
};

type SearchCondition = {
  id: number;
  name: string;
  sort_order: number;
  filters: {
    field: string;
    operator: string;
    value: string;
    is_fixed_column: boolean;
  }[];
  sorts: {
    sort_by: string;
    order_by: string;
    is_fixed_column: boolean;
  }[];
  columns: {
    field: string;
    sort_order: string;
    is_fixed_column: boolean;
  }[];
};
type FilterSortCondition = {
  column_type: string;
  fixed_column: string;
  custom_condition: string;
  order_by: string;
};

type SearchConditionColumnDic = {
  value: string;
  label: string;
  columnType: string;
  enable_sorting?: boolean;
};

type CriteriaOperatorDic = {
  value: string;
  label: string;
};

type CriteriaSelectItemDic = {
  value: string;
  label: string;
};

type SearchConditionColumns = {
  fixed_columns: { name: string; label: string }[];
  custom_columns: { name: string; label: string; enable_sorting: boolean }[];
};
// フィルター条件
type SearchConditionCriteria = {
  column_type: string;
  fixed_column: string | null;
  custom_column: string | null;
  operator_id: string;
  search_value: string;
};
// ソート条件
type SearchConditionSorts = {
  column_type: string;
  fixed_column: string | null;
  custom_column: string | null;
  order_by: string;
};
// 表示カラム
type SearchConditionFixedSetting = {
  column_type: string;
  fixed_column: string | null;
  custom_column: string | null;
  sort_order: number;
};

type CreateSearchConditionTmpForm = {
  name: string;
  criteria_target_columns: string[];
  criteria_operator_ids: string[];
  criteria_search_values: Array<string | date>;
  sort_target_column: string[];
  sort_order_by: string[];
  fixed_settings: SearchConditionFixedSetting[];
};
type CreateSearchConditionForm = {
  name: string;
  criteria: SearchConditionCriteria[];
  sorts: SearchConditionSorts[];
  fixed_settings: SearchConditionFixedSetting[];
};

type CreateSearchConditionSort = {
  column: string;
  order_by: string;
};
type SearchConditionChangeSortOrder = {
  id: number;
  sort_order: number;
};

type Training = {
  id: number;
  training_record_id: number;
  training_title: string;
  training_description: string | null;
  training_thumbnail_path: string | null;
  publish_status: string;
  progress: number;
  created_at: string;
  updated_at: string;
};

type TrainingModalInnerProps = {
  trainingID?: number | undefined;
  mode: TrainingModifierMode;
  opened: boolean;
};

type TrainingModifierMode = "newTraining" | "newContent" | "share";

type TrainingModifierProps = {
  trainingID: number | undefined;
  mode: TrainingModifierMode | undefined;
  close: () => void;
  opened: boolean;
  mutate?: () => void;
};

type TrainingPermissionPartner = {
  id: number;
  name: string;
  has_access: boolean;
};

type TrainingPermissionPartnerUser = {
  id: number;
  name: string;
  email: string;
  partner: number;
  partner_name: string;
  has_access: boolean;
};

type TrainingContent = {
  id: number;
  training_id: number;
  content_title: string;
  content_description: string | null;
  content_order: number;
  content_type: number;
  content_thumbnail_path: string | null;
  web_test_id: number | null;
  file_id: number | null;
  created_at: string;
  updated_at: string;
  content_name: string | null;
  is_available: boolean;
  is_published: boolean;
};

type TrainingResult = {
  id: number;
  training_id: number;
  attendance_status: number;
  vendor_user_id: number;
  partner_user_id: number;
  company_name: string;
  user_name: string;
  user_email: string;
  created_at: string;
  updated_at: string;
  first_completed_at: string | null;
};

type TrainingFilePreview = {
  url: string;
  name: string;
  is_limit_download_all: boolean;
  type: string;
};
type LeadCreationFormColumn = {
  column_id?: number;
  column_type: "FIXED" | "CUSTOM";
  name: string;
  value_type: ValueType;
  label: string;
  select_options?: LeadCreationFormSelectOption[];
  sort_order?: number;
  initial_value?: string;
  is_required?: boolean;
  is_visible?: boolean;
  read_permission?: number;
  write_permission?: number;
};

type LeadCreationFormSelectOption = {
  key: string;
  value: string;
  partner_id?: number;
};

type LeadCreationForm = {
  uuid: string;
  managed_partner_id: number;
  vendor_linked_partner_id: number;
  partner_collaboration_id: string;
  partner_name: string;
  title: string;
  display_title: string;
  form_url: string;
  description: string;
  is_enabled: boolean;
  is_mail_notified: boolean;
  created_at: string;
  updated_at: string;
};

type LeadCustomForm = {
  vendor_linked_partner_id: number | string | null;
  title: string;
  display_title: string;
  description: string;
  logo: File | null;
  is_enabled: boolean;
  is_partner_user_required: boolean;
  is_mail_notified: boolean;
};

type LeadCustomFormUpdateForm = LeadCustomForm & {
  is_logo_updated?: boolean;
};

type LeadCustomFormColumns = {
  column_id?: number;
  column_type: string;
  column_name: string;
  column_label: string;
  display_column_label: string;
  initial_value: string | null;
  is_visible: boolean;
  is_required: boolean;
  sort_order: number;
  value_type: ValueType;
  select_options?: LeadCreationFormSelectOption[];
  read_permission?: number;
  write_permission?: number;
};

type ValueType =
  | "STRING"
  | "INTEGER"
  | "DATE"
  | "SELECT"
  | "VENDOR_USER"
  | "PARTNER_USER"
  | "FILE"
  | "LONG_TEXT"
  | "DATETIME_LOCAL";

type ContactForm = {
  uuid: string;
  title: string;
  description: string;
  is_published: boolean;
  logo: string | null;
  priority: number;
  created_at: string;
  updated_at: string;
};

type ContactFormDetail = {
  uuid: string;
  title: string;
  description: string | null;
  is_description_visible: boolean;
  logo: File | null;
  logo_path: string | null;
  is_logo_updated?: boolean;
  thumbnail: File | null;
  thumbnail_path: string | null;
  is_thumbnail_updated?: boolean;
  is_published: boolean;
  vendor_assignee_type: ContactFromVendorAssigneeType | null;
  vendor_assignee_user_id?: number | null; // vendor_assignee_type === "MEMBER" の場合のみ使用
  vendor_assignee_user_name?: string | null; // vendor_assignee_type === "MEMBER" の場合のみ使用
  is_vendor_notified: boolean;
  is_partner_inquirer_notified: boolean;
  vendor_notified_type: ContactFormVendorNotifiedType | null;
  vendor_notified_users: { id: number; name: string }[] | null;
  priority: number; // 1 - 10
  created_at: string;
  updated_at: string;
  form_columns: ContactFormColumn[];
};

type ContactFromVendorAssigneeType =
  | "ALL_VENDOR_USER"
  | "ADMIN_GROUP"
  | "MEMBER_GROUP"
  | "MEMBER"
  | "NONE";

type ContactFormVendorNotifiedType = "ALL_VENDOR_USER" | "MEMBER";

type ContactFormColumn = {
  uuid: string;
  column_type: ContactFormColumnTypes;
  label: string;
  select_columns: ContactFormSelectColumn[] | null;
  description: string | null; // column_type === "DESCRIPTION" の場合のみ使用
  logo: File | null; // column_type === "LOGO" の場合のみ使用]
  logo_path: string | null; // column_type === "LOGO" の場合のみ使用
  is_required?: boolean;
  sort_order: number;
  is_new?: boolean;
};

type ContactFormColumnTypes =
  | "STRING"
  | "TEXT"
  | "DATE"
  | "CHECKBOX"
  | "RADIO"
  | "FILE"
  | "DESCRIPTION"
  | "LOGO"
  | "DATETIME_LOCAL";

type ContactFormSelectColumn = {
  label: string;
  uuid: string;
  sort_order: number;
};

type SharedPartner = {
  id: number;
  name: string;
  partner_name?: string;
  has_access?: boolean;
};

type SharedPartnerUser = {
  id: number;
  name: string;
  partner_id?: number;
  partner_name?: string;
  has_access: boolean;
};

type ContactTicketStatus =
  | "UN_RESOLVED"
  | "IN_PROGRESS"
  | "PENDING"
  | "RESOLVED";

type InboxType = "ALL" | "NEW" | "MY_TICKET" | "UNRESOLVED_BY_ME";

type InboxStatusType = ContactTicketStatus | InboxType;

type TicketType = {
  label: string;
  value: InboxType;
  apiQueryString: FetchTicketListRequest;
};

type AssigneeTypes = {
  value: ContactFromVendorAssigneeType;
  label: string;
};

type FetchTicketListRequest = {
  page?: number;
  status?: ContactTicketStatus;
  is_latest?: 1 | 0;
  is_assigned?: 1 | 0;
  keyword?: string;
};

type PageWithTransitionKeyGetter<
  C extends React.ComponentType = React.ComponentType,
> = C & {
  getTransitionKey?: () => string;
};

type InputFormProps = {
  id?: string | number;
  columnName: string;
  initialValue: string;
  formDisplay: string;
};

type InputItemRowProps = {
  inputForm: InputFormProps;
  index?: number;
  isDraggable?: boolean;
  moveRow?: (index: number, toIndex: number) => void;
  handleRemoveFromTable: (id: string | number) => void;
};

type DragItemInputFormProps = {
  inputForm: InputFormProps;
  onRemove?: () => void;
};

type DraggedItemInputForm = {
  type: string;
  index: number;
  inputForm: InputFormProps;
};

type SidebarProps = {
  inputForms: InputFormProps[];
  handleRemove: (id?: string | number | undefined) => void;
  setInputForm?: (inputForm: InputFormProps) => void;
};

type TableInputProps = {
  handleRemove: (id?: string | number) => void;
  handleReturnToSidebar: (inputForm: InputFormProps) => void;
};

type GiftHistory = {
  id: number;
  partner_user_name: string;
  send_date: string;
  subject: string;
  description: string;
  recipient_address: string;
  recipient_postal_code: string;
};
