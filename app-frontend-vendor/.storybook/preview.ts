import type { Preview } from "@storybook/react";
import { withScreenshot } from "storycap";
import "ress";

export const decorators = [
  withScreenshot, // Registration the decorator is required
];

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on.*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export default preview;
