import { useRouter } from "next/router";
import { useEffect } from "react";
import {
  useCurrentPagePath,
  usePrevPagePath,
  useSetCurrentPagePath,
  useSetPrevPagePath,
} from "utils/recoil/navigation/navigationState";

/**
 * ナビゲーション状態を管理するコンポーネント
 * pathnameが変更されたときにRecoil stateのprevPageとcurrentPageを更新する
 */
export const NavigationStateManager = () => {
  const router = useRouter();
  const setPrevPagePath = useSetPrevPagePath();
  const setCurrentPagePath = useSetCurrentPagePath();
  const currentPrevPage = usePrevPagePath();
  const currentPagePath = useCurrentPagePath();

  // pathnameが変更されたときにナビゲーション状態を更新
  useEffect(() => {
    // ページ遷移時に現在のページをprevPageに保存
    if (currentPagePath && currentPagePath !== router.pathname) {
      // 現在の値が新しい値と異なる場合のみ更新
      if (currentPrevPage !== currentPagePath) {
        setPrevPagePath(currentPagePath);
      }
    }

    // 現在のページを更新
    setCurrentPagePath(router.pathname);
  }, [
    router.pathname,
    setPrevPagePath,
    setCurrentPagePath,
    currentPrevPage,
    currentPagePath,
  ]);

  return null;
};
