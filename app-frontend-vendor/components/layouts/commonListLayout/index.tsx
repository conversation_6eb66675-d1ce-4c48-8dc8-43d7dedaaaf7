import styled from "@emotion/styled";
import { propcolors } from "styles/colors";

export const CommonListLayout = styled.div`
  height: calc(100% - 80px);
  display: grid;
  grid-template-rows: auto 1fr;
  gap: 0px;
  @media screen and (max-width: 512px) {
    height: 100%;
    overflow-y: scroll;
  }
  header {
    border-top: 1px solid #e8eaed;
    padding: 16px 24px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    @media screen and (max-width: 512px) {
      flex-direction: column;
      gap: 1rem;
    }
    .commonList-header {
      display: flex;
      align-items: center;
      &-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        background: ${propcolors.partnerRed};
        border: 2px solid ${propcolors.red[800]};
        border-radius: 100%;
        padding: 0.5rem;
        svg {
          fill: ${propcolors.white};
          width: 1.5rem;
          height: 1.5rem;
        }
      }
      &-breadcrumbs {
        font-size: 0.75rem;
      }
    }
    .header-buttons {
      display: flex;
      gap: 1rem;
    }
    button {
      text-align: center;
      overflow: hidden;
      height: 42px;
      &:hover {
        opacity: 0.8;
      }
    }
    h2 {
      display: flex;
      gap: 0.5rem;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      svg {
        width: 1.5rem;
        height: 1.5rem;
        fill: ${propcolors.black};
      }
    }
  }
`;
