import styled from "@emotion/styled";
import { propcolors } from "styles/colors";

export const SettingsLayout = styled.main`
  height: 100%;
  display: grid;
  grid-template-columns: auto 1fr;
  header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .header-buttons {
      display: flex;
      gap: 1rem;
    }
    button {
      text-align: center;
      border-radius: 5px;
      overflow: hidden;
      &:hover {
        opacity: 0.8;
      }
    }
    h2 {
      display: flex;
      gap: 0.5rem;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      svg {
        width: 1.5rem;
        height: 1.5rem;
        fill: ${propcolors.black};
      }
    }
  }
`;
