import styled from "@emotion/styled";
import React, { ReactNode, useEffect } from "react";
import { getMasters } from "utils/axios/getMasters";
import { useGetDataMasters, useSetDataMasters } from "utils/recoil/dataMasters";
import { parseCookies } from "nookies";
import {
  useSessionUser,
  useSetSessionUser,
} from "utils/recoil/sessionUserState";

type PresentationProps = {
  className?: string;
  children: ReactNode;
};

const Presentation: React.FC<PresentationProps> = ({ className, children }) => {
  return (
    <div id="layout-main" className={className}>
      {children}
    </div>
  );
};

const Styled = styled(Presentation)`
  height: 100svh;
  display: flex;
  grid-template-rows: auto 1fr;
  @media screen and (max-width: 512px) {
    grid-template-rows: 1fr auto;
    max-height: 100dvh;
  }
`;

const LayoutMain: React.FC<{ children: ReactNode }> = ({ children }) => {
  const masters = useGetDataMasters();
  const setMasters = useSetDataMasters();
  const setSessionUser = useSetSessionUser();
  const sessionUser = useSessionUser();
  useEffect(() => {
    if (parseCookies(null, ["user"]) && parseCookies(null, ["user"]).user) {
      const userCookies = JSON.parse(parseCookies(null, ["user"]).user);
      if (!sessionUser && userCookies) {
        setSessionUser({
          id: Number(userCookies.user_id),
          user_id: Number(userCookies.user_id),
          user_name: userCookies.user_name,
          user_role: Number(userCookies.user_role),
          user_email: userCookies.user_email,
          user_division: userCookies.user_division,
          vendor_id: Number(userCookies.vendor_id),
          vendor_name: userCookies.vendor_name,
          vendor_collaboration_id: userCookies.vendor_collaboration_id,
          vendor_logo: userCookies.vendor_logo,
          avatar_url: userCookies.avatar_url,
          dashboard_view_permission: userCookies.dashboard_view_permission, // デモダッシュボード表示用の権限
          dashboard_permission: userCookies.dashboard_permission, // ダッシュボード表示権限
          is_api_integration_allowed: userCookies.is_api_integration_allowed,
        });
      }
    }
  }, [sessionUser, setSessionUser]);
  useEffect(() => {
    if (sessionUser && !masters) {
      getMasters().then((res) => {
        setMasters({
          prefectures: res.prefectures,
          negotiation: res.negotiation,
          contract: res.contract,
          link: res.link,
          lead: res.lead,
          column_permissions: res.column_permissions,
        });
      });
    }
  }, [sessionUser, masters, setMasters]);
  return <Styled>{children}</Styled>;
};

export default LayoutMain;
