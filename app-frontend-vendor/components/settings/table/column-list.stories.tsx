import { <PERSON>a, StoryObj } from "@storybook/react/*";
import { ColumnList } from "./column-list";

const meta = {
  title: "shared/settings/table/column-list",
  component: ColumnList,
  args: {
    tableName: "Table Name",
    columns: [
      {
        columnLabel: "Column 1",
        type: "テキスト",
        selections: ["選択肢1", "選択肢2"],
      },
      {
        columnLabel: "Column 2",
        type: "テキスト",
        selections: ["選択肢1", "選択肢2"],
      },
      {
        columnLabel: "Column 3",
        type: "テキスト",
        selections: ["選択肢1", "選択肢2"],
      }
    ],
  },
} as Meta<typeof ColumnList>;

export default meta;

export const Default = {} as StoryObj<typeof meta>;
