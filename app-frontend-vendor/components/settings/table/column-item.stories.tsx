import { <PERSON>a, StoryObj } from "@storybook/react/*";
import { ColumnItem } from "./column-item";

const meta = {
  title: "shared/settings/table/column-item",
  component: ColumnItem,
  decorators: [
    (Story: any) => (
      <table style={{ width: "100%" }}>
        <Story />
      </table>
    ),
  ],
  args: {
    cellData: {
      columnName: "123123",
      columnLabel: "Column Name",
      type: "Type",
      selections: ["Selection 1", "Selection 2"],
    },
  },
} as Meta<typeof ColumnItem>;

export default meta;

export const Default = {} as StoryObj<typeof meta>;
