import { CSSObject } from "@emotion/react";
import {
  Delete16Filled,
  Edit16Filled,
  ReOrderDotsVertical20Filled,
} from "@fluentui/react-icons";
import { ActionIcon } from "@mantine/core";
import { propcolors } from "styles/colors";

type CellData = {
  columnLabel: string;
  type: string;
  selections: string[];
};

const tdBaseStyle: CSSObject = {
  padding: "7px 10px",
  verticalAlign: "middle",
};

export const ColumnItem = ({
  cellData,
  onDelete,
  onEdit,
}: {
  cellData: CellData;
  onDelete: () => void;
  onEdit: () => void;
}) => {
  return (
    <tr
      css={{
        backgroundColor: propcolors.white,
        fontSize: "14px",
        lineHeight: "1.5",
        ":not(:last-child)": {
          borderBottom: "1px solid #dee2e6",
        },
      }}
    >
      <td
        css={{
          ...tdBaseStyle,
          position: "relative",
          padding: "7px 10px 7px 50px",
        }}
      >
        <ReOrderDotsVertical20Filled
          css={{
            position: "absolute",
            top: "50%",
            left: "20px",
            height: "fit-content",
            transform: "translateY(-50%)",
          }}
        />
        {cellData.columnLabel}
      </td>
      <td css={tdBaseStyle}>{cellData.type}</td>
      <td css={tdBaseStyle}>
        {cellData.selections.map((selection) => (
          <p>{selection}</p>
        ))}
      </td>
      <td css={tdBaseStyle}>
        <ActionIcon variant="outline" onClick={onDelete}>
          <Delete16Filled />
        </ActionIcon>
        <ActionIcon variant="outline" onClick={onEdit}>
          <Edit16Filled />
        </ActionIcon>
      </td>
    </tr>
  );
};
