import { Add16Filled } from "@fluentui/react-icons";
import { NavLink } from "@mantine/core";
import { Button } from "features/workflow/button";
import { propcolors } from "styles/colors";

// TODO: この型は仮のもの 置き換える
type Table = { name: string };

export const TableList = ({
  listCategory,
  listName,
  tableList,
  onNewTable,
}: {
  listCategory: string;
  listName: string;
  onNewTable: () => void;
  tableList: Table[];
}) => (
  <div
    css={{
      display: "flex",
      flexDirection: "column",
      gap: "16px",
      width: "304px",
      backgroundColor: propcolors.white,
    }}
  >
    <div
      css={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        gap: "16px",
      }}
    >
      <div
        css={{
          display: "flex",
          flexDirection: "column",
          lineHeight: "1.3",
          fontWeight: "700",
        }}
      >
        <span css={{ fontSize: "12px" }}>{listCategory}</span>
        <span css={{ fontSize: "20px" }}>{listName}</span>
      </div>
      <Button
        variant="primary"
        size="xs"
        leftIcon={<Add16Filled />}
        onClick={onNewTable}
      >
        新規テーブル
      </Button>
    </div>
    <div>
      {tableList.map((table, i) => (
        <NavLink
          key={i}
          label={table.name}
          active={i === 0} // TODO: active のロジックを実装する
          color="gray"
          variant="filled"
          css={{ borderRadius: "4px" }}
        />
      ))}
    </div>
  </div>
);
