import { CSSObject } from "@emotion/react";
import {
  Add20Filled,
  Delete20Filled,
  Edit20Filled,
} from "@fluentui/react-icons";
import { ActionIcon } from "@mantine/core";
import { Button } from "features/workflow/button";
import { ComponentProps, useMemo, useState } from "react";
import { propcolors } from "styles/colors";
import { ColumnItem } from "./column-item";

const baseThStyle: CSSObject = {
  padding: "7px 10px",
  verticalAlign: "middle",
  textAlign: "left",
};

export const ColumnList = ({
  tableName,
  columns: initialColumns,
  onEditTableName,
  onDeleteTable,
  onAddColumn,
  onSave,
}: {
  tableName: string;
  columns: ComponentProps<typeof ColumnItem>["cellData"][];
  onEditTableName: () => void;
  onDeleteTable: () => void;
  onAddColumn: () => void;
  onSave: () => void;
}) => {
  const [columns, setcolumns] = useState(initialColumns);
  const isModified = useMemo(
    () => JSON.stringify(columns) !== JSON.stringify(initialColumns),
    [columns, initialColumns]
  );
  return (
    <div css={{ backgroundColor: propcolors.white }}>
      <div
        css={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "0 0 16px 16px",
        }}
      >
        <div css={{ display: "flex", alignItems: "center", gap: "12px" }}>
          <h2>{tableName}</h2>
          <ActionIcon onClick={onEditTableName}>
            <Edit20Filled color="gray" className="button" />
          </ActionIcon>
          <ActionIcon onClick={onDeleteTable}>
            <Delete20Filled color="gray" className="button" />
          </ActionIcon>
        </div>
        <div css={{ display: "flex", gap: "8px" }}>
          <Button onClick={onAddColumn} leftIcon={<Add20Filled />}>
            カラムを追加
          </Button>
          <Button onClick={onSave} disabled={!isModified} variant="important">
            変更を保存
          </Button>
        </div>
      </div>
      <table css={{ width: "100%", borderCollapse: "collapse" }}>
        <thead css={{ borderBottom: "solid 1px #dee2e6" }}>
          <tr css={{ fontSize: "14px" }}>
            <th css={{ ...baseThStyle, padding: "7px 10px 7px 50px" }}>
              カラム名
            </th>
            <th css={baseThStyle}>型</th>
            <th css={baseThStyle}>選択可能</th>
            <th css={baseThStyle}>編集</th>
          </tr>
        </thead>
        <tbody>
          {columns.map((column, i) => (
            <ColumnItem
              key={i} // TODO: 実際は column.column_name を使う
              cellData={column}
              onDelete={() => setcolumns(columns.filter((_, j) => i !== j))} // TODO: ダイアログ出すように
              onEdit={() => {}} // TODO: Drawer 出すように
            />
          ))}
        </tbody>
      </table>
    </div>
  );
};
