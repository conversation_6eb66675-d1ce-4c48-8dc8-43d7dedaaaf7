import styled from "@emotion/styled";
import { Skeleton, Grid } from "@mantine/core";
import React from "react";
import { propcolors } from "styles/colors";

const LoadSkeleton = styled.div`
  .mantine-Skeleton-root {
    margin-left: auto;
    margin-right: auto;
  }
  .skeleton-child {
    margin-left: 0;
  }
`;

const SeparatorLine = styled.div({
  width: "100%",
  height: 1,
  backgroundColor: propcolors.border,
});

interface LoadingSkeletonProps {
  numberOfRows?: number;
  height?: number;
}

const LoadingSkeleton = (props: LoadingSkeletonProps) => {
  const { numberOfRows = 12, height } = props;
  const rows = new Array(numberOfRows).fill(0);
  return (
    <>
      {rows.map((_, index) => (
        <React.Fragment key={index}>
          {!height && <SeparatorLine />}
          <Grid style={{ margin: height ? 0 : 14 }} align="center">
            <Grid.Col span={1}>
              <LoadSkeleton>
                <Skeleton height={height ? height - 16 : 50} circle />
              </LoadSkeleton>
            </Grid.Col>
            <Grid.Col span={1}>
              <LoadSkeleton>
                <Skeleton height={14} mt={10} radius="4px" />
                <Skeleton
                  className="skeleton-child"
                  height={10}
                  mt={6}
                  width="50%"
                  radius="4px"
                />
              </LoadSkeleton>
            </Grid.Col>
            <Grid.Col span={1}>
              <LoadSkeleton>
                <Skeleton
                  className="skeleton-circle"
                  height={height ? height - 16 : 50}
                  circle
                />
              </LoadSkeleton>
            </Grid.Col>
            <Grid.Col span={1}>
              <LoadSkeleton>
                <Skeleton height={16} width="80%" radius="4px" />
              </LoadSkeleton>
            </Grid.Col>
            <Grid.Col span={1}>
              <LoadSkeleton>
                <Skeleton height={16} width="70%" radius="4px" />
              </LoadSkeleton>
            </Grid.Col>
            <Grid.Col span={1}>
              <LoadSkeleton>
                <Skeleton height={16} width="90%" radius="4px" />
              </LoadSkeleton>
            </Grid.Col>
            <Grid.Col span={1}>
              <LoadSkeleton>
                <Skeleton height={16} width="50%" radius="4px" />
              </LoadSkeleton>
            </Grid.Col>
            <Grid.Col span={1}>
              <LoadSkeleton>
                <Skeleton height={16} width="60%" radius="4px" />
              </LoadSkeleton>
            </Grid.Col>
            <Grid.Col span={1}>
              <LoadSkeleton>
                <Skeleton height={16} width="50%" radius="4px" />
              </LoadSkeleton>
            </Grid.Col>
            <Grid.Col span={1}>
              <LoadSkeleton>
                <Skeleton height={16} width="50%" radius="4px" />
              </LoadSkeleton>
            </Grid.Col>
            <Grid.Col span={1}>
              <LoadSkeleton>
                <Skeleton height={16} width="50%" radius="4px" />
              </LoadSkeleton>
            </Grid.Col>
            <Grid.Col span={1}>
              <LoadSkeleton>
                <Skeleton height={16} width="80%" radius="4px" />
              </LoadSkeleton>
            </Grid.Col>
          </Grid>
        </React.Fragment>
      ))}
    </>
  );
};

export default LoadingSkeleton;
