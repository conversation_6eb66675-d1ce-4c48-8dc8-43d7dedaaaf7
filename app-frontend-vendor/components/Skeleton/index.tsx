import styled from "@emotion/styled";
import { LOADING_CAPTION } from "constants/ja/common";
import { propcolors } from "styles/colors";

type PresentationProps = {
  caption?: string
}

export const Styled = styled.div`
  border: 2px dashed ${propcolors.gray[300]};
  background-color: ${propcolors.gray[200]};
  border-radius: 10px;
  animation: pulse 1.5s infinite;
  display: flex;
  justify-content: center;
  align-items: center;
  p {
    font-weight: bold;
  }
  @keyframes pulse {
    0% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.5;
    }
  }
`;

export const Skeleton: React.FC<PresentationProps> = ({caption = LOADING_CAPTION}) => {
  return (
    <Styled>
      <p>{caption}</p>
    </Styled>
  );
};
