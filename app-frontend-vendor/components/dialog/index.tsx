import * as Dialog from "@radix-ui/react-dialog";
import styled from "@emotion/styled";

export const DialogRoot = styled(Dialog.Root)``;
export const DialogTrigger = styled(Dialog.Trigger)``;
export const DialogPortal = styled(Dialog.Portal)`
  z-index: 20000;
`;
export const DialogOverlay = styled(Dialog.Overlay)`
  background-color: #00000080;
  position: fixed;
  inset: 0;
  animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 20000;
`;

export const RequestDialogContent = styled(Dialog.Content)`
  background-color: white;
  border-radius: 6px;
  box-shadow: hsl(206 22% 7% / 35%) 0px 10px 38px -10px,
    hsl(206 22% 7% / 20%) 0px 10px 20px -15px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 720px;
  max-height: 85vh;
  padding: 25px;
  animation: contentShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 20001;
  &:focus {
    outline: none;
  }
`;
export const DialogContent = styled(Dialog.Content)`
  background-color: white;
  border-radius: 6px;
  box-shadow: hsl(206 22% 7% / 35%) 0px 10px 38px -10px,
    hsl(206 22% 7% / 20%) 0px 10px 20px -15px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 700px;
  max-height: 85vh;
  padding: 25px;
  animation: contentShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 20001;
  &:focus {
    outline: none;
  }
`;
export const DialogTitle = styled(Dialog.Title)`
  margin: 0;
  font-weight: bold;
  font-size: 20px;
  margin-bottom: 16px;
`;
export const DialogDescription = styled(Dialog.Description)`
  margin: 8px 0 16px;
  font-size: 14px;
  line-height: 1.5;
`;
export const DialogClose = styled(Dialog.Close)``;
