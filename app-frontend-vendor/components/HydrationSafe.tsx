import { useEffect, useState, ReactNode } from "react";

interface HydrationSafeProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * このコンポーネントは子要素がクライアント側でのみレンダリングされることを保証します
 * サーバーとクライアントのレンダリングの違いによるHydrationエラーを防ぎます
 */
export default function HydrationSafe({
  children,
  fallback = null,
}: HydrationSafeProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // クライアント側の場合のみ子要素をレンダリングします
  return isClient ? <>{children}</> : <>{fallback}</>;
}
