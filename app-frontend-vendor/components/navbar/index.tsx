import styled from "@emotion/styled";
import Link from "next/link";
import { useRouter } from "next/router";
import { propcolors } from "../../styles/colors";
import { links } from "api";
import { destroyCookie } from "nookies";
import { useEffect, useState } from "react";
import { AnimatePresence } from "framer-motion";
import { useSessionUser } from "utils/recoil/sessionUserState";
import { resetAllRecoil } from "utils/recoil/resetRecoil/resetRecoil";
import { useSetRecoilState } from "recoil";
import { NavbarMenu } from "./menu";
import SettingsIcon from "public/icons/sidebar/settings-1.svg";
import { Menu } from "@mantine/core";
import { TabNames } from "constants/tab_names";
import { ax } from "utils/axios";

type NavbarProps = {};

type PresentationProps = {
  className?: string;
  current: string;
  handleLogout: () => void;
  userInfo: SessionUser;
  isMenuVisible: boolean;
  toggleMenu: () => void;
  hasDashboardAccess: boolean;
} & NavbarProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  current,
  handleLogout,
  userInfo,
  isMenuVisible,
  toggleMenu,
  hasDashboardAccess,
}) => {
  return (
    <>
      <header id="navbar" className={className}>
        <div className="navbar-content">
          <nav className="navbar-links">
            <ul>
              {links.map(
                (
                  { name, Svg, SvgActive, href, availability, children },
                  index
                ) => {
                  //TODO: PROP-4142 デモダッシュボード移行後に除却
                  if (name === "レポート" && !hasDashboardAccess) {
                    return null;
                  }
                  if (
                    name === TabNames.INSIGHT &&
                    !userInfo.dashboard_permission
                  ) {
                    return null;
                  }
                  if (href == "chat/all" && current.includes("/chat/[id]")) {
                    return (
                      <li className={`navbar-links-item`} key={index}>
                        <Link
                          href={`/${href}`}
                          className="navbar-links-item-link active"
                        >
                          <SvgActive />
                          <span>{name}</span>
                        </Link>
                      </li>
                    );
                  }
                  if (!children && href) {
                    return (
                      <li className={`navbar-links-item`} key={index}>
                        <Link
                          href={`/${href}`}
                          className={`navbar-links-item-link ${
                            current.includes(href) &&
                            !current.includes("/settings")
                              ? "active"
                              : ""
                          }`}
                        >
                          {current.includes(href) &&
                          !current.includes("/settings") ? (
                            <SvgActive />
                          ) : (
                            <Svg />
                          )}
                          <span>{name}</span>
                        </Link>
                      </li>
                    );
                  } else {
                    return (
                      <li className={`navbar-links-item`} key={index}>
                        <Menu
                          width={120}
                          position="right-start"
                          shadow="md"
                          offset={{ mainAxis: 9, crossAxis: 35 }}
                        >
                          <Menu.Target>
                            {href === "" ? (
                              <a
                                className={`navbar-links-item-link ${
                                  current.includes("webTest") ||
                                  (current.includes("training") &&
                                    !current.includes("/settings"))
                                    ? "active"
                                    : ""
                                }`}
                              >
                                {current.includes("webTest") ||
                                (current.includes("training") &&
                                  !current.includes("/settings")) ? (
                                  <SvgActive />
                                ) : (
                                  <Svg />
                                )}
                                <span>{name}</span>
                              </a>
                            ) : (
                              <a
                                className={`navbar-links-item-link ${
                                  current.includes(href) &&
                                  !current.includes("/settings")
                                    ? "active"
                                    : ""
                                }`}
                              >
                                {current.includes(href) &&
                                !current.includes("/settings") ? (
                                  <SvgActive />
                                ) : (
                                  <Svg />
                                )}
                                <span>{name}</span>
                              </a>
                            )}
                          </Menu.Target>
                          <Menu.Dropdown
                            style={{ padding: 0, borderRadius: "8px" }}
                          >
                            {children?.map((child, index) => {
                              return (
                                <Menu.Item
                                  key={index}
                                  className="navbar-links-item-child"
                                >
                                  <Link
                                    href={href ? `/${href + "/" + child.child_href}` : `/${child.child_href}`}
                                    className="navbar-links-item-child-link"
                                  >
                                    {child.name}
                                    {child.is_beta && (
                                      <div className="is_beta">β版</div>
                                    )}
                                  </Link>
                                </Menu.Item>
                              );
                            })}
                          </Menu.Dropdown>
                        </Menu>
                      </li>
                    );
                  }
                }
              )}
            </ul>
          </nav>
          <div className="navbar-right">
            <div className="navbar-links-item">
              <Link
                href={`/settings`}
                className={`navbar-links-item-link navbar-item-settings ${
                  current.includes("/settings") ? `active` : ``
                }`}
              >
                <SettingsIcon style={{ width: "28px", height: "28px" }} />
                {/* <span>設定</span> */}
              </Link>
            </div>
            {/* <button onClick={toggleMenu} className="navbar-user">
              {userInfo?.user_name}
              <ListRtl16Filled />
            </button> */}
          </div>
        </div>
      </header>
      <AnimatePresence>
        {isMenuVisible && (
          <NavbarMenu
            toggle={toggleMenu}
            logout={handleLogout}
            userInfo={userInfo}
          />
        )}
      </AnimatePresence>
    </>
  );
};

const Styled = styled(Presentation)`
  filter: drop-shadow(0 0 2px ${propcolors.gray[200]}1f);
  z-index: 200;
  @media screen and (max-width: 512px) {
    display: none;
  }
  span {
    display: block;
  }
  .prop {
    &-logo {
      &-icon {
        display: inline-block;
        height: 24px;
      }
    }
  }
  .navbar {
    &-content {
      grid-template-columns: auto 1fr auto;
      align-items: center;
      background-color: ${propcolors.gray[150]};
      filter: drop-shadow(0 0 2px ${propcolors.gray[200]});
      padding: 24px 8px 24px 8px;
      border-radius: 0 40px 40px 0;
      width: 88px;
      height: 100%;
      box-shadow: 0px 4px 8px 0px ${propcolors.gray[200]};
    }
    &-vendor {
      display: flex;
      gap: 8px;
      padding: 0 12px;
      align-items: center;
      height: 100%;
      width: 200px;
      border-right: 1px solid ${propcolors.gray[300]};
      color: ${propcolors.partnerRed};
      overflow: hidden;
      &-icon {
        width: 20px;
        height: 20px;
        fill: ${propcolors.partnerRed};
      }
    }
    &-right {
      display: block;
      align-items: center;
      gap: 4px;
      padding-bottom: 30px;
      position: absolute;
      bottom: 0;
    }
    &-item-settings {
      padding: 16px 0 !important;
      display: flex !important;
      align-items: center;
      justify-content: center;
    }
    &-user {
      border: 0;
      background: transparent;
      padding: 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    &-links {
      height: 100%;
      ul {
        max-height: calc(100% - 90px);
        overflow-y: auto;
        list-style: none;
        gap: 8px;
        float: left;
        scrollbar-width: none;
      }
      &-item {
        .mantine-Menu-dropdown {
          width: auto !important;
        }
        &-child {
          border-radius: 0;
          border-bottom: 1px solid ${propcolors.gray[200]};
          height: auto;
          font-size: 14px;
          line-height: 21px;
          text-align: center;
          padding: 0;
          overflow: hidden;
          &:first-of-type {
            border-radius: 8px 8px 0 0;
          }
          &:last-child {
            border-bottom: 0px;
            border-radius: 0 0 8px 8px;
          }
          &:hover {
            background-color: #222222;
            color: white;
            border-bottom: 1px solid #222222;
          }
          &-link {
            display: flex;
            font-size: 1rem;
            padding: 16px 24px;
            color: #222222;
            white-space: nowrap;
            align-items: center;
            &:hover {
              background-color: #222222;
              color: white;
            }
            .is_beta {
              background-color: ${propcolors.navbar_menu_beta_icon};
              color: ${propcolors.navbar_menu_beta_icon_text};
              font-size: 12px;
              padding: 0 20px;
              border-radius: 4px;
              margin-left: 8px;
            }
          }
        }
        &-link {
          border-radius: 16px;
          transition: 0.2s;
          padding: 16px 0 16px 0;
          color: ${propcolors.greyDefault};
          display: block;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          text-align: center;
          width: 72px;
          height: 76px;
          &.active {
            background-color: ${propcolors.blackLight};
            border-color: ${propcolors.blackLight};
            color: ${propcolors.gray[200]};
            svg,
            path {
              fill: ${propcolors.gray[200]};
            }
          }
          svg {
            fill: ${propcolors.greyDefault};
          }
        }
        &_request {
          &-amount {
            width: 20px;
            text-align: center;
          }
        }
        &_IDcopy {
          font-size: 14px;
        }
      }
    }
  }
`;

export const Navbar: React.FC<NavbarProps> = () => {
  const router = useRouter();
  const sessionUser = useSessionUser();
  const resetAllState = useSetRecoilState(resetAllRecoil);
  const [isMenuVisible, toggleMenu] = useState<boolean>(false);
  const [hasDashboardAccess, setHasDashboardAccess] = useState<boolean>(false);

  useEffect(() => {
    if (sessionUser && sessionUser.dashboard_view_permission !== undefined) {
      setHasDashboardAccess(sessionUser.dashboard_view_permission === 1);
    }
  }, [sessionUser]);

  useEffect(() => {
    const grid = document.querySelector(".layout-content");
    if (grid && isMenuVisible) {
      grid.classList.add("disabled");
    } else {
      setTimeout(() => {
        grid?.classList.remove("disabled");
      }, 500);
    }
  }, [isMenuVisible]);
  const handleLogout = async () => {
    await ax.post("/logout");
    await destroyCookie(null, "user", { path: "/" });
    await resetAllState(undefined);
    await router.push("/login");
  };
  return (
    sessionUser && (
      <Styled
        current={router.pathname}
        handleLogout={handleLogout}
        userInfo={sessionUser}
        isMenuVisible={isMenuVisible}
        toggleMenu={() => toggleMenu(!isMenuVisible)}
        hasDashboardAccess={hasDashboardAccess}
      />
    )
  );
};
