import styled from "@emotion/styled";
import Link from "next/link";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";

type MobileListItemProps = {
  className?: string;
  data: Lead | Partner | undefined;
}

const Presentational: React.FC<MobileListItemProps> = ({ className, data }) => {
  const { asPath } = useRouter();
  return <Link href={`${asPath}/${((data as Lead).lead_id) || (data as Partner)?.managed_partner_id}`} className={className}>
    <h2>
      {((data as Lead)?.lead_name) || (data as Partner)?.managed_partner_name }
    </h2>
    <p>
    {((data as Lead)?.lead_status_name) || (data as Partner)?.url }
    </p>
  </Link>;
}

const Styled = styled(Presentational)`
  padding: 0.5rem;
  border: 1px solid ${propcolors.gray[200]};
  border-radius: 4px;
  background-color: #fff;
  overflow: hidden;
  width: 100%;
  
  h2 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: ${propcolors.gray[900]};
    line-height: 1.5;
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding-bottom: 0.5rem;
  }
  p {
    font-size: 0.8rem;
    color: ${propcolors.gray[500]};
  }
`;

export const MobileListItem: React.FC<MobileListItemProps> = ({ data }) => {
  return <Styled data={data} />;
}