import { Variants, motion } from "framer-motion";
import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import { PropNormal } from "components/svgs/logo/prop";
import { propVersion } from "api";
import { Button } from "@mantine/core";

type NavbarMenuProps = {
  className?: string;
  toggle: () => void;
  logout: () => void;
  userInfo: SessionUser;
};

const animVars: Variants = {
  initial: {
    opacity: 0,
    x: 12,
    transition: {
      staggerChildren: 0.05,
      duration: 0.2,
    },
  },
  enter: {
    opacity: 1,
    x: 0,
    transition: {
      ease: "easeOut",
      duration: 0.3,
    },
  },
  exit: {
    opacity: 0,
    x: 12,
    transition: {
      ease: "easeIn",
      duration: 0.2,
    },
  },
};

const parentVars: Variants = {
  initial: {
    width: 0,
    x: 12,
    transition: {
      staggerChildren: 0.05,
      duration: 0.2,
    },
  },
  enter: {
    width: "340px",
    x: 0,
    transition: {
      when: "beforeChildren",
      ease: "easeOut",
      staggerChildren: 0.05,
      duration: 0.3,
    },
  },
  exit: {
    width: 0,
    x: 12,
    transition: {
      ease: "easeIn",
      when: "afterChildren",
    },
  },
};

const backdropVars: Variants = {
  initial: {
    opacity: 0,
  },
  enter: {
    opacity: 1,
    transition: {
      ease: "easeOut",
      duration: 0.6,
    },
  },
  exit: {
    opacity: 0,
    transition: {
      ease: "easeIn",
    },
  },
};

const Presentation: React.FC<NavbarMenuProps> = ({
  className,
  toggle,
  logout,
  userInfo,
}) => {
  return (
    <div className={className}>
      <motion.div
        variants={backdropVars}
        initial="initial"
        animate="enter"
        exit="exit"
        className="navbar-menu-backdrop"
        onClick={toggle}
      />
      <motion.div
        variants={parentVars}
        initial="initial"
        animate="enter"
        exit="exit"
        className="navbar-menu-content"
      >
        <div className="navbar-menu-info">
          <motion.div className="navbar-head" variants={animVars}>
            <Button
              variant="link"
              className="navbar-head-button"
              onClick={toggle}
            >
              閉じる
            </Button>
            <Button
              variant="link"
              className="navbar-head-button"
              onClick={logout}
            >
              ログアウト
            </Button>
          </motion.div>
          <motion.div className="navbar-menu-info-name" variants={animVars}>
            {userInfo?.user_name}
          </motion.div>
          <motion.div className="navbar-menu-info-vendor" variants={animVars}>
            {userInfo?.vendor_name}
          </motion.div>
          <motion.span
            className="navbar-menu-info-division"
            variants={animVars}
          >
            {userInfo?.user_division}
          </motion.span>
          <motion.span className="navbar-menu-info-id" variants={animVars}>
            <span className="navbar-menu-info-id-label">連携ID</span>
            <span>{userInfo?.vendor_collaboration_id}</span>
          </motion.span>
        </div>
        <motion.div className="corporate-info" variants={animVars}>
          <motion.p variants={animVars}>
            <PropNormal className="logo" />
            <span className="company-motto">
              Creating the Future through the Power of Collaboration
            </span>
            <span className="company-location">EST.MMXXIII : TOKYO, JAPAN</span>
            <span className="copyright">2023 PartnerProp Inc.</span>
          </motion.p>
          <p className="software-version">Version {propVersion}</p>
        </motion.div>
      </motion.div>
    </div>
  );
};

const Styled = styled(Presentation)`
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 120;
  .corporate-info {
    & {
      .logo {
        height: 20px;
        margin-bottom: 0.5rem;
      }
      .company-motto {
        font-style: italic;
        font-size: 0.8rem;
      }
      .corporate-link {
        display: inline-block;
        margin-top: 1rem;
      }
      .company-location {
        font-size: 10px;
        margin-top: 0.25rem;
      }
    }
  }
  .navbar {
    &-head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid ${propcolors.gray[200]};
      margin-bottom: 1rem;
      padding: 1rem 0;
      &-button {
        color: ${propcolors.red[600]};
        &:hover {
          color: ${propcolors.red[300]};
        }
      }
    }
    &-menu {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-content {
        display: flex;
        flex-flow: column;
        justify-content: space-between;
        position: fixed;
        right: 0;
        top: 0;
        background-color: ${propcolors.white};
        border-radius: 16px 0 0 16px;
        border: 1px solid ${propcolors.gray[200]};
        border-width: 0px 0px 1px 1px;
        padding: 0 32px 32px 32px;
        height: 100vh;
        z-index: 101;
      }
      &-backdrop {
        position: fixed;
        width: 100vw;
        height: 100vh;
        top: 0;
        left: 0;
        z-index: 100;
        background-color: ${propcolors.black}20;
      }
      &-info {
        &-name {
          font-size: 1rem;
          font-weight: bold;
          border-bottom: 1px dashed ${propcolors.gray[200]};
          padding-bottom: 0.5rem;
          margin-bottom: 0.5rem;
        }
        &-id {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border: 1px solid ${propcolors.gray[200]};
          padding: 0.5rem;
          border-radius: 5px;
          font-size: 0.88rem;
          margin-top: 1rem;
          &-label {
            font-weight: bold;
          }
        }
      }
      &-detail {
        color: ${propcolors.black};
        text-align: right;
        font-size: 14px;
        line-height: 1.6;
        padding: 6px 12px 12px 12px;
        &-name {
          font-size: 16px;
        }
      }
      &-button {
        padding: 0 12px;
        border: 0;
      }
      &-options {
        position: fixed;
        right: -16px;
        top: -16px;
        background-color: ${propcolors.white};
        border-radius: 0 0 0 5px;
        border: 1px solid ${propcolors.gray[200]};
        border-width: 0px 0px 1px 1px;
        &-wrapper {
          position: fixed;
          width: 100vw;
          height: 100vh;
          top: 0;
          right: 0;
          z-index: 100;
        }
      }
    }
    &-label {
      display: inline-block;
      font-size: 14px;
      margin-bottom: 4px;
    }
    &-content {
    }
    &-logout {
      border: 0;
      background: 0;
      padding: 0;
      svg,
      path {
        fill: ${propcolors.red[700]};
      }
    }
  }
  .software-version {
    font-size: 12px;
    opacity: 0.5;
    margin-top: 1rem;
  }
`;

export const NavbarMenu: React.FC<NavbarMenuProps> = ({
  logout,
  userInfo,
  toggle,
}) => {
  return <Styled logout={logout} userInfo={userInfo} toggle={toggle} />;
};
