import styled from "@emotion/styled";
import { useRouter } from "next/router";
import { propcolors } from "../../styles/colors";
import { destroyCookie } from "nookies";
import { useSessionUser } from "utils/recoil/sessionUserState";
import { resetAllRecoil } from "utils/recoil/resetRecoil/resetRecoil";
import { useSetRecoilState } from "recoil";
import { SegmentedControl, Menu } from "@mantine/core";
import { ax } from "utils/axios";
import Logo from "public/icons/logo.svg";
import LogoForVendor from "public/icons/logo-for-vendor.svg";
import UserIcon from "public/icons/icon-user.svg";
import CloseIcon from "public/icons/icon-close.svg";
import { useIsMobileByUA } from "utils/hooks/useIsMobile";
import Image from "next/image";

type NavbarProps = {};

type PresentationProps = {
  className?: string;
  current: string;
  handleLogout: () => void;
  userInfo: SessionUser;
} & NavbarProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  current,
  handleLogout,
  userInfo,
}) => {
  const isMobile = useIsMobileByUA();
  return (
    <>
      {!isMobile && (
        <header id="navbar" className={className}>
          <div className="navbar-content">
            <Logo className="prop-logo" />
            <div className="navbar-logo">
              <LogoForVendor />
            </div>
            <div className="navbar-right">
              <div className="navbar-links-item">
                <SegmentedControl
                  radius="xl"
                  size="md"
                  value="ベンダー"
                  data={["ベンダー", "パートナー"]}
                  onClick={() => {
                    const link = document.createElement("a");
                    link.target = "_blank";
                    link.href = process.env.NEXT_PUBLIC_PARTNER_URL
                      ? process.env.NEXT_PUBLIC_PARTNER_URL
                      : "";
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }}
                  disabled
                  style={{ cursor: "pointer" }}
                />
              </div>
              <div className="navbar-text">|</div>
              <Menu shadow="md" width={240}>
                <Menu.Target>
                  <button className="navbar-user">
                    <div
                      className={`commonList-header-icon ${userInfo?.avatar_url && "avatar-box"}`}
                    >
                      {userInfo?.avatar_url ? (
                        <Image
                          src={userInfo.avatar_url}
                          style={{ borderRadius: "50%" }}
                          width={36}
                          height={36}
                          alt=""
                        />
                      ) : (
                        <UserIcon />
                      )}
                    </div>
                  </button>
                </Menu.Target>

                <Menu.Dropdown>
                  {/* <Menu.Label>Application</Menu.Label> */}
                  <Menu.Label
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      gap: 10,
                    }}
                  >
                    <span className="popup-info-name">
                      {userInfo?.user_name}
                    </span>
                    <div>
                      <Menu.Target>
                        <CloseIcon className="btn-close" />
                      </Menu.Target>
                    </div>
                  </Menu.Label>
                  <Menu.Label>
                    <span className="popup-info-vendor">
                      {userInfo?.vendor_name}
                    </span>
                  </Menu.Label>
                  <Menu.Label>
                    <span className="popup-info-division">
                      {userInfo?.user_division}
                    </span>
                  </Menu.Label>
                  <Menu.Divider />
                  <Menu.Label className="popup-info-id">
                    <span className="popup-info-id-label">連携ID</span>
                    <span className="popup-info-id-label-right">
                      {userInfo?.vendor_collaboration_id}
                    </span>
                  </Menu.Label>
                  <Menu.Divider />
                  <Menu.Item onClick={handleLogout}>ログアウト</Menu.Item>
                </Menu.Dropdown>
              </Menu>
            </div>
          </div>
        </header>
      )}
    </>
  );
};

const Styled = styled(Presentation)`
  width: 100%;
  padding: 1rem 1.5rem;
  filter: drop-shadow(0 0 2px ${propcolors.gray[200]}1f);
  z-index: 100;
  border-left: 0px;
  position: relative;
  .mantine-Input-icon {
    padding: 12px 8px 12px 20px;
  }
  .mantine-Input-input {
    padding-left: 40px;
  }
  .navbar-logo {
    display: grid;
    grid-template-columns: auto 1fr auto;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 40px;
  }
  .btn-close {
    cursor: pointer;
  }
  .mantine-Menu-dropdown {
    left: inherit !important;
    right: 1.5rem;
  }
  .mantine-Menu-dropdown {
    border-radius: 12px;
  }
  .mantine-Menu-label:first-of-type {
    margin: 15px 16px 0px 10px;
  }
  .mantine-Menu-label {
    color: ${propcolors.blackLight};
    margin-top: -5px;
  }
  .mantine-Menu-item {
    width: 210px;
    margin: 6px 10px 5px 10px;
  }
  .popup-info-vendor,
  .popup-info-division,
  .popup-info-id-label,
  .popup-info-id-label-right {
    font-weight: 400;
    font-size: 14px;
    margin: 0 10px;
  }
  .popup-info-id-label-right {
    right: 16px;
    position: absolute;
  }
  .popup-info-name {
    font-size: 14px;
    font-weight: 400;
  }
  .popup-info-id {
    margin: 10px 0px;
  }
  .mantine-Menu-divider {
    margin-right: 22px;
    margin-left: 22px;
    border-color: ${propcolors.gray[200]};
  }
  .prop {
    &-logo {
      margin-right: 2rem;
    }
  }
  .icon-menu {
    color: ${propcolors.greyDefault} !important;
  }
  .commonList-header-icon {
    background-color: ${propcolors.black};
    width: 36px;
    height: 36px;
    line-height: 40px;
    border-radius: 50%;
    svg {
      width: 36px !important;
    }
  }
  .avatar-box {
    background-color: ${propcolors.white} !important;
  }
  .navbar {
    &-content {
      display: grid;
      grid-template-columns: auto 1fr auto;
      align-items: center;
      background-color: ${propcolors.white};
    }
    &-text {
      color: ${propcolors.gray[200]};
      padding: 0px 10px;
    }
    &-right {
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }
    &-user {
      background: white;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 4px 0px;
      border: none !important;
    }
    &-links {
      ul {
        list-style: none;
        display: flex;
        gap: 8px;
        padding: 0 16px;
      }
      &-item {
        &-child {
          border-radius: 0;
          border: none;
          color: ${propcolors.black};
          height: 2rem;
          display: block;
          line-height: 2rem;
          text-align: center;
          &:hover {
            background-color: ${propcolors.gray[200]};
          }
        }
        &-link {
          border-radius: 32px;
          transition: 0.2s;
          padding: 0px 10px;
          color: ${propcolors.black};
          display: inline-flex;
          align-items: center;
          gap: 8px;
          border: 1px solid ${propcolors.gray[200]};
          &:hover {
            background-color: ${propcolors.gray[200]};
          }
          &.active {
            background-color: ${propcolors.black};
            border-color: ${propcolors.black};
            color: #fff;
            svg,
            path {
              fill: #fff;
            }
          }
          svg {
            fill: ${propcolors.black};
          }
        }
        &_request {
          &-amount {
            width: 20px;
            text-align: center;
          }
        }
        &_IDcopy {
          font-size: 14px;
        }
        .mantine-SegmentedControl-root {
          border: 1px solid ${propcolors.gray[200]};
          background-color: ${propcolors.greyBreadcrumb};
          width: 212px;
          height: 45px;
        }
        .mantine-SegmentedControl-label {
          font-size: 14px;
          font-weight: 400;
          color: ${propcolors.blackLight};
          font-family: "Inter", "system-ui";
        }
        .mantine-SegmentedControl-label[data-active] {
          height: 33px;
        }
      }
    }
  }
`;

export const Header: React.FC<NavbarProps> = () => {
  const router = useRouter();
  const sessionUser = useSessionUser();
  const resetAllState = useSetRecoilState(resetAllRecoil);
  const handleLogout = async () => {
    await ax.post("/logout");
    await destroyCookie(null, "user", { path: "/" });
    await resetAllState(undefined);
    await router.push("/login");
  };
  return (
    sessionUser && (
      <Styled
        current={router.pathname}
        handleLogout={handleLogout}
        userInfo={sessionUser}
      />
    )
  );
};
