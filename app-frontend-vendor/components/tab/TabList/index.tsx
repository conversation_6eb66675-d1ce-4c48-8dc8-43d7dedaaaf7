import styled from "@emotion/styled";
import { propcolors } from "../../../styles/colors";
import { List, Trigger } from "@radix-ui/react-tabs";

type TabsProps = {
  tabItem: { id: string; name: string }[];
};

type PresentationProps = {
  className?: string;
} & TabsProps;

// const Presentation: React.FC<PresentationProps> = ({ className, tabItem }) => {
//   return (
//     <TabsListUnstyled className={className}>
//       {tabItem.map((item) => (
//         <TabUnstyled key={item.id} value={item.id} className="propTabs-item">
//           {item.name}
//         </TabUnstyled>
//       ))}
//     </TabsListUnstyled>
//   );
// };
const Presentation: React.FC<PresentationProps> = ({ className, tabItem }) => {
  return (
    <List className={className}>
      {tabItem.map((item) => (
        <Trigger key={item.id} value={item.id} className="propTabs-item">
          {item.name}
        </Trigger>
      ))}
    </List>
  );
};

const Styled = styled(Presentation)`
  display: inline-flex;
  width: 100%;
  border-bottom: 1px solid ${propcolors.gray[200]};
  .propTabs {
    &-item {
      all: unset;
      padding: 0 12px 12px 12px;
      &[data-state="active"] {
        color: ${propcolors.red[700]};
      }
    }
  }
`;

export const PropTabs: React.FC<TabsProps> = ({ tabItem }) => {
  return <Styled tabItem={tabItem} />;
};
