import styled from "@emotion/styled";
import { ReactNode } from "react";

type TabContentProps = {
  tabID: number;
  children: ReactNode;
};

type PresentationProps = {
  className?: string;
} & TabContentProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  tabID,
  children,
}) => {
  return <>a</>;
};

const Styled = styled(Presentation)``;

export const PropTabContent: React.FC<TabContentProps> = ({
  tabID,
  children,
}) => {
  return <Styled tabID={tabID}>{children}</Styled>;
};
