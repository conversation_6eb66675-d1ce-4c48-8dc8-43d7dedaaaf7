// ImagePicker.tsx
import React, { useEffect, useState } from 'react';
import { Center, Text, Box, Image, Input } from '@mantine/core';
import { propcolors } from 'styles/colors';

type ImagePickerProps = {
    selectedImage: File | null;
    onImageSelect: (file: File | null) => void;
    placeholder: string;
    width: number;
    height: number;
};

export const ImagePicker: React.FC<ImagePickerProps> = ({ 
    selectedImage,
    onImageSelect,
    placeholder,
    width,
    height,
}) => {
    const [imagePreview, setImagePreview] = useState<string | null>(null);

    useEffect(() => {
        if (selectedImage) {
            const reader = new FileReader();
            reader.onloadend = () => {
                setImagePreview(reader.result as string);
            };
            reader.readAsDataURL(selectedImage);
        } else {
            setImagePreview(null);
        }
    }, [selectedImage]);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files ? event.target.files[0] : null;
        onImageSelect(file);
    };

    return (
        <Box>
            <Center
                sx={{
                    border: '1px solid #ccc',
                    borderRadius: '4px',
                    padding: '10px',
                    cursor: 'pointer',
                    position: 'relative',
                    backgroundColor: propcolors.gray[200],
                    width: `${width}px`,
                    height: `${height}px`,
                }}
                onClick={() => document.getElementById('file-input')?.click()}
            >
                {imagePreview ? (
                    <Image src={imagePreview} alt="Selected thumbnail" width={width} height={height} fit="contain" />
                ) : (
                    <Text color="gray">{placeholder}</Text>
                )}
                <Input
                    id="file-input"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    style={{
                        display: 'none', // Inputを隠す
                    }}
                />
            </Center>
        </Box>
    );
};
