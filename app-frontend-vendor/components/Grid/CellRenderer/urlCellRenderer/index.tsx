import { ICellRendererParams } from "ag-grid-community";
import IconAttachmentFile from "../../../../public/icons/attachment-file.svg";
import IconDownloadFile from "../../../../public/icons/download-file.svg";

/**
 * URLをリンクでAGGridに表示する際に利用
 * 「ダウンロード」などを表示する場合は、cellRendererParamsのcustomTextに指定してください。
 * see : features/settings/jobStatus/index.tsx
 */
const UrlCellRenderer = (
  props: ICellRendererParams & { customText?: string }
) => {
  try {
    const url = new URL(props.value);
    const displayText = props.customText || url.hostname; // customText が存在すれば使用し、なければURLのホスト名を表示
    return (
      <div
        className="file-attachments-item"
        onClick={() => window.open(props.value, "_blank")}
      >
        <span>
          <IconAttachmentFile className="attachments-icon" />
          ダウンロード
        </span>
        <IconDownloadFile />
      </div>
    );
  } catch (error) {
    console.error("Invalid URL:", props.value);
    return null;
  }
};

export default UrlCellRenderer;
