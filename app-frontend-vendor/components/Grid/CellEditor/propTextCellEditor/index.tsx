import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useRef,
  useEffect,
} from "react";
import { ICellEditorParams } from "ag-grid-community";
import { TextInput } from "@mantine/core";
import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import {
  STRING_MAX_LENGTH,
} from "../../../../constants/commonSetting";


interface TextEditorParams extends ICellEditorParams {
  value: string;
  setIsComposing: (isComposing: boolean) => void;
  stopEditingCallback: () => void;
}

const StyledTextInput = styled(TextInput)`
  margin-top: 1.7rem;
  .mantine-TextInput-input {
    border-color: ${propcolors.gray[200]};
    background-color: white;
    color: ${propcolors.blackLight};
    font-family: "Inter", "system-ui";
  }
  .mantine-TextInput-input:focus {
    background-color: ${propcolors.gray[150]};
  }
`;

export const propTextCellEditor = forwardRef<any, TextEditorParams>(
  (props, ref) => {
    const [value, setValue] = useState<string>(props.value);
    const inputRef = useRef<HTMLInputElement>(null);
    const [error, setError] = useState("");

    useImperativeHandle(ref, () => ({
      getValue: () => value,
    }));

    const handleChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
      const tmpValue = event.target.value;
      if (tmpValue !== null && tmpValue.toString().length > STRING_MAX_LENGTH) {
        setError(
          STRING_MAX_LENGTH.toLocaleString() + "文字以下で入力してください。"
        );
        return;
      }
      await setValue(tmpValue);
    };

    // 全角入力中とする
    const handleCompositionStart = () => {
      props.setIsComposing(true);
    };

    // 全角入力完了とする
    const handleCompositionEnd = () => {
      props.setIsComposing(false);
    };

    useEffect(() => {
      inputRef.current?.focus();
      setError("");
    }, []);

    return (
      <StyledTextInput
        ref={inputRef}
        value={value}
        onChange={handleChange}
        onCompositionStart={handleCompositionStart}
        onCompositionEnd={handleCompositionEnd}
        error={error}
        onBlur={() => {
          props.stopEditingCallback();
        }}
      />
    );
  }
);

propTextCellEditor.displayName = "PropTextCellEditor";
