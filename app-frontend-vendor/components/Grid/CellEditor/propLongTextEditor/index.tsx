import React, { forwardRef, useImperativeHandle, useState, useRef, useEffect } from 'react';
import { ICellEditorParams } from 'ag-grid-community';
import { Textarea } from '@mantine/core';
import styled from '@emotion/styled';
import { propcolors } from "styles/colors";
import { LONG_TEXT_MAX_LENGTH, SUB_TABLE_LARGE_COLUMN_MOBILE_WIDTH, SUB_TABLE_LARGE_COLUMN_WIDTH } from 'constants/commonSetting';
import { useIsMobileByUA } from 'utils/hooks/useIsMobile';

interface LongTextEditorParams extends ICellEditorParams {
    value: string;
    setIsComposing: (isComposing: boolean) => void;
    stopEditingCallback: () => void;
}

const StyledTextarea = styled(Textarea)`
    margin-top: 1.9rem;
    .mantine-Textarea-input {
        border-color: ${propcolors.gray[200]};
        background-color: white;
        color: ${propcolors.blackLight};
        margin-top: -25px;
        margin-left: 10px;
        height: 4.8rem;
        width: 360px;
        border-radius: 8px;
    }
    .mantine-Textarea-input:focus{
        background-color: ${propcolors.gray[150]};
    }
`;

export const propLongTextEditor = forwardRef<any, LongTextEditorParams>((props, ref) => {
    const [value, setValue] = useState<string>(props.value);
    const inputRef = useRef<HTMLTextAreaElement>(null);
    const [error, setError] = useState("");
    const isMobile = useIsMobileByUA();

    useImperativeHandle(ref, () => ({
        getValue: () => value,
    }));

    // 保存ボタンクリック時に入力値を保存して編集状態を解除
    const handleChange = async (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        const tmpValue = event.target.value;
        setError("");
        if(tmpValue !== null && tmpValue.toString().length > LONG_TEXT_MAX_LENGTH){
            setError(LONG_TEXT_MAX_LENGTH.toLocaleString() + "文字以下で入力してください。");
            return
        }
        await setValue(tmpValue)
    };

    // 全角入力中とする
    const handleCompositionStart = () => {
        props.setIsComposing(true);
    };

    // 全角入力完了とする
    const handleCompositionEnd = () => {
        props.setIsComposing(false);
    };

    useEffect(() => {
        inputRef.current?.focus();
        setError("");
    }, []);

    return (
        <div style={isMobile?{width:SUB_TABLE_LARGE_COLUMN_MOBILE_WIDTH}:{width: SUB_TABLE_LARGE_COLUMN_WIDTH}}>
            <StyledTextarea
                ref={inputRef}
                value={value}
                onChange={handleChange}
                onCompositionStart={handleCompositionStart}
                onCompositionEnd={handleCompositionEnd}
                onBlur={() => {props.stopEditingCallback();}}
                style={{whiteSpace: 'pre-wrap'}}
                autosize={false}
                minRows={2}
                maxRows={10}
                error={error}
            />
        </div>
    );
});

propLongTextEditor.displayName = 'PropLongTextEditor';
