import { Select } from '@mantine/core';
import { memo, forwardRef, useState, useEffect, useImperativeHandle } from 'react';
import styled from '@emotion/styled';
import { propcolors } from "styles/colors";
import IconArrowDown from "public/icons/icon-arrow-down.svg";

interface PropSelectEditorProps {
    value: string | null;
    values: { value: string; label: string }[];
    stopEditingCallback: () => void;
}

const StyledSelect = styled(Select)`
    margin-top: 1.7rem;
    color: ${propcolors.inputBackground};
    .mantine-Select-input {
        border-color: ${propcolors.gray[200]};
        background-color: white;
        color: ${propcolors.blackLight};
        font-family: "Inter", "system-ui";
    }
    .mantine-Select-item[data-selected] {
        background-color: ${propcolors.partnerRed};
        color: ${propcolors.white};
    }
`;

export const propSelectEditor = memo(
    forwardRef<unknown, PropSelectEditorProps>((props, ref) => {
        const [selectedValue, setSelectedValue] = useState<string | null>(props.value);
        
        useImperativeHandle(ref, () => ({
            getValue: () => selectedValue,
        }));

        const handleChange = (value: string) => {
            setSelectedValue(value);
        };

        // selectedValueが更新された後に編集を停止する
        useEffect(() => {
            // selectedValueが初期値から変更された場合のみ、編集を停止する
            if (selectedValue !== props.value) {
                props.stopEditingCallback();
            }
        }, [selectedValue, props.value, props.stopEditingCallback]);

        return (
            <div>
                <StyledSelect
                    className="propSelectEditor"
                    variant="filled"
                    value={selectedValue}
                    onChange={handleChange}
                    data={props.values.map(({ value, label }) => ({ value, label: label ?? '未定義' }))}
                    searchable
                    nothingFound="該当なし"
                    rightSection={<IconArrowDown />}
                    placeholder="選択してください"
                    styles={{ rightSection: { pointerEvents: 'none' } }}
                />
            </div>
        );
    })
);
