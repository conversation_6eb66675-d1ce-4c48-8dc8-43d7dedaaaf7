import { FileInput } from "@mantine/core";
import { forwardRef, memo, useImperativeHandle, useRef, useState } from "react";
import styled from '@emotion/styled';
import { propcolors } from "styles/colors";

const StyledFileInput = styled(FileInput)`
    position: absolute;
    margin-top: 70px;
    margin-left: 1.1rem;
    width: 360px;
    .mantine-FileInput-root {
      margin-top: 5rem;
      font-family: "Inter", "system-ui";
    }
    .mantine-FileInput-description {
      padding: 0.6rem;
      border-radius: 0.25rem 0.25rem 0 0;
      background-color: white;
      border-top: 1px solid ${propcolors.gray[200]};
      border-right: 1px solid ${propcolors.gray[200]};
      border-left: 1px solid ${propcolors.gray[200]};
      font-family: "Inter", "system-ui";
    }
    .mantine-Input-input,
    .mantine-FileInput-input {
      border-radius: 0 0 0.25rem 0.25rem;
      background-color: ${propcolors.gray[150]};
      color: ${propcolors.blackLight};
      font-family: "Inter", "system-ui";
    }
    .mantine-FileInput-placeholder {
      color: ${propcolors.blackLight};
      font-family: "Inter", "system-ui";
    }
    .mantine-FileInput-wrapper {
      margin-top: 0;
      font-family: "Inter", "system-ui";
    }
`;

export const propFileCellEditor = memo(
  forwardRef((props, ref) => {
    const [fileValue, setFileValue] = useState<File | null>(null);
    const refInput = useRef(null);
    const [error, setError] = useState("");

    useImperativeHandle(ref, () => ({
      getValue: () => fileValue,
    }));

    const handleFileChange = (file: File | null) => {
      setError("");
      const maxFileSize = 500 * 1024 * 1024; // 500MB
      if (file && file.size > maxFileSize) {
        setError("ファイルサイズは500MB以下で選択してください。");
        return;
      }
      setFileValue(file);
    };

    return (
      <div ref={refInput}>
        <StyledFileInput
          onChange={handleFileChange}
          placeholder="ファイルを選択"
          description="最大500MBまで"
          error={error}
        />
      </div>
    );
  })
);
