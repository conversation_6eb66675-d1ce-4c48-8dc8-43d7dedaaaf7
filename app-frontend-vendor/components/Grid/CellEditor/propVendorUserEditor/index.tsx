import { forwardRef, memo, useEffect, useRef } from "react";
import { Select, SelectItem } from "@mantine/core";
import { useState, useImperativeHandle } from "react";
import { useVendorUserList } from "utils/recoil/vendorUserListState";
import styled from '@emotion/styled';
import { propcolors } from "styles/colors";
import IconArrowDown from "public/icons/icon-arrow-down.svg";

interface PropVenderUserEditorProps {
  value: string | null;
  stopEditingCallback: () => void;
}

const StyledSelect = styled(Select)`
    margin-top: 1.7rem;
    .mantine-Select-input {
        border-color: ${propcolors.gray[200]};
        background-color: white;
        color: ${propcolors.blackLight};
        font-family: "Inter", "system-ui";
    }
    .mantine-Select-item[data-selected] {
        background-color: ${propcolors.gray[150]};
        color: ${propcolors.blackLight};
    }
    .mantine-Select-rightSection {
        pointer-events: none;
    }
`;

export const propVendorUserEditor = memo(
  forwardRef<unknown, PropVenderUserEditorProps>((props, ref) => {
    const vendorUserListData = useVendorUserList();
    const [vendorUserList, setVendorUserList] = useState<
      | {
        id: number | null;
        name: string;
      }[]
      | null
    >(null);

    const refInput = useRef(null);
    const [selectedValue, setSelectedValue] = useState<string | null>(null);
    const handleChange = (value: string) => {
      setSelectedValue(value);
    };

    useEffect(() => {
      const user = vendorUserListData?.find(user => user.name === props.value);
      if (user) {
        setSelectedValue(user.id ? user.id.toString() : null);
      } else {
        setSelectedValue(props.value);
      }
    }, [props.value]);

    // selectedValueが更新された後に編集を停止する
    useEffect(() => {
      // selectedValueが初期値から変更された場合のみ、編集を停止する
      if (selectedValue !== null && selectedValue !== props.value) {
        props.stopEditingCallback();
      }
    }, [selectedValue, props.value, props.stopEditingCallback]);

    useEffect(() => {
      if (vendorUserListData) {
        const newData = [
          {
            id: null,
            name: "値を削除",
          },
          ...vendorUserListData,
        ];
        setVendorUserList(newData);
      }
    }, [vendorUserListData]);

    useImperativeHandle(ref, () => ({
      getValue: () => selectedValue,
    }));

    return (
      <div ref={refInput}>
        <StyledSelect
          className="vendorUserSelect"
          data={
            vendorUserList
              ? vendorUserList.map((user, index) => {
                return {
                  value: user.id ? user.id.toString() : "",
                  label: user.name,
                } as SelectItem;
              })
              : []
          }
          onChange={(value) => handleChange(value ?? "")}
          rightSection={<IconArrowDown />}
          value={selectedValue}
          searchable
        />
      </div>
    );
  })
);
