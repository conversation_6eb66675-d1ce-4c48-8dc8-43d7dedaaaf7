import styled from "@emotion/styled";
import { MIME_TYPE_IMAGE_LIST, MIME_TYPE_PDF } from "constants/ruleValidation";
import { Document, Page, pdfjs } from "react-pdf";
import CustomRendererPdfAsImage from "./CustomRenderPdfAsImage";
import { CustomNextImage } from "components/FileUpload/CustomNextImage";

interface Props {
  file: attached_files;
  onClick: () => void;
}

export const CustomPreviewFileChat = (props: Props) => {
  const { file, onClick } = props;
  pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.min.js`;
  const WIDTH = 240;

  return (
    <FileItemContent
      display={file.url.length ? "true" : "false"}
      onClick={onClick}
    >
      {file && MIME_TYPE_IMAGE_LIST.includes(file.contentType) ? (
        <CustomNextImage
          src={file.url}
          width={WIDTH}
          height={WIDTH}
          sizes="100vw"
          alt={file.name}
          style={{
            objectFit: "contain",
          }}
        />
      ) : (
        <></>
      )}
      {file.contentType === MIME_TYPE_PDF ? (
        <div
          style={{
            width: WIDTH,
            height: 240,
            maxWidth: WIDTH,
            maxHeight: 240,
          }}
        >
          <Document file={file.url}>
            <Page
              pageNumber={1}
              renderTextLayer={false}
              renderAnnotationLayer={false}
              renderMode="custom"
              customRenderer={CustomRendererPdfAsImage}
            ></Page>
          </Document>
        </div>
      ) : (
        <></>
      )}
    </FileItemContent>
  );
};

const FileItemContent = styled.div((props: { display: string }) => ({
  display: props.display == "true" ? "flex" : "none",
  cursor: "zoom-in",
  justifyContent: "center",
  height: 240,
}));
