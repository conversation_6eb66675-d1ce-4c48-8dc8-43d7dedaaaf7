import Document from "@tiptap/extension-document";
import Paragraph from "@tiptap/extension-paragraph";
import Text from "@tiptap/extension-text";
import History from "@tiptap/extension-history";
import PlaceHolder from "@tiptap/extension-placeholder";
import Link from "@tiptap/extension-link";
import { useEditor } from "@tiptap/react";
import { CustomMention } from "../extensions/mention";
import type { MentionItem } from "../message-box";

export const useMentionEditor = ({
  mentions,
  editable = true,
}: {
  mentions: MentionItem[];
  editable?: boolean;
}) => {
  const editor = useEditor(
    {
      editable,
      extensions: [
        Link,
        Document,
        Paragraph,
        Link,
        Text,
        PlaceHolder.configure({
          placeholder: "メッセージを入力してください",
        }),
        History,
        CustomMention(mentions),
      ],
      onUpdate: ({ editor }) => {
        const selection = editor.state.selection;
        editor.commands.setTextSelection(selection);
      },
    },
    [mentions, editable]
  );
  return { editor };
};
