import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

type MentionItem = {
  id: string;
  label: string;
};

export interface MentionListProps {
  items: MentionItem[];
  command: (item: MentionItem) => void;
}

export interface MentionListRef {
  onKeyDown: (props: { event: KeyboardEvent }) => boolean;
}

const MentionList = forwardRef<MentionListRef, MentionListProps>(
  (props, ref) => {
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [prevItems, setPrevItems] = useState<MentionItem[]>([]);
    const itemRefs = useRef<(HTMLButtonElement | null)[]>([]);
    if (!props.items.every((item, index) => item.id === prevItems[index]?.id)) {
      setSelectedIndex(0);
      setPrevItems(props.items);
    }

    const selectItem = (index: number) => {
      const item = props.items[index];

      if (item) {
        props.command(item);
      }
    };

    const upHandler = () => {
      setSelectedIndex(
        (selectedIndex + props.items.length - 1) % props.items.length
      );
    };

    const downHandler = () => {
      setSelectedIndex((selectedIndex + 1) % props.items.length);
    };

    const enterHandler = () => {
      selectItem(selectedIndex);
    };

    useEffect(() => {
      const currentItem = itemRefs.current[selectedIndex];
      currentItem?.scrollIntoView({
        block: "nearest",
        inline: "nearest",
      });
    }, [selectedIndex]);

    useImperativeHandle(ref, () => ({
      onKeyDown: ({ event }) => {
        if (event.key === "ArrowUp") {
          upHandler();
          return true;
        }

        if (event.key === "ArrowDown") {
          downHandler();
          return true;
        }

        if (event.key === "Enter") {
          enterHandler();
          return true;
        }

        return false;
      },
    }));

    return (
      <div
        css={{
          background: "white",
          borderRadius: "0.25rem",
          boxShadow: "0 0 0.5rem rgba(0, 0, 0, 0.1)",
          display: "flex",
          flexDirection: "column",
          maxHeight: "200px",
          minWidth: "200px",
          maxWidth: "500px",
          gap: "0.1rem",
          overflow: "auto",
          position: "relative",
        }}
      >
        {props.items.length
          ? props.items.map((item, index) => (
              <button
                type="button"
                css={{
                  alignItems: "center",
                  border: "solid 1px transparent",
                  display: "flex",
                  width: "100%",
                  backgroundColor:
                    index === selectedIndex ? "lightgray" : "white",
                  padding: "0.5rem",
                }}
                key={item.id}
                onClick={() => selectItem(index)}
                ref={(el) => (itemRefs.current[index] = el)}
              >
                {item.label}
              </button>
            ))
          : null}
      </div>
    );
  }
);

export default MentionList;
