import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { useEffect, useState } from "react";
import { MessageBox, type MentionItem } from "./message-box";
import { useMentionEditor } from "./hooks/use-mention-editor";

const meta = {
  title: "shared/form/message-box",
  component: MessageBox,
  parameters: {
    layout: "centered",
  },
  args: {
    editor: null,
  },
} satisfies Meta<typeof MessageBox>;

export default meta;
type Story = StoryObj<typeof meta>;

export const AsyncMentions: Story = {
  render: function Render() {
    const [mentions, setMentions] = useState<MentionItem[]>([]);
    const { editor } = useMentionEditor({
      mentions,
      editable: true,
    });
    useEffect(() => {
      setTimeout(() => {
        setMentions([
          {
            id: "hoge_id",
            label: "hoge_label",
          },
          {
            id: "fuga_id",
            label: "fuga_label",
          },
          {
            id: "piyo_id",
            label: "piyo_label",
          },
          {
            id: "foo_id",
            label: "foo_label",
          },
          {
            id: "bar_id",
            label: "bar_label",
          },
          {
            id: "baz_id",
            label: "baz_label",
          },
          {
            id: "qux_id",
            label: "qux_label",
          },
          {
            id: "quux_id",
            label: "quux_label",
          },
          {
            id: "too_long_id",
            label:
              "とても長いラベルで、表示が崩れないか確認するためのテストです。maxWidthを超えた場合、どうなるか確認するためのテストです。",
          },
        ]);
      }, 1000);
    }, []);
    return <MessageBox editor={editor} />;
  },
};
