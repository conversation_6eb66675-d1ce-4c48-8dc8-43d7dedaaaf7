import { EditorContent, type Editor } from "@tiptap/react";

export type MentionItem = {
  id: string;
  label: string;
};

type MessageBoxProps = {
  editor: Editor | null;
};

export const MessageBox = ({ editor }: MessageBoxProps) => {
  return (
    <div
      css={{
        width: "100%",
        ".tiptap": {
          minHeight: "62px",
          maxHeight: "260px",
          overflowY: "auto",
          padding: "8px",
          ...(editor?.isEditable && { border: "1px solid #eee" }),
          borderRadius: "4px",
          outline: "none",
          // placeholder
          "p.is-editor-empty:first-child::before": {
            color: "gray",
            content: "attr(data-placeholder)",
            float: "left",
            height: 0,
            pointerEvents: "none",
          },
          ".mention": {
            boxDecorationBreak: "clone",
            color: "red",
            padding: "0.1rem 0.3rem",
          },
        },
      }}
    >
      <EditorContent editor={editor} />
    </div>
  );
};
