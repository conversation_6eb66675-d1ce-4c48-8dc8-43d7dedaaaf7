import { toHTML } from ".";

describe("toHTML", () => {
  test("基本的なメンション変換", () => {
    const input = "@[user]%text:123% こんにちは";
    const expected =
      '<p><span class="mention" data-type="mention" data-id="123" data-label="user">@user</span> こんにちは</p>';
    expect(toHTML(input)).toBe(expected);
  });

  test("複数のメンションを含むケース", () => {
    const input =
      "@[hoge_label]%text:hoge_id% @[fuga_label]%text:fuga_id% hoge fuga";
    const expected =
      '<p><span class="mention" data-type="mention" data-id="hoge_id" data-label="hoge_label">@hoge_label</span> <span class="mention" data-type="mention" data-id="fuga_id" data-label="fuga_label">@fuga_label</span> hoge fuga</p>';
    expect(toHTML(input)).toBe(expected);
  });

  test("日本語を含むケース", () => {
    const input =
      "@[田中]%text:tanaka_id% こんにちは、@[鈴木]%text:suzuki_id% さん";
    const expected =
      '<p><span class="mention" data-type="mention" data-id="tanaka_id" data-label="田中">@田中</span> こんにちは、<span class="mention" data-type="mention" data-id="suzuki_id" data-label="鈴木">@鈴木</span> さん</p>';
    expect(toHTML(input)).toBe(expected);
  });

  test("空の入力", () => {
    const input = "";
    const expected = "<p></p>";
    expect(toHTML(input)).toBe(expected);
  });

  test("メンションのないテキスト", () => {
    const input = "こんにちは、世界！";
    const expected = "<p>こんにちは、世界！</p>";
    expect(toHTML(input)).toBe(expected);
  });

  test("複数行のテキスト", () => {
    const input = "@[user1]%text:123% line1\n\n@[user2]%text:456% line2";
    const expected =
      '<p><span class="mention" data-type="mention" data-id="123" data-label="user1">@user1</span> line1</p><p></p><p><span class="mention" data-type="mention" data-id="456" data-label="user2">@user2</span> line2</p>';
    expect(toHTML(input)).toBe(expected);
  });

  test("react-mentions が生成するテキストのパース", () => {
    const input =
      "@[channel]%text:all:channel% @[株式会社ABC]%text:株式会社ABC:vendor%";
    const expected =
      '<p><span class="mention" data-type="mention" data-id="all:channel" data-label="channel">@channel</span> <span class="mention" data-type="mention" data-id="株式会社ABC:vendor" data-label="株式会社ABC">@株式会社ABC</span></p>';
    expect(toHTML(input)).toBe(expected);
  });

  test("URLを<a>タグに変換", () => {
    const input = "Visit https://example.com for more info.";
    const expected =
      '<p>Visit <a href="https://example.com" target="_blank" rel="noopener noreferrer">https://example.com</a> for more info.</p>';
    expect(toHTML(input)).toBe(expected);
  });

  test("複数のURLを含むテキスト", () => {
    const input = "Check http://example.com and https://test.com";
    const expected =
      '<p>Check <a href="http://example.com" target="_blank" rel="noopener noreferrer">http://example.com</a> and <a href="https://test.com" target="_blank" rel="noopener noreferrer">https://test.com</a></p>';
    expect(toHTML(input)).toBe(expected);
  });

  test("URLとメンションが混在するケース", () => {
    const input = "@[user]%text:user% Check https://example.com";
    const expected =
      '<p><span class="mention" data-type="mention" data-id="user" data-label="user">@user</span> Check <a href="https://example.com" target="_blank" rel="noopener noreferrer">https://example.com</a></p>';
    expect(toHTML(input)).toBe(expected);
  });
});
