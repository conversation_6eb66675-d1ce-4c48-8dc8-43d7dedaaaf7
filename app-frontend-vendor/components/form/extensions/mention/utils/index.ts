/**
 * メンションテキストをHTML形式に変換する関数
 */
export const toHTML = (content: string) => {
  // 改行で分割して段落を作成
  const paragraphs = content.split("\n");

  // メンションパターンを検出する正規表現
  const mentionPattern = /@\[([^\]]+)\]%text:([^%]+)%/g;

  // URLパターンを検出する正規表現
  const urlPattern = /(https?:\/\/[-_.!~*\'()a-zA-Z0-9;\/?:\@&=+\$,%#]+)/g;

  // 各段落をHTMLに変換
  const htmlParagraphs = paragraphs.map((paragraph) => {
    let pos = 0;
    let result = "";
    let match;

    // メンションパターンを検出して変換
    while ((match = mentionPattern.exec(paragraph)) !== null) {
      // メンション前のテキストを追加
      result += paragraph.slice(pos, match.index);

      // メンションをspanタグに変換
      const label = match[1];
      const id = match[2];
      result += `<span class="mention" data-type="mention" data-id="${id}" data-label="${label}">@${label}</span>`;

      pos = match.index + match[0].length;
    }

    // 残りのテキストを追加
    result += paragraph.slice(pos);

    // URLを<a>タグに変換
    result = result.replace(
      urlPattern,
      '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>'
    );

    // 空の段落も含めて<p>タグで囲む
    return `<p>${result}</p>`;
  });

  // 全ての段落を結合
  return htmlParagraphs.join("");
};
