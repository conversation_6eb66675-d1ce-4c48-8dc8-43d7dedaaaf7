import Mention from "@tiptap/extension-mention";
import { ReactRenderer } from "@tiptap/react";
import type {
  MentionListProps,
  MentionListRef,
} from "components/form/mention-list";
import MentionList from "components/form/mention-list";
import type { MentionItem } from "components/form/message-box";
import type { GetReferenceClientRect } from "tippy.js";
import tippy from "tippy.js";
import { toHTML } from "./utils";

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    mention: {
      setMentionContent: (content: string) => ReturnType;
    };
  }
}

export const CustomMention = (mentions: MentionItem[]) =>
  Mention.extend({
    addCommands() {
      return {
        setMentionContent:
          (content) =>
          ({ commands }) => {
            return commands.setContent(toHTML(content));
          },
      };
    },
  }).configure({
    HTMLAttributes: { class: "mention" },
    renderText(props) {
      return `${props.options.suggestion.char}[${props.node.attrs.label}]%text:${props.node.attrs.id}%`;
    },
    suggestion: {
      items: ({ query }) =>
        mentions.filter((item) =>
          item.label.toLowerCase().includes(query.toLowerCase())
        ),
      render: () => {
        let component:
          | ReactRenderer<
              MentionListRef,
              MentionListProps & React.RefAttributes<MentionListRef>
            >
          | undefined;
        let popup: ReturnType<typeof tippy> | undefined;

        return {
          onStart: (props) => {
            component = new ReactRenderer(MentionList, {
              props,
              editor: props.editor,
            });

            if (!props.clientRect) return;

            popup = tippy("body", {
              getReferenceClientRect:
                props.clientRect as GetReferenceClientRect,
              appendTo: () => document.body,
              content: component.element,
              showOnCreate: true,
              interactive: true,
              trigger: "manual",
              placement: "top-start",
            });
          },
          onUpdate(props) {
            component?.updateProps(props);
            if (!props.clientRect) return;

            popup?.[0].setProps({
              getReferenceClientRect:
                props.clientRect as GetReferenceClientRect,
            });
          },
          onKeyDown(props) {
            if (props.event.key === "Escape") {
              popup?.[0].hide();
              return true;
            }
            return !!component?.ref?.onKeyDown(props);
          },
          onExit() {
            popup?.[0].destroy();
            component?.destroy();
          },
        };
      },
    },
  });
