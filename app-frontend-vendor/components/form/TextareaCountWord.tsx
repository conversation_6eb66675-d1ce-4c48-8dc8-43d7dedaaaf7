import styled from "@emotion/styled";
import { Textarea, TextareaProps } from "@mantine/core";
import { ReactNode, useRef, useState } from "react";
import { propcolors } from "styles/colors";

interface TextareaCountWordProps extends TextareaProps {
  validate: any;
  errorMessage?: ReactNode;
  wordCount: number;
  maxWordCount: number;
  title: string;
}

const Wrapper = styled("div")`
  .mantine-Textarea-wrapper {
    margin-bottom: 0;
  }

  .mantine-Textarea-error {
    display: none;
  }

  textarea {
    border: none !important;
  }
`;

const Title = styled.p({
  fontSize: 14,
  fontWeight: 600,
  marginBottom: "1rem",
});

const IconRequired = styled.span(
  (props: { required: boolean | undefined }) => ({
    color: propcolors.red[700],
    display: props.required ? "inline" : "none",
  })
);

const TextWordCount = styled.div(() => ({
  fontSize: 12,
  color: "#8992A0",
  fontWeight: 300,
  display: "flex",
  justifyContent: "flex-end",
  padding: 5,
  paddingRight: 15,
  backgroundColor: "#f7f8f9",
  borderRadius: "0.25rem",
  cursor: "text",
}));

const ErrorMessage = styled.div({
  color: propcolors.red[700],
  fontSize: 12,
  fontWeight: 300,
  marginTop: "calc(0.625rem / 2);"
});

export const TextareaCountWord = (props: TextareaCountWordProps) => {
  const { validate, title, errorMessage, wordCount, maxWordCount, ...rest } =
    props;
  const textareaRef = useRef<any>(null);
  const [focusStatus, setFocusStatus] = useState<boolean>(false);

  return (
    <Wrapper>
      <Title>
        {title}
        <IconRequired required={props.required}>*</IconRequired>
      </Title>
      <div
        style={{
          borderRadius: "0.25rem",
          border: `0.0625rem solid ${focusStatus ? propcolors.inputFocus : propcolors.border}`,
        }}
      >
        <Textarea
          ref={textareaRef}
          {...props}
          {...validate}
          onClick={() => {
            setFocusStatus(true);
          }}
          onBlur={() => {
            setFocusStatus(false);
          }}
        />
        <TextWordCount
          onClick={() => {
            setFocusStatus(true);
            if (textareaRef.current) textareaRef.current.focus();
          }}
        >
          {wordCount} / {maxWordCount}
        </TextWordCount>
      </div>

      <ErrorMessage>{errorMessage}</ErrorMessage>
    </Wrapper>
  );
};
