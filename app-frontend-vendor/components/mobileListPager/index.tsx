import styled from "@emotion/styled";
import { ArrowLeft16Filled, ArrowRight16Filled } from "@fluentui/react-icons";
import { Button } from "@mantine/core";

const Presentation: React.FC<{
  className?: string;
  setPageNo: (value: number) => void;
  pageNo: number;
  total: number;
  perPage?: number;
}> = ({ className, setPageNo, pageNo, total, perPage = 10 }) => {
  return (
    <div className={className}>
      <Button
        onClick={() => setPageNo(pageNo - 1)}
        disabled={pageNo === 0}
        leftIcon={<ArrowLeft16Filled />}
      >
        前の{perPage}件
      </Button>
      <Button
        onClick={() => setPageNo(pageNo + 1)}
        disabled={(pageNo + 1) * perPage > total}
        rightIcon={<ArrowRight16Filled />}
      >
        次の{perPage}件
      </Button>
    </div>
  );
};

const Styled = styled(Presentation)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-bottom: 6rem;
`;

export const MobileListPager: React.FC<{
  setPageNo: (value: number) => void;
  pageNo: number;
  total: number;
  perPage?: number;
}> = ({ setPageNo, pageNo, total, perPage }) => {
  return (
    <Styled
      setPageNo={setPageNo}
      pageNo={pageNo}
      total={total}
      perPage={perPage}
    />
  );
};
