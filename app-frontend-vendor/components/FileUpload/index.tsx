import styled from "@emotion/styled";
import React, { useEffect, useState } from "react";
import { propcolors } from "styles/colors";
import { useSetCompanyProps } from "utils/recoil/company/companyState";
import { useSessionUser } from "utils/recoil/sessionUserState";

type PresentationProps = {
  className?: string;
} & Omit<FileUploadItemProps, "onSuccess"> & { onSuccess: (data: any) => void };

type FileUploadItemProps = {
  label: string | React.ReactNode;
  fileName?: string | null;
  msgErrorFile?: string | null;
  onFileChange: (file: File | null) => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  label,
  onFileChange,
  msgErrorFile,
  fileName,
}) => {
  const user = useSessionUser();

  const [nameFile, setNameFile] = useState<string | number | null>(null);
  const [dragging, setDragging] = useState<boolean>(false);

  useEffect(() => {
    if (fileName) {
      setNameFile(fileName);
    }
  }, [fileName]);

  const uploadFile = (file: File) => {
    setNameFile(file.name);
    onFileChange(file);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files && event.target.files[0];
    if (file) {
      uploadFile(file);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragging(true);
  };

  const handleDragLeave = () => {
    setDragging(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragging(false);

    const file = event.dataTransfer.files && event.dataTransfer.files[0];
    if (file) {
      uploadFile(file);
    }
  };

  const id = `file-upload-${new Date().getTime().toString()}`;

  return (
    <div className={className}>
      <label>{label}</label>
      <div
        className={`box-upload-file ${dragging ? "dragging" : ""}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <label className="custom-file-upload">
          <h4>ここにドラッグ&ドロップまたは</h4>
          <label htmlFor={id} className="btn-select-file">
            {nameFile || "ファイルを選択"}
          </label>
        </label>
        <input
          id={id}
          type="file"
          onChange={handleFileChange}
          className="d-none"
        />
      </div>
      {msgErrorFile && <div className="message-error">{msgErrorFile}</div>}
    </div>
  );
};

const Styled = styled(Presentation)`
  button {
    all: unset;
    margin-right: 8px;
  }
  .message-error {
    word-break: break-word;
    color: ${propcolors.partnerRed};
    font-size: calc(0.875rem - 0.125rem);
    line-height: 1.2;
    display: block;
    margin-top: 6px;
  }
  display: grid;
  grid-template-columns: initial;
  align-items: center;
  padding: 0px;
  font-size: 14px;
  font-weight: 400 !important;
  .box-upload-file {
    h4,
    label {
      font-weight: 400 !important;
    }
  }
  .info {
    &-label {
      width: 150px;
      font-weight: 400;
      color: ${propcolors.blackLightLabel};
    }
    &-value {
      width: 100%;
      color: ${propcolors.blackLight};
      font-size: 14px;
      font-weight: 400;
      &-wrapper {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 48px;
        margin-top: 0.5rem;
        svg {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
  .btn-select-file {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: inherit;
    font-size: inherit;
    width: 125px;
    color: ${propcolors.blackLight} !important;
  }
  .icon-delete-file {
    border: 0px;
    cursor: pointer;
    vertical-align: middle;
    margin-left: 5px;
  }
  @media screen and (max-width: 1220px) {
    display: block;
    margin-top: 16px;
  }
`;

export const FileUpload: React.FC<FileUploadItemProps> = ({
  label,
  fileName,
  onFileChange,
  msgErrorFile,
}) => {
  const setCompanyProps = useSetCompanyProps();

  const handleEditSuccess = (updateData: Vendor) => {
    setCompanyProps(updateData);
  };

  return (
    <Styled
      onFileChange={onFileChange}
      fileName={fileName}
      label={label}
      msgErrorFile={msgErrorFile}
      onSuccess={handleEditSuccess}
    />
  );
};
