export const LeadsIcon: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M11.497 3.40796C11.4824 3.17149 11.412 2.94183 11.2916 2.73781C11.1711 2.53378 11.004 2.36119 10.804 2.23416C10.604 2.10714 10.3768 2.0293 10.1409 2.00702C9.90505 1.98473 9.66725 2.01864 9.447 2.10596L3.579 4.42996C3.11309 4.61459 2.71342 4.93499 2.43186 5.34957C2.15029 5.76416 1.99983 6.2538 2 6.75496V12.497C1.99987 12.737 2.05736 12.9736 2.16762 13.1868C2.27788 13.4 2.4377 13.5836 2.63365 13.7223C2.82961 13.8609 3.05597 13.9505 3.29373 13.9835C3.53148 14.0165 3.77369 13.992 4 13.912V7.90596C3.99994 7.45502 4.13539 7.01446 4.38877 6.64143C4.64215 6.2684 5.00178 5.98011 5.421 5.81396L11.497 3.40796ZM14.499 5.49996C14.4989 5.25536 14.4389 5.0145 14.3244 4.79836C14.2099 4.58223 14.0442 4.39738 13.8419 4.25993C13.6396 4.12248 13.4067 4.03659 13.1636 4.00976C12.9204 3.98292 12.6744 4.01595 12.447 4.10596L6.264 6.55596C5.89112 6.70354 5.57122 6.9598 5.34582 7.29148C5.12042 7.62316 4.99993 8.01494 5 8.41596V14.498C5.00006 14.7427 5.05999 14.9837 5.17457 15.1999C5.28916 15.4162 5.45491 15.6011 5.65738 15.7386C5.85984 15.8761 6.09287 15.9619 6.33614 15.9886C6.5794 16.0154 6.82551 15.9822 7.053 15.892L8 15.517V9.53696C7.99993 9.13594 8.12042 8.74416 8.34582 8.41248C8.57122 8.0808 8.89112 7.82454 9.264 7.67696L14.499 5.60396V5.49996V5.49996ZM15.948 6.10496C16.1755 6.01492 16.4216 5.9819 16.6648 6.00879C16.908 6.03567 17.141 6.12165 17.3433 6.25921C17.5457 6.39678 17.7113 6.58176 17.8258 6.79803C17.9402 7.01429 18 7.25528 18 7.49996V10.341C17.7713 9.99805 17.4745 9.70594 17.1279 9.48284C16.7813 9.25975 16.3925 9.11046 15.9857 9.04429C15.5789 8.97812 15.1628 8.99648 14.7634 9.09824C14.364 9.2 13.9898 9.38296 13.6643 9.63573C13.3387 9.88849 13.0687 10.2056 12.8712 10.5673C12.6736 10.9291 12.5527 11.3276 12.516 11.7381C12.4793 12.1487 12.5275 12.5624 12.6578 12.9534C12.788 13.3444 12.9975 13.7044 13.273 14.011C12.6519 14.0676 12.0745 14.3543 11.6539 14.8148C11.2334 15.2753 11.0002 15.8763 11 16.5C11 16.943 11.089 17.389 11.274 17.807L11.052 17.895C10.8245 17.985 10.5784 18.018 10.3352 17.9911C10.092 17.9643 9.85904 17.8783 9.65668 17.7407C9.45433 17.6032 9.2887 17.4182 9.17424 17.2019C9.05977 16.9856 8.99996 16.7447 9 16.5V9.87696C8.99995 9.5762 9.09031 9.28236 9.25936 9.0336C9.42841 8.78484 9.66834 8.59265 9.948 8.48196L15.948 6.10596V6.10496ZM17.5 12C17.5 12.5304 17.2893 13.0391 16.9142 13.4142C16.5391 13.7892 16.0304 14 15.5 14C14.9696 14 14.4609 13.7892 14.0858 13.4142C13.7107 13.0391 13.5 12.5304 13.5 12C13.5 11.4695 13.7107 10.9608 14.0858 10.5858C14.4609 10.2107 14.9696 9.99996 15.5 9.99996C16.0304 9.99996 16.5391 10.2107 16.9142 10.5858C17.2893 10.9608 17.5 11.4695 17.5 12ZM19 16.5C19 17.745 18 19 15.5 19C13 19 12 17.75 12 16.5C12 16.1021 12.158 15.7206 12.4393 15.4393C12.7206 15.158 13.1022 15 13.5 15H17.5C17.8978 15 18.2794 15.158 18.5607 15.4393C18.842 15.7206 19 16.1021 19 16.5Z" />
    </svg>
  );
};
