export const ProductsIcon: React.FC<{ className?: string }> = ({
  className,
}) => {
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M10 7.96L13.029 6.75L5.529 3.75L2.943 4.784C2.81252 4.83594 2.68999 4.90596 2.579 4.992L10 7.961V7.96ZM2.035 5.853C2.01167 5.95908 1.99994 6.06738 2 6.176V13.822C1.99995 14.1218 2.08977 14.4148 2.25786 14.6631C2.42594 14.9114 2.66459 15.1037 2.943 15.215L8.7 17.518C8.96 17.622 9.228 17.693 9.5 17.732V8.838L2.035 5.852V5.853ZM10.5 17.733C10.7742 17.6936 11.0427 17.6217 11.3 17.519L17.057 15.216C17.3356 15.1046 17.5743 14.9122 17.7424 14.6637C17.9105 14.4152 18.0003 14.122 18 13.822V6.176C18 6.066 17.988 5.957 17.965 5.852L10.5 8.838V17.732V17.733ZM17.421 4.993L14.375 6.212L6.875 3.212L8.7 2.48C9.53452 2.14615 10.4655 2.14615 11.3 2.48L17.057 4.783C17.1875 4.83495 17.31 4.90497 17.421 4.991V4.993Z" />
    </svg>
  );
};

export const DropDownIcons: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    className={className}
    width="8"
    height="4"
    viewBox="0 0 8 4"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M0 0L4 4L8 0H0Z" fill="#4D6071" />
  </svg>
);
