export const VendorIcon: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <svg
      className={className}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M4 2C3.46957 2 2.96086 2.21071 2.58579 2.58579C2.21071 2.96086 2 3.46957 2 4V17C2 17.2652 2.10536 17.5196 2.29289 17.7071C2.48043 17.8946 2.73478 18 3 18H10.5C10.6326 18 10.7598 17.9473 10.8536 17.8536C10.9473 17.7598 11 17.6326 11 17.5V15.5C11 15.3674 11.0527 15.2402 11.1464 15.1464C11.2402 15.0527 11.3674 15 11.5 15H14.5C14.6326 15 14.7598 15.0527 14.8536 15.1464C14.9473 15.2402 15 15.3674 15 15.5V17.5C15 17.6326 15.0527 17.7598 15.1464 17.8536C15.2402 17.9473 15.3674 18 15.5 18H17C17.2652 18 17.5196 17.8946 17.7071 17.7071C17.8946 17.5196 18 17.2652 18 17V7C18 6.46957 17.7893 5.96086 17.4142 5.58579C17.0391 5.21071 16.5304 5 16 5H12V4C12 3.46957 11.7893 2.96086 11.4142 2.58579C11.0391 2.21071 10.5304 2 10 2H4ZM11 5H10C9.46957 5 8.96086 5.21071 8.58579 5.58579C8.21071 5.96086 8 6.46957 8 7V17H3V4C3 3.73478 3.10536 3.48043 3.29289 3.29289C3.48043 3.10536 3.73478 3 4 3H10C10.2652 3 10.5196 3.10536 10.7071 3.29289C10.8946 3.48043 11 3.73478 11 4V5ZM5.25 9.5C5.44891 9.5 5.63968 9.42098 5.78033 9.28033C5.92098 9.13968 6 8.94891 6 8.75C6 8.55109 5.92098 8.36032 5.78033 8.21967C5.63968 8.07902 5.44891 8 5.25 8C5.05109 8 4.86032 8.07902 4.71967 8.21967C4.57902 8.36032 4.5 8.55109 4.5 8.75C4.5 8.94891 4.57902 9.13968 4.71967 9.28033C4.86032 9.42098 5.05109 9.5 5.25 9.5ZM6 5.75C6 5.94891 5.92098 6.13968 5.78033 6.28033C5.63968 6.42098 5.44891 6.5 5.25 6.5C5.05109 6.5 4.86032 6.42098 4.71967 6.28033C4.57902 6.13968 4.5 5.94891 4.5 5.75C4.5 5.55109 4.57902 5.36032 4.71967 5.21967C4.86032 5.07902 5.05109 5 5.25 5C5.44891 5 5.63968 5.07902 5.78033 5.21967C5.92098 5.36032 6 5.55109 6 5.75ZM5.25 15.5C5.44891 15.5 5.63968 15.421 5.78033 15.2803C5.92098 15.1397 6 14.9489 6 14.75C6 14.5511 5.92098 14.3603 5.78033 14.2197C5.63968 14.079 5.44891 14 5.25 14C5.05109 14 4.86032 14.079 4.71967 14.2197C4.57902 14.3603 4.5 14.5511 4.5 14.75C4.5 14.9489 4.57902 15.1397 4.71967 15.2803C4.86032 15.421 5.05109 15.5 5.25 15.5ZM6 11.75C6 11.9489 5.92098 12.1397 5.78033 12.2803C5.63968 12.421 5.44891 12.5 5.25 12.5C5.05109 12.5 4.86032 12.421 4.71967 12.2803C4.57902 12.1397 4.5 11.9489 4.5 11.75C4.5 11.5511 4.57902 11.3603 4.71967 11.2197C4.86032 11.079 5.05109 11 5.25 11C5.44891 11 5.63968 11.079 5.78033 11.2197C5.92098 11.3603 6 11.5511 6 11.75ZM11.25 9.5C11.0511 9.5 10.8603 9.42098 10.7197 9.28033C10.579 9.13968 10.5 8.94891 10.5 8.75C10.5 8.55109 10.579 8.36032 10.7197 8.21967C10.8603 8.07902 11.0511 8 11.25 8C11.4489 8 11.6397 8.07902 11.7803 8.21967C11.921 8.36032 12 8.55109 12 8.75C12 8.94891 11.921 9.13968 11.7803 9.28033C11.6397 9.42098 11.4489 9.5 11.25 9.5ZM12 11.75C12 11.9489 11.921 12.1397 11.7803 12.2803C11.6397 12.421 11.4489 12.5 11.25 12.5C11.0511 12.5 10.8603 12.421 10.7197 12.2803C10.579 12.1397 10.5 11.9489 10.5 11.75C10.5 11.5511 10.579 11.3603 10.7197 11.2197C10.8603 11.079 11.0511 11 11.25 11C11.4489 11 11.6397 11.079 11.7803 11.2197C11.921 11.3603 12 11.5511 12 11.75ZM14.75 9.5C14.5511 9.5 14.3603 9.42098 14.2197 9.28033C14.079 9.13968 14 8.94891 14 8.75C14 8.55109 14.079 8.36032 14.2197 8.21967C14.3603 8.07902 14.5511 8 14.75 8C14.9489 8 15.1397 8.07902 15.2803 8.21967C15.421 8.36032 15.5 8.55109 15.5 8.75C15.5 8.94891 15.421 9.13968 15.2803 9.28033C15.1397 9.42098 14.9489 9.5 14.75 9.5ZM15.5 11.75C15.5 11.9489 15.421 12.1397 15.2803 12.2803C15.1397 12.421 14.9489 12.5 14.75 12.5C14.5511 12.5 14.3603 12.421 14.2197 12.2803C14.079 12.1397 14 11.9489 14 11.75C14 11.5511 14.079 11.3603 14.2197 11.2197C14.3603 11.079 14.5511 11 14.75 11C14.9489 11 15.1397 11.079 15.2803 11.2197C15.421 11.3603 15.5 11.5511 15.5 11.75Z" />
    </svg>
  );
};
