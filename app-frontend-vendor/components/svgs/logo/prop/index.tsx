export const PropNormal: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    width="200"
    height="38"
    viewBox="0 0 200 38"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M77.5808 13.4147V10.7812H74.0547V26.7158H77.7147V19.3511C77.7147 16.4945 78.295 13.861 82.6245 13.861H83.2494V10.7812H82.5353C80.0804 10.7812 78.4735 11.6293 77.5808 13.4147Z"
      fill="black"
    />
    <path
      d="M132.974 13.4147V10.7812H129.492V26.7158H133.152V19.3511C133.152 16.4945 133.732 13.861 138.062 13.861H138.642V10.7812H137.928C135.518 10.7812 133.911 11.6293 132.974 13.4147Z"
      fill="black"
    />
    <path
      d="M166.855 10.7812H166.141C163.686 10.7812 162.079 11.6293 161.186 13.4147V10.7812H157.66V26.7158H161.32V19.3511C161.32 16.4945 161.945 13.861 166.23 13.861H166.855V10.7812Z"
      fill="black"
    />
    <path
      d="M93.3399 23.724C91.9563 23.724 91.2421 23.0098 91.2421 21.5369V13.8597H95.3931V10.7799H91.2421V6.62891H87.5821V10.7799H84.8594V13.8597H87.5821V21.5369C87.5821 25.063 89.3228 26.893 92.5812 26.893C93.7417 26.893 94.7683 26.7591 95.3039 26.5359L95.4378 26.4913V23.4115L95.17 23.4561C94.7236 23.5901 94.0987 23.724 93.3399 23.724Z"
      fill="black"
    />
    <path
      d="M105.212 10.4219C103.204 10.4219 101.686 11.1807 100.793 12.5643V10.7789H97.2227V26.7135H100.927V17.5188C100.927 15.1531 102.222 13.6802 104.364 13.6802C106.998 13.6802 107.399 16.0458 107.399 17.4741V26.7135H111.059V17.0278C111.059 12.8768 108.872 10.4219 105.212 10.4219Z"
      fill="black"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M127.754 18.4561V19.7059H116.194C116.417 22.3839 118.068 24.1247 120.434 24.1247C122.219 24.1247 123.469 23.3659 124.094 21.8037L124.138 21.6698H127.575L127.531 21.9822C126.683 25.062 123.915 27.1152 120.434 27.1152C115.747 27.1152 112.578 23.723 112.578 18.7685C112.578 13.9926 115.881 10.4219 120.3 10.4219C124.853 10.4219 127.754 13.5463 127.754 18.4561ZM124.272 16.8493C123.915 14.6175 122.398 13.2338 120.255 13.2338C117.89 13.2338 116.64 15.0639 116.283 16.8493H124.272Z"
      fill="black"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M182.695 18.7685C182.695 23.4998 179.258 27.1152 174.75 27.1152C170.197 27.1152 166.805 23.4998 166.805 18.7685C166.805 13.9926 170.197 10.4219 174.75 10.4219C179.258 10.4219 182.695 13.9926 182.695 18.7685ZM179.035 18.7685C179.035 16.2244 177.517 13.5463 174.75 13.5463C171.938 13.5463 170.465 16.2244 170.465 18.7685C170.465 21.2681 171.938 23.9015 174.75 23.9015C177.517 23.9015 179.035 21.2681 179.035 18.7685Z"
      fill="black"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M199.841 18.7685C199.841 23.4998 196.672 27.1152 192.521 27.1152C190.468 27.1152 188.638 26.1779 187.656 24.6603V32.2482H183.996V10.7789H187.567V12.8321C188.593 11.3592 190.423 10.4219 192.521 10.4219C196.672 10.4219 199.841 13.9926 199.841 18.7685ZM196.137 18.7685C196.137 16.269 194.664 13.5909 191.986 13.5909C189.486 13.5909 187.656 15.8227 187.656 18.7685C187.656 21.6698 189.486 23.9015 191.986 23.9015C194.664 23.9015 196.137 21.2681 196.137 18.7685Z"
      fill="black"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M68.4347 10.7789H72.0054V26.7135H68.3454V24.6603C67.3188 26.1779 65.5334 27.1152 63.4802 27.1152C59.2846 27.1152 56.1602 23.4998 56.1602 18.7685C56.1602 13.9926 59.2846 10.4219 63.4802 10.4219C65.5334 10.4219 67.4081 11.3592 68.4347 12.8321V10.7789ZM68.3454 18.7685C68.3454 15.8227 66.4707 13.6356 64.0158 13.6356C61.2931 13.6356 59.8202 16.269 59.8202 18.7685C59.8202 21.2681 61.2931 23.9462 64.0158 23.9462C66.4707 23.9462 68.3454 21.7144 68.3454 18.7685Z"
      fill="black"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M56.2035 11.7627C56.2035 15.646 53.1237 18.324 48.7049 18.324H44.7324C44.2861 18.324 43.929 18.7257 43.929 19.1721V26.7153H40.1797V5.24609H48.7049C53.1237 5.24609 56.2035 7.92417 56.2035 11.7627ZM52.4542 11.7627C52.4542 9.75419 50.8474 8.45978 48.3925 8.45978H44.7324C44.2861 8.45978 43.929 8.8615 43.929 9.30785V14.3069C43.929 14.7533 44.2861 15.1103 44.7324 15.1103H48.3925C50.8474 15.1103 52.4542 13.8159 52.4542 11.7627Z"
      fill="black"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M156.451 11.7627C156.451 15.646 153.371 18.324 148.953 18.324H144.98C144.489 18.324 144.132 18.7257 144.132 19.1721V26.7153H140.383V5.24609H148.953C153.371 5.24609 156.451 7.92417 156.451 11.7627ZM152.702 11.7627C152.702 9.75419 151.095 8.45978 148.596 8.45978H144.98C144.489 8.45978 144.132 8.8615 144.132 9.30785V14.3069C144.132 14.7533 144.489 15.1103 144.98 15.1103H148.596C151.095 15.1103 152.702 13.8159 152.702 11.7627Z"
      fill="black"
    />
    <path
      d="M17.057 37.5989C23.038 37.5989 27.8585 32.7784 27.8585 26.7974V11.3984C27.8585 17.3348 23.038 22.1554 17.057 22.1554H15.584C14.9145 22.1554 14.3789 22.691 14.3789 23.3605V36.4384C14.3789 37.0633 14.9145 37.5989 15.584 37.5989H17.057Z"
      fill="#C8C8C8"
    />
    <path
      d="M1.6605 29.8819C1.03562 29.8819 0.5 29.3462 0.5 28.7214V1.8067C0.5 1.13718 1.03562 0.601562 1.6605 0.601562H17.0594C23.0405 0.601562 27.861 5.4221 27.861 11.4031C27.861 17.3395 23.0405 22.1601 17.0594 22.1601H15.5865C14.917 22.1601 14.3814 22.6957 14.3814 23.3652V29.8819H1.6605Z"
      fill="url(#paint0_linear_17940_65072)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_17940_65072"
        x1="27.8637"
        y1="29.8903"
        x2="0.505355"
        y2="29.8903"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#FF0000" />
        <stop offset="1" stop-color="#FF8282" />
      </linearGradient>
    </defs>
  </svg>
);
