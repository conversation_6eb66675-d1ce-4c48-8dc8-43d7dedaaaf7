import { <PERSON>a, <PERSON>Obj } from "@storybook/react/*";
import { SSRAGGrid } from ".";

const meta = {
  title: "shared/SSRAGGrid",
  component: SSRAGGrid,
  decorators: [
    (Story) => (
      <div css={{ display: "grid" }}>
        <Story />
      </div>
    ),
  ],
  args: {
    rowModelType: "clientSide",
    columnDefs: [
      { field: "make" },
      { field: "model" },
      { field: "price" },
      { field: "electric" },
    ],
    rowData: [
      { make: "Tesla", model: "Model Y", price: 64950, electric: true },
      { make: "Ford", model: "F-Series", price: 33850, electric: false },
      { make: "Toyota", model: "Corolla", price: 29600, electric: false },
    ],
  },
} as Meta<typeof SSRAGGrid>;

export default meta;

export const Story = {} as StoryObj<typeof meta>;
