import { IDateFilterParams } from "ag-grid-community";

export const textFilterParams = {
    filterOptions: ['contains', 'notContains', 'equals'],
    textFormatter: (text: string) => {
        if (text == null) return null;
        return text
            .toLowerCase()
            .trim()
    },
    debounceMs: 200,
    maxNumConditions: 1,
};

export const selectFilterParams = {
    filterOptions: ['equals', 'notEqual'],
    textFormatter: (text: string) => {
        if (text == null) return null;
        return text
            .toLowerCase()
            .trim()
    },
    debounceMs: 200,
    maxNumConditions: 1,
}

export const userFilterParams = {
    filterOptions: ['equals', 'notEqual'],
    debounceMs: 200,
    maxNumConditions: 1,
}

export const numberFilterParams = {
    filterOptions: ['equals', 'notEqual', 'greaterThan', 'lessThan'],
    allowedCharPattern: '\\d', // 0から9までの数字のみ許可
    numberParser: (text: string) => {
        return text == null ? null : parseFloat(text);
    },
    numberFormatter: (value: number) => {
        return value == null ? null : value.toString();
    },
    debounceMs: 200,
    maxNumConditions: 1,
};


// TODO 日付のピッカーをmantineに
export const dateFilterParams: IDateFilterParams = {
    filterOptions: ['startsWith', 'endsWith'],
    maxNumConditions: 1,
    comparator: (filterLocalDateAtMidnight: Date, cellValue: string) => {
        var dateAsString = cellValue;
        if (dateAsString == null) return -1;
        var dateParts = dateAsString.split('/');
        var cellDate = new Date(
            Number(dateParts[2]),
            Number(dateParts[1]) - 1,
            Number(dateParts[0])
        );
        if (filterLocalDateAtMidnight.getTime() === cellDate.getTime()) {
            return 0;
        }
        if (cellDate < filterLocalDateAtMidnight) {
            return -1;
        }
        if (cellDate > filterLocalDateAtMidnight) {
            return 1;
        }
        return 0;
    },
};


// フィルターの状態をAPIリクエスト可能な形に整形
interface FilterModel {
    [key: string]: {
        type: string; // オペレータ（contains, equals...) 
        filterType: string; // text, date, select ...
        filter: string; // filter value
        dateFrom?: string;
        dateTo?: string;
    };
}

interface TransformedFilter {
    field: string;
    operator: string;
    value: string | undefined;
}

export function transformFilterModel(
    filterModel: FilterModel,
    prefectureList: Prefecture[],
    linkStatusList: LinkStatusList,
    LeadStatusList: LeadStatusList,
    contractStatusList: ContractStatusList,
    negotiationStatusList: NegotiationStatusList,
): TransformedFilter[] {
    if (!filterModel) {
        return [];
    }

    return Object.entries(filterModel).map(([field, details]) => {
        // フィルターAPI用に、field名を調整
        field = transformFieldName(field);
        let operator = transformOperator(details.type, details.filterType, field);
        let value: string | undefined = ""

        // 日付型の場合は開始日付、または終了日付のいずれかをvalueにセット
        if (details.filterType === "date") {
            if (null !== details.dateFrom && undefined !== details.dateFrom) {
                value = getYYYYMMDD(details.dateFrom);
            }
            else if (null !== details.dateTo && undefined !== details.dateTo) {
                value = getYYYYMMDD(details.dateTo);
            }
        }
        else {
            value = details.filter;
        }

        if (field === 'prefecture_id') {
            value = getPrefectureCode(prefectureList, value);
        }
        else if (field === 'link_status') {
            value = getKeyByLinkStatusName(linkStatusList, value);
        }
        else if (field === 'lead_status') {
            value = getKeyByLeadStatusName(LeadStatusList, value);
        }
        else if (field === 'contract_status') {
            value = getKeyByConstastStatusCode(contractStatusList, value);
        }
        else if (field.includes("sales_status")) {
            value = getKeyBySalesStatusValue(value);
        }
        // else if (field.includes("sellable_status")) {
        //     value = getKeyBySellableStatusValue(value);
        // }
        else if (field.includes("approval_status")) {
            value = getKeyByApprovalStatusValue(value);
        }
        else if (field.includes("negotiation_status")) {
            value = getKeyByNegotiationStatusName(negotiationStatusList,value);
        }

        // AGGridのoperator仕様をPROPのAPI仕様に修正
        if (operator === 'notEqual') {
            operator = 'NOTEQUALS'
        }
        else if (operator === 'greaterThan') {
            operator = 'GREATER_THAN'
        }
        else if (operator === 'lessThan') {
            operator = 'LESS_THAN'
        }
        else if (operator === 'startsWith') {
            operator = 'STARTS'
        }
        else if (operator === 'endsWith') {
            operator = 'ENDS'
        }

        return {
            field,
            operator,
            value,
        };
    }).filter(filter => filter.value !== undefined);
}

function transformOperator(type: string, filterType: string, field: string): string {
    const textOperatorMap: { [key: string]: string } = {
        equals: 'EQUALS',
        contains: 'INCLUDES',
        notContains: 'EXCLUDES'
    };

    const selectOperatorMap: { [key: string]: string } = {
        equals: 'MATCHES',
        notEqual: 'NOTEQUALS',
    };

    const numberOperatorMap: { [key: string]: string } = {
        equals: 'EQUALS',
        notEqual: 'NOTEQUALS',
        greaterThan: 'GRATER_THAN',
        lessThan: 'LESS_THAN',
    };

    const prefecttureOperatorMap: { [key: string]: string } = {
        equals: 'EQUALS',
        notEqual: 'NOTEQUALS',
    };

    if (filterType === 'text') {
        return textOperatorMap[type] || type;
    } else if (filterType === 'number') {
        return numberOperatorMap[type] || type;
    }

    if (field === 'prefecture_id') {
        return prefecttureOperatorMap[type] || type;
    }

    // 選択型は有料版AGGridの機能なため、個別で指定して実現
    if (field = 'link_status') {
        return selectOperatorMap[type] || type;
    }

    return type; // デフォルトの場合
}

function transformFieldName(field: string): string {
    if (field === 'link_status_name') {
        return 'link_status';
    }
    else if (field === 'contract_status_name') { // 契約ステータス
        return 'contract_status'
    }
    else if (field === 'lead_status_name') {
        return 'lead_status'
    }
    else if (field === 'vendor_user_name') {
        return 'vendor.user_name'
    }
    else if (field === 'partner_user_name') {
        return 'partner.user_name'
    }
    // カスタムカラム用
    else if (field.includes('custom_column')) {
        const fields = field.split('.');

        // クラッシュしないよう、if分岐
        // prefix, 型, idになっていない場合、そのまま送って400エラーにする
        if (fields.length === 3) {
            const prefix = fields[0];
            const customColumnType = fields[1];
            const customColumnId = fields[2];
            if (customColumnType === 'STRING' ||
                customColumnType === 'INTEGER' ||
                customColumnType === 'DATE' ||
                customColumnType === 'SELECT') {
                return prefix + '.' + customColumnId + '.' + 'value';
            }
            // if (customColumnType === 'SELECT') {
            //     return prefix + customColumnId + 'select_id'
            // }
            if (customColumnType === 'VENDOR_USER' || customColumnType === 'PARTNER_USER') {
                return prefix + '.' + customColumnId + '.' + 'user_name'
            }
            if (customColumnType === 'FILE') {
                return prefix + '.' + customColumnId + '.' + 'file_name'
            }
        }
    }
    // 商品カラム用
    else if (field.includes('product')) {
        const fields = field.split('.');
        if (fields.length === 3) {
            const prefix = fields[0];
            const columnType = fields[1]; // sales_status or sellable_status or approval_status
            const productId = fields[2];
            return prefix + '.' + productId + '.' + columnType;
        }
    }

    return field;
}

function getPrefectureCode(prefectureList: Prefecture[], prefectureName: string): string | undefined {
    const prefecture = prefectureList.find(p => p.label === prefectureName);
    return prefecture ? (prefecture.value).toString() : undefined;
}

function getKeyByLinkStatusName(linkStatusList: LinkStatusList, name: string): string | undefined {
    for (const key in linkStatusList) {
        if (linkStatusList[key].name === name) {
            return key;
        }
    }
    return undefined; // 一致するものが見つからない場合はundefinedを返す
}

function getKeyByLeadStatusName(leadStatusList: LeadStatusList, name: string): string | undefined {
    for (const key in leadStatusList) {
        if (leadStatusList[key].name === name) {
            return key;
        }
    }
    return undefined; // 一致するものが見つからない場合はundefinedを返す
}

function getKeyByNegotiationStatusName(negotiationStatusList: NegotiationStatusList, name: string): string | undefined {
    for (const key in negotiationStatusList) {
        if (negotiationStatusList[key].name === name) {
            return key;
        }
    }
    return undefined; // 一致するものが見つからない場合はundefinedを返す
}

function getKeyByConstastStatusCode(contractStatusList: ContractStatusList, name: string): string | undefined {
    for (const key in contractStatusList) {
        if (contractStatusList[key].name === name) {
            return key;
        }
    }
    return undefined; // 一致するものが見つからない場合はundefinedを返す
}

// input : yyyy-mm-dd hh:MM:ss
// output : yyyy-mm-dd
function getYYYYMMDD(dateTimeString: string): string {
    return dateTimeString.split(" ")[0];
}

function getKeyBySalesStatusValue(value: string): string | undefined {
    if (value === "有効") {
        return "ENABLED"
    }
    else if (value === "無効") {
        return "DISABLED"
    }
    else {
        return undefined;
    }
}

function getKeyByApprovalStatusValue(value: string): string | undefined {
    if (value === "未対応") {
        return "PENDING"
    }
    else if (value === "承認") {
        return "APPROVE"
    }
    else if (value === "否認"){
        return "DENY"
    }
    else {
        return undefined;
    }
}
