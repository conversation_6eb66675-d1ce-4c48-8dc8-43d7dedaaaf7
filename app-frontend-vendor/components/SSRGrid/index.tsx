import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.min.css";
import styled from "@emotion/styled";
import type {
  ColDef,
  PaginationNumberFormatterParams,
  SortChangedEvent,
} from "ag-grid-community";
import LoadingSkeletonTable from "components/LoadingSkeleton/table";
import { IconSortAscending } from "components/svgs/base64/iconSortAscending";
import type React from "react";
import { type ComponentProps, useCallback, useMemo } from "react";
import { propcolors } from "styles/colors";
import { AG_GRID_LOCALE_JA } from "utils/aggrid/locale.ja";

const Presentation: React.FC<{
  columnDefs: ColDef[];
  rowData?: ComponentProps<typeof AgGridReact>["rowData"];
  rowModelType?: ComponentProps<typeof AgGridReact>["rowModelType"];
  className?: string;
  onRowSelected?: (event: any) => void;
  domLayout?: "autoHeight";
  onCellValueChanged?: (event: any) => void;
  onPaginationChanged?: (event: any) => void;
  onGridReady?: (params: any) => void;
  onFilterChanged?: () => void;
  onSortChanged?: (params: SortChangedEvent) => void;
  gridRef: any;
  components?: {
    [p: string]: any;
  };
  isLoadingOverlaySkeleton?: boolean;
  paginationPageSize?: number;
}> = ({
  columnDefs,
  rowData,
  rowModelType = "infinite",
  className,
  onRowSelected,
  domLayout = "autoHeight",
  onCellValueChanged,
  onGridReady,
  onFilterChanged,
  onSortChanged,
  gridRef,
  components,
  isLoadingOverlaySkeleton = false,
  paginationPageSize = 50,
}) => {
  const NoRowsOverlay = () => <div>データがありません</div>;
  const LoadingOverlay = () => <div>読み込み中</div>;
  const LoadingOverlaySkeleton = () => (
    <div
      style={{
        position: "absolute",
        top: 40,
        left: 0,
        width: "100%",
      }}
    >
      <LoadingSkeletonTable />
    </div>
  );

  const loadingOverlayComponent = useMemo(() => {
    return isLoadingOverlaySkeleton ? LoadingOverlaySkeleton : LoadingOverlay;
  }, [isLoadingOverlaySkeleton]);
  const noRowsOverlayComponent = useMemo(() => {
    return NoRowsOverlay;
  }, []);
  const noRowsOverlayComponentParams = useMemo(() => {
    return {
      noRowsMessageFunc: () =>
        "No rows found at: " + new Date().toLocaleTimeString(),
    };
  }, []);

  const paginationNumberFormatter = useCallback(
    (params: PaginationNumberFormatterParams) => {
      const totalRecords = params.api.getDisplayedRowCount();
      const currentPage = params.api.paginationGetCurrentPage() + 1;
      let startRange =
        (currentPage - 1) * params.api.paginationGetPageSize() + 1;
      const endRange = Math.min(
        currentPage * params.api.paginationGetPageSize(),
        totalRecords,
      );
      if (totalRecords === 0) startRange = 0;
      return `${totalRecords} <span class='total-of'>件中</span>${startRange} - ${endRange} 件`;
    },
    [],
  );

  return (
    <div className={`ag-theme-alpine ${className ? className : ""}`}>
      <AgGridReact
        ref={gridRef}
        columnDefs={columnDefs}
        rowData={rowData}
        localeText={{
          ...AG_GRID_LOCALE_JA,
          page: "",
          of: "中",
        }}
        //localeText={AG_GRID_LOCALE_JA}
        rowHeight={88}
        paginationNumberFormatter={paginationNumberFormatter}
        pagination={true}
        rowModelType={rowModelType}
        paginationPageSize={paginationPageSize}
        cacheBlockSize={paginationPageSize}
        onGridReady={onGridReady}
        onFilterChanged={onFilterChanged}
        onSortChanged={onSortChanged}
        rowSelection="single"
        onRowSelected={onRowSelected}
        onCellValueChanged={onCellValueChanged}
        animateRows={true}
        loadingOverlayComponent={loadingOverlayComponent}
        noRowsOverlayComponent={noRowsOverlayComponent}
        noRowsOverlayComponentParams={noRowsOverlayComponentParams}
        components={components}
        headerHeight={56}
      />
    </div>
  );
};

export const SSRAGGrid = styled(Presentation)`
  min-height: 500px;
  --ag-header-background-color: #ffffff;
  --ag-border-color: #e8eaed;
  font-family: "Inter", "system-ui";

  .ag-paging-number {
    font-size: 14px;
    line-height: 14px;
  }
  .ag-paging-panel {
    background-color: ${propcolors.white};
    height: 64px;
  }

  .total-of {
    margin-right: 8px;
  }

  .ag-icon-menu::before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url(${IconSortAscending});
    background-size: contain;
    background-repeat: no-repeat;
  }
  .ag-paging-row-summary-panel {
    display: none !important;
  }

  .ag-root-wrapper {
    border: 1px solid ${propcolors.border};
    border-left: none;
    border-bottom: none;
  }
  .ag-paging-button {
    border: 1px solid ${propcolors.gray[200]};
    background-color: ${propcolors.white};
    color: ${propcolors.blackLight};
    padding: 7px;
    margin: 0 5px;
    border-radius: 4px;
    cursor: pointer;
    width: 32px;
    height: 32px;
  }
  .ag-cell,
  .ag-header-cell {
    padding-left: 24px;
    padding-right: 0;
  }
  .ag-paging-description {
    order: -1;
    margin-right: 11px;
  }
  .ag-paging-button.ag-disabled {
    border: 1px solid ${propcolors.gray[200]};
    color: ${propcolors.gray[200]};
    background-color: ${propcolors.gray[150]};
    cursor: not-allowed;
  }

  .ag-paging-button-disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .ag-paging-page-summary-panel {
    color: #333;
    font-weight: bold;
  }
  .ag-header-cell-text {
    font-size: 12px;
    font-weight: 600;
    color: #666666;
  }

  .ag-cell {
    line-height: 88px;
    font-size: 14px;
    color: #222222;
  }

  .ag-row-hover {
    &:before {
      background-color: ${propcolors.gray[150]};
    }
  }

  .ag-row-selected {
    &:before {
      background-color: ${propcolors.gray[150]}!important;
      background-image: none !important;
    }
  }
  .ag-cell-focus {
    border-style: none !important;
    border-color: white !important;
  }

  .ag-theme-alpine {
    // --ag-card-radius: 10px;
    --ag-card-shadow: 0 10px 40px ${propcolors.red};
    --ag-popup-shadow: var(--ag-card-shadow);
  }
  .ag-icon-menu {
    color: ${propcolors.greyDefault};
    font-size: 1.4rem;
    margin-top: -8px;
    // margin-left: -30px;
    margin-right: 10px;
  }
  .ag-ltr .ag-header-cell-resize {
    right: 0px;
    top: -2px;
  }
  .ag-row-odd {
    background-color: white !important;
  }
  .ag-header {
    height: 48px !important;
    min-height: 48px !important;
    line-height: 48px !important;
    box-shadow: 0px 1px 3px 0px #f3f3f3;
    z-index: 1;
  }
  .ag-paging-description span:nth-of-type(3),
  .ag-paging-description span:nth-of-type(4) {
    display: none;
  }
  .ag-disabled {
    .ag-icon {
      display: inline-block;
      background-size: contain;
      background-repeat: no-repeat;
      margin-right: 4px;
      &:before {
        filter: invert(200%);
      }
    }
  }
  .ag-icon-last::before {
    content: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNy4wMTUwNCA3LjQ2NjIxTDMuMjk2MjkgMTEuMTg1QzMuMDM5MjYgMTEuNDQyIDIuNjIzNjMgMTEuNDQyIDIuMzY5MzQgMTEuMTg1TDEuNzUxMzcgMTAuNTY3QzEuNDk0MzQgMTAuMzEgMS40OTQzNCA5Ljg5NDM0IDEuNzUxMzcgOS42NDAwNEw0LjM4NzMgNy4wMDQxTDEuNzUxMzcgNC4zNjgxNkMxLjQ5NDM0IDQuMTExMTMgMS40OTQzNCAzLjY5NTUxIDEuNzUxMzcgMy40NDEyMUwyLjM2NjYgMi44MTc3N0MyLjYyMzYzIDIuNTYwNzQgMy4wMzkyNiAyLjU2MDc0IDMuMjkzNTUgMi44MTc3N0w3LjAxMjMgNi41MzY1MkM3LjI3MjA3IDYuNzkzNTUgNy4yNzIwNyA3LjIwOTE4IDcuMDE1MDQgNy40NjYyMVpNMTIuMjY1IDYuNTM2NTJMOC41NDYyOSAyLjgxNzc3QzguMjg5MjYgMi41NjA3NCA3Ljg3MzYzIDIuNTYwNzQgNy42MTkzNCAyLjgxNzc3TDcuMDAxMzcgMy40MzU3NEM2Ljc0NDM0IDMuNjkyNzcgNi43NDQzNCA0LjEwODQgNy4wMDEzNyA0LjM2MjdMOS42MzczIDYuOTk4NjNMNy4wMDEzNyA5LjYzNDU3QzYuNzQ0MzQgOS44OTE2IDYuNzQ0MzQgMTAuMzA3MiA3LjAwMTM3IDEwLjU2MTVMNy42MTkzNCAxMS4xNzk1QzcuODc2MzcgMTEuNDM2NSA4LjI5MTk5IDExLjQzNjUgOC41NDYyOSAxMS4xNzk1TDEyLjI2NSA3LjQ2MDc0QzEyLjUyMjEgNy4yMDkxOCAxMi41MjIxIDYuNzkzNTUgMTIuMjY1IDYuNTM2NTJaIiBmaWxsPSIjMjIyMjIyIi8+DQo8L3N2Zz4NCg==);
  }
  .ag-icon-next::before {
    content: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTAuNDE3NSA3LjQ3MzQyTDUuMTAzMzcgMTIuNzg3NUM0Ljg0NzA4IDEzLjA0MzggNC40MzE1NiAxMy4wNDM4IDQuMTc1MyAxMi43ODc1TDMuNTU1NDkgMTIuMTY3N0MzLjI5OTY0IDExLjkxMTggMy4yOTkxNSAxMS40OTcyIDMuNTU0NCAxMS4yNDA3TDcuNzY1OTEgNy4wMDkzN0wzLjU1NDQgMi43NzgwNkMzLjI5OTE1IDIuNTIxNjEgMy4yOTk2NCAyLjEwNjk0IDMuNTU1NDkgMS44NTEwOEw0LjE3NTMgMS4yMzEyOEM0LjQzMTU5IDAuOTc0OTg5IDQuODQ3MSAwLjk3NDk4OSA1LjEwMzM3IDEuMjMxMjhMMTAuNDE3NCA2LjU0NTM1QzEwLjY3MzcgNi44MDE2MSAxMC42NzM3IDcuMjE3MTMgMTAuNDE3NSA3LjQ3MzQyWiIgZmlsbD0iIzIyMjIyMiIvPg0KPC9zdmc+DQo=);
  }
  .ag-icon-first::before {
    content: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNNy4wMDEwMyA2LjUzNjUyTDEwLjcxOTggMi44MTc3N0MxMC45NzY4IDIuNTYwNzQgMTEuMzkyNCAyLjU2MDc0IDExLjY0NjcgMi44MTc3N0wxMi4yNjQ3IDMuNDM1NzRDMTIuNTIxNyAzLjY5Mjc3IDEyLjUyMTcgNC4xMDg0IDEyLjI2NDcgNC4zNjI2OUw5LjYzMTUgNy4wMDEzN0wxMi4yNjc0IDkuNjM3M0MxMi41MjQ1IDkuODk0MzMgMTIuNTI0NSAxMC4zMSAxMi4yNjc0IDEwLjU2NDNMMTEuNjQ5NSAxMS4xODVDMTEuMzkyNCAxMS40NDIgMTAuOTc2OCAxMS40NDIgMTAuNzIyNSAxMS4xODVMNy4wMDM3NiA3LjQ2NjIxQzYuNzQ0IDcuMjA5MTggNi43NDQgNi43OTM1NSA3LjAwMTAzIDYuNTM2NTJaTTEuNzUxMDMgNy40NjYyMUw1LjQ2OTc4IDExLjE4NUM1LjcyNjgxIDExLjQ0MiA2LjE0MjQzIDExLjQ0MiA2LjM5NjczIDExLjE4NUw3LjAxNDcgMTAuNTY3QzcuMjcxNzMgMTAuMzEgNy4yNzE3MyA5Ljg5NDM0IDcuMDE0NyA5LjY0MDA0TDQuMzgxNSA3LjAwMTM3TDcuMDE3NDMgNC4zNjU0M0M3LjI3NDQ2IDQuMTA4NCA3LjI3NDQ2IDMuNjkyNzcgNy4wMTc0MyAzLjQzODQ4TDYuMzk5NDYgMi44MTc3N0M2LjE0MjQzIDIuNTYwNzQgNS43MjY4MSAyLjU2MDc0IDUuNDcyNTEgMi44MTc3N0wxLjc1Mzc2IDYuNTM2NTJDMS40OTQgNi43OTM1NSAxLjQ5NCA3LjIwOTE4IDEuNzUxMDMgNy40NjYyMVoiIGZpbGw9IiMyMjIyMjIiLz4NCjwvc3ZnPg0K);
  }
  .ag-icon-previous::before {
    content: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMy41NTU0NCA2LjU0NTJMOC44Njk3IDEuMjMxMjJDOS4xMjU5MSAwLjk3NTAxIDkuNTQxNTMgMC45NzUwMSA5Ljc5Nzc0IDEuMjMxMjJMMTAuNDE3NiAxLjg1MTFDMTAuNjczNiAyLjEwNzA0IDEwLjY3MzggMi41MjE1NyAxMC40MTg3IDIuNzc4MDZMNi4yMDY5NiA3LjAwOTIzTDEwLjQxODQgMTEuMjQwN0MxMC42NzM4IDExLjQ5NzIgMTAuNjczMyAxMS45MTE3IDEwLjQxNzQgMTIuMTY3Nkw5Ljc5NzQ3IDEyLjc4NzVDOS41NDEyNiAxMy4wNDM3IDkuMTI1NjMgMTMuMDQzNyA4Ljg2OTQyIDEyLjc4NzVMMy41NTU0NCA3LjQ3MzI1QzMuMjk5MjMgNy4yMTcwNCAzLjI5OTIzIDYuODAxNDIgMy41NTU0NCA2LjU0NTJaIiBmaWxsPSIjMjIyMjIyIi8+DQo8L3N2Zz4NCg==);
  }
  .ag-paging-description span {
    font-size: 14px;
    line-height: 21px;
    font-weight: 400 !important;
    color: ${propcolors.blackLight};
  }
`;
