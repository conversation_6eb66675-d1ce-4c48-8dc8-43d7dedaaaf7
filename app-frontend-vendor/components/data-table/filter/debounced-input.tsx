import { NumberInput, Textarea, TextInput } from "@mantine/core";
import { useCallback, useEffect, useState } from "react";
import { debounce, debounceNumber } from "./debounce";

export const DebouncedInput = ({
  value: initialValue,
  onChange,
  wait = 500,
  type = "text",
}: {
  value: string;
  onChange: (value: string) => void;
  wait?: number;
  type?: "text" | "longtext";
}) => {
  const [value, setValue] = useState(() => initialValue);

  const execute = useCallback(debounce(onChange, wait), []);

  useEffect(() => {
    if (value === initialValue) return;
    execute(value);
  }, [execute, initialValue, value]);

  return type === "text" ? (
    <TextInput
      css={{ width: "100%" }}
      size="sm"
      value={value}
      onChange={(e) => setValue(e.target.value)}
    />
  ) : (
    <Textarea
      css={{ width: "100%" }}
      value={value}
      onChange={(e) => setValue(e.target.value)}
    />
  );
};

export const DebouncedNumberInput = ({
  value: initialValue,
  onChange,
  wait = 500,
  isArrowUnderZero = false,
}: {
  value: number;
  onChange: (value: number) => void;
  wait?: number;
  isArrowUnderZero?: boolean;
}) => {
  const [value, setValue] = useState(() => initialValue);
  const execute = useCallback(debounceNumber(onChange, wait), []);

  useEffect(() => {
    if (value === initialValue) return;
    execute(value);
  }, [execute, initialValue, value]);

  return (
    <NumberInput
      css={{ width: "100%" }}
      size="sm"
      value={value}
      min={isArrowUnderZero ? undefined : 0}
      onChange={(num) => setValue(num === "" ? 0 : num)}
    />
  );
};
