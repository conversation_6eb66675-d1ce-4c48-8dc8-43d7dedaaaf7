import { NumberInput, Select } from "@mantine/core";
import { Variant } from "../type";
import { DebouncedInput } from "./debounced-input";
import dayjs from "dayjs";

export const ValueInput = ({
  variant,
  value,
  selectOptions,
  onChange,
}: {
  variant: Variant;
  value: string;
  selectOptions?: { value: string; label: string }[];
  onChange: (value: string | number | null) => void;
}) => {
  const emptyOption = { value: "", label: "(未選択)" };
  switch (variant) {
    case "SELECT":
      return (
        <Select
          data={[emptyOption, ...(selectOptions ?? [])]}
          onChange={onChange}
          value={value}
        />
      );
    case "DATE":
      return (
        <input
          css={{ width: "100%" }}
          type="date"
          value={value}
          onChange={(e) => onChange(e.target.value)}
        />
      );
    case "DATETIME_LOCAL":
      const isoValue = value
        ? dayjs(value, "YYYY-MM-DD HH:mm").format("YYYY-MM-DDTHH:mm")
        : "";
      return (
        <input
          css={{ width: "100%" }}
          type="datetime-local"
          value={isoValue}
          onChange={(e) => {
            const v = e.target.value;
            if (!v) {
              onChange(null);
            } else {
              const formatted = dayjs(v).format("YYYY-MM-DD HH:mm");
              onChange(formatted);
            }
          }}
        />
      );
    case "INTEGER":
      return <NumberInput value={Number(value)} onChange={onChange} />;
    default:
      return <DebouncedInput onChange={onChange} value={value} />;
  }
};
