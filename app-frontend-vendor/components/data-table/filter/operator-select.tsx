import { Select } from "@mantine/core";
import { Operator } from "features/partners/type/api";
import { Variant } from "../type";

type OperatorOptions = { value: Operator; label: string }[];

const TEXT_OPTIONS: OperatorOptions = [
  { value: "INCLUDES", label: "含む" },
  { value: "EXCLUDES", label: "含まない" },
  { value: "MATCHES", label: "完全一致" },
];

const INTEGER_OPTIONS: OperatorOptions = [
  { value: "EQUALS", label: "等しい" },
  { value: "NOTEQUALS", label: "等しくない" },
  { value: "GREATER_THAN", label: "より大きい" },
  { value: "LESS_THAN", label: "より小さい" },
];

const SELECT_OPTIONS: OperatorOptions = [
  { value: "EQUALS", label: "等しい" },
  { value: "NOTEQUALS", label: "等しくない" },
];

const DATE_OPTIONS: OperatorOptions = [
  { value: "STARTS", label: "開始" },
  { value: "ENDS", label: "終了" },
];

export const VARIANT_OPERATOR_MAP: {
  [key in Variant]: OperatorOptions;
} = {
  STRING: TEXT_OPTIONS,
  // NOTE: LONG_TEXT はフィルタ対象ではないため空配列
  LONG_TEXT: [],
  SELECT: SELECT_OPTIONS,
  FILE: TEXT_OPTIONS,
  VENDOR_USER: TEXT_OPTIONS,
  PARTNER_USER: TEXT_OPTIONS,
  INTEGER: INTEGER_OPTIONS,
  DATE: DATE_OPTIONS,
  DATETIME_LOCAL: DATE_OPTIONS,
};

export const OperatorSelect = ({
  initailValue,
  variant,
  onChange,
}: {
  initailValue?: Operator;
  variant: Variant;
  onChange: (value: Operator) => void;
}) => {
  const options = VARIANT_OPERATOR_MAP[variant];
  return (
    <Select
      value={initailValue ?? options.at(0)?.value}
      data={options}
      onChange={onChange}
    />
  );
};
