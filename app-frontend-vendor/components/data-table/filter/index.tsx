import type { Column } from "@tanstack/react-table";
import type { Operator } from "features/partners/type/api";
import type { FilterValue } from "features/partners/type/filter";
import { useCallback, useState } from "react";
import { OperatorSelect, VARIANT_OPERATOR_MAP } from "./operator-select";
import { ValueInput } from "./value-input";

export const Filter = <T,>({ column }: { column: Column<T> }) => {
  const columnFilterValue = column.getFilterValue() as FilterValue | undefined;
  const customize = column.columnDef.meta?.customize;
  const initialOperator =
    columnFilterValue?.operator ??
    VARIANT_OPERATOR_MAP[customize?.variant ?? "STRING"].at(0)?.value ??
    "EQUALS";
  const [operator, setOperator] = useState<Operator>(initialOperator);
  const [value, setValue] = useState<string | number | null>(
    columnFilterValue?.value ?? null,
  );

  const handleOperatorChange = useCallback(
    (operator: string) => {
      setOperator(operator as Operator);
      const filterValue = value
        ? { operator, value: String(value) }
        : undefined;
      column.setFilterValue(filterValue);
    },
    [column, value],
  );

  const handleValueChange = useCallback(
    (value: string | number | null) => {
      setValue(value);
      const filterValue = value
        ? { operator, value: String(value) }
        : undefined;
      column.setFilterValue(filterValue);
    },
    [column, operator],
  );

  if (!customize?.variant) {
    return null;
  }
  return (
    <div css={{ fontWeight: "normal" }}>
      <OperatorSelect
        initailValue={operator}
        variant={customize.variant}
        onChange={handleOperatorChange}
      />
      <ValueInput
        variant={customize.variant}
        value={value ? String(value) : ""}
        selectOptions={
          customize?.variant === "SELECT" ? customize?.selectOptions : undefined
        }
        onChange={handleValueChange}
      />
    </div>
  );
};
