/**
 * @param func debounce したい関数
 * @param wait 関数を実行するまでの待ち時間
 * @returns debounce された関数
 */

export const debounce = (func: (input: string) => void, wait: number = 500) => {
  let timeout: NodeJS.Timeout;
  return (text: string) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func(text);
    }, wait);
  };
};

export const debounceNumber = (
  func: (input: number) => void,
  wait: number = 500
) => {
  let timeout: NodeJS.Timeout;
  return (num: number) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func(num);
    }, wait);
  };
};
