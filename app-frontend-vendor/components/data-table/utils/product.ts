/** columnId（sort filterの key）として使われる想定 */
export const getProductId = (product: Product) =>
  ["product", product.product_id, "sales_status"].join(".");

export const getProductNegotiationId = (product: Product) =>
  ["product", product.product_id, "negotiation_status"].join(".");

export const getProductApprovalId = (product: Product) =>
  ["product", product.product_id, "approval_status"].join(".");

export const extractProductColumnId = (columnId: string) => {
  const isProductColumn = columnId.startsWith("product");
  if (!isProductColumn) {
    return { isProductColumn: false } as const;
  }
  const [_, productId, suffix] = columnId.split(".");
  return {
    isProductColumn: true,
    productId: Number(productId),
    suffix,
  } as const;
};
