import { extractProductColumnId, getProductId } from "./product";

describe("extractProductColumnId", () => {
  test.each([
    [
      "product.1.sales_status",
      { isProductColumn: true, productId: 1, suffix: "sales_status" },
    ],
    ["invalid_prefix.1.sales_status", { isProductColumn: false }],
  ])("columnId: %s -> %o", (columnId, expected) => {
    const actual = extractProductColumnId(columnId);
    expect(actual).toEqual(expected);
  });
});

describe("getProductId", () => {
  test.each([
    [{ product_id: 1 } as unknown as Product, "product.1.sales_status"],
    [{ product_id: 2 } as unknown as Product, "product.2.sales_status"],
  ])("product: %o -> %s", (product, expected) => {
    const actual = getProductId(product);
    expect(actual).toEqual(expected);
  });
});
