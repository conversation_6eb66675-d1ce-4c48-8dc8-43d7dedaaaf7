import { extractCustomColumnId, getCustomColumnId } from "./custom";

describe("extractCustomColumnId", () => {
  test.each([
    ["custom_column.1.select_id", { isCustomColumn: true, customColumnId: 1 }],
    ["invalid_suffix.1.select_id", { isCustomColumn: false }],
  ])("columnId: %s -> %o", (columnId, expected) => {
    const actual = extractCustomColumnId(columnId);
    expect(actual).toEqual(expected);
  });
});

describe("getCustomColumnId", () => {
  test.each([
    [
      {
        type_status: "STRING",
        column_id: "unique_name",
      } as unknown as CustomColumn,
      "custom_column.unique_name.value",
    ],
    [
      {
        type_status: "SELECT",
        column_id: "unique_name",
      } as unknown as CustomColumn,
      "custom_column.unique_name.select_id",
    ],
    [
      {
        type_status: "VENDOR_USER",
        column_id: "unique_name",
      } as unknown as CustomColumn,
      "custom_column.unique_name.user_name",
    ],
    [
      {
        type_status: "PARTNER_USER",
        column_id: "unique_name",
      } as unknown as CustomColumn,
      "custom_column.unique_name.user_name",
    ],
    [
      {
        type_status: "FILE",
        column_id: "unique_name",
      } as unknown as CustomColumn,
      "custom_column.unique_name.file_name",
    ],
  ])("customColumn: %o) -> %s", (customColumn, expected) => {
    const actual = getCustomColumnId(customColumn);
    expect(actual).toEqual(expected);
  });
});
