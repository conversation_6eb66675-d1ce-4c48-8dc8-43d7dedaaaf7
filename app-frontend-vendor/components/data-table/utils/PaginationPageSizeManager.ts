import {
  getPaginationPageSizeFromLocalStorage,
  savePaginationPageSizeToLocalStorage,
} from "utils/localStorage/data-tables/selectedPaginationPageSize";

// グローバル状態を管理するシングルトン
class PaginationPageSizeManager {
  private static instance: PaginationPageSizeManager;
  private listeners: Set<(pageSize: number) => void> = new Set();
  private _pageSize: number = getPaginationPageSizeFromLocalStorage();

  static getInstance(): PaginationPageSizeManager {
    if (!PaginationPageSizeManager.instance) {
      PaginationPageSizeManager.instance = new PaginationPageSizeManager();
    }
    return PaginationPageSizeManager.instance;
  }

  get pageSize(): number {
    return this._pageSize;
  }

  setPageSize(newSize: number): void {
    if (this._pageSize === newSize) return;
    this._pageSize = newSize;
    savePaginationPageSizeToLocalStorage(newSize);
    this.notifyListeners();
  }

  subscribe(listener: (pageSize: number) => void): () => void {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach((listener) => listener(this._pageSize));
  }
}

export default PaginationPageSizeManager;
