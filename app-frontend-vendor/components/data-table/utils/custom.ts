import { usePartnerUsers } from "features/partners/hooks/partner";
import { usePartnerUsersWithLeadId } from "features/leads/hooks/lead";
import { Customize } from "../hooks";
import { Variant } from "../type";

export const extractCustomColumnId = (columnId: string) => {
  const isCustomColumn = columnId.startsWith("custom_column");
  if (!isCustomColumn) {
    return { isCustomColumn: false } as const;
  }
  const [_, customColumnId] = columnId.split(".");
  return {
    isCustomColumn: true,
    customColumnId: Number(customColumnId),
  } as const;
};

// TODO: type CustomColumn を index.d.ts から使わないように
export const getCustomColumnId = (column: CustomColumn) => {
  const suffix = getSuffix(column.type_status as Variant);
  return ["custom_column", column.column_id, suffix].join(".");
};
const getSuffix = (columnType: Variant) => {
  switch (columnType) {
    case "SELECT":
      return "select_id";
    case "VENDOR_USER":
    case "PARTNER_USER":
      return "user_name";
    case "FILE":
      return "file_name";
    default:
      return "value";
  }
};

// TODO: type CustomColumn を index.d.ts から使わないように
export const getCustomColumnCellValue = (
  columnType: Variant,
  column: CustomColumn
) => {
  // 選択肢で選ばせたいものはそれぞれの id を cellvalue として扱う
  switch (columnType) {
    case "SELECT":
      return column.select_id;
    case "VENDOR_USER":
      return column.vendor_user_id;
    case "PARTNER_USER":
      return column.partner_user_id;
    default:
      return column.current;
  }
};

export const getCustomColumnMeta = <T>(
  columnType: Variant,
  column: CustomColumn,
  vendorUsers: VendorUser[]
): Customize<T> => {
  switch (columnType) {
    case "SELECT":
      return {
        variant: "SELECT",
        selectOptions: column.select_contents?.map((c) => ({
          label: c.select_value,
          value: String(c.select_id),
        })),
      };
    case "PARTNER_USER":
      return {
        variant: "PARTNER_USER",
        useSelectOptions: (partnerId) => {
          const users = usePartnerUsers(partnerId);
          return users?.map((pu) => ({
            label: pu.name + "-" + pu.email,
            value: String(pu.id),
          }));
        },
      };
    case "VENDOR_USER":
      return {
        variant: "VENDOR_USER",
        selectOptions: vendorUsers?.map((vu) => ({
          label: vu.name + "-" + vu.email,
          value: String(vu.id),
        })),
      };
    default:
      return { variant: columnType };
  }
};

export const getLeadCustomColumnMeta = <
  T extends {
    lead_id: number;
    custom: CustomColumn[];
  },
>(
  columnType: Variant,
  column: CustomColumn,
  vendorUsers: VendorUser[]
): Customize<T> => {
  switch (columnType) {
    case "SELECT":
      return {
        variant: "SELECT",
        selectOptions: column.select_contents?.map((c) => ({
          label: c.select_value,
          value: String(c.select_id),
        })),
      };
    case "PARTNER_USER":
      return {
        variant: "PARTNER_USER",
        useSelectOptions: (id) => {
          const users = usePartnerUsersWithLeadId(id);
          return users?.map((pu) => ({
            label: pu.name + "-" + pu.email,
            value: String(pu.id),
          }));
        },
      };
    case "VENDOR_USER":
      return {
        variant: "VENDOR_USER",
        selectOptions: vendorUsers?.map((vu) => ({
          label: vu.name + "-" + vu.email,
          value: String(vu.id),
        })),
      };
    default:
      return { variant: columnType };
  }
};

export const getCustomUserColumnMeta = <
  T extends {
    partner_user_id: number;
    partner_user_custom_columns: CustomUserColumn[];
  },
>(
  columnType: Variant,
  column: CustomUserColumn,
  vendorUsers: VendorUser[]
): Customize<T> => {
  switch (columnType) {
    case "SELECT":
      return {
        variant: "SELECT",
        selectOptions: column.select_contents?.map((c) => ({
          label: c.select_value,
          value: String(c.select_id),
        })),
      };
    case "PARTNER_USER":
      return {
        variant: "PARTNER_USER",
        useSelectOptions: (partnerUserId) => {
          const users = usePartnerUsers(partnerUserId);
          return users?.map((pu) => ({
            label: pu.name + "-" + pu.email,
            value: String(pu.id),
          }));
        },
      };
    case "VENDOR_USER":
      return {
        variant: "VENDOR_USER",
        selectOptions: vendorUsers?.map((vu) => ({
          label: vu.name + "-" + vu.email,
          value: String(vu.id),
        })),
      };
    default:
      return { variant: columnType};
  }
};
