import { Select } from "@mantine/core";
import { CellContext } from "@tanstack/react-table";
import {
  DebouncedInput,
  DebouncedNumberInput,
} from "components/data-table/filter/debounced-input";
import { ToggleEdit } from "components/data-table/toggle-edit";
import { SelectOptions } from "components/data-table/type";
import { EllipsisText } from "components/ellipsis-text/EllipsisText";
import { PropsWithChildren, useState, useEffect, ComponentProps } from "react";
import dayjs from "dayjs";
import { Cell } from "..";

const toDisplayFormat = (raw: string | null): string =>
  raw ? dayjs(raw).format("YYYY-MM-DD HH:mm") : "";

export const KeepEditArea = (props: PropsWithChildren) => (
  <div
    css={{
      alignItems: "center",
      display: "flex",
      width: "100%",
      minHeight: "40px",
    }}
  >
    {props.children}
  </div>
);

export const EditableSelectCell = <T,>({
  initialValue,
  params,
  onChange,
  withEmptyOption,
  editable = true,
}: {
  /** 未選択の場合は空文字を指定する */
  initialValue: string | null;
  params: CellContext<T, unknown>;
  onChange: (text: string | null) => void;
  withEmptyOption?: boolean;
  editable?: boolean;
}) => {
  const customize = params.column.columnDef.meta?.customize;

  if (customize?.variant !== "SELECT" && customize?.variant !== "VENDOR_USER") {
    throw new Error(
      "SELECT or VENDOR_USER is expected as variant, but got" +
        customize?.variant
    );
  }
  const { selectOptions } = customize;
  return (
    <Cell>
      <ToggleEdit
        renderItem={(isEdit) => (
          <KeepEditArea>
            {isEdit && editable ? (
              <Select
                css={{ width: "100%" }}
                data={[
                  ...(withEmptyOption
                    ? [{ label: "(未選択)", value: "" }]
                    : []),
                  ...(selectOptions ?? []),
                ]}
                value={initialValue}
                onChange={onChange}
              />
            ) : (
              <EllipsisText
                value={
                  selectOptions?.find((ops) => ops.value === initialValue)
                    ?.label ?? ""
                }
              />
            )}
          </KeepEditArea>
        )}
      />
    </Cell>
  );
};

export const EditableTextCell = ({
  initialValue,
  onChange,
  editable = true,
}: {
  initialValue: string;
  onChange: (text: string | null) => void;
  editable?: boolean;
}) => {
  const [value, setValue] = useState(initialValue);
  // NOTE: updateData されたら initialValue が更新されるので同期する
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  return (
    <Cell>
      <ToggleEdit
        renderItem={(isEdit) => (
          <KeepEditArea>
            {isEdit && editable ? (
              <DebouncedInput
                value={value}
                onChange={(text) => {
                  onChange(text);
                  setValue(text);
                }}
              />
            ) : (
              <EllipsisText value={value} />
            )}
          </KeepEditArea>
        )}
      />
    </Cell>
  );
};

export const EditableNumberCell = ({
  initialValue,
  onChange,
  editable = true,
  isArrowUnderZero,
}: {
  initialValue: number | null;
  onChange: (num: number | null) => void;
  editable?: boolean;
  isArrowUnderZero?: boolean;
}) => {
  const [value, setValue] = useState<number | null>(initialValue);
  // NOTE: updateData されたら initialValue が更新されるので同期する
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  return (
    <Cell>
      <ToggleEdit
        renderItem={(isEdit) => (
          <KeepEditArea>
            {isEdit && editable ? (
              <DebouncedNumberInput
                value={value ?? 0}
                onChange={(num) => {
                  onChange(num);
                  setValue(num);
                }}
                isArrowUnderZero={isArrowUnderZero}
              />
            ) : (
              <EllipsisText value={value === null ? "" : String(value)} />
            )}
          </KeepEditArea>
        )}
      />
    </Cell>
  );
};

export const EditablePartnerUserCell = <T,>({
  displayText: _displayText,
  initialValue,
  useSelectOptions,
  partnerId,
  onChange,
  withEmptyOption,
  editable = true,
}: {
  /** PartnerUser のリストを取得せずに表示は行いたいため */
  displayText: string;
  /** 未選択の場合は空文字を指定する */
  initialValue: string | null;
  useSelectOptions: (row: number) => SelectOptions | undefined;
  partnerId: number;
  onChange: (text: string | null) => void;
  withEmptyOption?: boolean;
  editable?: boolean;
}) => {
  const [value, setValue] = useState<string | null>(initialValue);
  const [displayText, setDisplayText] = useState<string>(_displayText);
  // NOTE: updateData されたら initialValue が更新されるので同期する
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  return (
    <Cell>
      <ToggleEdit
        renderItem={(isEdit) => (
          <KeepEditArea>
            {isEdit && editable ? (
              <PartnerUserSelect
                partnerId={partnerId}
                withEmptyOption={withEmptyOption}
                useSelectOptions={useSelectOptions}
                setDisplayText={setDisplayText}
                value={value}
                onChange={(text) => {
                  onChange(text);
                  setValue(text);
                }}
              />
            ) : (
              <EllipsisText value={displayText} />
            )}
          </KeepEditArea>
        )}
      />
    </Cell>
  );
};

// 一覧表示のときにパートナーごとに API を叩いてしまうのを避けるため
// 編集モードのときのみ useSelectOption を呼びたい
// そのため別コンポーネントに切り出している
const PartnerUserSelect = ({
  withEmptyOption,
  partnerId,
  useSelectOptions,
  setDisplayText,
  onChange,
  value,
}: {
  withEmptyOption?: boolean;
  partnerId: number;
  useSelectOptions: (row: number) => SelectOptions | undefined;
  // パートナーユーザーが変更されたら親が持つ displayText を更新するためのハンドラ
  // selectOptions から displayText を取得するために子で親の state を更新する
  setDisplayText: (text: string) => void;
} & Pick<ComponentProps<typeof Select>, "onChange" | "value">) => {
  const selectOptions = useSelectOptions(partnerId);
  return (
    <Select
      css={{ width: "100%" }}
      value={value}
      data={[
        ...(withEmptyOption ? [{ label: "(未選択)", value: "" }] : []),
        ...(selectOptions ?? []),
      ]}
      onChange={(text) => {
        onChange?.(text);
        const displayText =
          selectOptions?.find((ops) => ops.value === text)?.label ?? "";
        setDisplayText(displayText);
      }}
    />
  );
};

export const EditableDateCell = ({
  initialValue,
  onChange,
  editable = true,
}: {
  initialValue: string;
  onChange: (text: string | null) => void;
  editable?: boolean;
}) => {
  const [value, setValue] = useState(initialValue);
  // NOTE: updateData されたら initialValue が更新されるので同期する
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  return (
    <Cell>
      <ToggleEdit
        renderItem={(isEdit) => (
          <KeepEditArea>
            {isEdit && editable ? (
              <input
                css={{ width: "100%" }}
                type="date"
                value={value}
                onChange={(e) => {
                  setValue(e.target.value);
                  onChange(e.target.value);
                }}
              />
            ) : (
              <EllipsisText value={value} />
            )}
          </KeepEditArea>
        )}
      />
    </Cell>
  );
};

export const EditableDateTimeLocalCell = ({
  initialValue,
  onChange,
  editable = true,
}: {
  initialValue: string;
  onChange: (text: string | null) => void;
  editable?: boolean;
}) => {
  // input には "T" 付き文字列を保持する
  const [inputValue, setInputValue] = useState(initialValue);
  // NOTE: updateData されたら initialValue が更新されるので同期する
  useEffect(() => {
    setInputValue(initialValue);
  }, [initialValue]);

  return (
    <Cell>
      <ToggleEdit
        renderItem={(isEdit) => (
          <KeepEditArea>
            {isEdit && editable ? (
              <input
                css={{ width: "100%" }}
                type="datetime-local"
                value={inputValue}
                onChange={(e) => {
                  const raw = e.target.value;
                  setInputValue(raw);
                  onChange(toDisplayFormat(raw));
                }}
                onBlur={(e) => onChange(toDisplayFormat(e.target.value))}
              />
            ) : (
              <EllipsisText value={toDisplayFormat(inputValue)} />
            )}
          </KeepEditArea>
        )}
      />
    </Cell>
  );
};

// TODO: ファイル削除の手段を提供する
export const EditableFileCell = ({
  initialValue,
  onChange,
  editable = true,
}: {
  initialValue: string;
  onChange: (text: File | null) => void;
  editable?: boolean;
}) => {
  const [value, setValue] = useState(initialValue);
  // NOTE: updateData されたら initialValue が更新されるので同期する
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  return (
    <Cell>
      <ToggleEdit
        renderItem={(isEdit) => (
          <KeepEditArea>
            {isEdit && editable ? (
              <div>
                <p>{value}</p>
                <input
                  css={{ width: "100%" }}
                  type="file"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (!file) {
                      return;
                    }
                    onChange(file);
                    setValue(file.name);
                  }}
                />
              </div>
            ) : (
              <EllipsisText value={value} />
            )}
          </KeepEditArea>
        )}
      />
    </Cell>
  );
};

export const EditableLongTextCell = ({
  initialValue,
  onChange,
  editable = true,
}: {
  initialValue: string;
  onChange: (text: string | null) => void;
  editable?: boolean;
}) => {
  const [value, setValue] = useState(initialValue);
  // NOTE: updateData されたら initialValue が更新されるので同期する
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  return (
    <Cell>
      <ToggleEdit
        renderItem={(isEdit) => (
          <KeepEditArea>
            {isEdit && editable ? (
              <DebouncedInput
                type="longtext"
                value={value}
                onChange={(text) => {
                  onChange(text);
                  setValue(text);
                }}
              />
            ) : (
              <EllipsisText value={value} />
            )}
          </KeepEditArea>
        )}
      />
    </Cell>
  );
};

export const EditableDatetimeCell = ({
  initialValue,
  onChange,
  editable = true,
}: {
  initialValue: string;
  onChange: (text: string | null) => void;
  editable?: boolean;
}) => {
  const [inputValue, setInputValue] = useState(initialValue);
  // NOTE: updateData されたら initialValue が更新されるので同期する
  useEffect(() => {
    setInputValue(initialValue);
  }, [initialValue]);

  return (
    <Cell>
      <ToggleEdit
        renderItem={(isEdit) => (
          <KeepEditArea>
            {isEdit && editable ? (
              <input
                css={{ width: "100%" }}
                type="datetime-local"
                value={inputValue}
                onChange={(e) => {
                  const raw = e.target.value;
                  setInputValue(raw);
                  onChange(toDisplayFormat(raw));
                }}
                onBlur={(e) => onChange(toDisplayFormat(e.target.value))}
              />
            ) : (
              <EllipsisText value={toDisplayFormat(inputValue)} />
            )}
          </KeepEditArea>
        )}
      />
    </Cell>
  );
};
