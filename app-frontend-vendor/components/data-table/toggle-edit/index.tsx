import ClickBlurListener from "components/click-blur-listener";
import { useState } from "react";

type ToggleEditProps = {
  renderItem: (isEdit: boolean) => JSX.Element;
};

export const ToggleEdit = (props: ToggleEditProps) => {
  const [isEdit, setEdit] = useState(false);
  return (
    <ClickBlurListener
      onClick={() => setEdit(true)}
      onBlur={() => setEdit(false)}
    >
      {props.renderItem(isEdit)}
    </ClickBlurListener>
  );
};
