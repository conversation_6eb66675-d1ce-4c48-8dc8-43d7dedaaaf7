import { Meta, StoryObj } from "@storybook/react";
import { ToggleEdit } from ".";
const meta = {
  title: "shared/data-table/toggle-edit",
  component: ToggleEdit,
} as Meta<typeof ToggleEdit>;
export default meta;

export const Primary = {
  args: {
    renderItem: (isEdit: boolean) => (
      <div
        css={{
          backgroundColor: isEdit ? "gray" : "white",
          color: isEdit ? "#EEE" : "black",
          padding: "8px",
        }}
      >
        Click me
      </div>
    ),
  },
} as StoryObj<typeof meta>;
