import {
  ArrowSortDown16Regular,
  ArrowSortUp16Regular,
  Filter12Regular,
  NavigationFilled,
} from "@fluentui/react-icons";
import { Popover } from "@mantine/core";
import { type Header } from "@tanstack/react-table";
import { PropsWithChildren } from "react";
import { Filter } from "../filter";

type HeadProps<T> = {
  header: Header<T, unknown>;
};

export const Head = <T,>({
  header,
  children,
}: PropsWithChildren<HeadProps<T>>) => {
  return (
    <div
      css={{
        color: "#666666",
        display: "flex",
        fontSize: "12px",
        fontWeight: "600",
        height: "100%",
        paddingLeft: "24px",
      }}
    >
      <div
        css={{
          alignItems: "center",
          display: "flex",
          flex: 1,
          justifyContent: "space-between",
          minWidth: 0,
          ":hover": {
            "button.filter-button": {
              opacity: 1,
            },
          },
        }}
      >
        <button
          onClick={header.column.getToggleSorting<PERSON><PERSON><PERSON>()}
          disabled={!header.column.getCanSort()}
          css={{
            alignItems: "center",
            borderColor: "transparent",
            color: "#666666",
            display: "flex",
            flex: 1,
            gap: "4px",
            minWidth: 0,
            padding: "6px 0px",
            ":hover:enabled": {
              backgroundColor: "#f7f8f9",
            },
          }}
        >
          <span
            css={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            {children}
          </span>
          {header.column.getIsFiltered() && <Filter12Regular />}
          <span css={{ alignItems: "center", display: "flex" }}>
            {{
              asc: <ArrowSortUp16Regular />,
              desc: <ArrowSortDown16Regular />,
            }[header.column.getIsSorted() as string] ?? null}
          </span>
        </button>
        {header.column.getCanFilter() && (
          <Popover trapFocus withArrow shadow="md" position="bottom-start">
            <Popover.Target>
              <button
                className="filter-button"
                css={{
                  alignItems: "center",
                  borderColor: "transparent",
                  color: "#8992A0",
                  display: "flex",
                  flexShrink: 0,
                  justifyContent: "center",
                  opacity: 0,
                  padding: "6px",
                  transition: "opacity 0.2s",
                  ":hover": {
                    backgroundColor: "#f7f8f9",
                  },
                }}
              >
                <NavigationFilled />
              </button>
            </Popover.Target>
            <Popover.Dropdown sx={{ backgroundColor: "#f7f8f9" }}>
              <Filter column={header.column} />
            </Popover.Dropdown>
          </Popover>
        )}
      </div>
      <div
        {...{
          onDoubleClick: () => header.column.resetSize(),
          onMouseDown: header.getResizeHandler(),
          onTouchStart: header.getResizeHandler(),
          css: {
            cursor: "col-resize",
            position: "relative",
            touchAction: "none",
            userSelect: "none",
            width: "8px",
            "::after": {
              backgroundColor: "#dde2eb",
              content: '""',
              height: "30%",
              position: "absolute",
              display: "block",
              right: "calc(50% - 0.5px)",
              top: "35%",
              width: "1px",
              zIndex: 1,
            },
          },
        }}
      />
    </div>
  );
};
