import { <PERSON>a, <PERSON>Fn, <PERSON>Obj } from "@storybook/react";
import { Head } from ".";
import { useDataTable } from "../hooks";
import { flexRender } from "@tanstack/react-table";

const meta = {
  title: "shared/data-table/head",
  component: Head,
} as Meta<typeof Head>;

export default meta;

const Template: StoryFn<typeof meta> = () => {
  const table = useDataTable({
    columns: [
      {
        id: "id",
        header: ({ header }) => <Head header={header}>カラム名が入る</Head>,
        enableSorting: true,
        enableColumnFilter: true,
        size: 100,
      },
    ],
    data: [{ id: "1" }, { id: "2" }],
    pageCount: 1,
    initialTableState: { pagination: { pageIndex: 0, pageSize: 10 } },
  });

  return (
    <>
      {table.getHeaderGroups().map((headerGroup) => {
        return headerGroup.headers.map((header) => (
          <div key={header.id} css={{ width: header.getSize() }}>
            <>
              {flexRender(header.column.columnDef.header, header.getContext())}
            </>
          </div>
        ));
      })}
    </>
  );
};

export const Primary = {
  render: Template,
} as StoryObj<typeof meta>;
