import { useMemo, useState } from "react";

import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  InitialTableState,
  PaginationState,
  RowData,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { SelectOptions, Variant } from "../type";

export type Customize<TData> =
  | {
      variant: Extract<Variant, "SELECT" | "VENDOR_USER">;
      selectOptions: SelectOptions | undefined;
    }
  | {
      variant: Extract<Variant, "PARTNER_USER">;
      /** NOTE: partner ごとに selectOptions が変わるため関数で定義する */
      useSelectOptions: (partnerId: number) => SelectOptions | undefined;
    }
  | {
      variant: Exclude<Variant, "SELECT" | "VENDOR_USER" | "PARTNER_USER">;
    };

declare module "@tanstack/react-table" {
  interface ColumnMeta<TData extends RowData, TValue> {
    customize: Customize<TData>;
  }
  interface TableMeta<TData extends RowData> {
    // TODO: add table meta props
  }
}

type DataTableProps<T> = {
  /** NOTE: id に sort, filter で使いたい値を設定する */
  columns: ColumnDef<T>[];
  defaultColumn?: Partial<ColumnDef<T>>;
  data: T[];
  pageCount: number;
  initialTableState?: InitialTableState;
  columnOrder?: string[];
};

export const useDataTable = <T,>({
  columns,
  defaultColumn,
  data,
  pageCount,
  initialTableState,
  columnOrder,
}: DataTableProps<T>) => {
  const defaultColumnFallback: Partial<ColumnDef<T>> = useMemo(
    () => ({
      enableColumnFilter: false,
      enableSorting: false,
      enableResizing: true,
      // NOTE: クライアントサイドでフィルタしないので常に true を返す
      filterFn: () => true,
    }),
    []
  );

  const defaultData = useMemo<T[]>(() => [], []);

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 25,
    ...initialTableState?.pagination,
  });

  const [sorting, setSorting] = useState<SortingState>(
    initialTableState?.sorting ?? []
  );
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(
    initialTableState?.columnFilters ?? []
  );

  return useReactTable({
    data: data || defaultData,
    defaultColumn: { ...defaultColumnFallback, ...defaultColumn },
    columns,
    columnResizeMode: "onChange",
    columnResizeDirection: "ltr",
    pageCount,
    initialState: initialTableState,
    state: {
      sorting,
      columnFilters,
      pagination,
      columnOrder
    },
    onPaginationChange: (args) => setPagination(args),
    // NOTE: フィルタ、ソートされたらページをリセットする
    onColumnFiltersChange: (args) => {
      setColumnFilters(args);
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    },
    onSortingChange: (args) => {
      setSorting(args);
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    },
    getCoreRowModel: getCoreRowModel(),
    autoResetPageIndex: false,
    manualFiltering: true,
    manualPagination: true,
    manualSorting: true,
    // NOTE: sortDescFirst を設定しないと特定のカラムでソートが効かない
    sortDescFirst: false,
    debugTable: true,
  });
};
