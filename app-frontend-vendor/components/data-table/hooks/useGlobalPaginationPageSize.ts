import { useCallback, useEffect, useState } from "react";
import PaginationPageSizeManager from "../utils/PaginationPageSizeManager";

export const useGlobalPaginationPageSize = () => {
  const manager = PaginationPageSizeManager.getInstance();
  const [pageSize, setPageSize] = useState(manager.pageSize);

  useEffect(() => {
    const unsubscribe = manager.subscribe(setPageSize);
    return unsubscribe;
  }, [manager]);

  const updatePageSize = useCallback(
    (newSize: number) => {
      manager.setPageSize(newSize);
    },
    [manager],
  );

  return [pageSize, updatePageSize] as const;
};

// 表示件数を取得したいときに使用
export const useGlobalPaginationPageSizeValue = () => {
  const [pageSize] = useGlobalPaginationPageSize();
  return pageSize;
};

// 表示件数を更新したいときに使用
export const useSetGlobalPaginationPageSize = () => {
  const [, setPageSize] = useGlobalPaginationPageSize();
  return setPageSize;
};
