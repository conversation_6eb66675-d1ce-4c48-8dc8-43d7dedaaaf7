import { ActionIcon, Select } from "@mantine/core";
import type { Table } from "@tanstack/react-table";
import {
  useGlobalPaginationPageSize,
  useSetGlobalPaginationPageSize,
} from "components/data-table/hooks/useGlobalPaginationPageSize";
import { PAGINATION_PAGE_SIZE_OPTIONS } from "constants/commonSetting";
import { useEffect } from "react";
import { propcolors } from "styles/colors";

export const PaginationControl = <T,>({
  table,
  rowCount,
}: {
  table: Table<T>;
  rowCount: number;
}) => {
  const [pageSize] = useGlobalPaginationPageSize();
  const setGlobalPageSize = useSetGlobalPaginationPageSize();
  const { pagination } = table.getState();
  const { pageIndex } = pagination;
  const firstIndex = Math.min(pageIndex * pageSize + 1, rowCount);
  const lastIndex = Math.min((pageIndex + 1) * pageSize, rowCount);

  //他画面で変更された表示件数を初期表示時に反映
  useEffect(() => {
    table.setPageSize(pageSize);
  }, [pageSize, table]);

  const handlePageSizeChange = (value: string | null) => {
    const newSize = Number(value);
    if (!Number.isNaN(newSize)) {
      setGlobalPageSize(newSize);
      table.setPageSize(newSize);
      table.setPageIndex(0);
    }
  };

  return (
    <div
      css={{
        alignItems: "center",
        borderTop: `1px solid ${propcolors.gray[200]}`,
        color: propcolors.blackLight,
        display: "flex",
        fontSize: "14px",
        gap: "16px",
        height: "64px",
        justifyContent: "flex-end",
        padding: "16px 24px",
      }}
    >
      <div css={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <span>表示件数</span>
        <Select
          data={PAGINATION_PAGE_SIZE_OPTIONS.map(String)}
          value={String(pageSize)}
          onChange={handlePageSizeChange}
          size="xs"
          withinPortal
          w={70}
          variant="unstyled"
          styles={{
            input: {
              border: "1px solid #ccc",
              borderRadius: "4px",
              padding: "4px 8px",
              textAlign: "center",
              height: "28px",
              fontSize: "14px",
              cursor: "pointer",
            },
            dropdown: { zIndex: 9999 },
          }}
        />
      </div>
      <div css={{ display: "flex", alignItems: "center", gap: "16px" }}>
        <div css={{ display: "flex", gap: "4px", alignItems: "center" }}>
          <span>{rowCount}件中</span>
          <span>
            {firstIndex}-{lastIndex}件
          </span>
        </div>
        <div css={{ alignItems: "center", display: "flex", gap: "8px" }}>
          {/* TODO: Remix Icon に置き換える */}
          <ActionIcon
            onClick={() => table.firstPage()}
            disabled={!table.getCanPreviousPage()}
          >
            {"<<"}
          </ActionIcon>
          <ActionIcon
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            {"<"}
          </ActionIcon>
          <ActionIcon
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            {">"}
          </ActionIcon>
          <ActionIcon
            onClick={() => table.lastPage()}
            disabled={!table.getCanNextPage()}
          >
            {">>"}
          </ActionIcon>
        </div>
      </div>
    </div>
  );
};
