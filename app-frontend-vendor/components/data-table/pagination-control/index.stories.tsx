import { <PERSON>a, <PERSON>Fn, <PERSON>Obj } from "@storybook/react";
import { PaginationControl } from ".";
import { useDataTable } from "../hooks";
const meta = {
  title: "shared/data-table/pagination-control",
  component: PaginationControl,
  args: {
    rowCount: 100,
  },
} as Meta<typeof PaginationControl>;
export default meta;

const Template: StoryFn<typeof PaginationControl> = (args) => {
  const table = useDataTable({
    columns: [],
    data: [],
    pageCount: 4,
    initialTableState: { pagination: { pageIndex: 0, pageSize: 25 } },
  });
  return <PaginationControl {...args} table={table} />;
};
export const Primary = {
  render: Template,
} as StoryObj<typeof meta>;
