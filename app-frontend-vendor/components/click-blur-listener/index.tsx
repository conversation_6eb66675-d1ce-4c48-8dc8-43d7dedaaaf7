import { useOutsideClick } from "hooks/use-outside-click";
import { ReactNode, FC, useRef } from "react";

interface ClickBlurListenerProps {
  onClick: () => void;
  onBlur: () => void;
  children: ReactNode;
}

const ClickBlurListener: FC<ClickBlurListenerProps> = ({
  onClick,
  onBlur,
  children,
}) => {
  const ref = useRef<HTMLDivElement | null>(null);
  useOutsideClick({ handler: onBlur, ref });
  // TODO: more accesible
  return (
    <div
      ref={ref}
      onClick={onClick}
      tabIndex={0}
      css={{
        borderColor: "transparent",
        borderRadius: "4px",
        cursor: "text",
        padding: "0",
        width: "100%",
        ":hover": { backgroundColor: "#f8f8f8" },
        ":focus-visible": { outline: "1px solid blue" },
      }}
    >
      {children}
    </div>
  );
};

export default ClickBlurListener;
