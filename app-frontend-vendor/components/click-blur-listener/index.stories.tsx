import { Meta, StoryObj } from "@storybook/react";
import ClickBlurListener from ".";

const meta = {
  title: "shared/click-blur-listener",
  component: ClickBlurListener,
} as Meta<typeof ClickBlurListener>;

export default meta;

export const Primary = {
  render: (props) => (
    <ClickBlurListener {...props}>
      <div css={{ backgroundColor: "gray", color: "#EEE", padding: "8px" }}>
        Click me
      </div>
    </ClickBlurListener>
  ),
} as StoryObj<typeof meta>;
