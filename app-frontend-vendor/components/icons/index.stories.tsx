import { Meta, <PERSON>Obj } from "@storybook/react";
import * as Icons from ".";
const meta = {
  title: "shared/icons",
} as Meta<typeof Icons>;

export default meta;

export const AllIcons = {
  render: () => (
    <div css={{ display: "flex", flexWrap: "wrap", gap: "12px" }}>
      {Object.entries(Icons).map(([key, Icon]) => (
        <div
          key={key}
          css={{
            display: "flex",
            flexDirection: "column",
            gap: "12px",
            alignItems: "center",
          }}
        >
          <Icon />
          <span css={{ fontSize: "10px" }}>{key}</span>
        </div>
      ))}
    </div>
  ),
} as StoryObj<typeof meta>;
