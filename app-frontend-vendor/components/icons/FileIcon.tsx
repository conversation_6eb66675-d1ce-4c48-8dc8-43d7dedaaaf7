import React from "react";
import {
  RiFileExcel2Line,
  RiImageLine,
  RiMusicLine,
  RiFilePdf2Line,
  RiFileTextLine,
  RiFileWord2Line,
  RiMovieLine,
} from "@remixicon/react";

interface FileIconProps {
  fileName: string;
}

const FileIcon: React.FC<FileIconProps> = ({ fileName }) => {
  const extension = fileName.split(".").pop()?.toLowerCase();

  switch (extension) {
    case "xls":
    case "xlsm":
    case "xlsb":
    case "xlsx":
      return <RiFileExcel2Line style={{ color: "green" }} />;
    case "doc":
    case "docm":
    case "docx":
      return <RiFileWord2Line style={{ color: "blue" }} />;
    case "pdf":
      return <RiFilePdf2Line style={{ color: "red" }} />;
    case "jpg":
    case "jpeg":
    case "png":
    case "gif":
      return <RiImageLine style={{ color: "black" }} />;
    case "mp4":
    case "avi":
    case "mov":
    case "wmv":
    case "flv":
    case "webm":
    case "mpg":
    case "mpeg":
      return <RiMovieLine style={{ color: "black" }} />;
    case "mp3":
    case "wav":
    case "wma":
    case "asf":
    case "mov":
    case "m4a":
    case "alac":
      return <RiMusicLine style={{ color: "black" }} />;
    default:
      return <RiFileTextLine style={{ color: "black" }} />;
  }
};

export default FileIcon;
