import { Variants, motion } from "framer-motion";

const variant: Variants = {
  initial: {
    opacity: 0,
    y: -25,
    minHeight: "100%",
    transition: {
      ease: "easeOut",
    },
  },
  enter: {
    opacity: 1,
    y: 0,
    minHeight: "100%",
    transition: {
      ease: "easeOut",
    },
  },
  exit: {
    opacity: 0,
    y: 25,
    minHeight: "100%",
    transition: {
      ease: "easeIn",
    },
  },
};

export const GeneralTransition: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  return (
    <motion.div
      className="layout-content"
      variants={variant}
      initial="initial"
      animate="enter"
      exit="exit"
    >
      {children}
    </motion.div>
  );
};
