import { useDrop } from "react-dnd";
import styled from "@emotion/styled";
import DraggableItem from "./DraggableItem";
import { Dispatch, SetStateAction } from "react";
import { propcolors } from "../../styles/colors";

interface DroppableListProps<T> {
  id: string;
  items: T[];
  setItems: Dispatch<SetStateAction<T[]>>;
  otherItems: T[];
  setOtherItems: Dispatch<SetStateAction<T[]>>;
  initialUnselected: T[];
  renderItem: (item: T) => React.ReactNode;
  color?: string;
  searchText?: string;
  resetInitialValue?: boolean;
  className: string;
}

const ListContainer = styled.div`
  width: 100%;
  height: 100%;
  border-radius: 5px;
  overflow-y: auto;

  &.form-items-unselected {
    max-height: 670px;
  }
  &.custom-form-items-selected {
    height: 640px;
  }
  &.form-input-items-selected {
    height: 500px;
  }
  
  @media (min-height: 1024px) {
    &.form-items-unselected {
      max-height: calc(100vh - 340px);
    }
    &.custom-form-items-selected {
      height: calc(100vh - 365px);
    }
    &.form-input-items-selected {
      height: calc(100vh - 505px);
    }
`;

const DroppableList = <T,>({
  id,
  items,
  setItems,
  otherItems,
  setOtherItems,
  initialUnselected,
  renderItem,
  color = propcolors.gray[200],
  searchText = "",
  resetInitialValue,
  className,
}: DroppableListProps<T>) => {
  const moveItem = (dragIndex: number, hoverIndex: number) => {
    const draggedItem = items[dragIndex];
    const newItems = [...items];
    newItems.splice(dragIndex, 1);
    newItems.splice(hoverIndex, 0, draggedItem);
    setItems(newItems);
  };

  ListContainer.__emotion_styles["backgroundColor"] = "#FFFFFF";

  const [, ref] = useDrop({
    accept: "ITEM",
    drop: (draggedItem: { item: T; index: number; sourceListId: string }) => {
      if (draggedItem.sourceListId === id) return;

      setItems((prevItems) => [
        ...prevItems,
        resetInitialValue &&
        (draggedItem.item as LeadCreationFormColumn).value_type != "SELECT"
          ? { ...draggedItem.item, initial_value: null }
          : draggedItem.item,
      ]);
      setOtherItems((prevOtherItems) => {
        const newItems = [...prevOtherItems];
        newItems.splice(draggedItem.index, 1);
        return newItems;
      });
    },
    canDrop: (draggedItem: {
      item: T;
      index: number;
      sourceListId: string;
    }) => {
      return draggedItem.sourceListId !== id;
    },
  });

  return (
    <ListContainer ref={ref} className={className}>
      {id === "unselected"
        ? initialUnselected
            .filter((item: any) =>
              item?.key
                ? !otherItems.map((other: any) => other.key).includes(item.key)
                : !otherItems.includes(item)
            )
            .filter((item: any) => {
              if (item.display_column_label && searchText !== "") {
                return item.display_column_label.indexOf(searchText) !== -1;
              } else {
                return item;
              }
            })
            .map((item, index) => (
              <DraggableItem<T>
                key={index}
                item={item}
                index={items.findIndex((i) => i === item)}
                sourceListId={id}
                renderItem={renderItem}
              />
            ))
        : items.map((item, index) => (
            <DraggableItem<T>
              key={index}
              item={item}
              index={index}
              sourceListId={id}
              moveItem={moveItem}
              renderItem={renderItem}
            />
          ))}
    </ListContainer>
  );
};

export default DroppableList;
