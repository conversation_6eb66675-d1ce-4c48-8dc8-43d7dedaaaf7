// src/components/DraggableItem.tsx
import { useDrag, useDrop } from "react-dnd";
import { useRef } from "react";
import styled from "@emotion/styled";
import { usedClassNameState } from "../../utils/recoil/inputForm/inputFormState";

interface DraggableItemProps<T> {
  item: T;
  index: number;
  sourceListId: string;
  moveItem?: (dragIndex: number, hoverIndex: number) => void;
  renderItem: (item: T) => React.ReactNode;
}
const Item = styled.div<{ isDragging: boolean; isSidebarInputForm: boolean }>`
  padding: 8px;
  margin-bottom: 8px;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 3px;
  opacity: ${(props) => (props.isDragging ? 0.5 : 1)};
  ${(props) =>
    props.isSidebarInputForm &&
    ` 
    border: none;
    border-bottom: 1px solid #E8EAED;
    border-radius: 8px;
    padding: 16px 24px;
  `}
`;
const DraggableItem = <T,>({
  item,
  index,
  sourceListId,
  moveItem,
  renderItem,
}: DraggableItemProps<T>) => {
  const ref = useRef<HTMLDivElement>(null);

  const [{ isDragging }, drag] = useDrag({
    type: "ITEM",
    item: { item, index, sourceListId },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: "ITEM",
    hover: (draggedItem: { item: T; index: number; sourceListId: string }) => {
      if (
        !ref.current ||
        !moveItem ||
        draggedItem.index === index ||
        draggedItem.sourceListId !== sourceListId
      ) {
        return;
      }
      moveItem(draggedItem.index, index);
      draggedItem.index = index;
    },
  });

  drag(drop(ref));

  const inputFormClassName = usedClassNameState();

  return (
    <Item
      ref={ref}
      isDragging={isDragging}
      isSidebarInputForm={inputFormClassName === "sidebarInputForm"}
    >
      {renderItem(item)}
    </Item>
  );
};

export default DraggableItem;
