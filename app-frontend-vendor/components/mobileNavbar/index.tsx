import styled from "@emotion/styled";
import { Chat20Filled, List20Filled } from "@fluentui/react-icons";
import { UnstyledButton } from "@mantine/core";
import { NavbarMenu } from "components/navbar/menu";
import { LeadsIcon } from "components/svgs/leads";
import { PartnerIcon } from "components/svgs/partner";
import { AnimatePresence } from "framer-motion";
import router, { useRouter } from "next/router";
import { destroyCookie } from "nookies";
import { useState } from "react";
import { useSetRecoilState } from "recoil";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import { resetAllRecoil } from "utils/recoil/resetRecoil/resetRecoil";
import { useSessionUser } from "utils/recoil/sessionUserState";

type MobileNavbarPresentation = {
  className?: string;
}

const Presentation: React.FC<MobileNavbarPresentation> = ({ className }) => {
  const { push } = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const sessionUser = useSessionUser();
  const resetAllState = useSetRecoilState(resetAllRecoil);
  const handleLogout = async () => {
    await ax.post("/logout");
    await destroyCookie(null, "user", {path: "/"});
    await resetAllState(undefined);
    await router.push("/login");
  };
  return (
    <>
    <AnimatePresence>
      {isMenuOpen && sessionUser && (
        <NavbarMenu logout={handleLogout} toggle={() => setIsMenuOpen(false)} userInfo={sessionUser} />
      )}
    </AnimatePresence>
    <div className={className}>
      <UnstyledButton onClick={() => push("/partners")} className="button">
        <PartnerIcon />
      </UnstyledButton>
      <UnstyledButton onClick={() => push("/leads")} className="button">
        <LeadsIcon />
      </UnstyledButton>
      <UnstyledButton onClick={() => push("/chat")} className="button">
        <Chat20Filled />
      </UnstyledButton>
      <UnstyledButton onClick={() => {
          setIsMenuOpen(!isMenuOpen)
          console.log("menu opened")
      }} className="button">
        <List20Filled />
      </UnstyledButton>
    </div>
    </>
  )
}

const Styled = styled(Presentation)`
  width: 100vw;
  bottom: 0;
  position: fixed;
  background-color: #fff;
  border-top: 1px solid ${propcolors.gray[200]};
  display: grid;
  grid-template-columns: 1fr 1fr 1fr auto;
  height: 4rem;
  @media screen and (min-width: 512px) {
    display: none;
  }
  > .button {
    padding: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    svg {
      width: 1.5rem;
      fill: ${propcolors.black};
    }
  }
`;

export const MobileNavbar: React.FC = () => {
  return (
    <Styled />
  );
}
