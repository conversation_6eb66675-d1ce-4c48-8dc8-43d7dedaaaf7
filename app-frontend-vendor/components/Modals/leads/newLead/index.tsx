import React, { ReactNode } from "react";
import styled from "@emotion/styled";
import { useDisclosure } from "@mantine/hooks";
import { Button, Modal } from "@mantine/core";
import { NewLeadContent } from "./content";
import { propcolors } from "styles/colors";
import { IconSettings } from "@tabler/icons-react";
import { useRouter } from "next/router";

const ModalHeaderContainer = styled.div({
  width: "100%",
  textAlign: "center",
  fontWeight: 600,
  padding: "1rem 0",
  position: "absolute",
  top: 0,
  left: 0,
  borderBottom: `1px solid ${propcolors.gray[200]}`,
  backgroundColor: propcolors.white,
});

const HeaderContent = styled.div({
  display: "flex",
  justifyContent: "space-between",
  paddingLeft: "1rem",
  paddingRight: "1rem",
});

const Title = styled.div({
  textAlign: "center",
});

const Space = styled.div({
  width: "100px",
});

const SettingButton = styled(Button)<{
  onClick: () => void;
  variant: string;
  leftIcon: ReactNode;
}>({
  paddingRight: "2px",
  width: "100px",
  color: "#8992A0",
  backgroundColor: propcolors.white,
});

export const NewLead: React.FC = () => {
  const [opened, { open, close }] = useDisclosure(false);
  const { push } = useRouter();
  const pushColumnSetting = () => {
    close();
    push("settings/input-form");
  };

  return (
    <>
      <Button
        onClick={open}
        style={{
          width: "158px",
          fontSize: "14px",
          fontWeight: 400,
          borderRadius: "8px",
          height: "42px",
        }}
      >
        新規案件作成
      </Button>
      <Modal.Root opened={opened} onClose={close} size="640px">
        <Modal.Overlay />
        <Modal.Content style={{ overflowY: "visible" }}>
          <Modal.Header>
            <ModalHeaderContainer>
              <HeaderContent>
                <Space />
                <Title>新規案件作成</Title>
                <SettingButton
                  onClick={pushColumnSetting}
                  variant={"none"}
                  leftIcon={
                    <IconSettings size={25} style={{ color: "#8992A0" }} />
                  }
                />
              </HeaderContent>
            </ModalHeaderContainer>
          </Modal.Header>
          <Modal.Body style={{ position: "relative", padding: 0 }}>
            <NewLeadContent close={close} />
          </Modal.Body>
        </Modal.Content>
      </Modal.Root>
    </>
  );
};
