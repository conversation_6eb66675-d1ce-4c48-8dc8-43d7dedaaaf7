import React, { useEffect, useMemo, useState } from "react";
import styled from "@emotion/styled";
import { ax } from "utils/axios";
import { UseFormReturnType, useForm } from "@mantine/form";
import {
  Button,
  NumberInput,
  Select,
  SelectItem,
  Skeleton,
  TextInput,
  Textarea,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { propcolors } from "styles/colors";
import IconSelect from "public/icons/icon-arrow-down.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "../../../../public/icons/icon-noti-failed.svg";
import { revalidateFilteredLead } from "features/leads/hooks/lead";
import {
  CUSTOM_COLUMN_TYPE_NAME_DATE,
  CUSTOM_COLUMN_TYPE_NAME_FILE,
  CUSTOM_COLUMN_TYPE_NAME_INTEGER,
  CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT,
  CUSTOM_COLUMN_TYPE_NAME_PARTNER_USER,
  CUSTOM_COLUMN_TYPE_NAME_SELECT,
  CUSTOM_COLUMN_TYPE_NAME_STRING,
  CUSTOM_COLUMN_TYPE_NAME_TIME,
  CUSTOM_COLUMN_TYPE_NAME_VENDOR_USER,
} from "constants/ja/common";
import { DateInput, DateTimePicker } from "@mantine/dates";
import {
  useVendorUserList,
  useSetVendorUserList,
} from "utils/recoil/vendorUserListState";

import { APIStatusCode } from "constants/api_status_code";
import { FileUpload } from "components/FileUpload";

interface InputColumn {
  column_id: string | number;
  custom_column_content?: string | number | File | Blob;
  select_id?: string | number;
  vendor_user_id?: string | number;
  partner_user_id?: string | number;
  prefecture_id?: string | number;
  column_name: string;
  value?: string | number | Date;
  is_required?: boolean;
  column_label?: string;
  is_visible?: boolean;
  is_fixed_column: boolean;
  type_status: string;
  initial_value?: string | number;
}

const LEAD_FORM_FIXED_COLUMN = {
  PREFECTURE: 1,
  POSTAL_CODE: 2,
  ADDRESS: 3,
  TEL: 4,
  URL: 5,
  MEMO: 6,
  EMPLOYEES: 7,
  STATUS: 8,
};

type NewPartnerForm = {
  name: string;
  vendor_linked_partner_id?: string | number;
  linked_partner_user?: string | number;
  inputs: InputColumn[];
};

type NewLeadProps = {
  close: () => void;
};

type CustomFormColumn = {
  id: string | number;
  vendor_id: string | number;
  fixed_column_id: string | number;
  custom_column_id: string | number;
  is_required: boolean;
  sort_order: string | number;
  is_visible: boolean;
  created_at: string;
  updated_at: string;
  column_label: string;
  column_name: string;
  type: string | number;
  type_status: string;
  type_status_name: string;
  select_contents?: SelectItem[];
  default?: number;
  partner_user_id?: string | number;
  vendor_user_id?: string | number;
  initial_value?: string | number;
};

type PresentationalProps = {
  className?: string;
  prefectures: SelectItem[];
  close: () => void;
  handleSubmit: () => void;
  form: UseFormReturnType<NewPartnerForm>;
  activePartnerList: SelectItem[];
  customColumns: CustomFormColumn[];
  vendorUserList: VendorUser[] | null;
  msgErrorFile: (string | null)[];
  isLoading: boolean;
  isSubmitting: boolean;
};

const Presentational: React.FC<PresentationalProps> = ({
  className,
  prefectures,
  close,
  form,
  msgErrorFile,
  handleSubmit,
  activePartnerList,
  customColumns,
  vendorUserList,
  isLoading,
  isSubmitting,
}) => {
  const icon = <IconSelect />;

  const onFileChange = (file: File | null, index: number) => {
    form.setFieldValue(`inputs.${index}.custom_column_content`, file);
  };

  const [linkedPartnerUser, setLinkedPartnerUser] = useState<SelectItem[]>([]);

  // reset value of partner user inputs in form
  const resetPartnerUsersInForm = () => {
    const list = form.values.inputs.map((item) => {
      if (item.type_status === CUSTOM_COLUMN_TYPE_NAME_PARTNER_USER) {
        return {
          ...item,
          partner_user_id: item.initial_value || "",
        };
      }
      return item;
    });
    form.setValues({
      ...form.values,
      inputs: list,
    });
  };

  useEffect(() => {
    const vendorLinkedPartnerId = form.values.vendor_linked_partner_id;
    resetPartnerUsersInForm();
    if (vendorLinkedPartnerId) {
      ax.get(`/api/v1/leads/partner_users/${vendorLinkedPartnerId}`)
        .then((res) => {
          setLinkedPartnerUser(
            res.data.map((partner: PartnerUser) => {
              return {
                label: `${partner.name}-${partner.email}`,
                value: partner.id.toString(),
              };
            })
          );
        })
        .catch((err) => {});
    }
  }, [form.values.vendor_linked_partner_id]);

  const getPlaceholderText = (type: number | string) => {
    let placeholder = "";
    switch (type) {
      case LEAD_FORM_FIXED_COLUMN.POSTAL_CODE:
        placeholder = "000-0000";
        break;
      case LEAD_FORM_FIXED_COLUMN.ADDRESS:
        placeholder = "東京都新宿区新宿...";
        break;
      case LEAD_FORM_FIXED_COLUMN.TEL:
        placeholder = "00-0000-0000";
        break;
      case LEAD_FORM_FIXED_COLUMN.URL:
        placeholder = "https://example.com";
        break;
      case LEAD_FORM_FIXED_COLUMN.EMPLOYEES:
        placeholder = "0";
        break;
      case LEAD_FORM_FIXED_COLUMN.MEMO:
        placeholder = "メモ";
        break;
      default:
        break;
    }
    return placeholder;
  };

  // display input node from column data
  const displayInputNode = (
    type: string,
    column: CustomFormColumn,
    index: number
  ) => {
    switch (type) {
      case CUSTOM_COLUMN_TYPE_NAME_STRING:
        return (
          <TextInput
            key={`TextInput${index}String`}
            className={column.is_visible ? "" : "hidden"}
            placeholder={
              column.fixed_column_id
                ? getPlaceholderText(column.fixed_column_id)
                : ""
            }
            label={
              <span>
                {column.column_label}
                {column.is_required ? (
                  <span style={{ color: "red" }}> ※</span>
                ) : (
                  <></>
                )}
              </span>
            }
            type="text"
            {...(column.custom_column_id
              ? form.getInputProps(`inputs.${index}.custom_column_content`)
              : form.getInputProps(`inputs.${index}.value`))}
          />
        );
      case CUSTOM_COLUMN_TYPE_NAME_INTEGER:
        return (
          <NumberInput
            key={`TextInput${index}Integer`}
            className={column.is_visible ? "" : "hidden"}
            placeholder={
              column.fixed_column_id
                ? getPlaceholderText(column.fixed_column_id)
                : ""
            }
            label={
              <span>
                {column.column_label}
                {column.is_required ? (
                  <span style={{ color: "red" }}> ※</span>
                ) : (
                  <></>
                )}
              </span>
            }
            type="number"
            {...(column.custom_column_id
              ? form.getInputProps(`inputs.${index}.custom_column_content`)
              : form.getInputProps(`inputs.${index}.value`))}
          />
        );
      case CUSTOM_COLUMN_TYPE_NAME_SELECT:
        return (
          <Select
            maxDropdownHeight={164}
            key={`Select${index}`}
            className={column.is_visible ? "" : "hidden"}
            defaultValue={column.initial_value?.toString()}
            label={
              <span>
                {column.column_label}
                {column.is_required ? (
                  <span style={{ color: "red" }}> ※</span>
                ) : (
                  <></>
                )}
              </span>
            }
            data={
              column?.select_contents?.map((item) => ({
                value: item.select_id.toString(),
                label: item.select_value,
              })) || []
            }
            placeholder={
              column.fixed_column_id
                ? getPlaceholderText(column.fixed_column_id)
                : ""
            }
            rightSection={icon}
            styles={{ rightSection: { pointerEvents: "none" } }}
            {...(column.custom_column_id
              ? form.getInputProps(`inputs.${index}.select_id`)
              : form.getInputProps(`inputs.${index}.value`))}
          />
        );
      case CUSTOM_COLUMN_TYPE_NAME_DATE:
        return (
          <DateInput
            key={`DateInput${index}`}
            className={column.is_visible ? "" : "hidden"}
            label={
              <span>
                {column.column_label}
                {column.is_required ? (
                  <span style={{ color: "red" }}> ※</span>
                ) : (
                  <></>
                )}
              </span>
            }
            defaultValue={
              column.initial_value ? new Date(column.initial_value) : undefined
            }
            valueFormat="YYYY年MM月DD日"
            locale="ja"
            monthLabelFormat="YYYY年M月"
            yearLabelFormat="YYYY年"
            monthsListFormat="M"
            yearsListFormat="YYYY"
            firstDayOfWeek={0}
            placeholder={
              column.fixed_column_id
                ? getPlaceholderText(column.fixed_column_id)
                : ""
            }
            {...(column.custom_column_id
              ? form.getInputProps(`inputs.${index}.custom_column_content`)
              : form.getInputProps(`inputs.${index}.value`))}
          />
        );
      case CUSTOM_COLUMN_TYPE_NAME_TIME:
        return (
          <DateTimePicker
            key={`DateTimePicker${index}`}
            className={column.is_visible ? "" : "hidden"}
            label={
              <span>
                {column.column_label}
                {column.is_required ? (
                  <span style={{ color: "red" }}> ※</span>
                ) : null}
              </span>
            }
            placeholder={
              column.fixed_column_id
                ? getPlaceholderText(column.fixed_column_id)
                : "日時を選択"
            }
            value={form.getInputProps(`inputs.${index}.value`).value}
            onChange={(value: unknown) =>
              form.setFieldValue(`inputs.${index}.value`, value)
            }
            valueFormat="YYYY年MM月DD日 HH:mm"
          />
        );
      case CUSTOM_COLUMN_TYPE_NAME_FILE:
        return (
          <FileUpload
            key={`FileUpload${index}`}
            label={
              <span>
                {column.column_label}
                {column.is_required ? (
                  <span style={{ color: "red" }}> ※</span>
                ) : (
                  <></>
                )}
              </span>
            }
            fileName={""}
            msgErrorFile={
              msgErrorFile[index] !== "REQUIRE_INPUT" ? msgErrorFile[index] : ""
            }
            onFileChange={(file: File | null) => {
              onFileChange(file, index);
            }}
          />
        );
      case CUSTOM_COLUMN_TYPE_NAME_VENDOR_USER:
        return (
          <Select
            maxDropdownHeight={164}
            key={`Select${index}Vendor`}
            className={column.is_visible ? "" : "hidden"}
            label={
              <span>
                {column.column_label}
                {column.is_required ? (
                  <span style={{ color: "red" }}> ※</span>
                ) : (
                  <></>
                )}
              </span>
            }
            {...(column.custom_column_id
              ? form.getInputProps(`inputs.${index}.vendor_user_id`)
              : form.getInputProps(`inputs.${index}.value`))}
            data={
              vendorUserList
                ? vendorUserList.map((user) => {
                    return {
                      value: String(user.id),
                      label: `${user.name}-${user.email}`,
                    };
                  })
                : []
            }
            defaultValue={String(column.vendor_user_id)}
            placeholder={
              column.fixed_column_id
                ? getPlaceholderText(column.fixed_column_id)
                : ""
            }
            rightSection={<IconSelect />}
            styles={{ rightSection: { pointerEvents: "none" } }}
            searchable
            clearable
          />
        );
      case CUSTOM_COLUMN_TYPE_NAME_PARTNER_USER:
        return (
          <Select
            maxDropdownHeight={164}
            key={`TextInput${index}Partner`}
            className={column.is_visible ? "" : "hidden"}
            label={
              <span>
                {column.column_label}
                {column.is_required ? (
                  <span style={{ color: "red" }}> ※</span>
                ) : (
                  <></>
                )}
              </span>
            }
            {...(column.custom_column_id
              ? form.getInputProps(`inputs.${index}.partner_user_id`)
              : form.getInputProps(`inputs.${index}.value`))}
            data={linkedPartnerUser}
            placeholder={
              column.fixed_column_id
                ? getPlaceholderText(column.fixed_column_id)
                : ""
            }
            defaultValue={String(column.partner_user_id)}
            styles={{ rightSection: { pointerEvents: "none" } }}
            rightSection={<IconSelect />}
            searchable
            clearable
          />
        );
      case CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT:
        return (
          <Textarea
            key={`TextInput${index}LongText`}
            className={column.is_visible ? "" : "hidden"}
            label={
              <span>
                {column.column_label}
                {column.is_required ? (
                  <span style={{ color: "red" }}> ※</span>
                ) : (
                  <></>
                )}
              </span>
            }
            placeholder={
              column.fixed_column_id
                ? getPlaceholderText(column.fixed_column_id)
                : ""
            }
            {...(column.custom_column_id
              ? form.getInputProps(`inputs.${index}.custom_column_content`)
              : form.getInputProps(`inputs.${index}.value`))}
          />
        );

      // 入力項目制御の都道府県入力のrender
      case "PREFECTURE":
        return (
          <Select
            maxDropdownHeight={164}
            key={`Select${index}Prefecture`}
            defaultValue={column.initial_value?.toString()}
            label={
              <span>
                {"都道府県"}
                {column.is_required ? (
                  <span style={{ color: "red" }}> ※</span>
                ) : (
                  <></>
                )}
              </span>
            }
            data={prefectures}
            placeholder="選択してください"
            rightSection={icon}
            {...(column.custom_column_id
              ? form.getInputProps(`inputs.${index}.prefecture_id`)
              : form.getInputProps(`inputs.${index}.value`))}
            styles={{ rightSection: { pointerEvents: "none" } }}
          />
        );
      default:
        return (
          <TextInput
            key={`TextInput${index}Default`}
            className={column.is_visible ? "" : "hidden"}
            label={
              <span>
                {column.column_label}
                {column.is_required ? (
                  <span style={{ color: "red" }}> ※</span>
                ) : (
                  <></>
                )}
              </span>
            }
            type="text"
            placeholder={
              column.fixed_column_id
                ? getPlaceholderText(column.fixed_column_id)
                : ""
            }
            {...(column.custom_column_id
              ? form.getInputProps(`inputs.${index}.custom_column_content`)
              : form.getInputProps(`inputs.${index}.value`))}
          />
        );
    }
  };

  return (
    <div className={className}>
      <form className="form">
        <div className="form-items">
          {isLoading ? (
            <>
              <Skeleton height={8} mt={6} width="30%" radius="xl" />
              <Skeleton height={8} mt={6} radius="xl" />
              <Skeleton height={8} mt={6} radius="xl" />
              <Skeleton height={8} mt={6} radius="xl" />
              <Skeleton height={8} mt={6} width="30%" radius="xl" />
              <Skeleton height={8} mt={6} radius="xl" />
              <Skeleton height={8} mt={6} radius="xl" />
              <Skeleton height={8} mt={6} radius="xl" />
              <Skeleton height={8} mt={6} width="30%" radius="xl" />
              <Skeleton height={8} mt={6} radius="xl" />
              <Skeleton height={8} mt={6} radius="xl" />
              <Skeleton height={8} mt={6} radius="xl" />
            </>
          ) : (
            <>
              <TextInput
                key={`TextInputName`}
                label={
                  <span>
                    案件名
                    <span style={{ color: "red" }}> ※</span>
                  </span>
                }
                placeholder="株式会社パートナープロップ"
                required
                {...form.getInputProps("name")}
              />
              <Select
                maxDropdownHeight={164}
                dropdownPosition="bottom"
                key={`SelectVendorLinkedPartner`}
                label={
                  <span>
                    共有先パートナー
                    <span style={{ color: "red" }}> ※</span>
                  </span>
                }
                placeholder="選択してください"
                data={activePartnerList}
                rightSection={icon}
                required
                {...form.getInputProps("vendor_linked_partner_id")}
                styles={{ rightSection: { pointerEvents: "none" } }}
                defaultValue={""}
                searchable
              />

              {form.values.inputs.map((item, index) => {
                const column = customColumns[index];
                return displayInputNode(column.type_status, column, index);
              })}
            </>
          )}
        </div>
        <WrapperButton>
          <ButtonFooterModal
            as={Button}
            style={{ backgroundColor: propcolors.greyDefault }}
            onClick={close}
          >
            キャンセル
          </ButtonFooterModal>
          <Button loading={isSubmitting} onClick={handleSubmit}>
            追加する
          </Button>
        </WrapperButton>
      </form>
    </div>
  );
};

const WrapperButton = styled.div({
  display: "flex",
  justifyContent: "space-between",
  gap: "1rem",
  borderTop: `1px solid ${propcolors.gray[200]}`,
  backgroundColor: propcolors.gray[150],
  width: "100%",
  height: 100,
  padding: "1.8rem 2rem",
});

const ButtonFooterModal = styled.button({
  width: "100%",
  height: "auto",
  fontSize: "0.88rem",
  color: propcolors.white,
});

const Styled = styled(Presentational)`
  width: 100%;
  padding-top: 1.2rem;
  .hidden {
    display: none;
  }

  .form-items {
    overflow: scroll;
    padding: 0 1rem;
    max-height: calc(100vh - 290px);

    & > div {
      margin-bottom: 10px;
    }
    label {
      color: ${propcolors.blackLightLabel};
    }
  }
  .form {
    height: 100%;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    padding-top: 1rem;
    font-size: 12px;
  }
  .mantine-Select-input,
  .mantine-NumberInput-input,
  .mantine-TextInput-input {
    height: 48px;
    border-radius: 8px;
    margin-top: 0.5rem;
  }
  .mantine-Textarea-input {
    height: 96px;
    border-radius: 8px;
    margin-top: 0.5rem;
  }
  .mantine-Textarea-label,
  .mantine-Select-label,
  .mantine-TextInput-label {
    margin-top: 0.5rem;
    font-size: 12px;
  }
  .mantine-Select-required,
  .mantine-TextInput-required {
    display: none;
  }
  .mantine-Button-root {
    width: 100%;
    height: auto;
    font-size: 0.88rem;
    color: ${propcolors.white};
    background-color: ${propcolors.black};
  }
`;

export const NewLeadContent: React.FC<NewLeadProps> = ({ close }) => {
  const [activePartnerList, setActivePartnerList] = useState<SelectItem[]>([]);
  const [prefectureInputList, setPrefectureInputList] = useState<SelectItem[]>(
    []
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const [customColumns, setCustomColumns] = useState<CustomFormColumn[]>([]);
  const vendorUserList = useVendorUserList();
  const setVendorUserList = useSetVendorUserList();
  const [msgErrorFile, setMsgErrorFile] = useState<(string | null)[]>([]);
  const [isSubmitting, setSubmitting] = useState(false);

  const showInputRequiredNotification = (columnLabel: string) => {
    notifications.show({
      icon: <IconNotiFailed />,
      title: `${columnLabel}が入力されていません。`,
      message: `${columnLabel}は必須項目です。`,
      autoClose: 5000,
    });
  };

  const form = useForm<NewPartnerForm>({
    validateInputOnChange: false,
    initialValues: {
      name: "",
      vendor_linked_partner_id: "",
      linked_partner_user: "",
      inputs: [],
    },
    validate: {
      name: (value) => {
        // valueがundefinedか空文字列の場合はエラーなし
        if (value) {
          return null;
        }
        notifications.show({
          icon: <IconNotiFailed />,
          title: "案件名が入力されていません。",
          message: `案件名は必須項目です。`,
          autoClose: 5000,
        });
        return true;
      },
      vendor_linked_partner_id: (value) => {
        // valueがundefinedか空文字列の場合はエラーなし
        if (value) {
          return null;
        }
        notifications.show({
          icon: <IconNotiFailed />,
          title: "共有先パートナーが入力されていません。",
          message: `共有先パートナーは必須項目です。`,
          autoClose: 5000,
        });
        return true;
      },
      inputs: (values: InputColumn[]) => {
        const errors = values.map((item) => {
          if (item.is_visible && item.is_required) {
            const hasValue =
              (item.value !== null &&
                item.value !== undefined &&
                item.value !== "") ||
              !!item.custom_column_content ||
              !!item.partner_user_id ||
              !!item.vendor_user_id ||
              (item.type_status === CUSTOM_COLUMN_TYPE_NAME_SELECT &&
                !!item.select_id);
            if (!hasValue) {
              showInputRequiredNotification(item.column_label || "");
            }
            if (
              item.custom_column_content &&
              item.custom_column_content instanceof File
            ) {
              const value = item.custom_column_content;
              const maxFileSize = 15 * 1024 * 1024; // 15MB
              if (value && value.size > maxFileSize) {
                return "15MB以下のファイルを選択してください。";
              }

              const allowedFormats = ["image/png", "image/jpeg"];
              if (value && !allowedFormats.includes(value.type)) {
                return "画像ファイルはpngまたはjpegで選択してください。";
              }
            }
            return hasValue ? null : "REQUIRE_INPUT";
          }
          return null;
        });
        setMsgErrorFile(errors);
        return errors.some((err) => err) ? errors : null;
      },
    },
  });

  // file type columns
  const fileColumns: InputColumn[] = useMemo(() => {
    let fileColumns = [];
    fileColumns = form.values.inputs.filter((item: InputColumn) => {
      return (
        item.custom_column_content && item.custom_column_content instanceof File
      );
    });
    return fileColumns;
  }, [form.values.inputs]);

  const showSuccessNotification = () => {
    notifications.show({
      title: "案件が追加されました",
      message: "案件が正常に追加されました。",
      icon: <IconNotiSuccess />,
    });
  };

  const showFailNotification = () => {
    notifications.show({
      icon: <IconNotiFailed />,
      title: "案件の作成に失敗しました。",
      message: `操作内容をお確かめのうえ、時間をおいて、もう一度お試しください。`,
      autoClose: 5000,
    });
  };

  const getFixColumns = (formValues: NewPartnerForm) => {
    let returnColumns: { [key: string]: any } = {};
    formValues.inputs.map((column: InputColumn) => {
      if (column.is_fixed_column) {
        returnColumns[column.column_name] = column.value;
      }
    });
    return returnColumns;
  };

  const getCustomColumns = (formValues: NewPartnerForm) => {
    let returnColumns: InputColumn[] = [];
    returnColumns = formValues.inputs.filter((column: InputColumn) => {
      if (!column.is_fixed_column) {
        return column;
      }
    });

    return returnColumns;
  };

  const handleSubmit = async () => {
    // フォームのバリデーションを実行
    const errors = form.validate();

    const fixColumnsData = getFixColumns(form.values);
    if (!errors.hasErrors) {
      const activePartner: { [key: string]: any } | undefined =
        activePartnerList.find((partner) => {
          return partner.value === form.values.vendor_linked_partner_id;
        });

      const submitData = {
        lead_name: form.values.name,
        contract_status: 1,
        partner_user_id: form.values.linked_partner_user
          ? Number(form.values.linked_partner_user)
          : undefined,
        vendor_linked_partner_id: activePartner?.vendor_linked_partner_id,
        ...fixColumnsData,
      };
      // request create new lead
      setSubmitting(true);
      const createLeadResponse = await ax.post("/api/v1/leads", submitData);

      if (createLeadResponse.status === APIStatusCode.CODE_OK) {
        const leadId = createLeadResponse.data.lead_id;
        if (fileColumns.length > 0) {
          // request create new custom file by submit blob file
          const createCustomFileResponses = fileColumns.map(
            async (fileColumn: InputColumn) => {
              const formData = new FormData();
              formData.append("file", fileColumn.custom_column_content as Blob);
              return await ax.post(
                `/api/v1/leads/${leadId}/custom_file/${fileColumn.column_id}`,
                formData
              );
            }
          );
          await Promise.all(createCustomFileResponses);
        }
        const createCustomColumnData = {
          ...submitData,
          lead_id: leadId,
          custom_column_contents: getCustomColumns(form.values).map(
            (column: InputColumn) => {
              if (
                column.custom_column_content &&
                column.custom_column_content instanceof File
              ) {
                return {
                  ...column,
                  custom_column_content: column.custom_column_content.name,
                };
              }
              return column;
            }
          ),
        };

        // request create new custom column content
        const createCustomColumnResponse = await ax.put(
          `/api/v1/leads/${leadId}`,
          createCustomColumnData
        );
        if (createCustomColumnResponse.status === APIStatusCode.CODE_OK) {
          showSuccessNotification();
          revalidateFilteredLead();
          close();
        } else {
          showFailNotification();
        }
      } else {
        showFailNotification();
      }
      setSubmitting(false);
    }
  };
  useEffect(() => {
    ax.get("/api/v1/prefecture").then((response) => {
      setPrefectureInputList(response.data);
    });
    ax.get("/api/v1/partners/link_active").then((response) => {
      setActivePartnerList(
        response.data.map((partner: any) => {
          return {
            label: partner.partner_name,
            value: partner.partner_id,
            vendor_linked_partner_id: partner.vendor_linked_partner_id,
          };
        })
      );
    });

    ax.get("/api/v1/authenticated_lead_forms").then((res) => {
      setCustomColumns(res.data);
      setIsLoading(false);
    });
  }, []);

  useEffect(() => {
    form.setValues({
      ...form.values,
      inputs: customColumns.map((column) => {
        let returnInput: InputColumn = {
          column_id: column.custom_column_id
            ? column.custom_column_id
            : column.fixed_column_id,
          column_name: column.column_name,
          is_required: column.is_required,
          column_label: column.column_label,
          is_visible: column.is_visible,
          is_fixed_column: column.fixed_column_id ? true : false,
          type_status: column.type_status,
          initial_value: column.initial_value,
        };
        switch (column.type_status) {
          case CUSTOM_COLUMN_TYPE_NAME_VENDOR_USER:
            returnInput.vendor_user_id = column.initial_value || "";
            break;

          case CUSTOM_COLUMN_TYPE_NAME_PARTNER_USER:
            returnInput.partner_user_id = column.initial_value || "";
            break;

          case CUSTOM_COLUMN_TYPE_NAME_SELECT:
            returnInput.select_id = column.initial_value || "";
            break;

          case CUSTOM_COLUMN_TYPE_NAME_DATE:
            returnInput.value = column.initial_value
              ? new Date(column.initial_value)
              : "";
            break;

          // 入力項目制御の都道府県の初期値を反映させる
          case "PREFECTURE":
            returnInput.prefecture_id = column.initial_value
              ? Number(column.initial_value)
              : "";
            returnInput.value = column.initial_value
              ? Number(column.initial_value)
              : "";
            break;

          default:
            returnInput.custom_column_content = column.initial_value || "";
            returnInput.value = column.initial_value || "";
            break;
        }
        return returnInput;
      }),
    });
  }, [customColumns]);

  useEffect(() => {
    if (vendorUserList === null) {
      ax.get(`/api/v1/vendor_users/`)
        .then((res) => {
          setVendorUserList(res.data);
        })
        .catch((err) => {});
    }
  }, [vendorUserList, setVendorUserList]);

  return (
    <Styled
      form={form}
      isLoading={isLoading}
      isSubmitting={isSubmitting}
      handleSubmit={handleSubmit}
      close={close}
      msgErrorFile={msgErrorFile}
      prefectures={prefectureInputList}
      activePartnerList={activePartnerList}
      customColumns={customColumns}
      vendorUserList={vendorUserList}
    />
  );
};
