import { modals } from "@mantine/modals";
import { propcolors } from "styles/colors";
import styled from "@emotion/styled";

type ConfirmModalProps = {
  title: string;
  body: string;
  bottomMessage?: string;
  onConfirm: () => void;
};

const ModalTitle = styled.div`
  width: 100%;
  text-align: center;
  border-bottom: 1px solid ${propcolors.gray[200]};
  padding-bottom: 1rem;
  top: 1.3rem;
  position: absolute;
  margin: 0 -1rem;
  font-weight: 600;
  font-size: 16px;
  line-height: 27px;
`;
const ModalBody = styled.div`
  margin: 1rem 0;
  font-weight: 600;
  font-size: 18px;
  color: #222222;
`;
const ModalBottom = styled.div`
  font-size: 12px;
`;

/**
 * openDeleteConfirmModal 関数は、削除確認モーダルを表示するための関数です。
 *
 * @param {ConfirmModalProps} props - モーダルのプロパティ
 * @param {string} props.title - モーダルのタイトル「〇〇を削除」
 * @param {string} props.body - モーダルの本文
 * @param {string} [props.bottomMessage] - モーダルの下部メッセージ（オプション）
 * @param {() => void} props.onConfirm - 削除ボタンがクリックされた時に実行されるコールバック関数
 */
export const openDeleteConfirmModal = ({
  title,
  body,
  bottomMessage,
  onConfirm,
}: ConfirmModalProps) => {
  modals.openConfirmModal({
    title: <ModalTitle>{title}</ModalTitle>,
    labels: {
      confirm: "削除",
      cancel: "キャンセル",
    },
    size: "640px",
    children: (
      <div style={{ padding: "0.5rem" }}>
        <ModalBody>{body}</ModalBody>
        {bottomMessage && <ModalBottom>{bottomMessage}</ModalBottom>}
      </div>
    ),
    onConfirm: onConfirm,
    confirmProps: {
      sx: {
        width: "284px",
        borderRadius: "8px",
        fontSize: "14px",
        lineHeight: "14px",
        paddingTop: "14px",
        paddingBottom: "14px",
        marginRight: "8px",
        marginBottom: "8px",
        backgroundColor: `${propcolors.black}`,
        color: `${propcolors.white}`,
        height: "auto",
        "&:hover": {
          backgroundColor: `${propcolors.black}`,
        },
      },
    },
    cancelProps: {
      sx: {
        borderRadius: "8px",
        width: "284px",
        left: "24px",
        paddingTop: "14px",
        paddingBottom: "14px",
        position: "absolute",
        marginBottom: "8px",
        height: "auto",
        lineHeight: "14px",
        fontSize: "14px",
        borderColor: `${propcolors.greyDefault}`,
        backgroundColor: `${propcolors.greyDefault}`,
        color: `${propcolors.white}`,
        "&:hover": {
          backgroundColor: `${propcolors.greyDefault}`,
        },
      },
    },
  });
};
