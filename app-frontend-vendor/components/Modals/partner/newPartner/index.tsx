import styled from "@emotion/styled";
import { useDisclosure } from "@mantine/hooks";
import { Button, Modal } from "@mantine/core";
import { NewPartnerContent } from "./content";
import { propcolors } from "styles/colors";

const ModalTitle = styled.div({
  width: "100%",
  textAlign: "center",
  padding: "1rem 0",
  position: "absolute",
  top: 0,
  left: 0,
  backgroundColor: propcolors.white,
  borderBottom: `1px solid ${propcolors.gray[200]}`,
});

export const NewPartner = () => {
  const [opened, { open, close }] = useDisclosure(false);

  return (
    <>
      <Button
        onClick={open}
        style={{
          width: 158,
          fontSize: 14,
          fontWeight: 400,
          height: 42,
          borderRadius: 8,
          paddingLeft: 15,
          paddingRight: 15,
        }}
      >
        新規パートナー作成
      </Button>
      <Modal.Root opened={opened} onClose={close} size="640px">
        <Modal.Overlay />
        <Modal.Content>
          <Modal.Header>
            <ModalTitle>新規パートナーを追加</ModalTitle>
          </Modal.Header>
          <Modal.Body style={{ position: "relative" }}>
            <NewPartnerContent close={close} />
          </Modal.Body>
        </Modal.Content>
      </Modal.Root>
    </>
  );
};
