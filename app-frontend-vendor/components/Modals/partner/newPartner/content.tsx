import React, { useEffect, useState } from "react";
import styled from "@emotion/styled";
import { ax } from "utils/axios";
import { parseCookies } from "nookies";
import { UseFormReturnType, useForm } from "@mantine/form";
import {
  Button,
  NumberInput,
  Select,
  SelectItem,
  TextInput,
  Textarea,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { propcolors } from "styles/colors";
import IconSelect from "public/icons/icon-arrow-down.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { revalidateFilteredPartners } from "features/partners/hooks/partner";

type NewPartnerForm = {
  name: string;
  prefecture_id?: number;
  postal_code?: string;
  address?: string;
  tel?: string;
  memo?: string;
  url?: string;
  number_of_employees?: number;
  contract_status?: number;
};

type NewPartnerProps = {
  close: () => void;
};

type PresentationalProps = {
  className?: string;
  prefectures: SelectItem[];
  close: () => void;
  handleSubmit: () => void;
  form: UseFormReturnType<NewPartnerForm>;
  isPosting: boolean;
};

const Presentational: React.FC<PresentationalProps> = ({
  className,
  prefectures,
  close,
  form,
  handleSubmit,
  isPosting,
}) => {
  const icon = <IconSelect />;
  return (
    <div className={className}>
      <form className="form">
        <div className="form-items">
          <TextInput
            label={
              <span>
                パートナー名<span style={{ color: "red" }}> ※</span>
              </span>
            }
            placeholder="パートナー株式会社"
            required
            {...form.getInputProps("name")}
          />
          <TextInput
            label="郵便番号"
            placeholder="000-0000"
            {...form.getInputProps("postal_code")}
          />
          <Select
            label="都道府県"
            data={prefectures}
            placeholder="選択してください"
            rightSection={icon}
            {...form.getInputProps("prefecture_id")}
          />
          <TextInput
            label="住所"
            placeholder="東京都新宿区新宿..."
            {...form.getInputProps("address")}
          />
          <TextInput
            label="代表電話番号"
            placeholder="00-0000-0000"
            {...form.getInputProps("tel")}
          />
          <TextInput
            label="URL"
            placeholder="https://example.com"
            {...form.getInputProps("url")}
          />
          <NumberInput
            label="従業員数"
            type="number"
            min={0}
            placeholder="1234"
            {...form.getInputProps("number_of_employees")}
            hideControls
          />
          <Textarea
            label="メモ"
            {...form.getInputProps("memo")}
            maxLength={255}
            placeholder="255文字まで入力してください。"
          />
        </div>
        <WrapperButton>
          <ButtonFooterModal
            as={Button}
            style={{ backgroundColor: propcolors.greyDefault }}
            onClick={close}
          >
            キャンセル
          </ButtonFooterModal>
          <Button
            onClick={handleSubmit}
            loading={isPosting}
            style={{
              width: "100%",
              height: "auto",
              padding: "0.8rem 1rem",
              fontSize: "0.88rem",
              backgroundColor: propcolors.black,
              fontWeight: 600,
            }}
          >
            追加する
          </Button>
        </WrapperButton>
      </form>
    </div>
  );
};

const WrapperButton = styled.div({
  display: "flex",
  justifyContent: "space-between",
  gap: "1rem",
  borderTop: `1px solid ${propcolors.gray[200]}`,
  backgroundColor: propcolors.gray[150],
  width: "100%",
  marginTop: "1rem",
  padding: "1.8rem 2rem",
  position: "absolute",
  bottom: 0,
  left: 0,
});

const ButtonFooterModal = styled.button({
  padding: "0.8rem 1rem",
  width: "100%",
  height: "auto",
  fontSize: "0.88rem",
  color: propcolors.white,
});

const Styled = styled(Presentational)`
  width: 100%;
  min-height: 922px;
  padding-top: 1.2rem;
  .form-items {
    & > div {
      margin-bottom: 10px;
    }
    label {
      color: ${propcolors.blackLightLabel};
    }
  }
  .form {
    height: 100%;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    padding-top: 1rem;
    font-size: 12px;
  }
  .mantine-Select-input,
  .mantine-NumberInput-input,
  .mantine-TextInput-input {
    height: 48px;
    border-radius: 8px;
    margin-top: 0.5rem;
  }
  .mantine-Textarea-input {
    height: 96px;
    border-radius: 8px;
    margin-top: 0.5rem;
  }
  .mantine-Textarea-label,
  .mantine-TextInput-label,
  .mantine-Select-label {
    margin-top: 0.5rem;
    font-size: 12px;
  }
  .mantine-TextInput-required {
    display: none;
  }
  .mantine-Select-rightSection {
    pointer-events: none;
  }
`;

export const NewPartnerContent: React.FC<NewPartnerProps> = ({ close }) => {
  const [prefectureInputList, setPrefectureInputList] = useState<SelectItem[]>(
    []
  );
  const [isPosting, setIsPosting] = useState<boolean>(false);

  const form = useForm<NewPartnerForm>({
    validateInputOnChange: false,
    initialValues: {
      name: "",
    },
    validate: {
      name: (value) => {
        if (value.trim() === "") {
          notifications.show({
            icon: <IconNotiFailed />,
            title: "パートナー名が入力されていません。",
            message:
              "パートナー名は必須項目です。パートナー名をご入力ください。",
            autoClose: 5000,
          });
          return "パートナー名は必須項目です。";
        }
        return null;
      },
      postal_code: (value) => {
        if (!value) {
          return null;
        }
        return value.length <= 20
          ? null
          : "郵便番号は20文字以内で入力してください。";
      },
      tel: (value) => {
        if (!value) {
          return null;
        }
        return value.length <= 40
          ? null
          : "代表電話番号は40文字以内で入力してください。";
      },
      // URLの形式チェックを追加（http://またはhttps://で始まる形式）
      url: (value) => {
        if (!value) return null;
        // シンプルな正規表現でURL形式をチェック
        const pattern =
          /^(https?:\/\/)[\w\-_]+(\.[\w\-_]+)+([\w\-.,@?^=%&:/~+#]*[\w\-@?^=%&/~+#])?$/;
        if (!pattern.test(value)) {
          return "URLの形式が正しくありません。正しいURLを入力してください。";
        }
        return null;
      },
      // 従業員数がマイナスにならないようにチェック
      number_of_employees: (value) => {
        if (value == null) return null;
        if (value < 0) {
          return "従業員数は0以上の数値を入力してください。";
        }
        return null;
      },
    },
  });

  const handleSubmit = () => {
    // フォームのバリデーションを実行
    const errors = form.validate();

    if (!errors.hasErrors) {
      setIsPosting(true);
      const cookies = parseCookies(null, "user");
      const parsedcookies = JSON.parse(cookies.user);
      const submitData = {
        managed_partner_name: form.values.name,
        prefecture_id: Number(form.values.prefecture_id),
        postal_code: form.values.postal_code,
        address: form.values.address,
        tel: form.values.tel,
        memo: form.values.memo,
        url: form.values.url,
        number_of_employees: form.values.number_of_employees,
        contract_status: 1,
        vendor_user_id: parsedcookies.user_id,
      };

      ax.post("/api/v1/managed_partner", submitData)
        .then((res) => {
          notifications.show({
            title: "パートナーが追加されました",
            message: "パートナー情報が正常に追加されました。",
            icon: <IconNotiSuccess />,
          });
          // パートナー情報を最新化する
          revalidateFilteredPartners();
          close();
        })
        .catch((err) => {
          notifications.show({
            icon: <IconNotiFailed />,
            title: "パートナーの追加に失敗しました",
            message:
              "パートナー情報の追加中にエラーが発生しました。入力内容に誤りがないかご確認の上、再度お試しください。問題が解消されない場合は、システム管理者にお問い合わせください。",
            autoClose: 5000,
          });
        })
        .finally(() => {
          setIsPosting(false);
        });
    }
  };

  useEffect(() => {
    ax.get("/api/v1/prefecture").then((response) => {
      setPrefectureInputList(response.data);
    });
  }, []);

  return (
    <Styled
      form={form}
      handleSubmit={handleSubmit}
      close={close}
      prefectures={prefectureInputList}
      isPosting={isPosting}
    />
  );
};
