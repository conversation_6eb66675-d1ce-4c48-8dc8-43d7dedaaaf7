import styled from "@emotion/styled";
import { ax } from "utils/axios";
import { Button, Modal, TextInput } from "@mantine/core";
import { UseFormReturnType, useForm } from "@mantine/form";
import { propcolors } from "styles/colors";
import { notifications } from "@mantine/notifications";
import { useModalState, useSetModalState } from "utils/recoil/modalState";
import { useRouter } from "next/router";
import { getApiErrorMessage } from "utils/values/errorMessages";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { FormEvent, useState } from "react";

type NewTrainingProps = {
  training_title: string;
};

type PresentationalProps = {
  isOpen: boolean;
  className?: string;
  form: UseFormReturnType<NewTrainingProps>;
  close: () => void;
  onSubmit: (value?: FormEvent<HTMLFormElement> | undefined) => void;
};

const Presentation: React.FC<PresentationalProps> = ({
  className,
  close,
  form,
  onSubmit,
  isOpen,
}) => {
  const ModalTitle = styled.div`
    width: 100%;
    text-align: center;
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding-bottom: 1rem;
    top: 1.3rem;
    position: absolute;
    margin: 0 -1rem;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  `;
  return (
    <Modal
      title={<ModalTitle>新規トレーニングを追加</ModalTitle>}
      opened={isOpen}
      withCloseButton={false}
      onClose={close}
      className={className}
      size="640px"
    >
      <form onSubmit={onSubmit}>
        <div>
          <TextInput
            sx={{ marginBottom: "16px" }}
            label={
              <span>
                トレーニングタイトル
                <span style={{ color: "red" }}> ※ </span>
              </span>
            }
            description="日本語・英数字・記号等が使用できます"
            rightSection={
              <div>{`${
                form.values.training_title
                  ? form.values.training_title.length
                  : 0
              }/50`}</div>
            }
            rightSectionWidth={80}
            {...form.getInputProps("training_title")}
          />
        </div>
        <div className="modal-buttons">
          <Button className="modal-buttons-cancel" onClick={close}>
            キャンセル
          </Button>
          <Button
            color={"dark"}
            size={"md"}
            className="modal-buttons-submit full-width"
            type="submit"
          >
            追加する
          </Button>
        </div>
      </form>
    </Modal>
  );
};

const Styled = styled(Presentation)`
  .mantine-Modal-header {
    height: 56px;
  }
  .mantine-Modal-content {
    border-radius: 8px;
  }
  .mantine-TextInput-description {
    color: #666666;
  }
  .mantine-Select-required,
  .mantine-TextInput-required {
    display: none;
  }
  .buttons {
    display: flex;
    gap: 0.5rem;
  }
  .new-button {
    svg {
      margin-right: 1rem;
    }
  }
  .mantine-Modal-body {
    padding: 0px;
  }
  form {
    height: 100%;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    padding-top: 24px;
    .mantine-InputWrapper-root {
      padding: 0 24px;
      &:last-child {
        padding-bottom: 0px;
      }
    }
    .mantine-RadioGroup-label {
      font-weight: 600;
      font-size: 12px;
      color: #666666;
    }
    label {
      font-weight: 600;
      font-size: 12px;
      color: #666666;
    }

    input {
      padding: 13.5px 70px 13.5px 16px;
      height: auto;
      font-size: 14px;
      line-height: 21px;
      border-radius: 8px;
    }
  }
  .add-select {
    margin: 12px 0;
    font-size: 12px;
  }
  .selection {
    &-option {
      display: flex;
      justify-content: space-between;
      padding: 1rem 0;
      &-heading {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }
    &-input {
      display: flex;
      gap: 1rem;
      align-items: flex-end;
      .mantine-TextInput-root {
        width: 100%;
      }
    }
    &-item {
      border: 1px solid ${propcolors.gray["300"]};
      border-radius: 4px;
      padding: 4px 24px;
      list-style: none;
      margin-top: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .mantine-TextInput-root {
        width: 90%;
      }

      .delete {
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        background-color: ${propcolors.gray["200"]};
      }
    }
  }
  .modal-buttons {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    padding: 0px 24px 24px;
    margin-top: 0;
    // background-color: #f7f8f9;
    // border-top: 1px solid #e8eaed;
    &-cancel {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-weight: 600;
      font-size: 0.88rem;
      color: ${propcolors.white};
      background-color: ${propcolors.greyDefault};
      border-radius: 8px;
    }
    &-submit {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-size: 0.88rem;
      background-color: ${propcolors.black};
      border-radius: 8px;
    }
  }
`;

export const NewTraining: React.FC = () => {
  const modalState = useModalState();
  const setModal = useSetModalState();
  const [isCreatingTraining, setIsCreatingTraining] = useState<boolean>(false);
  const form = useForm<NewTrainingProps>({
    initialValues: {
      training_title: "",
    },
    validate: {
      training_title: (value) => {
        if (!value) {
          return "トレーニングタイトルを入力してください。";
        }
        if (value.length > 50) {
          return "トレーニングタイトルは50文字以内で入力してください。";
        }
      },
    },
    validateInputOnChange: true,
  });
  const router = useRouter();

  const close = () => {
    form.reset();
    setModal(null);
  };

  const postNewTraining = (newTrainingTitle: string) => {
    if (isCreatingTraining) return;
    setIsCreatingTraining(true);
    ax.post(`api/v1/trainings`, { training_title: newTrainingTitle })
      .then((res) => {
        notifications.show({
          icon: <IconNotiSuccess />,
          message: "トレーニングが作成されました。",
          autoClose: 5000,
        });
        setIsCreatingTraining(false);
        router.push(`/training/manage/${res.data}`);
        close();
      })
      .catch((error) => {
        notifications.show({
          icon: <IconNotiFailed />,
          title: "トレーニングが作成できませんでした。",
          message: getApiErrorMessage(error.response.data.message),
          autoClose: 5000,
        });
      });
  };

  const onSubmit = form.onSubmit((values) => {
    if (!values.training_title) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "カラムが作成できませんでした。",
        message: "トレーニングタイトルを入力してください。",
        autoClose: 5000,
      });
    } else {
      postNewTraining(values.training_title);
    }
  });

  return (
    <Styled
      isOpen={modalState === "newTraining"}
      close={close}
      form={form}
      onSubmit={onSubmit}
    />
  );
};
