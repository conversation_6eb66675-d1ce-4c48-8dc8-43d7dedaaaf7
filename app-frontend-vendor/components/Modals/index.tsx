import { NewCustomColumn } from "./settings/newCustomColumn";
import { NewCustomUserColumn } from "./settings/newCustomUserColumn";
import { NewProduct } from "./product/newProduct";
import { EditProduct } from "./product/editProduct";
import { NewSubtableModal } from "./settings/newSubtable";
import { EditCustomColumn } from "./settings/editCustomColumn";
import { EditCustomUserColumn } from "./settings/editCustomUserColumn";
import { EditSubtableModal } from "./settings/editSubtable";
import { NewSubtableColumn } from "./settings/newSubtableColumn";
import { EditSubtableColumn } from "./settings/editSubtableColumn";
import { NewWebTest } from "./webTest/newWebTest";
import { NewQuestion } from "./webTest/newQuestion";
import { NewTraining } from "./training/newTraining";

export const ModalContainer = () => {
  return (
    <>
      <NewCustomColumn />
      <NewCustomUserColumn />
      <EditCustomColumn />
      <EditCustomUserColumn />
      <NewProduct />
      <EditProduct />
      <NewSubtableModal />
      <EditSubtableModal />
      <NewSubtableColumn />
      <EditSubtableColumn />
      <NewWebTest />
      <NewQuestion />
      <NewTraining />
    </>
  );
};
