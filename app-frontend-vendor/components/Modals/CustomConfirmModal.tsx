import styled from "@emotion/styled";
import { Button, Modal, ModalProps } from "@mantine/core";
import { propcolors } from "styles/colors";
import CloseIcon from "public/icons/icon-close.svg";

interface CustomConfirmModalProps extends ModalProps {
  textSubmit?: string;
  textCancel?: string;
  onConfirm?: () => void;
  isDisplayFooter?: boolean;
}

export const CustomConfirmModal = (props: CustomConfirmModalProps) => {
  const {
    title,
    children,
    textSubmit,
    textCancel = "キャンセル",
    onConfirm,
    onClose,
    opened,
    isDisplayFooter = true,
  } = props;

  return (
    <Modal.Root opened={opened} onClose={onClose} size="640px">
      <Modal.Overlay />
      <Modal.Content style={{ borderRadius: 8 }}>
        <Modal.Header
          style={{
            padding: 16,
            borderBottom: `1px solid ${propcolors.gray[200]}`,
          }}
        >
          <ModalTitle>{title}</ModalTitle>
          <Modal.CloseButton size={18}>
            <CloseIcon />
          </Modal.CloseButton>
        </Modal.Header>
        <Modal.Body
          style={{ padding: 24, paddingBottom: isDisplayFooter ? 0 : 24 }}
        >
          {children}
        </Modal.Body>
        {isDisplayFooter ? (
          <ButtonGroup>
            <ButtonFooter as={Button} onClick={onClose} gray="true">
              {textCancel}
            </ButtonFooter>
            <ButtonFooter as={Button} onClick={onConfirm}>
              {textSubmit}
            </ButtonFooter>
          </ButtonGroup>
        ) : (
          <></>
        )}
      </Modal.Content>
    </Modal.Root>
  );
};

const ModalTitle = styled.div({
  width: "100%",
  textAlign: "center",
  fontSize: 16,
  fontWeight: 600,
  backgroundColor: propcolors.white,
  color: propcolors.blackLight,
});

const ButtonGroup = styled.div({
  display: "flex",
  justifyContent: "center",
  gap: 24,
  padding: 24,
});

const ButtonFooter = styled.div((props: {gray?: string}) => ({
  width: 284,
  height: 42,
  fontSize: 14,
  borderRadius: 8,
  padding: "14px 0",
  backgroundColor: props.gray ? propcolors.greyDefault : propcolors.black,
  borderColor: props.gray ? propcolors.greyDefault : propcolors.black,
  "&:hover": {
    backgroundColor: props.gray ? propcolors.greyDefault : propcolors.black,
  },
}));
