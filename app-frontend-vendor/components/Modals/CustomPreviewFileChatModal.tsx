import { Flex, Modal } from "@mantine/core";
import axios from "axios";
import { CustomNextImage } from "components/FileUpload/CustomNextImage";
import { MIME_TYPE_IMAGE_LIST, MIME_TYPE_PDF } from "constants/ruleValidation";
import { PDFViewer } from "features/drive/detail/components/pdfViever";
import CloseIcon from "public/icons/icon-close.svg";
import { useEffect, useState } from "react";

interface Props {
  opened: boolean;
  file: attached_files | null;
  close: () => void;
}
export const ax = axios.create();

export const axApi = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_HOST,
  withCredentials: true,
});


export const CustomPreviewFileChatModal = (props: Props) => {
  const { opened, file, close } = props;

  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [urlFileFetch, setUrlFileFetch] = useState("");
  useEffect(() => {
    checkSizeImage();
    handleFetchUrlPDF();
  }, [file?.url]);

  const checkSizeImage = () => {
    if (file && MIME_TYPE_IMAGE_LIST.includes(file.contentType)) {
      const img = new Image();
      img.src = file?.url ?? "";

      img.onload = () => {
        setImageSize({ width: img.width, height: img.height });
      };

      img.onerror = () => {
        console.log("Error loading image");
      };
    }
  };


  const handleFetchUrlPDF = (): void => {
    if (file && MIME_TYPE_PDF.includes(file.contentType)) {
      ax.get(file?.url, { responseType: "blob" }).then((res) => {
        const blob = new Blob([res.data], { type: "application/pdf" });
        const reader = new FileReader();

        reader.onloadend = () => {
          const base64String = reader.result as string;

          fetch(base64String)
            .then(res => res.blob())
            .then((base64Blob) => {
              const fileUrl = URL.createObjectURL(base64Blob);
              setUrlFileFetch(fileUrl);
            });
        };

        reader.readAsDataURL(blob);
      }).catch((err) => {
        if (err?.status == 403) {
          axApi.get(`/api/v1/chat/attached_files/${file?.id}`).then((res) => {
            setUrlFileFetch(res?.data?.url ?? "");
          });
        }
      });
    }

  };

  return (
    <Modal.Root
      opened={opened}
      onClose={close}
      size={"100%"}
      centered
      closeOnClickOutside
    >
      <Modal.Overlay />
      <Modal.Content style={{ borderRadius: 8 }}>
        <Modal.Header style={{ borderBottom: "1px solid #E8EAED" }}>
          <Modal.Title></Modal.Title>
          <Modal.CloseButton size={18}>
            <CloseIcon />
          </Modal.CloseButton>
        </Modal.Header>
        <Modal.Body style={{ padding: 24 }}>
          {file && MIME_TYPE_IMAGE_LIST.includes(file.contentType) ? (
            <Flex justify={"center"}>
              <CustomNextImage
                src={file.url}
                alt={file.name}
                width={imageSize.width}
                height={500}
                style={{
                  height: "auto",
                  maxHeight: "calc(100vh - 10dvh - 100px)",
                  objectFit: "contain",
                  maxWidth: "100%",
                }}
              />
            </Flex>
          ) : null}
          {file && MIME_TYPE_PDF.includes(file.contentType) ? (
            <div style={{ height: "calc(100vh - 10dvh - 100px)" }}>
              <PDFViewer fileUrl={urlFileFetch} isDownloadable={true} />
            </div>
          ) : null}
        </Modal.Body>
      </Modal.Content>
    </Modal.Root>
  );
};
