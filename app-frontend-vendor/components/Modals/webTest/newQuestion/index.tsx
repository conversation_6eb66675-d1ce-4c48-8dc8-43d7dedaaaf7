import React, { FormEvent } from "react";
import styled from "@emotion/styled";
import { ax } from "utils/axios";
import { Button, TextInput, Textarea, Modal } from "@mantine/core";
import { UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { useModalState, useSetModalState } from "utils/recoil/modalState";
import { useRouter } from "next/router";
import { useSetQuestionListState } from "utils/recoil/webTest/questionListState";
import { getApiErrorMessage } from "utils/values/errorMessages";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { propcolors } from "styles/colors";
import { useState } from "react";

type NewQuestionProps = {
  question_title: string;
  question_points: number;
};

type PresentationalProps = {
  isOpen: boolean;
  className?: string;
  isSubmitting: boolean;
  form: UseFormReturnType<NewQuestionProps>;
  close: () => void;
  onSubmit: (value?: FormEvent<HTMLFormElement> | undefined) => void;
};

const Presentation: React.FC<PresentationalProps> = ({
  className,
  close,
  form,
  onSubmit,
  isOpen,
  isSubmitting,
}) => {
  const ModalTitle = styled.div`
    width: 100%;
    text-align: center;
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding-bottom: 1rem;
    top: 1.3rem;
    position: absolute;
    margin: 0 -1rem;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  `;
  return (
    <Modal
      title={<ModalTitle>新規テストを追加</ModalTitle>}
      opened={isOpen}
      withCloseButton={false}
      onClose={close}
      className={className}
      size="640px"
    >
      <form onSubmit={onSubmit}>
        <div>
          <Textarea
            sx={{ marginBottom: "16px" }}
            label={
              <span>
                問題文
                <span style={{ color: "red" }}> ※ </span>
              </span>
            }
            description="日本語・英数字・記号等が使用できます"
            required
            rightSection={
              <div>{`${
                form.values.question_title
                  ? form.values.question_title.length
                  : 0
              }/500文字`}</div>
            }
            rightSectionWidth={100}
            {...form.getInputProps("question_title")}
          />
        </div>
        <div className="modal-input-number">
          <TextInput
            sx={{ marginBottom: "16px" }}
            label={
              <span>
                配点
                <span style={{ color: "red" }}> ※ </span>
              </span>
            }
            type="number"
            required
            rightSection="/100点"
            rightSectionWidth={80}
            {...form.getInputProps("question_points")}
          />
        </div>
        <div className="modal-buttons">
          <Button className="modal-buttons-cancel" onClick={close}>
            キャンセル
          </Button>
          <Button
            disabled={isSubmitting}
            color={"dark"}
            size={"md"}
            className="modal-buttons-submit full-width"
            type="submit"
          >
            追加する
          </Button>
        </div>
      </form>
    </Modal>
  );
};

const Styled = styled(Presentation)`
  .mantine-Modal-header {
    height: 56px;
  }
  .mantine-Modal-content {
    border-radius: 8px;
  }
  .mantine-TextInput-description {
    color: #666666;
  }
  .mantine-InputWrapper-required,
  .mantine-Select-required,
  .mantine-TextInput-required {
    display: none;
  }
  .buttons {
    display: flex;
    gap: 0.5rem;
  }
  .new-button {
    svg {
      margin-right: 1rem;
    }
  }
  .mantine-Modal-body {
    padding: 0px;
  }
  form {
    height: 100%;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    padding-top: 24px;
    .mantine-InputWrapper-root {
      padding: 0 24px;
      &:last-child {
        padding-bottom: 8px;
      }
    }
    .mantine-RadioGroup-label {
      font-weight: 600;
      font-size: 12px;
      color: #666666;
    }
    label {
      font-weight: 600;
      font-size: 12px;
      color: #666666;
    }

    input {
      padding: 13.5px 16px;
      height: auto;
      font-size: 14px;
      line-height: 21px;
      border-radius: 8px;
    }
  }
  .add-select {
    margin: 12px 0;
    font-size: 12px;
  }
  .modal-buttons {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    padding: 24px 40px;
    margin-top: 0;
    background-color: #f7f8f9;
    border-top: 1px solid #e8eaed;
    &-cancel {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-weight: 600;
      font-size: 0.88rem;
      color: ${propcolors.white};
      background-color: ${propcolors.greyDefault};
      border-radius: 8px;
    }
    &-submit {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-size: 0.88rem;
      background-color: ${propcolors.black};
      border-radius: 8px;
    }
  }
`;

export const NewQuestion: React.FC = () => {
  const modalState = useModalState();
  const setModal = useSetModalState();
  const setQuestionList = useSetQuestionListState();
  const [isSubmitting, setSubmitting] = useState<boolean>(false);

  const form = useForm<NewQuestionProps>({
    validate: {
      question_title: (value) => {
        if (!value) {
          return "設問文を入力してください。";
        }
        if (value.length > 500) {
          return "設問文は500文字以内で入力してください。";
        }
      },
      question_points: (value) => {
        if (!value) {
          return "配点を入力してください。";
        }
        if (value < 0 || value > 100) {
          return "配点は0~100点の間で入力してください。";
        }
      },
    },
    validateInputOnChange: true,
  });
  const router = useRouter();
  const { id } = router.query;

  const close = () => {
    form.reset();
    setModal(null);
  };

  form.onSubmit((_value, e) => {
    e.preventDefault();
  });

  const postNewQuestion = (newQuestion: NewQuestionProps) => {
    setSubmitting(true);

    ax.post(`/api/v1/web_tests/${id}/questions`, { ...newQuestion })
      .then((res) => {
        notifications.show({
          icon: <IconNotiSuccess />,
          title: "設問の作成に成功しました。",
          message: "処理は正常に行われました。",
          autoClose: 3000,
        });
        setQuestionList((prev) => {
          if (prev) {
            return [...prev, res.data];
          } else {
            return [res.data];
          }
        });
        router.push(
          `/webTest/manage/${router.query.id}/question/${res.data.id}`
        );
        close();
        setSubmitting(false);
      })
      .catch((error) => {
        notifications.show({
          icon: <IconNotiFailed />,
          title: "設問の作成に失敗しました。",
          message: getApiErrorMessage(error.response.data.message),
          autoClose: 5000,
        });
        setSubmitting(false);
      });
  };

  const onSubmit = form.onSubmit((values) => {
    if (isSubmitting) return;
    if (!values.question_title) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "設問が作成できませんでした。",
        message: "設問タイトルを入力してください。",
        autoClose: 5000,
      });
    } else if (!values.question_points) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "設問が作成できませんでした。",
        message: "配点を入力してください。",
        autoClose: 5000,
      });
    } else {
      const data = {
        question_title: values.question_title,
        question_points: values.question_points,
      };
      postNewQuestion(data);
    }
  });

  return (
    <Styled
      isSubmitting={isSubmitting}
      isOpen={modalState === "newQuestion"}
      close={close}
      form={form}
      onSubmit={onSubmit}
    />
  );
};
