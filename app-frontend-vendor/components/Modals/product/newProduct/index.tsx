import React from "react";
import styled from "@emotion/styled";
import { ax } from "utils/axios";
import { propcolors } from "styles/colors";
import { Button, Modal, TextInput } from "@mantine/core";
import { useModalState, useSetModalState } from "utils/recoil/modalState";
import { UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { useSetProductState } from "utils/recoil/product/productState";
import { getApiErrorMessage } from "utils/values/errorMessages";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";

type CreateButtonProps = {};

type PresentationalProps = {
  className?: string;
  close: () => void;
  isOpen: boolean;
  form: UseFormReturnType<{ product_name: string }>;
  handleSubmit: () => void;
} & CreateButtonProps;

const Presentational: React.FC<PresentationalProps> = ({
  className,
  isOpen,
  close,
  form,
  handleSubmit,
}) => (
  <Modal
    className={className}
    opened={isOpen}
    onClose={close}
    withCloseButton={false}
    padding={0}
    size={640}
    maw={640}
  >
    <div className="modal-wrap">
      <div className="modal-header">
        <p>新規プロダクトを追加</p>
      </div>
      <div className="modal-content">
        <div className="modal-content-input">
          <span className="modal-header-title">
            プロダクト名
            <span style={{ color: "red" }}> ※</span>
          </span>
          <TextInput
            placeholder=""
            className="modal-content-input-text"
            required
            {...form.getInputProps("product_name")}
          />
        </div>
        <div className="modal-content-actions">
          <Button
            className="modal-button modal-button-cancel"
            type="button"
            onClick={close}
          >
            キャンセル
          </Button>
          <Button
            className="modal-button modal-button-submit"
            type="button"
            onClick={handleSubmit}
          >
            追加する
          </Button>
        </div>
      </div>
    </div>
  </Modal>
);

const Styled = styled(Presentational)`
  .modal {
    &-wrap {
      background: ${propcolors.white};
      border-radius: 8px;
    }
    &-header {
      color: ${propcolors.blackLight};
      padding: 16px 40px;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      height: 51px;
      color: #222222;
      border-bottom: 1px solid ${propcolors.gray[200]};
      &-title {
        font-size: 12px;
        font-weight: 600;
        color: #666666;
      }
    }

    .warning {
      color: #f93932 !important;
      font-size: 50px;
    }

    &-content {
      padding: 24px;
      flex-direction: column;
      display: flex;
      gap: 16px;

      &-label {
        font-size: 12px;
        font-weight: 600;
        color: #666666;
      }
      &-warning {
        font-size: 21px;
        font-weight: 300;
        transform: translateY(2px);
        display: inline-block;
        color: #f93932;
      }
      &-input input {
        margin-top: 8px;
        border-radius: 8px;
        height: 48px;
        ::placeholder {
          color: #222222;
        }
      }
      &-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 24px;
      }
    }
    &-button {
      width: 284px;
      height: 42px;
      border-radius: 8px;
      font-size: 14px;
      &-cancel {
        background-color: ${propcolors.greyDefault};
      }
      &-submit {
        background-color: ${propcolors.blackLight};
      }
    }
  }
`;

export const NewProduct: React.FC<CreateButtonProps> = () => {
  const setProductList = useSetProductState();
  const modalState = useModalState();
  const setModalState = useSetModalState();
  const close = () => setModalState(null);
  const isOpen = modalState === "newProduct";
  const form = useForm<{ product_name: string }>({
    initialValues: {
      product_name: "",
    },
  });

  const handleSubmit = () => {
    // product_nameが空文字かどうかをチェック
    if (!form.values.product_name.trim()) {
      // product_nameが空文字の場合、エラーメッセージを表示して処理を中断
      notifications.show({
        title: "プロダクト名が入力されていません",
        message: "プロダクト名は必須項目です",
        icon: <IconNotiFailed />,
      });
      return; // ここで処理を中断
    }

    ax.post("/api/v1/products", {
      name: form.values.product_name,
      product_status: "ENABLED",
    })
      .then(() => {
        notifications.show({
          title: "プロダクトが作成されました",
          message: "プロダクトが正常に作成されました。",
          icon: <IconNotiSuccess />,
        });
        close();
        form.reset();
        setProductList(null);
      })
      .catch((err) => {
        notifications.show({
          title: "プロダクトの作成に失敗しました",
          message: getApiErrorMessage(err.response.data.message),
          icon: <IconNotiFailed />,
        });
      });
  };
  return (
    <Styled
      isOpen={isOpen}
      close={close}
      form={form}
      handleSubmit={handleSubmit}
    />
  );
};
