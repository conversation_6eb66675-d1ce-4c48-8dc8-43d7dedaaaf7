import React, { useEffect } from "react";
import styled from "@emotion/styled";
import { ax } from "utils/axios";
import { DropDownIcons } from "components/svgs/product";
import { propcolors } from "styles/colors";
import { Button, TextInput, Select, Modal } from "@mantine/core";
import { useModalState, useSetModalState } from "utils/recoil/modalState";
import {
  useEditProductState,
  useSetEditProductState,
} from "utils/recoil/product/editProductState";
import { UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { useSetProductState } from "utils/recoil/product/productState";
import { getApiErrorMessage } from "utils/values/errorMessages";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";

type CreateButtonProps = {};

type PresentationalProps = {
  className?: string;
  close: () => void;
  isOpen: boolean;
  productInfo: {
    name: string;
    status: string;
  } | null;
  form: UseFormReturnType<{ product_name: string; status: string }>;
  handleSubmit: () => void;
} & CreateButtonProps;

const Presentational: React.FC<PresentationalProps> = ({
  className,
  isOpen,
  close,
  productInfo,
  form,
  handleSubmit,
}) => (
  <Modal
    opened={isOpen}
    withCloseButton={false}
    size={640}
    maw={640}
    miw={320}
    padding={0}
    onClose={close}
    className={className}
  >
    <div className="modal-wrap">
      <div className="modal-header">
        <p>プロダクト「{productInfo?.name}」を編集</p>
      </div>
      <div className="modal-content">
        <div className="modal-content-input">
          <span className="modal-header-title">
            プロダクト名
            <span style={{ color: "red" }}> ※</span>
          </span>
          <TextInput
            label=""
            defaultValue={productInfo?.name}
            required
            {...form.getInputProps("product_name")}
          />
        </div>
        <div className="modal-content-select">
          <span className="modal-header-title">
            有効ステータス
            <span style={{ color: "red" }}> ※</span>
          </span>
          <Select
            label=""
            required
            className="modal-content-select-input"
            styles={{ rightSection: { pointerEvents: "none" } }}
            rightSection={<DropDownIcons />}
            defaultValue={productInfo?.status}
            dropdownPosition="bottom"
            zIndex={1000}
            data={[
              { value: "DISABLED", label: "無効" },
              { value: "ENABLED", label: "有効" },
            ]}
            {...form.getInputProps("status")}
          />
        </div>
        <div className="modal-content-actions">
          <Button
            className="modal-button modal-button-cancel"
            type="button"
            onClick={close}
          >
            キャンセル
          </Button>
          <Button
            className="modal-button modal-button-submit"
            type="button"
            onClick={handleSubmit}
          >
            保存する
          </Button>
        </div>
      </div>
    </div>
  </Modal>
);

const Styled = styled(Presentational)`
  .modal-content-select-input {
    input {
      border-radius: 8px;
    }
  }

  .modal {
    &-wrap {
      background-color: white;
      border-radius: 8px;
      scrollbar-width: none;
      box-shadow:
        0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.05),
        rgba(0, 0, 0, 0.05) 0 2.25rem 1.75rem -0.4375rem,
        rgba(0, 0, 0, 0.04) 0 1.0625rem 1.0625rem -0.4375rem;
    }
    &-content {
      &-label {
        font-size: 12px;
        font-weight: 600;
        color: #666666;
      }
      &-warning {
        font-size: 21px;
        font-weight: 300;
        transform: translateY(2px);
        display: inline-block;
        color: #f93932;
      }
    }
    &-header {
      color: ${propcolors.blackLight};
      padding: 16px 40px;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      border-bottom: 1px solid ${propcolors.gray[200]};
      &-title {
        font-size: 12px;
        font-weight: 600;
        color: #666666;
      }
    }

    &-content {
      padding: 24px;
      flex-direction: column;
      display: flex;
      gap: 16px;
      &-input input {
        border-radius: 8px;
        margin-top: 8px;
        height: 48px;
      }
      &-select input {
        margin-top: 8px;
        height: 48px;
        border-radius: 8px;
      }
      &-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 24px;
      }
    }
    &-button {
      width: 284px;
      height: 42px;
      border-radius: 8px;
      font-size: 14px;
      &-cancel {
        background-color: ${propcolors.greyDefault};
      }
      &-submit {
        background-color: ${propcolors.blackLight};
      }
    }
  }

  .mantine-Modal-content {
    height: 600px;
    background-color: transparent;
    box-shadow: none;
    scrollbar-width: none;
  }

  .mantine-Select-item {
    background-color: white;
    :hover {
      background-color: #f5f7f9;
    }
  }

  .mantine-Select-item[data-selected] {
    background-color: #F93832 !important;
    color: white;
  }
`;

export const EditProduct: React.FC<CreateButtonProps> = () => {
  const setProductList = useSetProductState();
  const productInfo = useEditProductState();
  const setEditProduct = useSetEditProductState();
  const modalState = useModalState();
  const setModalState = useSetModalState();
  const isOpen = modalState === "editProduct";
  const form = useForm<{ product_name: string; status: string }>({});

  const close = () => {
    setModalState(null);
    setEditProduct(null);
  };

  useEffect(() => {
    if (productInfo) {
      form.setValues({
        product_name: productInfo?.name,
        status: productInfo?.status,
      });
    }
  }, [productInfo]);

  const handleSubmit = () => {
    ax.put(`/api/v1/products/${productInfo!.product_id}`, {
      name: form.values.product_name,
      product_status: form.values.status,
    })
      .then(() => {
        notifications.show({
          title: "情報が更新されました",
          message: "プロダクトの情報更新が完了しました。",
          icon: <IconNotiSuccess />,
        });
        setProductList(null);
        close();
      })
      .catch((err) => {
        notifications.show({
          title: "情報更新に失敗しました",
          message: getApiErrorMessage(err.response.data.message),
          icon: <IconNotiFailed />,
        });
      });
  };
  return (
    <Styled
      isOpen={productInfo !== null ? isOpen : false}
      close={close}
      productInfo={productInfo}
      form={form}
      handleSubmit={handleSubmit}
    />
  );
};
