import styled from "@emotion/styled";
import { motion, Variants } from "framer-motion";
import { propcolors } from "styles/colors";
import { Portal } from "./portal";

type props = {
  className?: string;
  close: (e: any) => void;
  children: React.ReactNode;
};

const variants: Variants = {
  initial: {
    opacity: 0,
  },
  enter: {
    opacity: 1,
    transition: {
      duration: 0.1,
      ease: "easeIn",
    },
  },
  exit: {
    opacity: 0,
  },
};

const Presentation: React.FC<props> = ({ className, close, children }) => {
  return (
    <Portal>
      <motion.div
        variants={variants}
        initial="initial"
        animate="enter"
        exit="exit"
        className={className}
      >
        <div id="modal-backdrop" onClick={close} />
        <div id="modal-content">{children}</div>
      </motion.div>
    </Portal>
  );
};

const Styled = styled(Presentation)`
  position: fixed;
  z-index: 20000;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  #modal-backdrop {
    position: fixed;
    z-index: 20000;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: ${propcolors.black}80;
    backdrop-filter: blur(4px);
  }
  #modal-content {
    position: absolute;
    z-index: 20001;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    padding: 32px 0;
    max-width: 100vw;
    min-width: 600px;
    max-height: 90vh;
    display: inline-block;
    background-color: ${propcolors.white};
    border-radius: 10px;
    @media screen and (max-width: 512px) {
      width: 100vw;
      border-radius: 0;
      padding: 16px;
      max-width: unset;
      min-width: unset;
    }
  }
  
`;

export const Modal: React.FC<props> = ({ children, close }) => {
  return <Styled close={close}>{children}</Styled>;
};
