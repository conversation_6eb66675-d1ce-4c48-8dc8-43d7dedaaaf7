import styled from "@emotion/styled";
import { propcolors } from "styles/colors";

export const ModalFormButtons = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid ${propcolors.gray[200]};
  .buttons {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
    &-cancel {
      padding: 0.8rem 1rem;
      width: 100%;
      height: auto;
      font-size: 0.88rem;
      color: ${propcolors.white};
      background-color: ${propcolors.greyDefault};
    }
    &-submit {
      padding: 0.8rem 1rem;
      width: 100%;
      height: auto;
      font-size: 0.88rem;
      background-color: ${propcolors.black};
      color: ${propcolors.white};
    }
  }
`;

export const ModalFormHeading = styled.h2`
  font-size: 20px;
  margin-bottom: 16px;
`;

export const ModalFormRecommended = styled.p`
  font-size: 13px;
  margin-bottom: 14px;
`;

export const ModalForm = styled.form``;

export const ModalFormLabel = styled.label`
  & + & {
    margin-top: 12px;
    display: block;
  }
  > span {
    display: block;
    margin-bottom: 12px;
    line-height: 1;
  }
  > input, > select {
    width: 60%;
    &.width {
      &-max {
        width: 100%;
      }
      &-small {
        width: 25%;
      }
    }
  }
`;
