import React from "react";
import styled from "@emotion/styled";
import { Modal } from "@mantine/core";
import { propcolors } from "styles/colors";
import { Cross1Icon } from "@radix-ui/react-icons";

interface DriveModifierModal {
  opened: boolean;
  close: () => void;
  children: React.ReactNode;
  mode?: DriveModifierMode | undefined;
  title: string;
  closeMarkEnable?: boolean;
}

interface PresentationProps extends DriveModifierModal {
  className?: string;
}

const Presentation: React.FC<PresentationProps> = ({
  close,
  children,
  title,
  opened,
  className,
  closeMarkEnable,
  mode,
}) => {
  return (
    <Modal
      className={className}
      opened={opened}
      onClose={close}
      withCloseButton={false}
      padding={0}
      size={640}
      maw={640}
      miw={320}
    >
      <div
        className="modal-wrap"
        style={{ minHeight: mode === "share" ? "500px" : "auto" }}
      >
        <div className="modal-header">
          <p>{title}</p>
          {closeMarkEnable && (
            <div className="modal-header-close" onClick={close}>
              <Cross1Icon fill={"#8992A0"} color="#8992A0" />
            </div>
          )}
        </div>
        <div className="modal-content">{children}</div>
      </div>
    </Modal>
  );
};

const Styled = styled(Presentation)`
  .edit-portal-button {
    height: 42px;
    color: ${propcolors.blackLight};
    boder-radius: 6px;
    border: 1px solid ${propcolors.gray[200]};
  }

  .modal {
    &-wrap {
      background: ${propcolors.white};
      border-radius: 8px;
    }
    &-header {
      color: ${propcolors.blackLight};
      padding: 16px 40px;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      position: relative;
      border-bottom: 1px solid ${propcolors.gray[200]};
      &-close {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-40%);
      align-self: flex-end;
      scale: 1.2;
      cursor: pointer;
      }
    }

    &-content {
      flex-direction: column;
      display: flex;
      gap: 16px;
      &-input input {
        margin-top: 8px;
        height: 48px;
      }
      &-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 24px;
      }
    }
`;

export const DriveModal: React.FC<DriveModifierModal> = ({
  close,
  opened,
  children,
  title,
  mode,
  closeMarkEnable,
}) => {
  return (
    <Styled
      close={close}
      opened={opened}
      title={title}
      mode={mode}
      closeMarkEnable={closeMarkEnable}
    >
      {children}
    </Styled>
  );
};
