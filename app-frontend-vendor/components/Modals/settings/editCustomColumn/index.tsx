import styled from "@emotion/styled";
import {
  Button,
  Text,
  Group,
  Checkbox,
  Modal,
  Radio,
  Select,
  TextInput,
  rem,
} from "@mantine/core";
import { UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { RiDeleteBin6Line, RiMenuLine } from "@remixicon/react";

import { useRouter } from "next/router";
import ArrowDownIcon from "public/icons/arrow-down.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import React, { FormEvent, useEffect, useRef, useState } from "react";
import { useDrag, useDrop } from "react-dnd";
import { propcolors } from "styles/colors";
import {
  useCustomColumn,
  useSetCustomColumn,
} from "utils/recoil/customColumn/customColumnState";
import {
  useEditCustomColumn,
  useSetEditCustomColumn,
} from "utils/recoil/customColumn/editCustomColumnState";
import { useGetDataMasters } from "utils/recoil/dataMasters";

import { CUSTOM_COLUMN_TYPE_STR_VALUES } from "constants/ja/common";
import {
  usePermissions,
  PermissionKey,
  Permissions,
  useHandlePermissionChange,
} from "hooks/usePermissions";

import { useModalState, useSetModalState } from "utils/recoil/modalState";

type SelectItems = {
  select_id: number | null;
  select_value: string;
  // ドロップダウンの選択肢の表示順
  sort_order: number;
};

type NewPartnerColumnProps = {
  column_id: number | null;
  column_name: string;
  column_label: string;
  type_name: string;
  default: number | null;
  select_items: SelectItems[];
  select_default: string;
  read_permission: string;
  write_permission: string;
};

type PresentationalProps = {
  isOpen: boolean;
  className?: string;
  form: UseFormReturnType<NewPartnerColumnProps>;
  selectionInputRef: React.RefObject<HTMLInputElement>;
  addSelection: () => void;
  close: () => void;
  deleteSelectItem: (index: number) => void;
  onSubmit: (value?: FormEvent<HTMLFormElement> | undefined) => void;
  changeSelectItem: (
    newValue: string,
    content_index: number,
    content_id: number | null
  ) => void;
  editColumn: CustomColumn | null;
  moveItem: (fromIndex: number, toIndex: number) => void;
  statusSelect: { id: number; name: string }[];
  pathname: string;
  readPermissions: Permissions;
  setReadPermissions: React.Dispatch<React.SetStateAction<Permissions>>;
  writePermissions: Permissions;
  setWritePermissions: React.Dispatch<React.SetStateAction<Permissions>>;
};

const Presentation: React.FC<PresentationalProps> = ({
  className,
  close,
  form,
  onSubmit,
  addSelection,
  deleteSelectItem,
  isOpen,
  editColumn,
  changeSelectItem,
  moveItem,
  statusSelect,
  pathname,
  readPermissions,
  writePermissions,
  setWritePermissions,
  setReadPermissions,
}) => {
  const { resetPermissions, updateFormPermissions } = usePermissions(
    statusSelect,
    form
  );

  const { handleWritePermissionChange, handleReadPermissionChange } =
    useHandlePermissionChange(setReadPermissions, setWritePermissions);

  useEffect(() => {
    updateFormPermissions(readPermissions, writePermissions);
  }, [writePermissions, readPermissions, updateFormPermissions]);

  useEffect(() => {
    if (!isOpen) {
      resetPermissions();
    }
  }, [isOpen, resetPermissions]);

  const ModalTitle = styled.div`
    width: 100%;
    text-align: center;
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding-bottom: 1rem;
    top: 1.3rem;
    position: absolute;
    margin: 0 -1rem;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  `;
  return (
    <Modal
      title={<ModalTitle>カスタムカラム編集</ModalTitle>}
      opened={isOpen}
      withCloseButton={false}
      onClose={close}
      className={className}
      size="640px"
    >
      <form onSubmit={onSubmit}>
        <div>
          <TextInput
            sx={{ marginBottom: "16px" }}
            label={
              <span>
                表示名
                <span style={{ color: "red" }}> ※</span>
              </span>
            }
            description="日本語・英数字・記号等が使用できます"
            required
            {...form.getInputProps("column_label")}
          />
          <TextInput
            sx={{ marginBottom: "16px" }}
            label={
              <span>
                カスタムカラムID
                <span style={{ color: "red" }}> ※</span>
              </span>
            }
            description="英数字のみ"
            required
            {...form.getInputProps("column_name")}
          />
          <Select
            sx={{ marginBottom: "16px" }}
            label={
              <span>
                カスタムカラム型
                <span style={{ color: "red" }}> ※</span>
              </span>
            }
            disabled
            defaultValue={editColumn ? editColumn.type_status : ""}
            data={CUSTOM_COLUMN_TYPE_STR_VALUES}
            rightSection={<ArrowDownIcon />}
            required
            {...form.getInputProps("type_name")}
          />

          {editColumn && editColumn.type_status === "SELECT" && (
            <div>
              <Radio.Group
                sx={{ marginBottom: "16px" }}
                name="dropdownChoices"
                label="選択肢"
                {...form.getInputProps("default")}
              >
                <ol>
                  {form.values.select_items &&
                    form.values.select_items.map((item, index) => (
                      <DraggableItem
                        key={index}
                        index={index}
                        item={item}
                        moveItem={moveItem}
                      >
                        <li className="selection-item">
                          <TextInput
                            value={item.select_value}
                            placeholder="選択肢を入力してください。"
                            onChange={(e) =>
                              changeSelectItem(
                                e.currentTarget.value,
                                index,
                                item.select_id
                              )
                            }
                          />
                          <div
                            className="delete"
                            onClick={() => deleteSelectItem(index)}
                          >
                            <RiDeleteBin6Line
                              style={{
                                cursor: "pointer",
                                width: rem(24),
                                height: rem(24),
                                fill: "#8992A0",
                              }}
                            />
                          </div>
                        </li>
                      </DraggableItem>
                    ))}
                </ol>
                <div className="add-select">
                  <Button
                    type="button"
                    size="xs"
                    variant="subtle"
                    onClick={addSelection}
                  >
                    +選択肢を追加
                  </Button>
                </div>
              </Radio.Group>
              <div className="selection-default">
                <Select
                  label={
                    <span>
                      初期値
                      <span style={{ color: "red" }}> ※</span>
                    </span>
                  }
                  placeholder="初期値を選択してください"
                  defaultValue={editColumn && String(editColumn.default)}
                  data={
                    form.values.select_items &&
                    form.values.select_items !== undefined
                      ? form.values.select_items.map((item, index) => {
                          return {
                            value: String(index),
                            label: item.select_value,
                          };
                        })
                      : [""]
                  }
                  rightSection={<ArrowDownIcon />}
                  required
                  {...form.getInputProps("select_default")}
                  styles={{ rightSection: { pointerEvents: "none" } }}
                />
              </div>
            </div>
          )}
          {pathname === "/settings/columns/leads" && (
            <>
              <div className="permission-section">
                <Text className="form-label">
                  閲覧可能なユーザー <span style={{ color: "red" }}> ※ </span>
                </Text>
                <div className="permission-checkbox-group">
                  <Group className="checkbox-group">
                    {Object.keys(readPermissions).map((key) => (
                      <Checkbox
                        key={key}
                        name={key}
                        label={readPermissions[key as PermissionKey].name}
                        checked={readPermissions[key as PermissionKey].checked}
                        disabled={key === "VENDOR_ADMIN"}
                        onChange={handleReadPermissionChange}
                        sx={{
                          ".mantine-Checkbox-input": {
                            transform: "scale(0.6)",
                            borderRadius: "5px",
                          },
                          ".mantine-Checkbox-inner": {
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          },
                        }}
                      />
                    ))}
                  </Group>
                </div>
              </div>
              <div className="permission-section">
                <Text className="form-label">
                  入力可能なユーザー <span style={{ color: "red" }}> ※ </span>
                </Text>
                <div className="permission-checkbox-group">
                  <Group className="checkbox-group">
                    {Object.keys(writePermissions).map((key) => (
                      <Checkbox
                        key={key}
                        name={key}
                        label={writePermissions[key as PermissionKey].name}
                        checked={writePermissions[key as PermissionKey].checked}
                        disabled={key === "VENDOR_ADMIN"}
                        onChange={handleWritePermissionChange}
                        sx={{
                          ".mantine-Checkbox-input": {
                            transform: "scale(0.6)",
                            borderRadius: "5px",
                          },
                          ".mantine-Checkbox-inner": {
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          },
                        }}
                      />
                    ))}
                  </Group>
                </div>
              </div>
            </>
          )}
        </div>
        <div className="modal-buttons">
          <Button className="modal-buttons-cancel" onClick={close}>
            キャンセル
          </Button>
          <Button
            color={"dark"}
            size={"md"}
            className="modal-buttons-submit full-width"
            type="submit"
          >
            保存する
          </Button>
        </div>
      </form>
    </Modal>
  );
};

const Styled = styled(Presentation)`
  .mantine-Modal-header {
    height: 56px;
  }
  .mantine-Modal-content {
    border-radius: 8px;
  }
  .mantine-TextInput-description {
    color: #666666;
  }
  .mantine-Select-required,
  .mantine-TextInput-required {
    display: none;
  }
  .buttons {
    display: flex;
    gap: 0.5rem;
  }
  .new-button {
    svg {
      margin-right: 1rem;
    }
  }
  .mantine-Modal-body {
    padding: 0px;
  }
  form {
    height: 100%;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    padding-top: 24px;
    .mantine-InputWrapper-root {
      padding: 0 24px;
      &:last-child {
        padding-bottom: 8px;
      }
    }
    .mantine-RadioGroup-label {
      font-weight: 600;
      font-size: 12px;
      color: #666666;
      margin-bottom: 8px;
    }
    .mantine-InputWrapper-description {
      margin-bottom: 8px;
    }
    label {
      font-weight: 600;
      font-size: 12px;
      color: #666666;
      margin-bottom: 8px;
    }

    input {
      padding: 13.5px 16px;
      height: auto;
      font-size: 14px;
      line-height: 21px;
      border-radius: 8px;
    }
  }
  .add-select {
    margin: 12px 0;
    font-size: 12px;
  }

  .permission-checkbox-group {
    border: 1px solid #e0e0e0;
    padding: 14px 24px;
    margin-bottom: 16px;
    position: relative;
    border-radius: 8px;

    .checkbox-group {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .mantine-Checkbox-label {
        font-size: 14px;
        margin-left: 18px;
        font-weight: 500;
        margin-bottom: 0px;
      }
    }
  }
  .form-label {
    font-weight: 600;
    font-size: 12px;
    color: #666666;
    margin-bottom: 8px;
  }
  .permission-section {
    padding: 0 24px;
    &:last-child {
      padding-bottom: 8px;
    }
  }

  .selection {
    &-option {
      display: flex;
      justify-content: space-between;
      padding: 1rem 0;
      &-heading {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }
    &-input {
      display: flex;
      gap: 1rem;
      align-items: flex-end;
      .mantine-TextInput-root {
        width: 100%;
      }
    }
    &-item {
      flex-grow: 1;
      padding: 4px 0px;
      list-style: none;
      margin: 8px 0px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      &:first-child {
        margin-top: 0;
      }

      .mantine-TextInput-root {
        width: 90%;
        padding: 0px;
      }
      .delete {
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        // background-color: ${propcolors.gray["200"]};
      }
    }
  }
  .modal-buttons {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    padding: 24px 40px;
    margin-top: 0;
    background-color: #f7f8f9;
    border-top: 1px solid #e8eaed;
    &-cancel {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-weight: 600;
      font-size: 0.88rem;
      color: ${propcolors.white};
      background-color: ${propcolors.greyDefault};
      border-radius: 8px;
    }
    &-submit {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-size: 0.88rem;
      background-color: ${propcolors.black};
      border-radius: 8px;
    }
  }
`;

export const EditCustomColumn: React.FC = () => {
  const modalState = useModalState();
  const setModal = useSetModalState();
  const [isModified, toggleModified] = useState<boolean>(false);
  const customColumns = useCustomColumn();
  const setCustomColumns = useSetCustomColumn();
  const editColumn = useEditCustomColumn();
  const setEditColumn = useSetEditCustomColumn();
  const selectionInputRef = useRef<HTMLInputElement>(null);
  const dataMasters = useGetDataMasters();
  const [statusSelect, setStatusSelect] = useState<
    { id: number; name: string }[]
  >([]);
  const router = useRouter();

  const form = useForm<NewPartnerColumnProps>({
    initialValues: {
      column_id: null,
      column_name: "",
      column_label: "",
      type_name: "",
      default: null,
      select_items: [],
      select_default: "",
      read_permission: "",
      write_permission: "",
    },
    validate: {
      column_name: (value) =>
        /^[a-zA-Z0-9]+$/.test(value)
          ? null
          : "カスタムカラムIDは半角英数字を入力してください",
    },
  });

  const {
    setWritePermissions,
    setReadPermissions,
    writePermissions,
    readPermissions,
    initializeColumnPermissions,
  } = usePermissions(statusSelect, form);

  useEffect(() => {
    if (dataMasters && dataMasters.column_permissions) {
      const data = Object.keys(dataMasters.column_permissions).map((value) => ({
        id: dataMasters.column_permissions[value].id,
        name: value,
      }));
      setStatusSelect(data);
    }
  }, [dataMasters]);

  const close = () => {
    form.reset();
    setEditColumn(null);
    setModal(null);
  };

  form.onSubmit((value, e) => {
    e.preventDefault();
  });

  useEffect(() => {
    if (editColumn) {
      initializeColumnPermissions(
        editColumn.read_permission,
        editColumn.write_permission
      );
      if (editColumn.select_contents) {
        form.setFieldValue(
          "select_items",
          editColumn.select_contents.map((select_content) => {
            return {
              select_id: select_content.select_id,
              select_value: select_content.select_value,
              sort_order: select_content.sort_order,
            };
          })
        );
      }
      form.setFieldValue("column_name", editColumn.column_name);
      form.setFieldValue("column_label", editColumn.column_label);
      form.setFieldValue("select_default", String(editColumn.default));
      form.setFieldValue("type_name", editColumn.type_status);
    }
  }, [editColumn]);

  const isSameColumn = (column: CustomColumn) => {
    // ユニーク前提のカスタムカラムID(表示上の名称。ユーザーに入力してもらうもの)と表示名で、変更しているカラムと同じカラムかどうかを判定する
    return (
      column.column_name.toLowerCase() === editColumn?.column_name?.toLowerCase() &&
      column.column_label === editColumn?.column_label
    );
  };
  const onSubmit = form.onSubmit((values) => {
    if (
      customColumns.find(
        (value) =>
          value.column_name.toLowerCase() === values.column_name.toLowerCase() &&
          value.column_id !== editColumn?.column_id
      )
    ) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "カラムが作成できませんでした。",
        message:
          "このカスタムカラムIDは既に使用されています。IDを変えて、もう一度お試しください。カスタムカラムは大文字、小文字で区別されません。",
        autoClose: 5000,
      });
      return;
    } else if (
      values.type_name === "SELECT" &&
      values.select_items.length < 1
    ) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "カラムが作成できませんでした。",
        message: "選択肢を追加してください。",
        autoClose: 5000,
      });
      return;
    } else if (values.type_name === "SELECT" && !values.select_default) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "カラムが作成できませんでした。",
        message: "選択肢の初期値を選択してください。",
        autoClose: 5000,
      });
      return;
    } else if (values.type_name === "SELECT") {
      if (values.select_items.find((item) => item.select_value === "")) {
        notifications.show({
          icon: <IconNotiFailed />,
          title: "カラムが作成できませんでした。",
          message: "選択肢に空欄が存在します。",
          autoClose: 5000,
        });
        return;
      }
    }

    setCustomColumns((prev) =>
      prev.map((column) => {
        if (editColumn !== null && isSameColumn(column)) {
          return {
            column_id: editColumn.column_id,
            column_name: values.column_name.toLowerCase(),
            column_label: values.column_label,
            type_name: editColumn.type_name,
            type_status: editColumn.type_status,
            default: Number(values.select_default),
            select_contents: values.select_items,
            isModified: true,
            sort_order: editColumn.sort_order,
            write_permission: values.write_permission,
            read_permission: values.read_permission,
          } as unknown as CustomColumn;
        }
        return column;
      })
    );
    toggleModified(true);
    notifications.show({
      icon: <IconNotiSuccess />,
      title: "カスタムカラムが正常に追加されました！",
      message: "処理が正常に完了しました。",
      autoClose: 5000,
    });
    close();
  });

  const deleteSelectItem = (index: number) => {
    form.removeListItem("select_items", index);
  };

  const changeSelectItem = (newValue: string, content_index: number) => {
    const previousValue =
      form.values.select_items === undefined ? [] : form.values.select_items;
    previousValue[content_index].select_value = newValue;
    form.setFieldValue("select_items", [...previousValue]);
  };

  const addSelection = () => {
    const previousValue =
      form.values.select_items === undefined ? [] : form.values.select_items;
    form.setFieldValue("select_items", [
      ...previousValue,
      {
        select_id: null,
        select_value: "",
        sort_order: previousValue.length,
      },
    ]);
  };

  const moveItem = (fromIndex: number, toIndex: number) => {
    const items = [...form.values.select_items];
    const [movedItem] = items.splice(fromIndex, 1);
    items.splice(toIndex, 0, movedItem);
    // 並び替えた後のsort_orderを更新
    items.forEach((item, index) => {
      item.sort_order = index;
    });
    form.setFieldValue("select_items", items);
    const currentSelectedValue: number =
      form.getInputProps("select_default").value;
    if (currentSelectedValue) {
      if (currentSelectedValue == fromIndex)
        form.setFieldValue("select_default", String(toIndex));
      else if (currentSelectedValue == toIndex)
        form.setFieldValue("select_default", String(fromIndex));
    }
  };

  return (
    <Styled
      isOpen={modalState === "editCustomColumn"}
      close={close}
      form={form}
      onSubmit={onSubmit}
      addSelection={addSelection}
      selectionInputRef={selectionInputRef}
      deleteSelectItem={deleteSelectItem}
      editColumn={editColumn}
      changeSelectItem={changeSelectItem}
      moveItem={moveItem}
      statusSelect={statusSelect}
      pathname={router.pathname}
      readPermissions={readPermissions}
      setReadPermissions={setReadPermissions}
      writePermissions={writePermissions}
      setWritePermissions={setWritePermissions}
    />
  );
};

// ドロップダウンの並び替え用の型を定義
type DraggableItemProps = {
  item: SelectItems; // SelectItemsを適用
  index: number;
  moveItem: (fromIndex: number, toIndex: number) => void;
  [key: string]: any;
};
const ItemType = {
  ITEM: "ITEM",
};

// ドラッグアイテムの型を定義
type DraggedItem = {
  type: string;
  index: number;
  item: SelectItems;
};

// 選択肢のアイテムコンポーネントをドラッグ可能にする
const DraggableItem: React.FC<DraggableItemProps> = ({
  item,
  index,
  moveItem,
  ...props
}) => {
  const ref = React.useRef(null);

  const [, drop] = useDrop<DraggedItem>({
    accept: ItemType.ITEM,
    hover: (draggedItem) => {
      if (draggedItem.index !== index) {
        moveItem(draggedItem.index, index);
        draggedItem.index = index;
      }
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: ItemType.ITEM,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(drop(ref));

  return (
    <div
      ref={ref}
      style={{
        opacity: isDragging ? 0.5 : 1,
        display: "flex",
        alignItems: "center",
        gap: "6px",
      }}
      {...props}
    >
      <RiMenuLine
        style={{
          cursor: "grab",
          width: rem(24),
          height: rem(24),
          fill: "#8992A0",
        }}
      />
      {props.children}
    </div>
  );
};
