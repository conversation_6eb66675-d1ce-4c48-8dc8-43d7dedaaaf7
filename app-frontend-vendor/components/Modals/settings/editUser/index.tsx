import styled from "@emotion/styled";
import { <PERSON><PERSON>, Modal, Select, TextInput } from "@mantine/core";
import { type UseFormReturnType, useForm } from "@mantine/form";
import { useDisclosure } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";
import { FileUpload } from "components/FileUpload";
import Image from "next/image";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import type React from "react";
import { type FormEvent, useEffect, useState } from "react";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import { useSetCompanyUser } from "utils/recoil/company/companyUserState";
import {
  useSessionUser,
  useSetSessionUser,
} from "utils/recoil/sessionUserState";
import { getApiErrorMessage } from "utils/values/errorMessages";
import IconSelect from "../../../../public/icons/icon-arrow-down.svg";

type EditButtonProps = {
  user: ManagedUser;
  divisions: Division[];
  positions: Position[];
};

type NewUserProps = {
  name: string;
  name_kana: string;
  position_id: string;
  division: string;
  employment_type: string;
  file_name: string;
  flg_file_deleted?: number;
  email: string;
  tel: string;
  role: string;
  file: File | null;
  apply_2fa: string;
};

type Division = {
  label: string;
  value: string;
};

type Position = {
  label: string;
  value: string;
};

type PresentationalProps = {
  className?: string;
  msgErrorFile: string | null;
  user: ManagedUser;
  form: UseFormReturnType<NewUserProps>;
  opened: boolean;
  open: () => void;
  close: () => void;
  divisions: Division[];
  positions: Position[];
  fileName?: string;
  onSubmit: (value?: FormEvent<HTMLFormElement> | undefined) => void;
};

const Presentation: React.FC<PresentationalProps> = ({
  className,
  user,
  opened,
  open,
  close,
  form,
  msgErrorFile,
  fileName,
  onSubmit,
}) => {
  const sessionUser = useSessionUser();
  const sessionUserRole = sessionUser?.user_role;

  const onFileChange = (file: File | null) => {
    if (file === null) {
      form.setFieldValue("flg_file_deleted", 1);
    }
    form.setFieldValue("file", file);
  };

  return (
    <div className={className}>
      <Image
        width={32}
        height={32}
        src="/icons/edit.svg"
        alt="edit-button"
        className="edit-button"
        onClick={open}
      />
      <Modal
        className={className}
        opened={opened}
        onClose={close}
        withCloseButton={true}
        size={640}
        maw={640}
        miw={320}
        title={
          <div className="modal-header">
            <p>ユーザー情報を編集</p>
          </div>
        }
      >
        <div className="modal-wrap">
          <div className="modal-content">
            <div className="modal-content-input">
              <form onSubmit={onSubmit} className="form-modal">
                <div className="modal-form">
                  <TextInput label="名前" {...form.getInputProps("name")} />
                  <TextInput
                    label="名前（カナ）"
                    {...form.getInputProps("name_kana")}
                  />
                  <TextInput label="部署" {...form.getInputProps("division")} />
                  <TextInput
                    label="メールアドレス"
                    {...form.getInputProps("email")}
                  />
                  <TextInput label="電話番号" {...form.getInputProps("tel")} />
                  {sessionUserRole === 1 && (
                    <Select
                      label="ロール"
                      data={[
                        {
                          value: "1",
                          label: "管理者",
                        },
                        {
                          value: "2",
                          label: "ユーザー",
                        },
                      ]}
                      rightSection={<IconSelect />}
                      {...form.getInputProps("role")}
                      styles={{ rightSection: { pointerEvents: "none" } }}
                    />
                  )}
                  <Select
                    label="2段階認証"
                    data={[
                      {
                        value: "0",
                        label: "適用しない",
                      },
                      {
                        value: "1",
                        label: "適用する",
                      },
                    ]}
                    rightSection={<IconSelect />}
                    {...form.getInputProps("apply_2fa")}
                    styles={{ rightSection: { pointerEvents: "none" } }}
                  />
                  {form.getInputProps("apply_2fa").value === "1" && (
                    <div
                      css={{
                        width: "100%",
                        margin: "8px 0",
                        fontSize: "14px",
                        color: propcolors.blackLightLabel,
                      }}
                    >
                      ログイン時にワンタイムパスワードによる追加認証が必要となります。
                      <br />
                      ワンタイムパスワードは登録されたメールアドレス宛に送信されます。
                    </div>
                  )}
                  <FileUpload
                    label="プロフィール画像"
                    fileName={fileName}
                    msgErrorFile={msgErrorFile}
                    onFileChange={onFileChange}
                  />
                </div>
                <div className="modal-footer">
                  <div className="modal-buttons">
                    {sessionUserRole === 1 && (
                      <Button
                        className="modal-buttons-reset full-width"
                        type="button"
                        color="gray"
                        onClick={close}
                      >
                        キャンセル
                      </Button>
                    )}
                    {sessionUserRole !== 1 && user?.id === sessionUser?.id && (
                      <Button
                        className="modal-buttons-reset full-width"
                        type="button"
                        color="gray"
                        onClick={close}
                      >
                        キャンセル
                      </Button>
                    )}
                    <Button
                      className="modal-buttons-submit full-width"
                      type="submit"
                    >
                      保存
                    </Button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

const Styled = styled(Presentation)`
  .mantine-Modal-body {
    padding: 0px;
  }
  .edit-button {
    cursor: pointer;
  }
  .mantine-Modal-title {
    width: 100%;
  }
  .mantine-Modal-header {
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding: 16px 40px;
  }
  .mantine-Modal-close {
    right: 18px;
    position: absolute;
    svg {
      width: 24px !important;
      height: 24px !important;
    }
  }
  .modal {
    &-header {
      color: ${propcolors.blackLight};
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      width: 100%;
    }
    &-wrap {
      background: ${propcolors.white};
      border-radius: 8px;
    }
    &-content {
      flex-direction: column;
      display: flex;
      gap: 16px;
      &-input input {
        margin-top: 8px;
        height: 48px;
        border-radius: 0.5rem;
      }
      label {
        margin-top: 16px;
        color: ${propcolors.blackLightLabel};
        font-weight: 400;
        font-size: 12px;
      }
      &-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 24px;
      }
      .modal-form {
        padding: 8px 24px 24px 24px;
      }
      .modal-footer {
        background-color: ${propcolors.greyBreadcrumb};
        padding: 24px 24px 16px 8px;
        .modal-buttons {
          display: flex;
          justify-content: space-between;
          gap: 1rem;
          margin: 0px;
          font-weight: 400;
          padding: 0px 8px 0px 24px;
          &-cancel,
          &-reset {
            padding: 0.8rem 1rem;
            width: 268px;
            height: 42px;
            font-size: 14px;
            color: ${propcolors.white};
            background-color: ${propcolors.greyDefault};
            border: 0.0625rem solid transparent;
            border-radius: 8px;
          }
          &-submit {
            padding: 0.8rem 1rem;
            width: 268px;
            height: 42px;
            font-size: 14px;
            background-color: ${propcolors.black};
            border-radius: 8px;
          }
          &-delete {
            color: ${propcolors.orangeDel};
            border: 1px solid ${propcolors.orangeDel};
            background: ${propcolors.white};
            font-size: 14px;
            font-weight: 400;
            width: 60px;
            height: 42px;
            border-radius: 8px;
            padding: 0;
            margin-left: calc(50% - 22px);
            margin-top: 8px;
          }
        }
      }
    }
  }
`;

export const EditUser: React.FC<EditButtonProps> = ({
  user,
  divisions,
  positions,
}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const setCompanyUser = useSetCompanyUser();
  const sessionUser = useSessionUser();
  const setSessionUser = useSetSessionUser();
  const [msgErrorFile, setMsgErrorFile] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string | undefined>();
  const form = useForm<NewUserProps>({
    initialValues: {
      name: user.name,
      name_kana: user.name_kana,
      division: user.division,
      position_id: user.position_id,
      file_name: user.file_name,
      flg_file_deleted: 0,
      employment_type: user.employment_type,
      email: user.email,
      tel: user.tel,
      role: String(user.role),
      file: null,
      apply_2fa: "0",
    },
    validate: {
      name: (value) => {
        if (value === "") return "名前を入力してください。";
        // 名前に%または％を含む場合はエラー
        if (value.match(/[%％]/))
          return "名前に「%」を含めることはできません。";
      },
      name_kana: (value) => {
        if (value === "") return "名前（カナ）を入力してください。";
        if (!/^[ァ-ヶー 　]+$/.test(value))
          return "名前（カナ）は全角カタカナで入力してください。";
      },
      division: (value) => {
        if (value === "") return "部署を入力してください。";
      },
      role: (value) => {
        if (value === "") return "ロールを入力してください。";
      },
      file: (value) => {
        const maxFileSize = 15 * 1024 * 1024; // 15MB
        if (value && value.size > maxFileSize) {
          setMsgErrorFile("15MB以下のファイルを選択してください。");
          return "15MB以下のファイルを選択してください。";
        }

        const allowedFormats = ["image/png", "image/jpeg"];
        if (value && !allowedFormats.includes(value.type)) {
          setMsgErrorFile("画像ファイルはpngまたはjpegで選択してください。");
          return "画像ファイルはpngまたはjpegで選択してください。";
        }

        setMsgErrorFile(null);
      },
      email: (value) => {
        // メールアドレスかどうかの判定
        if (!value.match(/.+@.+\..+/))
          return "メールアドレスの形式が正しくありません。";
      },
      tel: (value) => {
        if (value === "") return "電話番号を入力してください。";
        //　電話番号にはハイフンを含まない
        if (value.match(/[-ー]/))
          return "電話番号にハイフンを含めることはできません。";
        // 電話番号かどうかの判定
        if (!value.match(/^[0-9]{10,11}$/))
          return "電話番号の形式が正しくありません。";
      },
    },
  });

  useEffect(() => {
    setMsgErrorFile(null);
    setFileName(user.file_name);
    form.setFieldValue("apply_2fa", user.two_factor_enabled ? "1" : "0");
  }, [user.file_name, user.two_factor_enabled, form.setFieldValue]);

  const fetchUserList = async () => {
    ax.get("/api/v1/vendor_users").then((res) => {
      setCompanyUser(res.data);
    });
  };

  const onSubmit = form.onSubmit((values) => {
    const data = {
      name: values.name,
      name_kana: values.name_kana,
      division: values.division,
      position_id: values.position_id,
      employment_type: values.employment_type,
      flg_file_deleted: values.flg_file_deleted || 0,
      email: values.email,
      tel: values.tel,
      role: Number(values.role),
      apply_2fa: Number(values.apply_2fa),
    };
    if (values.file) {
      // @ts-ignore
      data.file = values.file;
    }
    const formData = new FormData();
    Object.keys(data).forEach((key: string) => {
      // @ts-ignore
      formData.append(key, data[key]);
    });
    ax.post(`/api/v1/vendor_users/${user.id}?_method=PUT`, formData)
      .then((res) => {
        notifications.show({
          icon: <IconNotiSuccess />,
          title: "ユーザーが更新されました！",
          message: "処理が正常に完了しました。",
          autoClose: 5000,
        });
        form.reset();
        fetchUserList();
        // 修正対象が自分自身の場合、セッションユーザーの情報を更新する
        if (sessionUser && sessionUser.id === user.id) {
          const userData: SessionUser = {
            ...sessionUser, // **必須プロパティは既に number**
            user_name: form.values.name,
            user_email: form.values.email,
            user_role: Number(form.values.role),
            user_division: res.data?.division ?? sessionUser.user_division,
            avatar_url: res.data?.avatar_url ?? sessionUser.avatar_url,
          };
          setSessionUser(userData);
        }
        close();
      })
      .catch((err) => {
        notifications.show({
          icon: <IconNotiFailed />,
          title: "情報更新に失敗しました。",
          message: getApiErrorMessage(err.response.data.error),
          autoClose: 5000,
        });
      });
  });

  return (
    <Styled
      user={user}
      opened={opened}
      open={open}
      close={close}
      form={form}
      msgErrorFile={msgErrorFile}
      divisions={divisions}
      positions={positions}
      fileName={fileName}
      onSubmit={onSubmit}
    />
  );
};
