import styled from "@emotion/styled";
import {
  Button,
  Checkbox,
  Group,
  Modal,
  Radio,
  rem,
  Select,
  Text,
  TextInput,
} from "@mantine/core";
import { type UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { RiDeleteBin6Line, RiMenuLine } from "@remixicon/react";
import { CUSTOM_COLUMN_TYPE_INT_VALUES } from "constants/ja/common";
import {
  findPermissionIdByName,
  getPermissionsFromID,
  type PermissionKey,
  type Permissions,
  useHandlePermissionChange,
  usePermissions,
} from "hooks/usePermissions";
import { useRouter } from "next/router";
import ArrowDownIcon from "public/icons/arrow-down.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import React, { type FormEvent, useEffect, useRef, useState } from "react";
import { useDrag, useDrop } from "react-dnd";
import { propcolors } from "styles/colors";
import { useGetDataMasters } from "utils/recoil/dataMasters";
import { useModalState, useSetModalState } from "utils/recoil/modalState";
import {
  useEditSubtableColumn,
  useSetEditSubtableColumn,
} from "utils/recoil/subtableColumn/editSubtableColumnState";
import {
  useSetSubtableColumn,
  useSubtableColumn,
} from "utils/recoil/subtableColumn/subtableColumnState";

type SelectItems = {
  select_id: number | null;
  select_value: string;
  // ドロップダウンの選択肢の表示順
  sort_order: number;
};

type NewPartnerColumnProps = {
  column_id: number | null;
  column_name: string;
  column_label: string;
  // type_name: "1" | "2" | "3" | "4" | "5" | "6" | "7" | "";
  type_name: number;
  default: number | null;
  select_items: SelectItems[];
  select_default: string;
  date: Date;
  write_permission: string;
  read_permission: string;
};

type PresentationalProps = {
  isOpen: boolean;
  className?: string;
  form: UseFormReturnType<NewPartnerColumnProps>;
  selectionInputRef: React.RefObject<HTMLInputElement>;
  selectionInputDefault: React.RefObject<HTMLInputElement>;
  addSelection: () => void;
  close: () => void;
  deleteSelectItem: (index: number) => void;
  onSubmit: (value?: FormEvent<HTMLFormElement> | undefined) => void;
  changeSelectItem: (
    newValue: string,
    content_index: number,
    content_id: number | null,
  ) => void;
  statusSelect: { id: number; name: string }[];
  moveItem: (fromIndex: number, toIndex: number) => void;
  pathname: string;
  editColumn: SubtableColumn | null;
  readPermissions: Permissions;
  setReadPermissions: React.Dispatch<React.SetStateAction<Permissions>>;
  writePermissions: Permissions;
  setWritePermissions: React.Dispatch<React.SetStateAction<Permissions>>;
};

const Presentation: React.FC<PresentationalProps> = ({
  className,
  close,
  form,
  onSubmit,
  addSelection,
  deleteSelectItem,
  isOpen,
  changeSelectItem,
  moveItem,
  statusSelect,
  pathname,
  editColumn,
  setWritePermissions,
  setReadPermissions,
  readPermissions,
  writePermissions,
}) => {
  const { resetPermissions, updateFormPermissions } = usePermissions(
    statusSelect,
    form,
  );

  const { handleWritePermissionChange, handleReadPermissionChange } =
    useHandlePermissionChange(setReadPermissions, setWritePermissions);

  useEffect(() => {
    updateFormPermissions(readPermissions, writePermissions);
  }, [writePermissions, readPermissions, updateFormPermissions]);

  useEffect(() => {
    if (!isOpen) {
      resetPermissions();
    }
  }, [isOpen, resetPermissions]);

  const ModalTitle = styled.div`
    width: 100%;
    text-align: center;
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding-bottom: 1rem;
    top: 1.3rem;
    position: absolute;
    margin: 0 -1rem;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  `;

  return (
    <Modal
      title={<ModalTitle>カスタムカラム編集</ModalTitle>}
      opened={isOpen}
      withCloseButton={false}
      onClose={close}
      className={className}
      size="640px"
    >
      <form onSubmit={onSubmit}>
        <div>
          <TextInput
            sx={{ marginBottom: "16px" }}
            label={
              <span>
                表示名
                <span style={{ color: "red" }}> ※</span>
              </span>
            }
            description="日本語・英数字・記号等が使用できます"
            {...form.getInputProps("column_label")}
          />
          <TextInput
            sx={{ marginBottom: "16px" }}
            label={
              <span>
                カスタムカラムID
                <span style={{ color: "red" }}> ※</span>
              </span>
            }
            description="英数字のみ"
            required
            defaultValue={""}
            {...form.getInputProps("column_name")}
          />
          <Select
            sx={{ marginBottom: "16px" }}
            label={
              <span>
                カスタムカラム型
                <span style={{ color: "red" }}> ※</span>
              </span>
            }
            defaultValue={editColumn ? editColumn.type.toString() : ""}
            data={CUSTOM_COLUMN_TYPE_INT_VALUES}
            rightSection={<ArrowDownIcon />}
            disabled
            {...form.getInputProps("type")}
          />

          {form.values.type_name === 3 && (
            <div>
              <Radio.Group
                sx={{ marginBottom: "16px" }}
                name="dropdownChoices"
                label={
                  <span>
                    選択肢
                    <span style={{ color: "red" }}> ※</span>
                  </span>
                }
                {...form.getInputProps("default")}
              >
                <ol>
                  {form.values.select_items &&
                    form.values.select_items.map((item, index) => (
                      <DraggableItem
                        key={item.select_id}
                        index={index}
                        item={item}
                        moveItem={moveItem}
                      >
                        <li className="selection-item">
                          <TextInput
                            value={item.select_value}
                            placeholder="選択肢を入力してください。"
                            onChange={(e) =>
                              changeSelectItem(
                                e.currentTarget.value,
                                index,
                                item.select_id,
                              )
                            }
                          />
                          <button
                            type="button"
                            className="delete"
                            onClick={() => deleteSelectItem(index)}
                          >
                            <RiDeleteBin6Line
                              style={{
                                cursor: "pointer",
                                width: rem(24),
                                height: rem(24),
                                fill: "#8992A0",
                              }}
                            />
                          </button>
                        </li>
                      </DraggableItem>
                    ))}
                </ol>
                <div className="add-select">
                  <Button
                    type="button"
                    size="xs"
                    variant="subtle"
                    onClick={addSelection}
                  >
                    +選択肢を追加
                  </Button>
                </div>
              </Radio.Group>
              <div className="selection-default">
                <Select
                  label={
                    <span>
                      初期値
                      <span style={{ color: "red" }}> ※</span>
                    </span>
                  }
                  placeholder="初期値を選択してください"
                  data={
                    form.values.select_items &&
                    form.values.select_items !== undefined
                      ? form.values.select_items.map((item, index) => {
                          return {
                            value: String(index),
                            label: item.select_value,
                          };
                        })
                      : [""]
                  }
                  rightSection={<ArrowDownIcon />}
                  required
                  {...form.getInputProps("select_default")}
                  styles={{ rightSection: { pointerEvents: "none" } }}
                />
              </div>
            </div>
          )}
          {pathname === "/settings/subtable/lead" && (
            <>
              <div className="permission-section">
                <Text className="form-label">
                  閲覧可能なユーザー <span style={{ color: "red" }}> ※ </span>
                </Text>
                <div className="permission-checkbox-group">
                  <Group className="checkbox-group">
                    {Object.keys(readPermissions).map((key) => (
                      <Checkbox
                        key={key}
                        name={key}
                        label={readPermissions[key as PermissionKey].name}
                        checked={readPermissions[key as PermissionKey].checked}
                        disabled={key === "VENDOR_ADMIN"}
                        onChange={handleReadPermissionChange}
                        sx={{
                          ".mantine-Checkbox-input": {
                            transform: "scale(0.6)",
                            borderRadius: "5px",
                          },
                          ".mantine-Checkbox-inner": {
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          },
                        }}
                      />
                    ))}
                  </Group>
                </div>
              </div>
              <div className="permission-section">
                <Text className="form-label">
                  入力可能なユーザー <span style={{ color: "red" }}> ※ </span>
                </Text>
                <div className="permission-checkbox-group">
                  <Group className="checkbox-group">
                    {Object.keys(writePermissions).map((key) => (
                      <Checkbox
                        key={key}
                        name={key}
                        label={writePermissions[key as PermissionKey].name}
                        checked={writePermissions[key as PermissionKey].checked}
                        disabled={key === "VENDOR_ADMIN"}
                        onChange={handleWritePermissionChange}
                        sx={{
                          ".mantine-Checkbox-input": {
                            transform: "scale(0.6)",
                            borderRadius: "5px",
                          },
                          ".mantine-Checkbox-inner": {
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          },
                        }}
                      />
                    ))}
                  </Group>
                </div>
              </div>
            </>
          )}
        </div>
        <div className="modal-buttons">
          <Button className="modal-buttons-cancel" onClick={close}>
            キャンセル
          </Button>
          <Button
            color={"dark"}
            size={"md"}
            className="modal-buttons-submit full-width"
            type="submit"
          >
            保存する
          </Button>
        </div>
      </form>
    </Modal>
  );
};

const Styled = styled(Presentation)`
  .mantine-Modal-header {
    height: 65px;
  }
  .mantine-Modal-content {
    border-radius: 8px;
  }
  .mantine-TextInput-description {
    color: #666666;
  }
  .mantine-Select-required,
  .mantine-TextInput-required {
    display: none;
  }
  .buttons {
    display: flex;
    gap: 0.5rem;
  }
  .new-button {
    svg {
      margin-right: 1rem;
    }
  }
  .mantine-Modal-body {
    padding: 0px;
  }
  form {
    height: 100%;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    padding-top: 24px;
    .mantine-InputWrapper-root {
      padding: 0 24px;
      &:last-child {
        padding-bottom: 8px;
        margin-bottom: 16px;
      }
    }
    .mantine-RadioGroup-label {
      font-weight: 600;
      font-size: 12px;
      color: #666666;
      margin-bottom: 8px;
    }
    .mantine-InputWrapper-description {
      margin-bottom: 8px;
    }
    label {
      font-weight: 600;
      font-size: 12px;
      color: #666666;
      margin-bottom: 8px;
    }

    input {
      padding: 13.5px 16px;
      height: auto;
      font-size: 14px;
      line-height: 21px;
      border-radius: 8px;
    }
  }
  .add-select {
    margin: 12px 0 12px 16px;
    font-size: 12px;
  }
  .permission-checkbox-group {
    border: 1px solid #e0e0e0;
    padding: 14px 24px;
    margin-bottom: 16px;
    position: relative;
    border-radius: 8px;

    .checkbox-group {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .mantine-Checkbox-label {
        font-size: 14px;
        margin-left: 18px;
        font-weight: 500;
        margin-bottom: 0px;
      }
    }
  }
  .form-label {
    font-weight: 600;
    font-size: 12px;
    color: #666666;
    margin-bottom: 8px;
  }
  .permission-section {
    padding: 0 24px;
    &:last-child {
      padding-bottom: 8px;
    }
  }

  .selection {
    &-option {
      display: flex;
      justify-content: space-between;
      padding: 1rem 0;
      &-heading {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }
    &-input {
      display: flex;
      gap: 1rem;
      align-items: flex-end;
      .mantine-TextInput-root {
        width: 100%;
      }
    }
    &-item {
      flex-grow: 1;
      padding: 4px 0px;
      list-style: none;
      margin: 8px 0px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      &:first-child {
        margin-top: 0;
      }

      .mantine-TextInput-root {
        width: 90%;
        padding: 0px;
      }

      .delete {
        padding: 0;
        border-width: 0;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        // background-color: ${propcolors.gray["200"]};
      }
    }
  }
  .modal-buttons {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    padding: 24px 40px;
    margin-top: 16px;
    background-color: #f7f8f9;
    border-top: 1px solid #e8eaed;
    &-cancel {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-weight: 600;
      font-size: 0.88rem;
      color: ${propcolors.white};
      background-color: ${propcolors.greyDefault};
      border-radius: 8px;
    }
    &-submit {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-size: 0.88rem;
      background-color: ${propcolors.black};
      border-radius: 8px;
    }
  }
`;

export const EditSubtableColumn: React.FC = () => {
  const modalState = useModalState();
  const setModal = useSetModalState();
  const [isModified, toggleModified] = useState<boolean>(false);
  const subtableColumns = useSubtableColumn();
  const setSubtableColumns = useSetSubtableColumn();
  const editColumn = useEditSubtableColumn();
  const setEditColumn = useSetEditSubtableColumn();
  const selectionInputRef = useRef<HTMLInputElement>(null);
  const selectionInputDefault = useRef<HTMLInputElement>(null);

  const form = useForm<NewPartnerColumnProps>({
    initialValues: {
      column_id: null,
      column_name: "",
      column_label: "",
      type_name: 0,
      default: null,
      select_items: [],
      select_default: "",
      date: new Date(),
      read_permission: "",
      write_permission: "",
    },
  });
  const dataMasters = useGetDataMasters();
  const [statusSelect, setStatusSelect] = useState<
    { id: number; name: string }[]
  >([]);
  const router = useRouter();

  const {
    setWritePermissions,
    setReadPermissions,
    writePermissions,
    readPermissions,
    initializeColumnPermissions,
  } = usePermissions(statusSelect, form);

  useEffect(() => {
    if (dataMasters && dataMasters.column_permissions) {
      const data = Object.keys(dataMasters.column_permissions).map((value) => ({
        id: dataMasters.column_permissions[value].id,
        name: value,
      }));
      setStatusSelect(data);
    }
  }, [dataMasters]);

  const close = () => {
    form.reset();
    setModal(null);
    setEditColumn(null);
  };

  form.onSubmit((value, e) => {
    e.preventDefault();
  });

  useEffect(() => {
    if (editColumn) {
      const readPermissionID = findPermissionIdByName(
        editColumn.read_permission,
        statusSelect,
      );
      const writePermissionID = findPermissionIdByName(
        editColumn.write_permission,
        statusSelect,
      );

      setReadPermissions(getPermissionsFromID(readPermissionID));
      setWritePermissions(getPermissionsFromID(writePermissionID));

      if (editColumn.select_contents) {
        form.setFieldValue(
          "select_items",
          editColumn.select_contents.map((select_content) => {
            return {
              select_id: select_content.select_id,
              select_value: select_content.select_value,
              sort_order: select_content.sort_order,
            };
          }),
        );
      }
      form.setFieldValue("column_name", editColumn.column_name);
      form.setFieldValue("column_label", editColumn.column_label);
      form.setFieldValue("type_name", editColumn.type);
      form.setFieldValue("select_default", String(editColumn.default));
    }
  }, [
    editColumn,
    form.setFieldValue,
    statusSelect,
    setReadPermissions,
    setWritePermissions,
  ]);

  const onSubmit = form.onSubmit((values) => {
    if (
      subtableColumns.find(
        (value) =>
          value.column_name === values.column_name &&
          value.column_id !== editColumn?.column_id,
      )
    ) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "カラムが作成できませんでした。",
        message:
          "このカスタムカラムIDは既に使用されています。もう一度お試しください。",
        autoClose: 5000,
      });
    } else if (
      subtableColumns.find(
        (value) =>
          value.column_label === values.column_label &&
          value.column_id !== editColumn?.column_id,
      )
    ) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "カラムが作成できませんでした。",
        message: "この表示名は既に使用されています。もう一度お試しください。",
        autoClose: 5000,
      });
    } else if (values.type_name === 3 && values.select_items.length < 1) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "カラムが作成できませんでした。",
        message: "選択肢を追加してください。",
        autoClose: 5000,
      });
    } else if (values.type_name === 3 && !values.select_default) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "カラムが作成できませんでした。",
        message: "選択肢の初期値を選択してください。",
        autoClose: 5000,
      });
    } else {
      if (values.type_name === 3) {
        if (
          values.select_items.find((item) => item.select_value.trim() === "")
        ) {
          notifications.show({
            icon: <IconNotiFailed />,
            title: "カラムが作成できませんでした。",
            message: "選択肢に空欄が存在します。",
            autoClose: 5000,
          });
          return;
        }
        const duplicate_values = values.select_items.map((current_item) => {
          if (
            values.select_items.find(
              (item) =>
                current_item.select_value.trim() === item.select_value.trim() &&
                current_item.select_id !== item.select_id,
            )
          )
            return true;
        });
        if (duplicate_values.find((item) => item)) {
          // duplicate_valuesがtrueを含んでいる場合
          notifications.show({
            icon: <IconNotiFailed />,
            title: "カラムが作成できませんでした。",
            message: "重複している選択肢が存在します。",
            autoClose: 5000,
          });
          return;
        }
      }

      setSubtableColumns((prev) =>
        prev.map((column) => {
          if (column.column_id === editColumn?.column_id) {
            return {
              column_id: editColumn.column_id,
              column_name: values.column_name,
              column_label: values.column_label,
              type_name: editColumn.type_name,
              type: editColumn.type,
              sort_order: editColumn.sort_order,
              default: Number(values.select_default),
              select_contents: values.select_items,
              isModified: true,
              write_permission: values.write_permission,
              read_permission: values.read_permission,
            } as unknown as SubtableColumn;
          }
          return column;
        }),
      );
      toggleModified(true);
      notifications.show({
        icon: <IconNotiSuccess />,
        title: "カスタムカラムが正常に追加されました！",
        message: "処理が正常に完了しました。",
        autoClose: 5000,
      });
      close();
    }
  });

  const deleteSelectItem = (index: number) => {
    form.removeListItem("select_items", index);
  };

  const changeSelectItem = (
    newValue: string,
    content_index: number,
    content_id: number | null,
  ) => {
    const previousValue =
      form.values.select_items === undefined ? [] : form.values.select_items;
    previousValue[content_index].select_value = newValue;
    form.setFieldValue("select_items", [...previousValue]);
  };

  const addSelection = () => {
    const previousValue =
      form.values.select_items === undefined ? [] : form.values.select_items;
    form.setFieldValue("select_items", [
      ...previousValue,
      {
        select_id: null,
        select_value: "",
        sort_order: previousValue.length,
      },
    ]);
  };

  const moveItem = (fromIndex: number, toIndex: number) => {
    const items = [...form.values.select_items];
    const [movedItem] = items.splice(fromIndex, 1);
    items.splice(toIndex, 0, movedItem);
    // 並び替えた後のsort_orderを更新
    items.forEach((item, index) => {
      item.sort_order = index;
    });
    form.setFieldValue("select_items", items);
    const currentSelectedValue: number =
      form.getInputProps("select_default").value;
    if (currentSelectedValue) {
      if (currentSelectedValue === fromIndex)
        form.setFieldValue("select_default", String(toIndex));
      else if (currentSelectedValue === toIndex)
        form.setFieldValue("select_default", String(fromIndex));
    }
  };

  return (
    <Styled
      isOpen={modalState === "subtable_column_edit"}
      close={close}
      form={form}
      onSubmit={onSubmit}
      addSelection={addSelection}
      selectionInputRef={selectionInputRef}
      selectionInputDefault={selectionInputDefault}
      deleteSelectItem={deleteSelectItem}
      changeSelectItem={changeSelectItem}
      moveItem={moveItem}
      statusSelect={statusSelect}
      pathname={router.pathname}
      editColumn={editColumn}
      readPermissions={readPermissions}
      setReadPermissions={setReadPermissions}
      writePermissions={writePermissions}
      setWritePermissions={setWritePermissions}
    />
  );
};

type DraggableItemProps = {
  item: SelectItems; // SelectItemsを適用
  index: number;
  moveItem: (fromIndex: number, toIndex: number) => void;
  [key: string]: any;
};
const ItemType = {
  ITEM: "ITEM",
};

// ドラッグアイテムの型を定義
type DraggedItem = {
  type: string;
  index: number;
  item: SelectItems;
};

// 選択肢のアイテムコンポーネントをドラッグ可能にする
const DraggableItem: React.FC<DraggableItemProps> = ({
  item,
  index,
  moveItem,
  ...props
}) => {
  const ref = React.useRef(null);

  const [, drop] = useDrop<DraggedItem>({
    accept: ItemType.ITEM,
    hover: (draggedItem) => {
      if (draggedItem.index !== index) {
        moveItem(draggedItem.index, index);
        draggedItem.index = index;
      }
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: ItemType.ITEM,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(drop(ref));

  return (
    <div
      ref={ref}
      style={{
        opacity: isDragging ? 0.5 : 1,
        display: "flex",
        paddingLeft: "12px",
        alignItems: "center",
        gap: "6px",
      }}
      {...props}
    >
      <RiMenuLine
        style={{
          cursor: "grab",
          width: rem(24),
          height: rem(24),
          fill: "#8992A0",
        }}
      />
      {props.children}
    </div>
  );
};
