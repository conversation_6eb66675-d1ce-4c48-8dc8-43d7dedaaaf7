import React, {FormEvent, useEffect, useState} from "react";
import styled from "@emotion/styled";
import { ax } from "utils/axios";
import {
  Button,
  Modal,
  Select,
  Text,
  TextInput,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { UseFormReturnType, useForm } from "@mantine/form";
import { propcolors } from "styles/colors";
import { notifications } from "@mantine/notifications";
import { useSetCompanyUser } from "utils/recoil/company/companyUserState";
import { modals } from "@mantine/modals";
import { useSessionUser } from "utils/recoil/sessionUserState";
import { getApiErrorMessage } from "utils/values/errorMessages";
import { FileUpload } from "components/FileUpload";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconSelect from "../../../../public/icons/icon-arrow-down.svg";

type NewUserProps = {
  name: string;
  name_kana: string;
  position_id: string;
  division: string;
  employment_type: string;
  email: string;
  tel: string;
  role: string;
  file: File | null;
};

type Division = {
  label: string;
  value: string;
}

type Position = {
  label: string;
  value: string;
}

type PresentationalProps = {
  className?: string;
  msgErrorFile: string | null;
  form: UseFormReturnType<NewUserProps>;
  opened: boolean;
  open: () => void;
  close: () => void;
  divisions: Division[];
  positions: Position[];
  onSubmit: (value?: FormEvent<HTMLFormElement> | undefined) => void;
};
const Presentation: React.FC<PresentationalProps> = ({
  className,
  opened,
  open,
  close,
  form,
  msgErrorFile,
  divisions,
  positions,
  onSubmit,
}) => {
  const sessionUser = useSessionUser();
  const userRole = sessionUser?.user_role;

  const onFileChange = (file: File | null) => {
    form.setFieldValue("file", file);
  };

  return (
    <div className={className}>
      {/* 管理者の場合のみユーザ追加を許可 */}
      {userRole === 1 && <Button onClick={open} className="btn-add-user">新規ユーザーを追加する</Button>}
      <Modal
        className={className}
        opened={opened}
        onClose={close}
        withCloseButton={false}
        size={648}
        maw={640}
        miw={320}
        title={<div className="modal-header">
          <p>新規ユーザーを追加</p>
        </div>}
      >
        <div className="modal-wrap">
          <div className="modal-content">
            <div className="modal-content-input">
              <form onSubmit={onSubmit} className="form-modal">
                <div className="modal-form">
                  <TextInput label="名前" {...form.getInputProps("name")} />
                  <TextInput
                    label="名前（カナ）"
                    {...form.getInputProps("name_kana")}
                  />
                  <TextInput
                    label="部署"
                    {...form.getInputProps("division")}
                  />
                  <TextInput
                    label="メールアドレス"
                    {...form.getInputProps("email")}
                  />
                  <TextInput label="電話番号" {...form.getInputProps("tel")} />
                  <Select
                    label="ロール"
                    data={[
                      {
                        value: "1",
                        label: "管理者",
                      },
                      {
                        value: "2",
                        label: "ユーザー",
                      },
                    ]}
                    rightSection={<IconSelect />}
                    {...form.getInputProps("role")}
                    styles={{ rightSection: { pointerEvents: 'none' } }}
                  />
                  <FileUpload label="プロフィール画像" fileName="" msgErrorFile={msgErrorFile} onFileChange={onFileChange}/>
                </div>
                <div className="modal-footer">
                  <div className="modal-buttons">
                    <Button className="modal-buttons-cancel full-width" onClick={close}>
                      キャンセル
                    </Button>
                    <Button className="modal-buttons-submit full-width" type="submit">
                      追加する
                    </Button>
                  </div>
                  <Text fz="xs" c="dimmed" align="center" style={{lineHeight: '16px', margin: '16px 0'}}>
                    ※作成完了後、記入のメールアドレス宛に招待メールが送信されます。
                  </Text>
                </div>
              </form>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

const Styled = styled(Presentation)`
  .mantine-Modal-body {
    padding: 0px;
  }
  .btn-add-user {
    width: 186px;
    font-weight: 400;
    font-size: 14px;
    line-height: 14px;
    padding: 0px;
    border-radius: 8px;
  }
  .mantine-Modal-title {
    width: 100%;
  }
  .mantine-Modal-header {
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding: 16px 40px;
  }
  .mantine-Modal-close {
    right: 18px;
    position: absolute;
    svg {
      width: 24px !important;
      height: 24px !important;
    }
  }
  .modal {
    &-header {
      color: ${propcolors.blackLight};
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      width: 100%;
    }
    &-wrap {
      background: ${propcolors.white};
      border-radius: 8px;
    }
    &-content {
      flex-direction: column;
      display: flex;
      gap: 16px;
      .mantine-InputWrapper-label {
        font-weight: 400 !important;
        font-size: 12px !important;
      }
      &-input input {
        margin-top: 8px;
        height: 48px;
        border-radius: 0.5rem;
      }
      label {
        margin-top: 16px;
        color: ${propcolors.blackLightLabel};
        font-weight: 400;
        font-size: 12px;
      }
      &-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 24px;
      }
      .modal-form {
        padding: 8px 24px 24px 24px;
      }
      .modal-footer {
        background-color: ${propcolors.greyBreadcrumb};
        padding: 24px 40px 8px 24px;
        font-weight: 400 !important;
        .modal-buttons {
          display: flex;
          justify-content: space-between;
          gap: 24px;
          margin: 0px;
          padding: 0px 0px 0px 16px;

          &-cancel {
            padding: 0.8rem 1rem;
            width: 100%;
            height: 42px;
            font-size: 14px;
            color: ${propcolors.white};
            background-color: ${propcolors.greyDefault};
            border: 0.0625rem solid transparent;
            border-radius: 8px;
            width: 268px;
            font-weight: 400 !important;
          }

          &-submit {
            padding: 0.8rem 1rem;
            width: 100%;
            height: 42px;
            font-size: 14px;
            background-color: ${propcolors.black};
            border-radius: 8px;
            width: 268px;
            font-weight: 400 !important;
          }
        }
      }
    }
  }
`;
export const NewUser: React.FC<{
  divisions: Division[];
  positions: Position[];
}> = ({divisions, positions}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [msgErrorFile, setMsgErrorFile] = useState<string | null>(null);
  const setCompanyUser = useSetCompanyUser();
  const form = useForm<NewUserProps>({
    initialValues: {
      name: "",
      name_kana: "",
      division: "",
      position_id: "",
      employment_type: "",
      email: "",
      tel: "",
      role: "",
      file: null
    },
    validate: {
      name: (value) => {
        if (value === "") return "名前を入力してください。";
        // 名前に%または％を含む場合はエラー
        if (value.match(/[%％]/))
          return "名前に「%」を含めることはできません。";
      },
      name_kana: (value) => {
        if (value === "") return "名前（カナ）を入力してください。";
        if (!/^[ァ-ヶー 　]+$/.test(value)) return "名前（カナ）は全角カタカナで入力してください。";
      },
      division: (value) => {
        if (value === "") return "部署を入力してください。";
      },
      role: (value) => {
        if (value === "") return "ロールを入力してください。";
      },
      file: (value) => {
        const maxFileSize = 15 * 1024 * 1024; // 15MB
        if (value && value.size > maxFileSize) {
          setMsgErrorFile("15MB以下のファイルを選択してください。");
          return "15MB以下のファイルを選択してください。";
        }

        const allowedFormats = ["image/png", "image/jpeg"];
        if (value && !allowedFormats.includes(value.type)) {
          setMsgErrorFile("画像ファイルはpngまたはjpegで選択してください。")
          return "画像ファイルはpngまたはjpegで選択してください。";
        }

        setMsgErrorFile(null)
      },
      email: (value) => {
        if (value === "") return "メールアドレスを入力してください。";
        // メールアドレスかどうかの判定
        if (!value.match(/.+@.+\..+/))
          return "メールアドレスの形式が正しくありません。";
      },
      tel: (value) => {
        if (value === "") return "電話番号を入力してください。";
        //　電話番号にはハイフンを含まない
        if (value.match(/[-ー]/))
          return "電話番号にハイフンを含めることはできません。";
        // 電話番号かどうかの判定
        if (!value.match(/^[0-9]{10,11}$/))
          return "電話番号の形式が正しくありません。";
      },
    },
  });

  useEffect(() => {
    setMsgErrorFile(null)
  }, [opened]);

const ModalTitle = styled.div`
  width: 100%;
  text-align: center;
  border-bottom: 1px solid ${propcolors.gray[200]};
  padding-bottom: 1rem;
  top: 1.3rem;
  position: absolute;
  margin: 0 -1rem;
  font-size: 16px;
  font-weight: 600;
  color: ${propcolors.blackLight};
`;

const ModalContent = styled.div`
  padding: 10px 10px 5px 10px;
  .title-confirm {
    font-size: 18px;
    font-weight: 600;
    color: ${propcolors.blackLight};
    margin: 10px 0 0px 0;
  }
  .description {
    font-size: 12px;
    font-weight: 300;
    color: var(--Semantic-TEXT_BLACK, #23221E);
  }
`;

  const fetchUserList = async () => {
    ax.get("/api/v1/vendor_users").then((res) => {
      setCompanyUser(res.data);
    });
  };

  const onSubmit = form.onSubmit((values) => {

    if (!form.isValid()) return;
    modals.openConfirmModal({
      title: <ModalTitle>ユーザー追加</ModalTitle>,
      size: "640px",
      closeButtonProps: { size: '24px' },
      children: (
        <ModalContent>
          <Text className="title-confirm">{`ユーザー「${values.name}」を追加しますか？`}</Text>
        </ModalContent>
      ),
      labels: {
        confirm: "追加",
        cancel: "キャンセル",
      },
      onConfirm: () => {
        const formData = new FormData();
        Object.keys(values).forEach((key: string) => {
          if (key !== "file" || values[key] !== null) {
            // @ts-ignore
            formData.append(key, values[key]);
          }
        });

        ax.post("/api/v1/vendor_users", formData)
          .then((res) => {
            notifications.show({
              icon: <IconNotiSuccess />,
              title: "ユーザーが作成されました！",
              message: "処理が正常に完了しました。招待メールが送信されました。",
              autoClose: 5000,
            });
            fetchUserList();
            close();
            form.reset();
          })
          .catch((err) => {
            notifications.show({
              icon: <IconNotiFailed />,
              title: "ユーザーが作成できませんでした。",
              message: getApiErrorMessage(err.response.data.message),
              autoClose: 5000,
            });
          });
      },
      confirmProps: {
        sx: {
          width: '284px',
          height: '42px',
          right: '10px',
          fontSize: '14px',
          fontWeight: 400,
          marginBottom: '10px',
          borderRadius: '8px',
          backgroundColor: `${propcolors.black}`,
          color: `${propcolors.white}`,
          '&:hover': {
            backgroundColor: `${propcolors.black}`,
          },
        },
      },
      cancelProps: {
        variant: 'outline',
        sx: {
          width: '284px',
          height: '42px',
          left: '25px',
          position: 'absolute',
          fontSize: '14px',
          fontWeight: 400,
          marginBottom: '10px',
          borderRadius: '8px',
          borderColor: `${propcolors.greyDefault}`,
          backgroundColor: `${propcolors.greyDefault}`,
          color: `${propcolors.white}`,
          '&:hover': {
            backgroundColor: `${propcolors.greyDefault}`,
          },
        },
      },
    });
  });

  return (
    <Styled
      opened={opened}
      open={open}
      close={close}
      msgErrorFile={msgErrorFile}
      divisions={divisions}
      positions={positions}
      form={form}
      onSubmit={onSubmit}
    />
  );
};
