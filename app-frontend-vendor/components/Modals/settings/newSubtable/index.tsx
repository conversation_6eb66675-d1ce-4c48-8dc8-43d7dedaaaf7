import React, { FormEvent, useEffect, useRef, useState } from "react";
import styled from "@emotion/styled";
import { ax } from "utils/axios";
import { Checkbox, Group, TextInput, Text, Button, Modal } from "@mantine/core";
import { UseFormReturnType, useForm } from "@mantine/form";
import { useModalState, useSetModalState } from "utils/recoil/modalState";
import { notifications } from "@mantine/notifications";
import { useRouter } from "next/router";
import { getApiErrorMessage } from "utils/values/errorMessages";
import { useGetDataMasters } from "utils/recoil/dataMasters";
import {
  usePermissions,
  PermissionKey,
  useHandlePermissionChange,
} from "hooks/usePermissions";
import { propcolors } from "styles/colors";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";

type NewSubtableProps = {
  sub_table_name: string;
  read_permission: string;
};

type PresentationalProps = {
  isOpen: boolean;
  className?: string;
  form: UseFormReturnType<NewSubtableProps>;
  selectionInputRef: React.RefObject<HTMLInputElement>;
  close: () => void;
  onSubmit: (value?: FormEvent<HTMLFormElement>) => void;
  pathname: string;
};

const Presentation: React.FC<PresentationalProps> = ({
  className,
  close,
  form,
  onSubmit,
  isOpen,
  pathname,
}) => {
  const dataMasters = useGetDataMasters();
  const [statusSelect, setStatusSelect] = useState<
    { id: number; name: string }[]
  >([]);

  const {
    readPermissions,
    setReadPermissions,
    resetPermissions,
    updateFormPermissions,
  } = usePermissions(statusSelect, form);

  const { handleReadPermissionChange } =
    useHandlePermissionChange(setReadPermissions);

  useEffect(() => {
    updateFormPermissions(readPermissions);
  }, [readPermissions, updateFormPermissions]);

  useEffect(() => {
    if (!isOpen) {
      resetPermissions();
    }
  }, [isOpen, resetPermissions]);

  // マスターデータを取得し、状態を設定する
  useEffect(() => {
    if (dataMasters && dataMasters.column_permissions) {
      const data = Object.keys(dataMasters.column_permissions).map((value) => {
        return {
          id: dataMasters.column_permissions[value].id,
          name: value,
        };
      });
      setStatusSelect(data);
    }
  }, [dataMasters]);

  const ModalTitle = styled.div`
    width: 100%;
    text-align: center;
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding-bottom: 1rem;
    top: 1.3rem;
    position: absolute;
    margin: 0 -1rem;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  `;
  return (
    <Modal
      title={<ModalTitle>サブテーブルを追加</ModalTitle>}
      opened={isOpen}
      onClose={close}
      className={className}
      size="640px"
      withCloseButton={false}
    >
      <form onSubmit={onSubmit}>
        <div>
          <TextInput
            sx={{ marginBottom: "16px" }}
            label={
              <span>
                サブテーブル名
                <span style={{ color: "red" }}> ※</span>
              </span>
            }
            required
            {...form.getInputProps("sub_table_name")}
          />
        </div>
        {pathname === "/settings/subtable/lead" && (
          <>
            <div className="permission-section">
              <Text className="form-label">
                閲覧可能なユーザー <span style={{ color: "red" }}> ※ </span>
              </Text>
              <div className="permission-checkbox-group">
                <Group className="checkbox-group">
                  {Object.keys(readPermissions).map((key) => (
                    <Checkbox
                      key={key}
                      name={key}
                      label={readPermissions[key as PermissionKey].name}
                      checked={readPermissions[key as PermissionKey].checked}
                      disabled={key === "VENDOR_ADMIN"}
                      onChange={handleReadPermissionChange}
                      sx={{
                        ".mantine-Checkbox-input": {
                          transform: "scale(0.6)",
                          borderRadius: "5px",
                        },
                        ".mantine-Checkbox-inner": {
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        },
                      }}
                    />
                  ))}
                </Group>
              </div>
            </div>
          </>
        )}
        <div className="modal-buttons">
          <Button className="modal-buttons-cancel" onClick={close}>
            キャンセル
          </Button>
          <Button
            color={"dark"}
            size={"md"}
            className="modal-buttons-submit full-width"
            type="submit"
          >
            追加する
          </Button>
        </div>
      </form>
    </Modal>
  );
};

const Styled = styled(Presentation)`
  .mantine-Modal-header {
    height: 56px;
  }
  .mantine-Modal-content {
    border-radius: 8px;
  }
  .mantine-TextInput-description {
    color: #666666;
  }
  .mantine-Select-required,
  .mantine-TextInput-required {
    display: none;
  }
  .buttons {
    display: flex;
    gap: 0.5rem;
  }
  .new-button {
    svg {
      margin-right: 1rem;
    }
  }
  .mantine-Modal-body {
    padding: 0px;
  }
  .permission-checkbox-group {
    border: 1px solid #e0e0e0;
    padding: 14px 24px;
    margin-bottom: 16px;
    position: relative;
    border-radius: 8px;

    .checkbox-group {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .mantine-Checkbox-label {
        font-size: 14px;
        margin-left: 18px;
        font-weight: 500;
        margin-bottom: 0px;
      }
    }
  }
  .form-label {
    font-weight: 600;
    font-size: 12px;
    color: #666666;
    margin-bottom: 8px;
  }
  .permission-section {
    padding: 0 24px;
    &:last-child {
      padding-bottom: 8px;
    }
  }
  form {
    height: 100%;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    padding-top: 24px;
    .mantine-InputWrapper-root {
      padding: 0 24px;
      &:last-child {
        padding-bottom: 8px;
      }
    }
    label {
      font-weight: 600;
      font-size: 12px;
      color: #666666;
      margin-bottom: 8px;
    }

    input {
      padding: 13.5px 16px;
      height: auto;
      font-size: 14px;
      line-height: 21px;
      border-radius: 8px;
    }
  }
  .selection {
    &-option {
      display: flex;
      justify-content: space-between;
      padding: 1rem 0;
      &-heading {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }
    &-input {
      display: flex;
      gap: 1rem;
      align-items: flex-end;
      .mantine-TextInput-root {
        width: 100%;
      }
    }
  }
  .modal-buttons {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    padding: 0 24px 24px;
    margin-top: 0;
    &-cancel {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-weight: 600;
      font-size: 0.88rem;
      color: ${propcolors.white};
      background-color: ${propcolors.greyDefault};
      border-radius: 8px;
    }
    &-submit {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-size: 0.88rem;
      background-color: ${propcolors.black};
      border-radius: 8px;
    }
  }
`;

export const NewSubtableModal: React.FC = () => {
  const modalState = useModalState();
  const setModal = useSetModalState();
  const selectionInputRef = useRef<HTMLInputElement>(null);
  const form = useForm<NewSubtableProps>();
  const { push, reload, pathname } = useRouter();

  const close = () => {
    form.reset();
    setModal(null);
  };

  form.onSubmit((value, e) => {
    e.preventDefault();
  });

  const onSubmit = (e?: FormEvent<HTMLFormElement>) => {
    e?.preventDefault();
    let url = "";
    if (
      pathname === "/settings/subtable/partner" ||
      pathname === "/settings/subtable/partner/[id]"
    ) {
      url = "managed_partner";
    } else if (
      pathname === "/settings/subtable/lead" ||
      pathname === "/settings/subtable/lead/[id]"
    ) {
      url = "lead";
    }

    ax.post(`/api/v1/${url}_sub_table`, form.values)
      .then((response) => {
        notifications.show({
          message: "サブテーブルを作成しました",
          icon: <IconNotiSuccess />,
        });
        push(`/settings/subtable/${url === "lead" ? "lead" : "partner"}`);
        reload();
      })
      .catch((error) => {
        notifications.show({
          title: "サブテーブルを作成できませんでした",
          message: getApiErrorMessage(error.response.data.message),
          icon: <IconNotiFailed />,
        });
      });
  };

  return (
    <Styled
      isOpen={modalState === "subtable_make"}
      close={close}
      form={form}
      selectionInputRef={selectionInputRef}
      onSubmit={onSubmit}
      pathname={pathname}
    />
  );
};
