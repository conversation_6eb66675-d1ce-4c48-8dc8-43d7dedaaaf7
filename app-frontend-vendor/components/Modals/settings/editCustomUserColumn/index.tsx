import styled from "@emotion/styled";
import { But<PERSON>, Modal, Radio, Select, TextInput, rem } from "@mantine/core";
import { UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { RiDeleteBin6Line, RiMenuLine } from "@remixicon/react";
import { CUSTOM_COLUMN_TYPE_STR_VALUES } from "constants/ja/common";
import { useRouter } from "next/router";
import ArrowDownIcon from "public/icons/arrow-down.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import React, { FormEvent, useEffect, useRef, useState } from "react";
import { useDrag, useDrop } from "react-dnd";
import { propcolors } from "styles/colors";
import {
  useCustomUserColumn,
  useSetCustomUserColumn,
} from "utils/recoil/customUserColumn/customUserColumnState";
import {
  useEditCustomUserColumn,
  useSetEditCustomUserColumn,
} from "utils/recoil/customUserColumn/editCustomUserColumnState";
import { useGetDataMasters } from "utils/recoil/dataMasters";
import { useModalState, useSetModalState } from "utils/recoil/modalState";

type SelectItems = {
  select_id: number | null;
  select_value: string;
  // ドロップダウンの選択肢の表示順
  sort_order: number;
};

type NewPartnerUserColumnProps = {
  column_id: number | null;
  column_label: string;
  type_name: "STRING" | "INTEGER" | "SELECT" | "";
  default: number | null;
  select_items: SelectItems[];
  select_default: string;
  write_permission: string;
};

type PresentationalProps = {
  isOpen: boolean;
  className?: string;
  form: UseFormReturnType<NewPartnerUserColumnProps>;
  selectionInputRef: React.RefObject<HTMLInputElement>;
  addSelection: () => void;
  close: () => void;
  deleteSelectItem: (index: number) => void;
  onSubmit: (value?: FormEvent<HTMLFormElement> | undefined) => void;
  changeSelectItem: (
    newValue: string,
    content_index: number,
    content_id: number | null
  ) => void;
  editColumn: CustomUserColumn | null;
  moveItem: (fromIndex: number, toIndex: number) => void;
  statusSelect: { value: string; label: string }[];
  pathname: string;
};

const Presentation: React.FC<PresentationalProps> = ({
  className,
  close,
  form,
  onSubmit,
  addSelection,
  deleteSelectItem,
  isOpen,
  editColumn,
  changeSelectItem,
  moveItem,
  statusSelect,
  pathname,
}) => {
  const ModalTitle = styled.div`
    width: 100%;
    text-align: center;
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding-bottom: 1rem;
    top: 1.3rem;
    position: absolute;
    margin: 0 -1rem;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  `;
  return (
    <Modal
      title={<ModalTitle>カスタムカラム編集</ModalTitle>}
      opened={isOpen}
      withCloseButton={false}
      onClose={close}
      className={className}
      size="640px"
    >
      <form onSubmit={onSubmit}>
        <div>
          <TextInput
            sx={{ marginBottom: "16px" }}
            label={
              <span>
                表示名
                <span style={{ color: "red" }}> ※</span>
              </span>
            }
            description="日本語・英数字・記号等が使用できます"
            required
            {...form.getInputProps("column_label")}
          />
          <Select
            sx={{ marginBottom: "16px" }}
            label={
              <span>
                カスタムカラム型
                <span style={{ color: "red" }}> ※</span>
              </span>
            }
            disabled
            defaultValue={editColumn ? editColumn.type_status : ""}
            data={CUSTOM_COLUMN_TYPE_STR_VALUES}
            rightSection={<ArrowDownIcon />}
            required
            {...form.getInputProps("type_name")}
          />

          {editColumn && editColumn.type_status === "SELECT" && (
            <div>
              <Radio.Group
                sx={{ marginBottom: "16px" }}
                name="dropdownChoices"
                label="選択肢"
                {...form.getInputProps("default")}
              >
                <ol>
                  {form.values.select_items &&
                    form.values.select_items.map((item, index) => (
                      <DraggableItem
                        key={index}
                        index={index}
                        item={item}
                        moveItem={moveItem}
                      >
                        <li className="selection-item">
                          <TextInput
                            value={item.select_value}
                            placeholder="選択肢を入力してください。"
                            onChange={(e) =>
                              changeSelectItem(
                                e.currentTarget.value,
                                index,
                                item.select_id
                              )
                            }
                          />
                          <div
                            className="delete"
                            onClick={() => deleteSelectItem(index)}
                          >
                            <RiDeleteBin6Line
                              style={{
                                cursor: "pointer",
                                width: rem(24),
                                height: rem(24),
                                fill: "#8992A0",
                              }}
                            />
                          </div>
                        </li>
                      </DraggableItem>
                    ))}
                </ol>
                <div className="add-select">
                  <Button
                    type="button"
                    size="xs"
                    variant="subtle"
                    onClick={addSelection}
                  >
                    +選択肢を追加
                  </Button>
                </div>
              </Radio.Group>
              <div className="selection-default">
                <Select
                  label={
                    <span>
                      初期値
                      <span style={{ color: "red" }}> ※</span>
                    </span>
                  }
                  placeholder="初期値を選択してください"
                  defaultValue={editColumn && String(editColumn.default)}
                  data={
                    form.values.select_items &&
                    form.values.select_items !== undefined
                      ? form.values.select_items.map((item, index) => {
                          return {
                            value: String(index),
                            label: item.select_value,
                          };
                        })
                      : [""]
                  }
                  rightSection={<ArrowDownIcon />}
                  required
                  {...form.getInputProps("select_default")}
                />
              </div>
            </div>
          )}
          {pathname === "/settings/columns/leads" && (
            <Select
              sx={{ marginBottom: "16px" }}
              label={
                <span>
                  入力可能なユーザー
                  <span style={{ color: "red" }}> ※</span>
                </span>
              }
              required
              {...form.getInputProps("write_permission")}
              rightSection={<ArrowDownIcon />}
              data={statusSelect}
            />
          )}
        </div>
        <div className="modal-buttons">
          <Button className="modal-buttons-cancel" onClick={close}>
            キャンセル
          </Button>
          <Button
            color={"dark"}
            size={"md"}
            className="modal-buttons-submit full-width"
            type="submit"
          >
            保存する
          </Button>
        </div>
      </form>
    </Modal>
  );
};

const Styled = styled(Presentation)`
  .mantine-Modal-header {
    height: 56px;
  }
  .mantine-Modal-content {
    border-radius: 8px;
  }
  .mantine-TextInput-description {
    color: #666666;
  }
  .mantine-Select-required,
  .mantine-TextInput-required {
    display: none;
  }
  .buttons {
    display: flex;
    gap: 0.5rem;
  }
  .new-button {
    svg {
      margin-right: 1rem;
    }
  }
  .mantine-Modal-body {
    padding: 0px;
  }
  form {
    height: 100%;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    padding-top: 24px;
    .mantine-InputWrapper-root {
      padding: 0 24px;
      &:last-child {
        padding-bottom: 8px;
      }
    }
    .mantine-RadioGroup-label {
      font-weight: 600;
      font-size: 12px;
      color: #666666;
      margin-bottom: 8px;
    }
    .mantine-InputWrapper-description {
      margin-bottom: 8px;
    }
    label {
      font-weight: 600;
      font-size: 12px;
      color: #666666;
      margin-bottom: 8px;
    }

    input {
      padding: 13.5px 16px;
      height: auto;
      font-size: 14px;
      line-height: 21px;
      border-radius: 8px;
    }
  }
  .add-select {
    margin: 12px 0;
    font-size: 12px;
  }
  .selection {
    &-option {
      display: flex;
      justify-content: space-between;
      padding: 1rem 0;
      &-heading {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }
    &-input {
      display: flex;
      gap: 1rem;
      align-items: flex-end;
      .mantine-TextInput-root {
        width: 100%;
      }
    }
    &-item {
      flex-grow: 1;
      padding: 4px 0px;
      list-style: none;
      margin: 8px 0px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      &:first-child {
        margin-top: 0;
      }

      .mantine-TextInput-root {
        width: 90%;
        padding: 0px;
      }

      .delete {
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 24px;
        height: 24px;
        border-radius: 4px;
      }
    }
  }
  .modal-buttons {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    padding: 24px 40px;
    margin-top: 0;
    background-color: #f7f8f9;
    border-top: 1px solid #e8eaed;
    &-cancel {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-weight: 600;
      font-size: 0.88rem;
      color: ${propcolors.white};
      background-color: ${propcolors.greyDefault};
      border-radius: 8px;
    }
    &-submit {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-size: 0.88rem;
      background-color: ${propcolors.black};
      border-radius: 8px;
    }
  }
`;

export const EditCustomUserColumn: React.FC = () => {
  const modalState = useModalState();
  const setModal = useSetModalState();
  const [isModified, toggleModified] = useState<boolean>(false);
  const customUserColumns = useCustomUserColumn();
  const setCustomUserColumns = useSetCustomUserColumn();
  const editColumn = useEditCustomUserColumn();
  const setEditColumn = useSetEditCustomUserColumn();
  const selectionInputRef = useRef<HTMLInputElement>(null);
  const form = useForm<NewPartnerUserColumnProps>();
  const dataMasters = useGetDataMasters();
  const [statusSelect, setStatusSelect] = useState<
    { value: string; label: string }[]
  >([]);
  const router = useRouter();

  useEffect(() => {
    if (dataMasters && dataMasters.column_permissions) {
      const data = Object.keys(dataMasters.column_permissions).map((value) => {
        return {
          value: value,
          label: dataMasters.column_permissions[value].name,
        };
      });
      setStatusSelect(data);
    }
  }, [dataMasters]);

  const close = () => {
    form.reset();
    setEditColumn(null);
    setModal(null);
  };

  form.onSubmit((value, e) => {
    e.preventDefault();
  });

  useEffect(() => {
    if (editColumn) {
      if (editColumn.select_contents) {
        form.setFieldValue(
          "select_items",
          editColumn.select_contents.map((select_content) => {
            return {
              select_id: select_content.select_id,
              select_value: select_content.select_value,
              sort_order: select_content.sort_order,
            };
          })
        );
      }
      form.setFieldValue("column_label", editColumn.column_label);
      form.setFieldValue("select_default", String(editColumn.default));
      form.setFieldValue("write_permission", editColumn.write_permission);
    }
  }, [editColumn]);

  const isSameColumn = (column: CustomUserColumn) => {
    // ユニーク前提のカスタムカラムID(表示上の名称。ユーザーに入力してもらうもの)と表示名で、変更しているカラムと同じカラムかどうかを判定する
    return column.column_label === editColumn?.column_label;
  };
  const onSubmit = form.onSubmit((values) => {
    if (
      !customUserColumns.find(
        (value) => value.column_id === editColumn?.column_id
      )
    ) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "カラムが作成できませんでした。",
        message:
          "このカスタムカラムIDは既に使用されています。IDを変えて、もう一度お試しください。カスタムカラムは大文字、小文字で区別されません。",
        autoClose: 5000,
      });
    } else if (
      values.type_name === "SELECT" &&
      values.select_items.length < 1
    ) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "カラムが作成できませんでした。",
        message: "選択肢を追加してください。",
        autoClose: 5000,
      });
    } else if (values.type_name === "SELECT" && !values.select_default) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "カラムが作成できませんでした。",
        message: "選択肢の初期値を選択してください。",
        autoClose: 5000,
      });
    } else if (values.type_name === "SELECT") {
      if (values.select_items.find((item) => item.select_value === "")) {
        notifications.show({
          icon: <IconNotiFailed />,
          title: "カラムが作成できませんでした。",
          message: "選択肢に空欄が存在します。",
          autoClose: 5000,
        });
        return;
      }
      notifications.show({
        icon: <IconNotiFailed />,
        title: "カラムが作成できませんでした。",
        message: "選択肢に空欄が存在します。",
        autoClose: 5000,
      });
      return;
    } else {
      setCustomUserColumns((prev) =>
        prev.map((column) => {
          if (editColumn !== null && isSameColumn(column)) {
            return {
              column_id: editColumn.column_id,
              column_label: values.column_label,
              type_name: editColumn.type_name,
              type_status: editColumn.type_status,
              default: Number(values.select_default),
              select_contents: values.select_items,
              isModified: true,
              sort_order: editColumn.sort_order,
              write_permission: values.write_permission,
            } as unknown as CustomUserColumn;
          }
          return column;
        })
      );
      toggleModified(true);
      notifications.show({
        icon: <IconNotiSuccess />,
        title: "カスタムカラムが正常に追加されました！",
        message: "処理が正常に完了しました。",
        autoClose: 5000,
      });
      close();
    }
  });

  const deleteSelectItem = (index: number) => {
    form.removeListItem("select_items", index);
  };

  const changeSelectItem = (newValue: string, content_index: number) => {
    const previousValue =
      form.values.select_items === undefined ? [] : form.values.select_items;
    previousValue[content_index].select_value = newValue;
    form.setFieldValue("select_items", [...previousValue]);
  };

  const addSelection = () => {
    const previousValue =
      form.values.select_items === undefined ? [] : form.values.select_items;
    form.setFieldValue("select_items", [
      ...previousValue,
      {
        select_id: null,
        select_value: "",
        sort_order: previousValue.length,
      },
    ]);
  };

  const moveItem = (fromIndex: number, toIndex: number) => {
    const items = [...form.values.select_items];
    const [movedItem] = items.splice(fromIndex, 1);
    items.splice(toIndex, 0, movedItem);
    // 並び替えた後のsort_orderを更新
    items.forEach((item, index) => {
      item.sort_order = index;
    });
    form.setFieldValue("select_items", items);
    const currentSelectedValue: number =
      form.getInputProps("select_default").value;
    if (currentSelectedValue) {
      if (currentSelectedValue == fromIndex)
        form.setFieldValue("select_default", String(toIndex));
      else if (currentSelectedValue == toIndex)
        form.setFieldValue("select_default", String(fromIndex));
    }
  };

  return (
    <Styled
      isOpen={modalState === "editCustomUserColumn"}
      close={close}
      form={form}
      onSubmit={onSubmit}
      addSelection={addSelection}
      selectionInputRef={selectionInputRef}
      deleteSelectItem={deleteSelectItem}
      editColumn={editColumn}
      changeSelectItem={changeSelectItem}
      moveItem={moveItem}
      statusSelect={statusSelect}
      pathname={router.pathname}
    />
  );
};

// ドロップダウンの並び替え用の型を定義
type DraggableItemProps = {
  item: SelectItems; // SelectItemsを適用
  index: number;
  moveItem: (fromIndex: number, toIndex: number) => void;
  [key: string]: any;
};
const ItemType = {
  ITEM: "ITEM",
};

// ドラッグアイテムの型を定義
type DraggedItem = {
  type: string;
  index: number;
  item: SelectItems;
};

// 選択肢のアイテムコンポーネントをドラッグ可能にする
const DraggableItem: React.FC<DraggableItemProps> = ({
  item,
  index,
  moveItem,
  ...props
}) => {
  const ref = React.useRef(null);

  const [, drop] = useDrop<DraggedItem>({
    accept: ItemType.ITEM,
    hover: (draggedItem) => {
      if (draggedItem.index !== index) {
        moveItem(draggedItem.index, index);
        draggedItem.index = index;
      }
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: ItemType.ITEM,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(drop(ref));

  return (
    <div
      ref={ref}
      style={{
        opacity: isDragging ? 0.5 : 1,
        display: "flex",
        alignItems: "center",
        gap: "6px",
      }}
      {...props}
    >
      <RiMenuLine
        style={{
          cursor: "grab",
          width: rem(24),
          height: rem(24),
          fill: "#8992A0",
        }}
      />
      {props.children}
    </div>
  );
};
