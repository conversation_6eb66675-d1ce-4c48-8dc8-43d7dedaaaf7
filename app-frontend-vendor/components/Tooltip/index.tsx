import React, { useState } from 'react';
import styled from "@emotion/styled";
import {propcolors} from "../../styles/colors";

interface TooltipProps {
  text: string;
  children: any;
}

export const Styled = styled.div`
    display: contents;
    .tooltip {
      position: relative;
      display: inline-block;
    }

    .tooltip .tooltip-text {
      visibility: hidden;
      color: ${propcolors.white};
      text-align: center;
      border-radius: 4px;
      padding: 4px 8px;
      line-height: 18px;
      position: absolute;
      z-index: 1;
      bottom: 170%;
      right: -130%;
      margin-left: -60px;
      font-size: 12px;
      font-weight: 300;
      background-color: ${propcolors.blackLight};
    }

    .tooltip .tooltip-text::after {
      content: "";
      position: absolute;
      top: 115%;
      left: 50%;
      margin-left: -5px;
      border-width: 5px;
      border-style: solid;
      border-color: black transparent transparent transparent;
    }

    .tooltip:hover .tooltip-text {
      visibility: visible;
    }

    @media screen and (max-width: 600px) {
      .tooltip-text {
        display: none !important;
      }
    }
`;

const Tooltip: React.FC<TooltipProps> = ({ text, children }) => {
  const [showTooltip, setShowTooltip] = useState(false);

  const handleMouseEnter = () => {
    setShowTooltip(true);
  };

  const handleMouseLeave = () => {
    setShowTooltip(false);
  };

  return (
    <Styled>
      <div className="tooltip" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
        {children}
        {showTooltip && <div className="tooltip-text">{text}</div>}
      </div>
    </Styled>
  );
};

export default Tooltip;
