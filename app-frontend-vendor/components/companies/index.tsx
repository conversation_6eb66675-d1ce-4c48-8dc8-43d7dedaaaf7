import styled from "@emotion/styled";
import Link from "next/link";
import { useRouter } from "next/router";
import { propcolors } from "../../styles/colors";
import { links } from "api";
import SettingsSvg from "public/icons/sidebar/settings.svg";
import LogoutSvg from "public/icons/sidebar/signout.svg";
import toast from "react-hot-toast";
import { PropNormal } from "components/svgs/logo/prop";
import { destroyCookie } from "nookies";
import { resetAllRecoil } from "utils/recoil/resetRecoil/resetRecoil"
import { useSetRecoilState } from 'recoil';

type NavbarProps = {
  type: "vendor" | "partner" | "both";
};

type PresentationProps = {
  className?: string;
  current: string;
  copyID: () => void;
  handleLogout: () => void;
} & NavbarProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  type,
  current,
  copyID,
  handleLogout,
}) => {
  return (
    <div className={className}>
      <PropNormal />
      <p className="navbar-company-name">パートナープロップ株式会社</p>
      <nav className="navbar-links">
        <ul>
          {links.map(
            ({ name, Svg, href, availability }, index) =>
              availability === type && (
                <li
                  className={`navbar-links-item ${
                    current.includes(href) ? `active` : ``
                  }`}
                  key={index}
                >
                  <Link href={`/${href}`}>
                    <Svg />
                    <span>{name}</span>
                  </Link>
                </li>
              )
          )}
        </ul>
      </nav>
      <div>
        <ul>
          {/* {type === "both" && (
            <li className="navbar-links-item navbar-links-item_request">
              <LinkRequest />
            </li>
          )} */}
          <li className="navbar-links-item">
            <Link href={`/settings`}>
              <SettingsSvg />
              <span>基本設定</span>
            </Link>
          </li>
        </ul>
      </div>
      <div>
        <div className="navbar-userinfo">
          <div className="navbar-userinfo-content">
            <span className="navbar-userinfo-label">ログイン中</span>
            <p className="navbar-userinfo-username">ken fukumori</p>
          </div>
          <button onClick={handleLogout} className="navbar-userinfo-logout">
            <LogoutSvg />
          </button>
        </div>
        <button className="navbar-copyid" onClick={copyID}>
          ベンダーIDをコピー
        </button>
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  padding: 16px;
  display: flex;
  flex-flow: column;
  justify-content: space-between;
  height: 100vh;
  ul {
    list-style: none;
  }
  .navbar {
    &-company {
      &-name {
        margin-top: 8px;
        font-size: 14px;
        text-align: center;
      }
    }
    &-links {
      height: 100%;
      margin-top: 16px;
      &-item {
        border-radius: 5px;
        transition: 0.2s;
        padding: 8px 12px;
        button {
          border: 0;
          padding: 0;
          background: none;
          display: flex;
          align-items: center;
          gap: 8px;
        }
        &:hover {
          background-color: ${propcolors.gray[200]};
        }
        a {
          color: ${propcolors.black};
          display: flex;
          align-items: center;
          gap: 12px;
          svg {
            fill: ${propcolors.black};
          }
        }
        &.active {
          a {
            color: ${propcolors.red[700]};
            svg,
            path {
              fill: ${propcolors.red[700]};
            }
          }
        }
        &_request {
          &-amount {
            width: 20px;
            text-align: center;
          }
        }
        &_IDcopy {
          font-size: 14px;
        }
      }
    }
    &-userinfo {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      &-label {
        display: inline-block;
        font-size: 14px;
        margin-bottom: 4px;
      }
      &-content {
      }
      &-logout {
        border: 0;
        background: 0;
        padding: 0;
        svg,
        path {
          fill: ${propcolors.red[700]};
        }
      }
    }
    &-copyid {
      border: 0;
      background: 0;
      padding: 8px 12px;
      font-size: 14px;
    }
  }
`;

export const Navbar: React.FC<NavbarProps> = ({ type }) => {
  const router = useRouter();
  const resetAllState = useSetRecoilState(resetAllRecoil);
  const copyID = () => {
    toast("クリップボードにコピーしました！", {
      position: "bottom-center",
      icon: "✅",
    });
  };
  const handleLogout = async () => {
    await destroyCookie(null, "XSRF-TOKEN");
    await destroyCookie(null, "user", { path: "/" });
    await destroyCookie(null, "laravel_session");
    await resetAllState(undefined);
    await router.reload();
  };
  return (
    <Styled
      type={type}
      current={router.pathname}
      handleLogout={handleLogout}
      copyID={copyID}
    />
  );
};
