import styled from "@emotion/styled";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";

const Styled = styled(Link)`
  color: ${propcolors.black};
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  display: flex;
  gap: 0.8rem;
  align-items: center;
  &:hover {
    background-color: ${propcolors.black}1f;
  }
  &.selected {
    border-bottom: 2px solid ${propcolors.black};
    span {
      font-weight: 400;
      font-size: 14px;
    }
    &:hover {
      background-color: ${propcolors.white} !important;
    }
  }
`;

export const NavigationLink: React.FC<
  { children: React.ReactNode; className?: string } & LinkProps
> = ({ children, className, ...props }) => {
  const router = useRouter();
  return (
    <Styled
      className={`${className ? className : ""} ${
        router.asPath === props.href ? "selected" : ""
      }`}
      {...props}
    >
      {children}
    </Styled>
  );
};
