import styled from "@emotion/styled";
import { Anchor, Breadcrumbs, Toolt<PERSON> } from "@mantine/core";
import Link from "next/link";
import ArrowRightIcon from "public/icons/arrow-right-2.svg";
import { propcolors } from "styles/colors";

interface BreadcrumbListItem {
  title?: string;
  href?: string;
}

interface BreadcrumbItemProps {
  title?: string;
  href?: string;
  isMultipleLongText?: boolean;
}

interface CustomBreadcrumbProps {
  title: string;
  list?: Array<BreadcrumbListItem>;
  styleWrapper?: React.CSSProperties;
  isMultipleLongText?: boolean;
}

const Wrapper = styled.div({
  display: "flex",
  alignItems: "center",
});

const Title = styled.div({
  fontSize: 20,
  fontWeight: 600,
  color: propcolors.blackLight,
});

const SubTitle = styled.div({
  fontSize: 14,
  fontWeight: 300,
  color: propcolors.blackLight,
});

const SeparateLine = styled.span({
  width: 1,
  height: 24,
  display: "ineline-block",
  margin: "0 24px",
  verticalAlign: "middle",
  backgroundColor: propcolors.gray[200],
});

const BreadcrumbItem = (props: BreadcrumbItemProps) => {
  const { title, href, isMultipleLongText } = props;

  if (href && href.length) {
    const isInternalLink = !href?.startsWith("https");
    if (isInternalLink) {
      return (
        <Link href={href} css={{ textDecoration: "none" }}>
          <CustomTooltip
            title={title}
            isMultipleLongText={isMultipleLongText}
          />
        </Link>
      );
    } else {
      return (
        <Anchor
          href={href}
          underline={false}
          target="_blank"
          rel="noopener noreferrer"
        >
          <CustomTooltip
            title={title}
            isMultipleLongText={isMultipleLongText}
          />
        </Anchor>
      );
    }
  } else {
    return <CustomTooltip title={title} />;
  }
};

const CustomTooltip = (props: BreadcrumbItemProps) => {
  const { title, isMultipleLongText } = props;
  let maxTextDisplay = 20;
  if (isMultipleLongText) maxTextDisplay = 15;

  if (title) {
    if (title.length > maxTextDisplay) {
      return (
        <Tooltip
          label={title}
          maw={300}
          withArrow
          multiline
          transitionProps={{ duration: 200 }}
        >
          <SubTitle>{title.slice(0, maxTextDisplay) + "..."}</SubTitle>
        </Tooltip>
      );
    }
    return <SubTitle>{title}</SubTitle>;
  }
  return <></>;
};

export const CustomBreadcrumb = (props: CustomBreadcrumbProps) => {
  const { title, list, styleWrapper, isMultipleLongText = false } = props;

  return (
    <Wrapper style={styleWrapper}>
      <Title>{title}</Title>
      {list && list.length ? <SeparateLine /> : <></>}
      {list && list.length ? (
        <Breadcrumbs separator={<ArrowRightIcon />}>
          {list.map((breadcrumb: BreadcrumbListItem, index: number) => {
            return (
              <BreadcrumbItem
                key={index}
                title={breadcrumb.title}
                href={breadcrumb.href}
                isMultipleLongText={isMultipleLongText}
              />
            );
          })}
        </Breadcrumbs>
      ) : (
        <></>
      )}
    </Wrapper>
  );
};
