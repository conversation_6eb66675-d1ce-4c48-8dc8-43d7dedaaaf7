import {
  RiArchive2Fill,
  RiArchive2Line,
  RiMailSendLine,
} from "@remixicon/react";
import ChatIcon from "public/icons/sidebar/chat-1.svg";
import ChatIconActive from "public/icons/sidebar/chat-2.svg";
import ElearningIcon from "public/icons/sidebar/elearning.svg";
import ElearningIconActive from "public/icons/sidebar/elearning-2.svg";
import FolderIcon from "public/icons/sidebar/folder.svg";
import FolderIconActive from "public/icons/sidebar/folder-2.svg";
import InsightIcon from "public/icons/sidebar/insight-1.svg";
import InsightIconActive from "public/icons/sidebar/insight-2.svg";
import LeadsIcon from "public/icons/sidebar/leads-1.svg";
import LeadsIconActive from "public/icons/sidebar/leads-2.svg";
import PartnerIcon from "public/icons/sidebar/partner-4.svg";
import PartnerIconActive from "public/icons/sidebar/partner-5.svg";

export const links = [
  {
    name: "パートナー",
    href: "partners",
    Svg: PartnerIcon,
    SvgActive: PartnerIconActive,
    availability: "both",
    children: [
      {
        name: "企業一覧",
        child_href: "",
        is_beta: false,
      },
      {
        name: "ユーザー一覧",
        child_href: "users",
        is_beta: false,
      },
    ],
  },
  {
    name: "案件",
    href: "leads",
    Svg: LeadsIcon,
    SvgActive: LeadsIconActive,
    availability: "both",
  },
  {
    name: "ポータル",
    href: "drive",
    Svg: FolderIcon,
    SvgActive: FolderIconActive,
    availability: "both",
  },
  {
    name: "チャット",
    href: "chat/all",
    Svg: ChatIcon,
    SvgActive: ChatIconActive,
    availability: "both",
  },
  {
    name: "eラーニング",
    Svg: ElearningIcon,
    SvgActive: ElearningIconActive,
    availability: "none",
    href: "",
    children: [
      {
        name: "Webテスト管理",
        child_href: "webTest/manage",
        is_beta: false,
      },
      {
        name: "Webテスト受講",
        child_href: "webTest/attendance",
        is_beta: false,
      },
      {
        name: "トレーニング管理",
        child_href: "training/manage",
        is_beta: false,
      },
      {
        name: "トレーニング受講",
        child_href: "training/attendance",
        is_beta: false,
      },
    ],
  },
  {
    name: "問い合わせ",
    Svg: RiArchive2Line,
    SvgActive: RiArchive2Fill,
    availability: "none",
    href: "contact",
    children: [
      {
        name: "フォーム管理",
        child_href: "form",
        is_beta: false,
      },
      {
        name: "フォーム受信箱",
        child_href: "ticket",
        is_beta: false,
      },
    ],
  },
  {
    name: "メール",
    href: "mail",
    Svg: RiMailSendLine,
    SvgActive: RiMailSendLine,
    availability: "both",
  },
  //TODO: PROP-4142 デモダッシュボード移行後に除却
  {
    name: "レポート",
    href: "report",
    Svg: InsightIcon,
    SvgActive: InsightIconActive,
    availability: "both",
  },
  {
    name: "インサイト",
    href: "insight/dashboard",
    Svg: InsightIcon,
    SvgActive: InsightIconActive,
    availability: "both",
  },
];

export const productStatusList = [
  "未対応",
  "案件化",
  "アポ獲得",
  "商談中",
  "口頭合意",
  "受注",
  "保留",
  "失注",
];

export const productApprovalStatus = ["未承認", "承認済み"];

export const LeadProfile: LeadProfile = {
  name: "kenfukumori",
  maker: "partnerprop",
  staff: "customer name",
  staff_mail: "<EMAIL>",
  approval: "承認",
  shared_maker: "company maker",
  shared_partner: "company partner",
};

export const UserList = [
  {
    id: 1,
    vendor_id: 1,
    name: "Aubrey Drake Graham",
    name_kana: "オーブリー ドレイク グラハム",
    email: "<EMAIL>",
    password: "champagnepapi",
    tel: "*********",
    division: "CEO",
    role_id: 1,
    last_logined_at: "2022-11-13T02:20",
  },
  {
    id: 2,
    vendor_id: 1,
    name: "SUMIN",
    name_kana: "スミン",
    email: "<EMAIL>",
    password: "champagnepapi",
    tel: "*********",
    division: "Artist",
    role_id: 1,
    last_logined_at: "2022-11-13T02:20",
  },
];

export const ProductList = [
  {
    id: 1,
    name: "Product #1",
  },
];

export const propVersion = "2.0";
