export const REGEX_SPACE = /\s/g;
export const MIME_TYPE_IMAGE_LIST = [
  "image/png",
  "image/jpeg",
  "image/gif",
  "image/webp",
];
export const MIME_TYPE_PDF = "application/pdf";
export const MIME_TYPE_FILE_CHAT_DISPLAY = [
  "image/png",
  "image/jpeg",
  "image/gif",
  "image/webp",
  "application/pdf",
];
export const REGEX_MENTION = /@\[([^\]]+)\]/g;
export const REGEX_URL = /[(http(s)?):\/\/(www\.)?a-zA-Z0-9@:%._\+~#=-]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/ig;
export const REGEX_BETWEEN_PERCENT_CHARACTER = /%text[^%]*%/g;
export const REGEX_DATE_TIME = /^\d{4}-\d{2}-\d{2}( \d{2}:\d{2})?$/;
