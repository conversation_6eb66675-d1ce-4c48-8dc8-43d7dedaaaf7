// Vendor、Partner問わず横断的に使用するものを定義
export const CUSTOM_COLUMN_TYPE_NAME_STRING = "STRING";
export const CUSTOM_COLUMN_TYPE_NAME_INTEGER = "INTEGER";
export const CUSTOM_COLUMN_TYPE_NAME_SELECT = "SELECT";
export const CUSTOM_COLUMN_TYPE_NAME_DATE = "DATE";
export const CUSTOM_COLUMN_TYPE_NAME_FILE = "FILE";
export const CUSTOM_COLUMN_TYPE_NAME_VENDOR_USER = "VENDOR_USER";
export const CUSTOM_COLUMN_TYPE_NAME_PARTNER_USER = "PARTNER_USER";
export const CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT = "LONG_TEXT";
export const CUSTOM_COLUMN_TYPE_NAME_TIME = "DATETIME_LOCAL";

// 汎用的なテキスト
export const SAVE_BUTTON_CAPTION = "保存";
export const CANCEL_BUTTON_CAPTION = "キャンセル";

export const CUSTOM_COLUMN_TYPE_STR_VALUES = [
  {
    value: CUSTOM_COLUMN_TYPE_NAME_STRING,
    label: "文字列",
  },
  {
    value: CUSTOM_COLUMN_TYPE_NAME_INTEGER,
    label: "数値",
  },
  {
    value: CUSTOM_COLUMN_TYPE_NAME_SELECT,
    label: "選択",
  },
  {
    value: CUSTOM_COLUMN_TYPE_NAME_DATE,
    label: "日付（yyyy/mm/dd）",
  },
  {
    value: CUSTOM_COLUMN_TYPE_NAME_TIME,
    label: "日時（yyyy/mm/dd hh:mm）",
  },
  {
    value: CUSTOM_COLUMN_TYPE_NAME_FILE,
    label: "ファイル",
  },
  {
    value: CUSTOM_COLUMN_TYPE_NAME_VENDOR_USER,
    label: "ベンダーユーザー",
  },
  {
    value: CUSTOM_COLUMN_TYPE_NAME_PARTNER_USER,
    label: "パートナーユーザー",
  },
  {
    value: CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT,
    label: "ロングテキスト",
  },
];

export const CUSTOM_COLUMN_TYPE_INT_VALUES = [
  {
    value: "1",
    label: "文字列",
  },
  {
    value: "2",
    label: "数値",
  },
  {
    value: "3",
    label: "選択",
  },
  {
    value: "4",
    label: "日付（yyyy/mm/dd）",
  },
  {
    value: "9",
    label: "日時（yyyy/mm/dd hh:mm）",
  },
  {
    value: "5",
    label: "ベンダーユーザー",
  },
  {
    value: "6",
    label: "パートナーユーザー",
  },
  {
    value: "7",
    label: "ファイル",
  },
  {
    value: "8",
    label: "ロングテキスト",
  },
  // {
  //   value: "9",
  //   label: "都道府県",
  // },
];

export const JOB_STATUS_EXPORT_PENDING = "EXPORT_PENDING";
export const JOB_STATUS_EXPORT_IN_PROGRESS = "EXPORT_IN_PROGRESS";
export const JOB_STATUS_EXPORT_COMPLETED = "EXPORT_COMPLETED";
export const JOB_STATUS_EXPORT_FAILED = "EXPORT_FAILED";
export const JOB_STATUS_EXPORT_CANCELED = "EXPORT_CANCELED";
export const JOB_STATUS_EXPORT_PAUSED = "EXPORT_PAUSED";
export const JOB_STATUS_VALUES = [
  {
    value: JOB_STATUS_EXPORT_PENDING,
    label: "保留中",
  },
  {
    value: JOB_STATUS_EXPORT_IN_PROGRESS,
    label: "進行中",
  },
  {
    value: JOB_STATUS_EXPORT_COMPLETED,
    label: "完了済",
  },
  {
    value: JOB_STATUS_EXPORT_FAILED,
    label: "失敗",
  },
  {
    value: JOB_STATUS_EXPORT_CANCELED,
    label: "キャンセル済み",
  },
  {
    value: JOB_STATUS_EXPORT_PAUSED,
    label: "停止",
  },
];

export const JOB_DATA_TYPE_EXPORT_PARTNER = "MANAGED_PARTNER";
export const JOB_DATA_TYPE_EXPORT_LEAD = "LEAD";
export const JOB_DATA_TYPE_EXPORT_CONTACT_ANSWER = "CONTACT_ANSWER";
export const JOB_DATA_TYPE_VALUES = [
  {
    value: JOB_DATA_TYPE_EXPORT_PARTNER,
    label: "パートナー",
  },
  {
    value: JOB_DATA_TYPE_EXPORT_LEAD,
    label: "案件",
  },
  {
    value: JOB_DATA_TYPE_EXPORT_CONTACT_ANSWER,
    label: "問い合わせ回答",
  },
];
// フィルター作成関連
export const MANAGED_PARTNER_ID = "連携パートナーID";
export const MANAGED_PARTNER_RECORD_ID = "パートナーレコードID";
export const MANAGED_PARTNER_NAME = "パートナー名";
export const PARTNER_ID = "パートナーID";
export const PREFECTURE_ID = "都道府県";
export const POSTAL_CODE = "郵便番号";
export const ADDRESS = "住所";
export const TEL = "代表電話番号";
export const URL = "URL";
export const NUMBER_OF_EMPLOYEES = "従業員数";
export const CONTRACT_STATUS = "契約ステータス";
export const LINK_STATUS = "連携ステータス";
export const CREATED_AT = "作成日";
export const UPDATED_AT = "更新日";
export const PARTNER_FIXED_COLUMN_DIC: { [key: string]: string } = {
  managed_partner_id: MANAGED_PARTNER_ID,
  managed_partner_record_id: MANAGED_PARTNER_RECORD_ID,
  managed_partner_name: MANAGED_PARTNER_NAME,
  partner_id: PARTNER_ID,
  prefecture_id: PREFECTURE_ID,
  postal_code: POSTAL_CODE,
  address: ADDRESS,
  tel: TEL,
  url: URL,
  number_of_employees: NUMBER_OF_EMPLOYEES,
  contract_status: CONTRACT_STATUS,
  link_status: LINK_STATUS,
  created_at: CREATED_AT,
  updated_at: UPDATED_AT,
};
export const SORTABLE_COLUMNS = [
  // パートナー関連
  "managed_partner_id",
  "managed_partner_record_id",
  "managed_partner_name",
  "partner_id",
  "contract_status",
  "link_status",
  // 案件関連
  "lead_id",
  "lead_name",
  "lead_record_id",
  "lead_status",
  // 共通項目
  "prefecture_id",
  "postal_code",
  "address",
  "tel",
  "number_of_employees",
  "created_at",
  "updated_at",
];
export const SORT_CONDITION_LIST = [
  {
    value: "ASC",
    label: "昇順",
  },
  {
    value: "DESC",
    label: "降順",
  },
];

export const FILTER_OPERATOR_BASE_LIST = [
  {
    value: "INCLUDES",
    label: "含む",
    type: ["STRING", "FILE", "LONG_TEXT", "VENDOR_USER", "PARTNER_USER"],
  },
  {
    value: "EXCLUDES",
    label: "含まない",
    type: ["STRING", "FILE", "LONG_TEXT", "VENDOR_USER", "PARTNER_USER"],
  },
  {
    value: "MATCHES",
    label: "完全一致",
    type: ["STRING", "FILE", "LONG_TEXT", "VENDOR_USER", "PARTNER_USER"],
  },
  {
    value: "EQUALS",
    label: "等しい",
    type: ["INTEGER", "SELECT"],
  },
  {
    value: "NOTEQUALS",
    label: "等しくない",
    type: ["INTEGER", "SELECT"],
  },
  {
    value: "GREATER_THAN",
    label: "より大きい",
    type: ["INTEGER"],
  },
  {
    value: "LESS_THAN",
    label: "より小さい",
    type: ["INTEGER"],
  },
  {
    value: "STARTS",
    label: "開始",
    type: ["DATE", "DATETIME_LOCAL"],
  },
  {
    value: "ENDS",
    label: "終了",
    type: ["DATE", "DATETIME_LOCAL"],
  },
];
export const FIXED_COLUMN_TYPES = {
  number: [
    "managed_partner_id",
    "managed_partner_record_id",
    "number_of_employees",
    "partner_id",
  ],
  string: [
    "managed_partner_name",
    "postal_code",
    "address",
    "tel",
    "url",
    "vendor.user_name",
  ],
  date: ["created_at", "updated_at"],
  select: ["prefecture_id", "contract_status_name", "link_status_name"],
};
export const LEAD_FIXED_COLUMN_TYPES = {
  number: ["lead_id", "lead_record_id", "number_of_employees"],
  string: [
    "lead_name",
    "postal_code",
    "address",
    "tel",
    "url",
    "memo",
    "partner_name",
    "partner.user_name",
  ],
  date: ["created_at", "updated_at"],
  select: ["lead_status_name", "prefecture_id"],
};
export const VALIDATE_SEARCH_CONDITION_NAME_NONE = "フィルター名は必須項目です";
export const VALIDATE_CRITERIA_COLUMN_NONE =
  "フィルター条件のカラムに未設定の項目があります";
export const VALIDATE_CRITERIA_OPERATOR_NONE =
  "フィルター条件の演算子に未設定の項目があります";
export const VALIDATE_CRITERIA_SEARCH_VALUE_NONE =
  "フィルター条件の検索値に未設定の項目があります";
export const VALIDATE_SORT_TARGET_COLUMN_NONE =
  "ソート条件のカラムが未設定です";
export const VALIDATE_SORT_TARGET_ORDER_BY_NONE =
  "ソート条件のORDER BYが未設定です";

export const VALUE_TYPE_MAP = {
  STRING: "文字列",
  INTEGER: "数値",
  DATE: "日付",
  SELECT: "選択",
  VENDOR_USER: "ベンダーユーザー",
  PARTNER_USER: "パートナーユーザー",
  FILE: "ファイル",
  LONG_TEXT: "ロングテキスト",
  DATETIME_LOCAL: "日時",
} as const;

export const VALIDATE_SEARCH_CONDITION_FIXED_COLUMNS =
  "一覧画面に表示する列は必須項目です";

export const CONTRACT_STATUS_LIST = [
  { value: "CONTRACT_PENDING", label: "未対応" },
  { value: "CONTRACT_NEGOTIATION", label: "契約交渉中" },
  { value: "CONTRACT_SIGNING", label: "契約締結中" },
  { value: "CONTRACT_ACTIVE", label: "契約済み" },
  { value: "CONTRACT_CANCEL", label: "解約" },
];
export const LINK_STATUS_LIST = [
  { value: "LINK_PENDING", label: "未連携" },
  { value: "LINK_REQUEST", label: "連携依頼中" },
  { value: "LINK_CANCELLED", label: "連携依頼キャンセル" },
  { value: "LINK_ACTIVE", label: "連携中" },
  { value: "LINK_HOLD", label: "連携停止" },
  { value: "LINK_REJECT", label: "連携拒否" },
];

export const LEAD_STATUS_LIST = [
  { value: "LEAD_PENDING", label: "未対応" },
  { value: "LEAD_APPROACHING", label: "アプローチ中" },
  { value: "LEAD_APPOINTMENT", label: "アポ設定" },
  { value: "LEAD_IN_PROGRESS", label: "商談中" },
  { value: "LEAD_PIC_AGREEMENT", label: "担当者合意" },
  { value: "LEAD_APPROVER_AGREEMENT", label: "決裁者合意" },
  { value: "LEAD_APPLIED", label: "申込済" },
  { value: "LEAD_ON_HOLD", label: "保留" },
  { value: "LEAD_FAILURE", label: "不成立" },
  { value: "LEAD_TERMINATION", label: "解約" },
];

export const CONFIRM_CREATE_SEARCH_CONDITION_TITLE =
  "フィルターを作成します。本当によろしいですか？変更完了後に再読み込みが行われます。";
export const FILTER_DELETE_CONFIRM_TITLE = "フィルターを削除";
export const FILTER_DELETE_CONFIRM_MSG =
  "フィルターを削除しますか？\nこの操作はもとに戻すことができません。";
export const FILTER_DELETE_NOT_ALLOWED_TITLE = "フィルターを削除できません";
export const FILTER_DELETE_NOT_ALLOWED_MSG =
  "全てのフィルターを削除することはできません";
export const SEARCH_CONDITION_CREATE_EDIT_MODAL_CAPTIONS = {
  CREATE_TITLE: "フィルターを新規作成",
  EDIT_TITLE: "フィルターを編集",
  DUPLICATE_TITLE: "フィルターを複製",
  FILTER_NAME_LABEL: "フィルター名",
  CREATE_CRITERIA_LABEL: "フィルター条件を設定",
  DETAIL_CRITERIA_LABEL: "フィルター条件",
  NONE_CRITERIA_LABEL: "フィルター条件は設定されていません",
  CREATE_SORTS_LABEL: "ソート条件を選択",
  DETAIL_SORTS_LABEL: "ソート条件",
  NONE_SORTS_LABEL: "ソート条件は設定されていません",
  CREATE_COLUMNS: "一覧画面に表示する列選択",
  DISABLE_COLUMNS_LABEL: "非表示",
  ENABLE_COLUMNS_LABEL: "表示",
  DETAIL_COLUMNS_LABEL: "画面に表示する列",
  SEARCH_CONDITION_NO_SELECTED_COLUMN: "列が選択されていません",
  ENTER_FILTER_NAME: "フィルター名を入力",
  SELECT_COLUMN: "カラムを選択",
  SELECT_ORDER: "順序を選択",
  ENTER_CONDITION: "条件を入力",
  SEARCH_COLUMN_NAME: "カラム名で検索",
};
export const SEARCH_CONDITION_SETTING_BUTTON_LABEL = "フィルター設定";
export const SEARCH_CONDITION_ALL_SHOW = { value: "0", label: "全てを表示" };
export const SEARCH_PARTNER_NAME_PLACEHOLDER = "パートナー名で検索...";
export const SEARCH_PARTNER_USER_NAME_PLACEHOLDER = "ユーザー名で検索...";
export const SEARCH_CONTACT_PLACEHOLDER = "キーワードで検索...";
export const SEARCH_LEAD_NAME_PLACEHOLDER = "案件名で検索...";
export const LOADING_CAPTION = "データを取得しています...";
export const SUBTABLE_FILE_CHANGING = "ファイル更新中...";
export const SEARCH_PARTNER_NOT_FOUND = "該当するパートナーが見つかりません";
export const SEARCH_LEAD_NOT_FOUND = "該当する案件が見つかりません";

// 共有設定変更時のポップアップ
export const NOTIFICATION_MSG = {
  // 全てのパートナーを追加時
  SHARE_ALL_TITLE: "共有を追加しました。",
  SHARE_ALL_MESSAGE: "全てのパートナーを共有へ追加しました。",
  // 全てのパートナーを解除、個別パートナーを追加・解除時
  SHARE_SETTING_CHANGE: "共有設定を変更しました。",

  ERROR_SHARE_SETTING: "共有設定を変更できませんでした。",
};
