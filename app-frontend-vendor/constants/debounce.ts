/**
 * 連続して呼び出される処理を一定時間経過後に1回だけ実行する関数
 * @param func 実行したい関数
 * @param wait 待機時間（ミリ秒）
 * @returns デバウンスされた関数
 */
export const debounce = <T extends unknown[], R>(
  func: (...args: T) => R,
  wait = 300
) => {
  let timeoutId: NodeJS.Timeout | null = null;

  return (...args: T): void => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      func(...args);
      timeoutId = null;
    }, wait);
  };
};
