enum LeadStatus {
  LEAD_PENDING = "LEAD_PENDING",
  LEAD_APPROACHING = "LEAD_APPROACHING",
  LEAD_APPOINTMENT = "LEAD_APPOINTMENT",
  LEAD_IN_PROGRESS = "LEAD_IN_PROGRESS",
  LEAD_PIC_AGREEMENT = "LEAD_PIC_AGREEMENT",
  LEAD_APPROVER_AGREEMENT = "LEAD_APPROVER_AGREEMENT",
  LEAD_APPLIED = "LEAD_APPLIED",
  LEAD_ON_HOLD = "LEAD_ON_HOLD",
  LEAD_FAILURE = "LEAD_FAILURE",
  LEAD_TERMINATION = "LEAD_TERMINATION",
}

export enum COLUMN_TYPES {
  FIXED_COLUMN_TYPE = 0,
  CUSTOM_COLUMN_TYPE = 1,
}

export default LeadStatus;
