export const SUB_TABLE_DEFAULT_COLUMN_WIDTH = 220;
export const SUB_TABLE_LARGE_COLUMN_WIDTH = 400;
export const LONG_TEXT_MAX_LENGTH = 10000;
export const STRING_MAX_LENGTH = 255;
export const SUB_TABLE_LARGE_COLUMN_MOBILE_WIDTH = 250;
export const COLUMN_TYPE_FIXED = "FIXED_COLUMN";
export const COLUMN_TYPE_CUSTOM = "CUSTOM_COLUMN";
export const SEARCH_CONDITION_TARGET_PARTNER = "managed_partner";
export const SEARCH_CONDITION_TARGET_LEAD = "read";
export const SEARCH_CONDITION_GET_ALL_ID = "0";
export const SUB_TABLE_MOBILE_DOUBLE_CLICK_DELAY = 500;
export const ACCEPTABLE_DECIMAL_DIGIT_NUMBER = 5;
export const PER_PAGE_OF_LEAD_MOBILE = 10;
export const DEFAULT_PAGINATION_PAGE_SIZE = 25;
export const PAGINATION_PAGE_SIZE_OPTIONS = [25, 50, 100];
export const PER_PAGE_OF_CONTACT_FORM = 25;
export const PER_PAGE_OF_MESSAGES = 20;
export const PER_PAGE_OF_CHANNELS = 20;
export const MAX_CHANNELS_DISPLAY = 200;

export const FORM_DISPLAY_TITLE_MAX_LENGTH = 256;

export const FORM_TITLE_MAX_LENGTH = 256;

export const FORM_DESCRIPTION_MAX_LENGTH = 256;

export const POSTAL_CODE_MAX_LENGTH = 7;

export const TEL_MAX_LENGTH = 40;

export const FORM_COLUMN_INIT_VALUE_MAX_LENGTH = 255;

export const CONTACT_ACCEPT_FILES = [
  "image/png",
  "image/jpeg",
  "image/gif",
  "application/pdf",
  "application/msword", // 2007年以前のWord
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // 2007年以降のWord
  "application/vnd.ms-excel", // 2007年以前のExcel
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // 2007年以降のExcel
  "application/vnd.ms-powerpoint", // 2007年以前のPowerPoint
  "application/vnd.openxmlformats-officedocument.presentationml.presentation", // 2007年以降のPowerPoint
  "application/zip",
  "text/csv",
  "image/svg+xml",
].join(",");

export const DOWNLOAD_IFRAME_REMOVE_DELAY = 3000;

// Cookieの有効期間設定（3日 × 7日 × 24時間 × 60分 × 60秒 = 3週間）
export const COOKIE_MAX_AGE = 3 * 7 * 24 * 60 * 60;
