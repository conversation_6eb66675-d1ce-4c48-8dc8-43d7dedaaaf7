import LeadStatus from "./enums/leadStatus.enum";
import UserTitle from "./enums/userTitle.enum";
import WebTestStatus from "./enums/webTestStatus.enum";

export interface IStatusColor {
  textColor: string;
  borderColor: string;
}

export type LeadStatusColorType = {
  [key in LeadStatus]: IStatusColor;
};

export type WebTestStatusColorType = {
  [key in WebTestStatus]: IStatusColor;
};

export type UserTitleColorType = {
  [key in UserTitle]: IStatusColor;
};

export const LeadStatusColor: LeadStatusColorType = {
  [LeadStatus.LEAD_PENDING]: {
    textColor: "#8992A0",
    borderColor: "#E8EAED",
  },
  [LeadStatus.LEAD_APPROACHING]: {
    textColor: "#5285E9",
    borderColor: "#D2E1FF",
  },
  [LeadStatus.LEAD_APPOINTMENT]: {
    textColor: "#1AABF4",
    borderColor: "#D7EEFE",
  },

  [LeadStatus.LEAD_IN_PROGRESS]: {
    textColor: "#13BFB5",
    borderColor: "#CBF4EF",
  },

  [LeadStatus.LEAD_PIC_AGREEMENT]: {
    textColor: "#2DCD6E",
    borderColor: "#DBF5E3",
  },

  [LeadStatus.LEAD_APPROVER_AGREEMENT]: {
    textColor: "#FF924C",
    borderColor: "#FBF1CE",
  },

  [LeadStatus.LEAD_APPLIED]: {
    textColor: "#FF7B5E",
    borderColor: "#FFEDEC",
  },

  [LeadStatus.LEAD_ON_HOLD]: {
    textColor: "#FF569D",
    borderColor: "#FFE5F9",
  },

  [LeadStatus.LEAD_FAILURE]: {
    textColor: "#BF55EC",
    borderColor: "#F4DDFC",
  },

  [LeadStatus.LEAD_TERMINATION]: {
    textColor: "#8992A0",
    borderColor: "#E8EAED",
  },
};

export const WebTestStatusColor: WebTestStatusColorType = {
  [WebTestStatus.PUBLISHED]: {
    textColor: "#FF569D",
    borderColor: "#FFE5F9",
  },
  [WebTestStatus.UNPUBLISHED]: {
    textColor: "#8992A0",
    borderColor: "#E8EAED",
  },
  [WebTestStatus.DRAFT]: {
    textColor: "#1AABF4",
    borderColor: "#D7EEFE",
  },
};

export const UserTitleColor: UserTitleColorType = {
  [UserTitle.TITLE_REPRESENTATIVE]: {
    textColor: "#FF924C",
    borderColor: "#FBF1CE",
  },
  [UserTitle.TITLE_OFFICER]: {
    textColor: "#FF569D",
    borderColor: "#FFE5F9",
  },
  [UserTitle.TITLE_DIVISION_MANAGER]: {
    textColor: "#FF569D",
    borderColor: "#FFE5F9",
  },

  [UserTitle.TITLE_DEPARTMENT_MANAGER]: {
    textColor: "#BF55EC",
    borderColor: "#F4DDFC",
  },

  [UserTitle.TITLE_SECTION_MANAGER]: {
    textColor: "#5285E9",
    borderColor: "#D2E1FF",
  },

  [UserTitle.TITLE_LEADER]: {
    textColor: "#1AABF4",
    borderColor: "#D7EEFE",
  },

  [UserTitle.TITLE_STAFF]: {
    textColor: "#8992A0",
    borderColor: "#FFFFFF",
  },

  [UserTitle.TITLE_OTHER]: {
    textColor: "#8992A0",
    borderColor: "#FFFFFF",
  },
};

export const WebTestStatusColorLabel = {
  [WebTestStatus.PUBLISHED]: "公開済",
  [WebTestStatus.UNPUBLISHED]: "非公開",
  [WebTestStatus.DRAFT]: "下書き",
};

export const ContactFormColumnTypeLabel: {
  [key in string]: string;
} = {
  STRING: "一行テキスト",
  TEXT: "複数行テキスト",
  DATE: "日付（yyyy/mm/dd）",
  CHECKBOX: "複数選択",
  RADIO: "単一選択",
  FILE: "ファイル添付",
  DESCRIPTION: "文章",
  LOGO: "画像",
  DATETIME_LOCAL: "日時（yyyy/mm/dd hh:mm）",
};

export const ContactFormNotificationTypeLabel: {
  [key in string]: string;
} = {
  ALL_VENDOR_USER: "全てのユーザー",
  MEMBER: "個別な社内ユーザー",
};

export const ContactFormAssigneeTypeLabel: {
  [key in string]: string;
} = {
  ALL_VENDOR_USER: "全てのユーザー",
  ADMIN_GROUP: "管理者ユーザー",
  MEMBER_GROUP: "一般ユーザー",
  MEMBER: "個別な社内ユーザー",
  NONE: "未割り当て",
};

export const LAST_MESSAGE_CHAT_NUMBERS = [0, 1];

/**
 * Format Date
 * Convert format yyyy/mm/dd
 * @param date string
 * @returns string
 */
export const formatDate = (date: string): string => {
  return date?.replace(/-/g, "/");
};

export const SelectPermissionUser = [
  {
    option_name: "ベンダー管理者としてプレビュー",
    role: "vendor",
    user_role: "VENDOR_ADMIN",
  },
  {
    option_name: "ベンダーユーザーとしてプレビュー",
    role: "vendor",
    user_role: "VENDOR_USER",
  },
  {
    option_name: "パートナー管理者としてプレビュー",
    role: "partner",
    user_role: "PARTNER_ADMIN",
  },
  {
    option_name: "パートナーユーザーとしてプレビュー",
    role: "partner",
    user_role: "PARTNER_USER",
  },
];

export const UserRole = [
  "VENDOR_ADMIN",
  "PARTNER_ADMIN",
  "VENDOR_USER",
  "PARTNER_USER",
];
