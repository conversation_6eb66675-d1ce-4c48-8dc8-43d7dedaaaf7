import React from "react";
import styled from "@emotion/styled";
import { propcolors } from "styles/colors";

type ItemProps = {
  item: CustomUserColumn;
};

type PresentationalProps = {
  className?: string;
} & ItemProps;

const Presentation: React.FC<PresentationalProps> = ({ className, item }) => (
  <article className={className}>
    <div className="columns-item-label">
      <p className="columns-item-label-name">{item.column_name}</p>
      <select>
        <option value={1}>テキスト</option>
        <option value={2}>数値</option>
        <option value={3}>選択</option>
      </select>
    </div>
    <div className="columns-item-controls">
      <button className="columns-item-controls-button">編集</button>
      <button className="columns-item-controls-button">削除</button>
    </div>
  </article>
);

const Styled = styled(Presentation)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  & + & {
    border-top: 1px solid ${propcolors.gray[300]};
  }
  .columns {
    &-item {
      &-label {
        display: inline-flex;
        align-items: center;
        &-name {
          font-weight: bold;
        }
        &-type {
          margin-top: 0.25rem;
          font-size: 0.88rem;
        }
      }
      &-controls {
        &-button {
          height: 100%;
        }
      }
    }
  }
`;

export const UserColumnItem: React.FC<ItemProps> = ({ item }) => {
  return <Styled item={item} />;
};
