import styled from "@emotion/styled";
import { Anchor, Breadcrumbs, Button } from "@mantine/core";
import {
  ArrowDownload16Filled,
  Folder20Filled,
  Share16Filled,
} from "@fluentui/react-icons";
import { DriveModifierModal } from "features/drive/explorer/components/modifyModal";
import { useState, useCallback } from "react";

type DriveDetailNavbarProps = {
  mutateAll: () => void;
  filename: string;
  fileID: number;
} & CommonProps;

type CommonProps = {
  breadcrumbs: DriveBreadcrumbItem[];
};

type PresentationProps = {
  className?: string;
  openModal: (mode: DriveModifierMode) => void;
} & CommonProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  breadcrumbs,
  openModal,
}) => {
  return (
    <header className={className}>
      <div className="navbar-heading">
        <h2>
          <Folder20Filled />
          ドライブ
        </h2>
        <Breadcrumbs separator="/">
          {breadcrumbs.map((item, index) => (
            <Anchor href={`/drive/${item.id}`} key={index}>
              {item.name}
            </Anchor>
          ))}
        </Breadcrumbs>
      </div>
      <div className="navbar-buttons">
        <Button
          onClick={() => openModal("share")}
          leftIcon={<Share16Filled />}
          color="gray"
        >
          共有...
        </Button>
        <Button
          // onClick={() => openModal("newProduct")}
          leftIcon={<ArrowDownload16Filled />}
          color="gray"
        >
          ダウンロード
        </Button>
      </div>
    </header>
  );
};

const Styled = styled(Presentation)`
  .navbar {
    &-heading {
      display: flex;
      align-items: center;
      gap: 2rem;
    }
    &-buttons {
      display: flex;
      gap: 10px;
    }
  }
`;

export const DriveDetailNavbar: React.FC<DriveDetailNavbarProps> = ({
  breadcrumbs,
  mutateAll,
  filename,
  fileID,
}) => {
  const [modalInnerProps, setModalInnerProps] =
    useState<ModalInnerProps | null>(null);
  const openModal = useCallback((mode: DriveModifierMode) => {
    setModalInnerProps({
      mode,
      filename,
      fileID: fileID ? fileID : 0,
      opened: true,
    });
  }, []);
  return (
    <>
      <DriveModifierModal
        close={() => setModalInnerProps(null)}
        mode={modalInnerProps?.mode}
        fileID={fileID ? fileID : 0}
        filename={filename}
        opened={modalInnerProps ? true : false}
        mutate={mutateAll}
      />
      <Styled breadcrumbs={breadcrumbs} openModal={openModal} />
    </>
  );
};
