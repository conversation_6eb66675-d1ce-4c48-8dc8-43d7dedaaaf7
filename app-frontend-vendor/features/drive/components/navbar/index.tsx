import styled from "@emotion/styled";
import { <PERSON><PERSON>, Breadcrumbs, <PERSON><PERSON>, <PERSON>u } from "@mantine/core";
import {
  Add16Filled,
  ArrowDownload16Filled,
  Document16Filled,
  Folder16Filled,
  Folder20Filled,
  Share16Filled,
} from "@fluentui/react-icons";

type DriveNavbarProps = {
  breadcrumbs: DriveBreadcrumbItem[];
};

type PresentationProps = {
  className?: string;
} & DriveNavbarProps;

const items = [
  { title: "root", href: "/drive/0" },
  { title: "dir1", href: "/drive/2" },
  { title: "item", href: "#" },
];

const Presentation: React.FC<PresentationProps> = ({
  className,
  breadcrumbs,
}) => {
  return (
    <header className={className}>
      <div className="navbar-heading">
        <h2>
          <Folder20Filled />
          ドライブ
        </h2>
        <Breadcrumbs separator="/">
          {breadcrumbs.map((item, index) => (
            <Anchor href={`/drive/${item.id}`} key={index}>
              {item.name}
            </Anchor>
          ))}
        </Breadcrumbs>
      </div>
      <div className="navbar-buttons">
        <Button
          // onClick={() => openModal("newProduct")}
          leftIcon={<ArrowDownload16Filled />}
          color="gray"
        >
          ログデータ出力
        </Button>
        <Button
          // onClick={() => openModal("newProduct")}
          leftIcon={<Share16Filled />}
          color="gray"
        >
          共有先
        </Button>
        <Menu>
          <Menu.Target>
            <Button leftIcon={<Add16Filled />}>新規追加...</Button>
          </Menu.Target>
          <Menu.Dropdown>
            <Menu.Item icon={<Folder16Filled />}>フォルダ</Menu.Item>
            <Menu.Item icon={<Document16Filled />}>ファイル</Menu.Item>
          </Menu.Dropdown>
        </Menu>
      </div>
    </header>
  );
};

const Styled = styled(Presentation)`
  .navbar {
    &-heading {
      display: flex;
      align-items: center;
      gap: 2rem;
    }
    &-buttons {
      display: flex;
      gap: 10px;
    }
  }
`;

export const DriveNavbar: React.FC<DriveNavbarProps> = ({ breadcrumbs }) => {
  return <Styled breadcrumbs={breadcrumbs} />;
};
