import styled from "@emotion/styled";
import { formatDate } from "constants/commons";
import {
  Group,
  UnstyledButton,
  Text,
  Table,
  ScrollArea,
  ActionIcon,
  Tooltip,
} from "@mantine/core";
import { keys } from "@mantine/utils";
import { useRouter } from "next/router";
import { useCallback, useEffect, useMemo, useState } from "react";
import getFileSizeWithUnit from "utils/func/getFilesize";
import { DrivePermalinkButton } from "./components/permalinkButton";
import { propcolors } from "styles/colors";
import {
  RiDeleteBin6Line,
  RiFolderUserLine,
  RiPencilLine,
} from "@remixicon/react";
import { usePartnerName } from "./components/usePartnerName";

type customTHProps = {
  onSort: () => void;
  reversed: boolean;
  sorted: boolean;
  children: React.ReactNode;
  size?: string | number;
  color?: string;
};

type DriveExplorerProps = {
  data: DriveExplorerData[];
  path: number | undefined;
  mutate: () => void;
  openModal: (
    mode: DriveModifierMode,
    filename?: string,
    fileID?: number
  ) => void;
  isRoot: boolean;
  type: "directory" | "normal";
  share: () => void;
  sharePortal: () => void;
};

type PresentationProps = {
  className?: string;
  pushHistory: (id: number, type: "directory" | "normal") => void;
} & DriveExplorerProps;

const CustomTH: React.FC<customTHProps> = ({
  reversed,
  sorted,
  children,
  size,
  color,
}) => {
  return (
    <th className="list-folder-header-th">
      <UnstyledButton>
        <Group position="apart">
          <Text size={size || "sm"} color={color || "black"}>
            {children}
          </Text>
        </Group>
      </UnstyledButton>
    </th>
  );
};

const filterData = (data: DriveExplorerData[], search: string) => {
  const query = search.toLowerCase().trim();
  return data.filter((item) =>
    keys(data[0]).some((key) =>
      String(item[key]!).toLowerCase().includes(query)
    )
  );
};

const sortData = (
  data: DriveExplorerData[],
  payload: {
    sortBy: keyof DriveExplorerData | null;
    reversed: boolean;
    search: string;
  }
) => {
  const { sortBy } = payload;

  if (!sortBy) {
    return filterData(data, payload.search);
  }

  return filterData(
    [...data].sort((a, b) => {
      if (payload.reversed) {
        return String(b[sortBy]!).localeCompare(String(a[sortBy]));
      }

      return String(a[sortBy]!).localeCompare(String(b[sortBy]));
    }),
    payload.search
  );
};

const PartnerNameCell = ({ fileId }: { fileId: number }) => {
  const partner = usePartnerName(fileId);
  return (
    <Text size={12} className="row-text" color={propcolors.blackLightLabel}>
      {partner?.[0]?.name || "−"}
    </Text>
  );
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  data,
  pushHistory,
  openModal,
  isRoot,
  share,
}) => {
  const [search, setSearch] = useState("");
  const [sortedData, setSortedData] = useState(data);
  const [sortBy, setSortBy] = useState<keyof DriveExplorerData | null>(null);
  const [reverseSortDirection, setReverseSortDirection] = useState(false);

  const setSorting = (field: keyof DriveExplorerData) => {
    const reversed = field === sortBy ? !reverseSortDirection : false;
    setReverseSortDirection(reversed);
    setSortBy(field);
    setSortedData(sortData(data, { sortBy: field, reversed, search }));
  };

  useEffect(() => {
    setSorting("name");
  }, []);

  const rows = useMemo(
    () =>
      sortedData?.map((row) => {
        return (
          <tr key={row.id} className={className}>
            <td className="list-folder-content">
              <UnstyledButton
                className="file-title"
                onClick={() =>
                  pushHistory(row.id, row.type as "directory" | "normal")
                }
              >
                <RiFolderUserLine className="folder-icon" />
                <Text size="sm" color={propcolors.blackLight}>
                  {row.name}
                </Text>
              </UnstyledButton>
            </td>
            <td>
              <Text
                size={12}
                className="row-text"
                color={propcolors.blackLightLabel}
              >
                <PartnerNameCell fileId={row.id} />
              </Text>
            </td>
            <td>
              <Text
                size={12}
                className="row-text"
                color={propcolors.blackLightLabel}
              >
                {row.type === "directory" ? "" : getFileSizeWithUnit(row.size)}
              </Text>
            </td>
            <td>
              <Text
                size={12}
                className="row-text"
                color={propcolors.blackLightLabel}
              >
                {formatDate(row.updated_at)}
              </Text>
            </td>
            <td>
              <Text
                size={12}
                className="row-text"
                color={propcolors.blackLightLabel}
              >
                {formatDate(row.created_at)}
              </Text>
            </td>
            <td>
              <Group
                position="left"
                grow={false}
                spacing="12px"
                className="group-action"
              >
                <DrivePermalinkButton file_id={row.id} />
                <ActionIcon
                  className="tooltip-content"
                  onClick={() => {
                    const mode =
                      row.type === "directory"
                        ? "renameCollabFolder"
                        : "renameCollabFile";
                    openModal(mode, row.name, row.id);
                  }}
                >
                  <Tooltip
                    label="編集する"
                    withArrow
                    arrowSize={6}
                    offset={-3}
                    position="top"
                    transitionProps={{ duration: 200 }}
                  >
                    <ActionIcon>
                      <RiPencilLine size={20} />
                    </ActionIcon>
                  </Tooltip>
                </ActionIcon>
                <ActionIcon
                  onClick={() =>
                    openModal("deleteCollabFolder", row.name, row.id)
                  }
                  className="tooltip-content"
                >
                  <Tooltip
                    label="削除する"
                    withArrow
                    position="top"
                    transitionProps={{ duration: 200 }}
                  >
                    <ActionIcon>
                      <RiDeleteBin6Line size={19} />
                    </ActionIcon>
                  </Tooltip>
                </ActionIcon>
              </Group>
            </td>
          </tr>
        );
      }),
    [sortedData]
  );

  return (
    <Table className={className} miw={900}>
      <thead className="table-header-wrap">
        <tr className="table-header">
          <CustomTH
            reversed={reverseSortDirection}
            sorted={sortBy === "name"}
            onSort={() => setSorting("name")}
            size={16}
            color="#222222"
          >
            共同編集フォルダ
          </CustomTH>
          <CustomTH
            reversed={reverseSortDirection}
            sorted={sortBy === "partner"}
            onSort={() => setSorting("partner")}
            size={12}
            color="#666666"
          >
            共同編集パートナー
          </CustomTH>
          <CustomTH
            reversed={reverseSortDirection}
            sorted={sortBy === "size"}
            onSort={() => setSorting("size")}
            size={12}
            color="#666666"
          >
            サイズ
          </CustomTH>
          <CustomTH
            reversed={reverseSortDirection}
            sorted={sortBy === "updated_at"}
            onSort={() => setSorting("updated_at")}
            size={12}
            color="#666666"
          >
            最終更新日
          </CustomTH>
          <CustomTH
            reversed={reverseSortDirection}
            sorted={sortBy === "created_at"}
            onSort={() => setSorting("created_at")}
            size={12}
            color="#666666"
          >
            作成日
          </CustomTH>
        </tr>
      </thead>
      <tbody className="list-item-row">
        {sortedData && sortedData.length > 0 ? (
          rows
        ) : (
          <div className="list-folder-empty">
            <p>データがありません</p>
          </div>
        )}
      </tbody>
    </Table>
  );
};

const Styled = styled(Presentation)`
  height: auto;

  .list-folder-empty {
    width: 100%;
    height: 96px;
    display: flex;
    justify-content: center;
    flex-direction: wrap;
    align-items: center;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
  }

  .list-folder-header-th {
    padding: 10px 6px !important;
    border-bottom: 0;
    &-reset {
      padding-left: 0;
    }
  }

  .table-header {
    th {
      :first-child {
        padding-left: 0 !important;
      }
    }
  }

  .table-header-wrap {
    display: block;
    box-shadow: 0px 1px 3px 0px #f3f3f3;
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding: 0 16px 0 24px;
  }

  tr {
    display: grid;
    grid-template-columns: 1fr 15% 10% 15% 15% 17%;
    th {
      padding: 10px !important;
      height: 72px;
      display: flex;
      justify-content: start;
      align-items: center;
    }
  }
  .file {
    &-title {
      display: flex;
      align-items: center;
      gap: 1rem;
    }
  }
  .folder-icon {
    fill: #8992a0;
  }
  .folder-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 16px;
    &-button {
      cursor: pointer;
      color: ${propcolors.blackLight};
      padding: 6px 12px;
      white-space: nowrap;
      font-size: 14px;
      border: 1px solid ${propcolors.gray[200]};
      border-radius: 6px;
      transition: background-color 0.2s;
      :hover {
        background-color: ${propcolors.gray[150]};
      }
    }
  }

  .group-action {
    opacity: 0;
    transition: opacity 0.2s;
  }

  .list-item-row {
    max-height: 336px;
    min-height: 72px;
    display: block;
    overflow-y: scroll;
  }

  .list-item-row tr {
    min-height: 48px;
    transition: background-color 0.2s;
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding-left: 24px;
    :hover {
      background-color: ${propcolors.gray[150]};
      .group-action {
        opacity: 1;
      }
    }
    :nth-last-child(1) {
      border-bottom: 1px solid ${propcolors.gray[200]};
    }
    :first-child {
      .mantine-Tooltip-tooltip {
        top: 32px !important;
      }
      .mantine-Tooltip-arrow {
        rotate: 180deg;
        top: -8px !important;
      }
    }
    td {
      display: flex;
      justify-content: start;
      padding: 0;
      border: 0;
      align-items: center;
    }
    .row-text {
      display: flex;
      justify-content: start;
      align-items: center;
    }
  }
`;

export const CollabDriveExplorerTable: React.FC<DriveExplorerProps> = ({
  data,
  mutate,
  path,
  openModal,
  type,
  isRoot,
  sharePortal,
  share,
}) => {
  const { push, query } = useRouter();
  const pushHistory = useCallback(
    (id: number, type: "directory" | "normal") =>
      push({
        pathname:
          type === "directory"
            ? `/drive/collab_files/${id}`
            : `/drive/collab_files/detail/${id}`,
        query: { ...query },
      }),
    [push]
  );
  return (
    <ScrollArea>
      <Styled
        openModal={openModal}
        data={data}
        pushHistory={pushHistory}
        mutate={mutate}
        path={path}
        type={type}
        isRoot={isRoot}
        share={share}
        sharePortal={sharePortal}
      />
    </ScrollArea>
  );
};
