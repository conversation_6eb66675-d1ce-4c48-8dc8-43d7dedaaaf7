import styled from "@emotion/styled";
import {
  ChevronDown16Filled,
  ChevronUp16Filled,
  ChevronUpDown16Filled,
} from "@fluentui/react-icons";
import { Group, UnstyledButton, Text, Table, ScrollArea } from "@mantine/core";
import { keys } from "@mantine/utils";
import { useRouter } from "next/router";
import { useCallback, useEffect, useState } from "react";
import FileItem from "./file-item";
import { propcolors } from "styles/colors";

type customTHProps = {
  onSort: () => void;
  reversed: boolean;
  sorted: boolean;
  children: React.ReactNode;
  size?: string | number;
  color?: string;
};

type DriveExplorerProps = {
  data: DriveExplorerData[];
  path: number | undefined;
  mutate: () => void;
  openModal: (
    mode: DriveModifierMode,
    filename?: string,
    fileID?: number
  ) => void;
  isRoot: boolean;
  type: "directory" | "normal";
  share: () => void;
};

type PresentationProps = {
  className?: string;
  pushHistory: (id: number, type: "directory" | "normal") => void;
} & DriveExplorerProps;

const CustomTH: React.FC<customTHProps> = ({
  onSort,
  reversed,
  sorted,
  children,
  size,
  color,
}) => {
  const Icon = sorted
    ? reversed
      ? ChevronDown16Filled
      : ChevronUp16Filled
    : ChevronUpDown16Filled;
  return (
    <th className="list-files-header-th">
      <UnstyledButton>
        <Group position="apart">
          <Text size={size || "sm"} color={color || "black"}>
            {children}
          </Text>
        </Group>
      </UnstyledButton>
    </th>
  );
};

const filterData = (data: DriveExplorerData[], search: string) => {
  const query = search.toLowerCase().trim();
  return data.filter((item) =>
    keys(data[0]).some((key) =>
      String(item[key]!).toLowerCase().includes(query)
    )
  );
};

const sortData = (
  data: DriveExplorerData[],
  payload: {
    sortBy: keyof DriveExplorerData | null;
    reversed: boolean;
    search: string;
  }
) => {
  const { sortBy } = payload;

  if (!sortBy) {
    return filterData(data, payload.search);
  }

  return filterData(
    [...data].sort((a, b) => {
      if (payload.reversed) {
        return String(b[sortBy]!).localeCompare(String(a[sortBy]));
      }

      return String(a[sortBy]!).localeCompare(String(b[sortBy]));
    }),
    payload.search
  );
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  data,
  pushHistory,
  mutate,
  openModal,
  type,
  isRoot,
  share,
}) => {
  const [search, setSearch] = useState("");
  const [sortedData, setSortedData] = useState(data);
  const [sortBy, setSortBy] = useState<keyof DriveExplorerData | null>(null);
  const [reverseSortDirection, setReverseSortDirection] = useState(false);

  const setSorting = (field: keyof DriveExplorerData) => {
    const reversed = field === sortBy ? !reverseSortDirection : false;
    setReverseSortDirection(reversed);
    setSortBy(field);
    setSortedData(sortData(data, { sortBy: field, reversed, search }));
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.currentTarget;
    setSearch(value);
    setSortedData(
      sortData(data, { sortBy, reversed: reverseSortDirection, search: value })
    );
  };

  useEffect(() => {
    setSorting("name");
  }, []);

  return (
    <Table className={className}>
      <thead className="table-header-wrap">
        <tr className="table-header">
          <CustomTH
            reversed={reverseSortDirection}
            sorted={sortBy === "name"}
            onSort={() => setSorting("name")}
            size={16}
            color="#222222"
          >
            ファイル
          </CustomTH>
        </tr>
      </thead>
      <tbody className="list-item-row">
        {sortedData && sortedData.length > 0 ? (
          <div className="list-files-content">
            {sortedData.map((dataItem) => {
              return (
                <FileItem
                  key={dataItem.id}
                  dataItem={dataItem}
                  pushHistory={pushHistory}
                  openModal={openModal}
                />
              );
            })}
          </div>
        ) : (
          <div className="list-files-empty">
            <p>データがありません</p>
          </div>
        )}
      </tbody>
    </Table>
  );
};

const Styled = styled(Presentation)`
  overflow: none;

  .list-files-empty {
    width: 100%;
    height: 72px;
    display: flex;
    justify-content: center;
    flex-direction: wrap;
    align-items: center;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
  }

  .list-files-header-th {
    padding: 10px 0 !important;
    border: 0;
  }

  .custom-tooltip {
    z-index: 20;
    &-type {
      display: inline-block;
      margin-top: 8px;
    }
  }

  .file-item-name-wrap {
    .mantine-Tooltip-arrow {
      left: 20% !important;
    }

    .mantine-Tooltip-tooltip {
      padding: 16px;
      font-size: 12px;
      line-height: 20.4px;
      font-weight: 400;
    }
  }

  .table-header-wrap {
    display: block;
    box-shadow: unset;
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding: 0 16px 0 24px;
  }

  .group-action {
    opacity: 0;
    transition: opacity 0.2s;
  }

  .list-item-row {
    max-height: 600px;
    overflow-y: scroll;
  }

  .list-files-content {
    width: 100%;
    padding: 16px 0;
    height: auto;
    max-height: 600px;
    overflow-y: scroll;
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(auto-fill, minmax(245px, 1fr));
    transition: background-color 0.2s;
    padding: 16px 24px;
  }

  /* "ファイル"のコマ部分 file-item.tsx */
  .file-item {
    position: relative;
    background-color: white;
    border-radius: 16px;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    padding: 16px;
    gap: 16px;
    border: 1px solid ${propcolors.gray[200]};
    &-thumbnail {
      position: relative;
      width: 100%;
      padding-bottom: 56.25%; // 16:9のアスペクト比を実現
      display: block !important;

      &-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: white;
      }

      img {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
        object-fit: contain;
      }
    }
    &-name {
      p {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        position: relative;
        -webkit-line-clamp: 1;
      }
    }
    &-actions {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 21%;
      justify-content: center;
      background-color: ${propcolors.gray[150]};
      display: flex;
      border-radius: 16px;
      opacity: 0;
    }
    :hover {
      // "file-item"へのホバー時にfile-item-actionsを表示する
      .file-item-actions {
        opacity: 1;
      }
      background-color: ${propcolors.gray[150]};
      .group-action {
        opacity: 1;
      }
    }
  }

  height: auto;
  tr {
    display: grid;
    grid-template-columns: 1fr 1fr;
    th {
      padding: 10px !important;
      height: 72px;
      display: flex;
      justify-content: start;
      align-items: center;
    }
  }

  /* ファイルの上の"フォルダ"の行部分 */
  .file-title {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
`;

export const FilesExplorerTable: React.FC<DriveExplorerProps> = ({
  data,
  mutate,
  path,
  openModal,
  type,
  isRoot,
  share,
}) => {
  const { push, query } = useRouter();
  const pushHistory = useCallback(
    (id: number, type: "directory" | "normal") =>
      push({
        pathname: type === "directory" ? `/drive/${id}` : `/drive/detail/${id}`,
        query: { ...query },
      }),
    [push]
  );
  return (
    <ScrollArea style={{ overflow: "unset" }}>
      <Styled
        openModal={openModal}
        data={data}
        pushHistory={pushHistory}
        mutate={mutate}
        path={path}
        type={type}
        isRoot={isRoot}
        share={share}
      />
    </ScrollArea>
  );
};
