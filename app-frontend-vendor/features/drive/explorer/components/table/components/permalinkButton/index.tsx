import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>16Regular } from "@fluentui/react-icons";
import {
  ActionIcon,
  CopyButton,
  Modal,
  TextInput,
  Tooltip,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";
import { useRef, useState } from "react";
import { ax } from "utils/axios";
import { RiLink } from "@remixicon/react";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";

type DrivePermalinkButtonProps = {
  file_id: number;
};

export const DrivePermalinkButton: React.FC<DrivePermalinkButtonProps> = ({
  file_id,
}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [permalink, setPermalink] = useState<string | null>(null);
  const permalinkInput = useRef<HTMLInputElement>(null);
  const openModalAndFetchPermalink = async () => {
    try {
      const res = await ax.get(`api/v1/drive/file/${file_id}/permalink`);
      setPermalink(res.data);
      open();
    } catch (e) {
      notifications.show({
        title: "パーマリンクを取得できませんでした。",
        message: "エラーが発生しました。",
        icon: <IconNotiFailed />,
        autoClose: 5000,
      });
    }
  };
  return (
    <>
      <ActionIcon
        onClick={openModalAndFetchPermalink}
        className="tooltip-content"
      >
        <Tooltip
          label="リンク取得"
          withArrow
          position="top"
          offset={4}
          transitionProps={{ duration: 200 }}
        >
          <ActionIcon>
            <RiLink size={18} />
          </ActionIcon>
        </Tooltip>
      </ActionIcon>
      <Modal opened={opened} onClose={close} title="パーマリンク取得">
        {permalink && (
          <CopyButton value={permalink}>
            {({ copied, copy }) => (
              <TextInput
                ref={permalinkInput}
                onFocus={() => {
                  permalinkInput.current?.select();
                }}
                value={permalink}
                rightSection={
                  <Tooltip
                    label={copied ? "コピーしました" : "クリップボードにコピー"}
                    withArrow
                    position="right"
                  >
                    <ActionIcon onClick={copy}>
                      {copied ? (
                        <Checkmark16Filled color="green" />
                      ) : (
                        <Copy16Regular />
                      )}
                    </ActionIcon>
                  </Tooltip>
                }
              />
            )}
          </CopyButton>
        )}
      </Modal>
    </>
  );
};
