import Image from "next/image";
import { <PERSON><PERSON><PERSON>, UnstyledButton, Group, ActionIcon } from "@mantine/core";
import { formatDate } from "constants/commons";
import { notifications } from "@mantine/notifications";
import { ax } from "utils/axios";
import { DrivePermalinkButton } from "./permalinkButton";
import getFileSizeWithUnit from "utils/func/getFilesize";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import { useMemo, useState, useEffect } from "react";
import {
  RiDeleteBin6Line,
  RiDownloadLine,
  RiSettings4Line,
  RiUserAddLine,
} from "@remixicon/react";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { DOWNLOAD_IFRAME_REMOVE_DELAY } from "../../../../../../constants/commonSetting";

export default function FileItem({
  dataItem,
  pushHistory,
  openModal,
}: {
  dataItem: DriveExplorerData;
  pushHistory: (id: number, type: "directory" | "normal") => void;
  openModal: (
    mode: DriveModifierMode,
    filename?: string,
    fileID?: number,
    isLimitDownloadAll?: boolean
  ) => void;
}) {
  const isDownloadable = useMemo(
    () => !dataItem?.is_limit_download_all,
    [dataItem]
  );
  const [thumbnailUrl, setThumbnailUrl] = useState("/icons/file.svg");

  useEffect(() => {
    const loadThumbnail = async () => {
      if (!dataItem.thumbnail_path) {
        setThumbnailUrl("/icons/file.svg");
        return;
      }

      try {
        const response = await ax.get(
          `/api/v1/drive/file/${dataItem.id}/thumbnail`
        );
        // レスポンスデータがURLの文字列なので、それを直接使用
        setThumbnailUrl(response.data);
      } catch (error) {
        setThumbnailUrl("/icons/file.svg");
      }
    };

    loadThumbnail();
  }, [dataItem.id, dataItem.thumbnail_path]);

  return (
    <div className="file-item">
      <UnstyledButton
        onClick={() =>
          pushHistory(dataItem.id, dataItem.type as "directory" | "normal")
        }
        className="file-item-name-wrap"
      >
        <Tooltip
          multiline
          className="custom-tooltip"
          w={245}
          label={
            <>
              {dataItem.name}
              <br />
              <span className="custom-tooltip-type">
                種類：
                {dataItem.name.split(".").pop()?.toUpperCase() + "ファイル"}
              </span>
              <br />
              サイズ：{getFileSizeWithUnit(dataItem.size)}
              <br />
              最終更新日：{formatDate(dataItem.updated_at)}
              <br />
              作成日：{formatDate(dataItem.created_at)}
              <br />
            </>
          }
          withArrow
          arrowSize={10}
          zIndex={10}
          offset={22}
          datatype="html"
          position="top"
          transitionProps={{ duration: 200 }}
        >
          <div className="file-item-name">
            <p>{dataItem?.name}</p>
          </div>
        </Tooltip>
      </UnstyledButton>
      <UnstyledButton
        className="file-item-thumbnail"
        onClick={() =>
          pushHistory(dataItem.id, dataItem.type as "directory" | "normal")
        }
      >
        <div className="file-item-thumbnail-wrapper">
          <Image
            src={thumbnailUrl}
            width={0} // 動的サイズのため0を指定
            height={0} // 動的サイズのため0を指定
            alt="file-icons"
            sizes="100%"
          />
        </div>
      </UnstyledButton>
      <div className="file-item-actions">
        <Group
          position="center"
          grow={false}
          spacing="12px"
          className="group-action"
        >
          <ActionIcon
            className="tooltip-content"
            onClick={() => {
              const mode = "share";
              openModal(mode, dataItem.name, dataItem.id);
            }}
          >
            <Tooltip
              label="共有設定"
              withArrow
              position="top"
              transitionProps={{ duration: 200 }}
            >
              <ActionIcon>
                <RiUserAddLine size={20} />
              </ActionIcon>
            </Tooltip>
          </ActionIcon>
          <DrivePermalinkButton file_id={dataItem.id} />
          <ActionIcon
            className="tooltip-content"
            onClick={() => {
              const mode =
                dataItem.type === "directory" ? "renameFolder" : "renameFile";
              openModal(
                mode,
                dataItem.name,
                dataItem.id,
                dataItem.is_limit_download_all
              );
            }}
          >
            <Tooltip
              label="設定"
              withArrow
              position="top"
              transitionProps={{ duration: 200 }}
            >
              <ActionIcon>
                <RiSettings4Line size={20} />
              </ActionIcon>
            </Tooltip>
          </ActionIcon>
          <ActionIcon
            onClick={() => openModal("deleteFile", dataItem.name, dataItem.id)}
            className="tooltip-content"
          >
            <Tooltip
              label="削除する"
              withArrow
              position="top"
              transitionProps={{ duration: 200 }}
            >
              <ActionIcon>
                <RiDeleteBin6Line size={19} />
              </ActionIcon>
            </Tooltip>
          </ActionIcon>

          {dataItem.type !== "directory" && isDownloadable && (
            <ActionIcon
              className="tooltip-content"
              onClick={() => {
                notifications.show({
                  message: "ダウンロードを開始しています・・・",
                  icon: <IconNotiSuccess />,
                });
                ax.get(`api/v1/drive/file/${dataItem.id}/download`)
                  .then((res) => {
                    const downloadUrl = res.data.url;
                    // トリガー用非表示のiframeを作成して、srcにダウンロードURLを設定
                    const iframe = document.createElement("iframe");
                    iframe.style.display = "none";
                    iframe.src = downloadUrl;
                    document.body.appendChild(iframe);
                    // トリガー用のiframeを削除
                    setTimeout(() => {
                      document.body.removeChild(iframe);
                    }, DOWNLOAD_IFRAME_REMOVE_DELAY);
                  })
                  .catch((err) => {
                    notifications.show({
                      message: "ダウンロードに失敗しました",
                      icon: <IconNotiFailed />,
                      autoClose: 3000,
                    });
                  });
              }}
            >
              <Tooltip
                label="ダウンロード"
                withArrow
                position="top"
                transitionProps={{ duration: 200 }}
              >
                <ActionIcon>
                  <RiDownloadLine size={19} />
                </ActionIcon>
              </Tooltip>
            </ActionIcon>
          )}
        </Group>
      </div>
    </div>
  );
}
