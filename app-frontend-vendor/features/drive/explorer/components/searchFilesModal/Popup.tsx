import React, { useEffect, useRef, useState } from "react";
import { RiArrowUpDownLine } from "@remixicon/react";
import styled from "@emotion/styled";
import { propcolors } from "styles/colors";

type Option = {
  value: string;
  label: string;
  sort_by: string;
  order_by: "asc" | "desc";
};

type PopupProps = {
  onSelect: (params: { sort_by: string; order_by: "asc" | "desc" }) => void;
};

const folderOptions: Option[] = [
  {
    value: "relevance",
    label: "一致度の高い順",
    sort_by: "relevance_score",
    order_by: "desc",
  },
  {
    value: "updated_new",
    label: "最終更新日：新しい順",
    sort_by: "updated_at",
    order_by: "desc",
  },
  {
    value: "updated_old",
    label: "最終更新日：古い順",
    sort_by: "updated_at",
    order_by: "asc",
  },
  {
    value: "created_new",
    label: "作成日：新しい順",
    sort_by: "created_at",
    order_by: "desc",
  },
  {
    value: "created_old",
    label: "作成日：古い順",
    sort_by: "created_at",
    order_by: "asc",
  },
];

const Popup: React.FC<PopupProps> = ({ onSelect }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const popupRef = useRef<HTMLDivElement | null>(null);
  const iconRef = useRef<HTMLDivElement | null>(null);

  const togglePopup = () => {
    setIsOpen(!isOpen);
  };

  // popup外をクリックするとpopupを閉じる
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popupRef.current &&
        !popupRef.current.contains(event.target as Node) &&
        iconRef.current &&
        !iconRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleOptionClick = (option: Option) => {
    setSelectedOption(option.value);
    // 親コンポーネントに sort_by と order_by を含むオブジェクトを渡す
    onSelect({ sort_by: option.sort_by, order_by: option.order_by });
    setIsOpen(false);
  };

  return (
    <>
      <div
        ref={iconRef}
        style={{ display: "inline-flex", alignItems: "center" }}
      >
        <RiArrowUpDownLine
          style={{ color: "gray", cursor: "pointer" }}
          onClick={togglePopup}
        />
      </div>
      {isOpen && (
        <div ref={popupRef} style={popupStyles}>
          {folderOptions.map((option) => (
            <OptionItem
              key={option.value}
              isSelected={selectedOption === option.value}
              onClick={() => handleOptionClick(option)}
            >
              {option.label}
            </OptionItem>
          ))}
        </div>
      )}
    </>
  );
};

export default Popup;

const popupStyles: React.CSSProperties = {
  position: "absolute",
  top: "40px",
  right: "0",
  backgroundColor: "white",
  border: "1px solid #ccc",
  borderRadius: "8px",
  padding: "16px 0px",
  boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.2)",
  zIndex: 1000,
};

const OptionItem = styled.div<{ isSelected: boolean }>`
  padding: 16px;
  margin: 0px;
  width: auto;
  white-space: nowrap;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  font-family: Inter;
  text-align: left;
  background-color: ${({ isSelected }) =>
    isSelected ? propcolors.gray[200] : "white"};
  &:hover {
    background-color: ${propcolors.backgroundHover};
  }
`;
