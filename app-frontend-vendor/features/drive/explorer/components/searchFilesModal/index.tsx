import styled from "@emotion/styled";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, TextInput } from "@mantine/core";
import { UseFormReturnType, useForm } from "@mantine/form";
import { useEffect, useState } from "react";
import { propcolors } from "styles/colors";
import { RiSearchLine, RiFolderLine, RiFolderUserLine } from "@remixicon/react";
import { ax } from "utils/axios";
import FileIcon from "components/icons/FileIcon";
import Link from "next/link";
import { debounce } from "lodash";
import Popup from "./Popup";

type PresentationProps = {
  className?: string;
  isModalOpen: boolean;
  openModal: () => void;
  closeModal: () => void;
  form: UseFormReturnType<{ query: string }>;
};

type FileItem = {
  id: number;
  uuid: string;
  vendor_id: string;
  category: string;
  type: string;
  parent: string;
  name: string;
  size: number;
  s3_path: string;
  portal_id: string;
  created_at: string;
  updated_at: string;
  depth: number;
  portal_name: string;
  parent_name: string;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  isModalOpen,
  openModal,
  closeModal,
  form,
}) => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [folders, setFolders] = useState<FileItem[]>([]);
  const [isSearchExecuted, setIsSearchExecuted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const debounceMs = 200;
  const [sortParams, setSortParams] = useState({
    sort_by: "",
    order_by: "asc" as "asc" | "desc",
  });

  // 検索フォームに1文字以上入力し、さらに入力が0.2秒止まるとAPIを叩く
  useEffect(() => {
    const debouncedFetch = debounce(() => {
      if (form.values.query.length >= 1) {
        fetchSearchedFiles();
      }
    }, debounceMs);
    debouncedFetch();

    // 検索フォームが更新されるごとに古いタイマーをクリア
    return () => {
      debouncedFetch.cancel();
    };
  }, [form.values.query, sortParams]);

  // Popupモーダルからparamsを受け取ってセットする
  const handleSelect = (params: {
    sort_by: string;
    order_by: "asc" | "desc";
  }) => {
    setSortParams(params);
  };

  // Popupモーダルから受け取ったparamsを使ってAPIを叩く
  const fetchSearchedFiles = () => {
    setIsLoading(true);
    setIsSearchExecuted(false);
    ax.get("/api/v1/drive/portals/files/search", {
      params: {
        q: form.values.query,
        sort_by: sortParams.sort_by,
        order_by: sortParams.order_by,
      },
    })
      .then((response) => {
        setFiles(response.data.files);
        setFolders(response.data.folders);
      })
      .catch((error) => {})
      .finally(() => {
        setIsSearchExecuted(true);
        setIsLoading(false);
      });
  };

  return (
    <div className={className}>
      <Button
        color="gray"
        variant="outline"
        onClick={openModal}
        className="search-files-button"
      >
        <RiSearchLine style={{ color: "red", marginRight: "8px" }} />
        ポータルを検索
      </Button>
      <Modal
        className={className}
        opened={isModalOpen}
        onClose={closeModal}
        withCloseButton={false}
        padding={0}
        size="60%"
        styles={{
          content: {
            minWidth: "640px",
          },
        }}
      >
        <div className="modal-wrap">
          <div className="modal-header">
            <TextInput
              placeholder="ポータルを検索…"
              {...form.getInputProps("query")}
              icon={<RiSearchLine style={{ color: "gray" }} />}
              rightSection={<Popup onSelect={handleSelect} />}
              style={{ width: "100%" }}
            />
          </div>
          <div className="modal-content">
            {isLoading ? (
              /* ロード中 */
              <>
                <div className="modal-content-loadicon">
                  <Loader size="sm" color="gray" />
                </div>
              </>
            ) : (
              /* ロード以外 */
              <>
                {!isSearchExecuted ? (
                  // 検索条件未入力
                  <div className="modal-content-placeholder">
                    ファイル名、フォルダ名を入力して検索
                  </div>
                ) : files.length === 0 && folders.length === 0 ? (
                  // 検索条件入力済
                  /* 検索結果なし */
                  <div className="modal-content-noresult">
                    該当するデータがありません
                  </div>
                ) : (
                  /* 検索結果あり */
                  <>
                    <div className="modal-content-files">
                      <p>ファイル</p>
                      {files.length === 0 ? (
                        <div className="modal-content-noresult">
                          該当するファイルがありません
                        </div>
                      ) : (
                        <ul>
                          {files.map((file) => (
                            <li
                              key={file.uuid}
                              className="modal-content-files-item"
                            >
                              <Link
                                href={file.category == "collaborative" ? `/drive/collab_files/detail/${file.id}?portal_id=${file.portal_id}` : `/drive/detail/${file.id}?portal_id=${file.portal_id}`}
                                onClick={closeModal}
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: "8px",
                                  color: "inherit",
                                }}
                              >
                                <FileIcon fileName={file.name} />
                                <span className="modal-content-files-item-name">
                                  {file.name}
                                </span>
                                <span className="modal-content-files-item-breadcrumb">
                                  ― {file.portal_name}
                                  {file.depth >= 2 ? " / ... " : ""}
                                  {file.parent_name
                                    ? ` / ${file.parent_name}`
                                    : ""}
                                </span>
                              </Link>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                    <div className="modal-content-files">
                      <p>フォルダ</p>
                      {folders.length === 0 ? (
                        <div className="modal-content-noresult">
                          該当するフォルダがありません
                        </div>
                      ) : (
                        <ul>
                          {folders.map((folder) => (
                            <li
                            key={folder.uuid}
                            className="modal-content-files-item"
                            >
                              <Link
                                href={folder.category == "collaborative" ? `/drive/collab_files/${folder.id}?portal_id=${folder.portal_id}` : `/drive/${folder.id}?portal_id=${folder.portal_id}`}
                                onClick={closeModal}
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: "8px",
                                  color: "inherit",
                                }}
                              >
                                {folder.category == "collaborative" ? <RiFolderUserLine /> : <RiFolderLine />}
                                <span className="modal-content-files-item-name">
                                  {folder.name}
                                </span>
                                <span className="modal-content-files-item-breadcrumb">
                                  ― {folder.portal_name}
                                  {folder.depth >= 2 ? " / ... " : ""}
                                  {folder.parent_name
                                    ? ` / ${folder.parent_name}`
                                    : ""}
                                </span>
                              </Link>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </Modal>
    </div>
  );
};

const Styled = styled(Presentation)`
  color: ${propcolors.blackLight};
  boder-radius: 8px;

  .search-files-button {
    border: 1px solid ${propcolors.gray[200]};
    min-height: 42px;
    background-color: white;
    color: #222222;
    padding: 0px 12px;
    font-size: 14px;
    font-weight: 500 !important;
    font-family: "Inter", "system-ui";
    border: 1px solid #e8eaed;
    border-radius: 6px;
  }

  .modal {
    &-wrap {
      background: ${propcolors.white};
    }
    &-title {
      color: #666666;
      font-size: 12px;
      font-weight: 400;
    }
    &-header {
      color: ${propcolors.blackLight};
      padding: 16px 16px;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      border-bottom: 1px solid ${propcolors.gray[200]};
      input {
        height: 48px;
        font-weight: 400;
      }
    }
    &-content {
      flex-direction: column;
      display: flex;
      height: 60vh;
      &-input {
        padding: 24px;
        input {
          margin-top: 8px;
          height: 48px;
        }
      }
      &-loadicon,
      &-noresult,
      &-placeholder {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        font-size: 16px;
        color: ${propcolors.gray[500]};
        text-align: center;
      }
      &-files {
        padding: 8px 16px;
        ul {
          list-style: none;
          padding: 8px 8px;
          li {
            display: flex;
            align-items: center;
            gap: 2px;
            padding: 8px 4px;
          }
        }
        &-item {
          display: flex;
          align-items: center;
          gap: 8px;
          max-width: 100%;
          a {
            width: 100%;
            overflow: hidden;
          }
          svg {
            width: 32px;
            height: 32px;
            flex-shrink: 0;
          }
          &-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80%;
          }
          &-breadcrumb {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            min-width: 20%;
            color: ${propcolors.gray[500]};
          }
        }
      }
    }
  }
`;

export const SearchFilesModal: React.FC = () => {
  const [isModalOpen, toggleModal] = useState<boolean>(false);
  const closeModal = () => toggleModal(false);
  const openModal = () => toggleModal(true);

  const searchForm = useForm<{ query: string }>({
    initialValues: {
      query: "",
    },
    validateInputOnChange: true,
    validate: {},
  });

  return (
    <Styled
      isModalOpen={isModalOpen}
      openModal={openModal}
      closeModal={closeModal}
      form={searchForm}
    />
  );
};
