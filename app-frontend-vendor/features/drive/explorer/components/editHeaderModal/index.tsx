import styled from "@emotion/styled";
import { <PERSON><PERSON>, Text, Modal, Loader } from "@mantine/core";
// import { Modal } from "components/Modals/foundation";
import { UseFormReturnType, useForm } from "@mantine/form";
import { Dropzone } from "@mantine/dropzone";
import { notifications } from "@mantine/notifications";
import { useRef, useState } from "react";
import { ax } from "utils/axios";
import { propcolors } from "styles/colors";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";

type PresentationProps = {
  className?: string;
  isModalOpen: boolean;
  openModal: () => void;
  closeModal: () => void;
  onSubmit: () => void;
  isUploading: boolean;
  form: UseFormReturnType<{ file: File | null }>;
};

type DriveEditPortalListProps = {
  mutate: () => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  isModalOpen,
  openModal,
  closeModal,
  onSubmit,
  isUploading,
  form,
}) => {
  const openRef = useRef<() => void>(() => {});
  return (
    <div className={className}>
      <Button
        color="gray"
        variant="outline"
        onClick={openModal}
        className="upload-file-button"
      >
        ヘッダー設定
      </Button>
      <Modal
        className={className}
        opened={isModalOpen}
        onClose={closeModal}
        withCloseButton={false}
        padding={0}
        size={640}
        maw={640}
        miw={320}
      >
        <div className="modal-wrap">
          <div className="modal-header">
            <p>ヘッダー画像を編集</p>
          </div>
          <div className="modal-content">
            <div className="modal-content-input">
              {isUploading ? (
                <div className="modal-loading-container">
                  <Text size="sm" mt="md" color="dimmed">
                    ファイルをアップロード中...
                  </Text>
                  <Loader size="md" />
                </div>
              ) : (
                <>
                  <div className="modal-title">プロフィール画像</div>
                  {/* <ModalFormLabel>
                    <FileInput
                      label="ファイルを選択"
                      {...form.getInputProps("file")}
                    />
                  </ModalFormLabel> */}
                  <Dropzone
                    openRef={openRef}
                    accept={["image/png", "image/jpeg", "image/jpg"]}
                    multiple={false}
                    onDrop={(files) => form.setFieldValue("file", files[0])}
                    className="drop-zone"
                    activateOnClick={false}
                    {...form.getInputProps("file")}
                    h={117}
                    bg="#F7F8F9"
                    radius={8}
                    styles={{ inner: { pointerEvents: "all" } }}
                  >
                    <div className="drop-zone-inner">
                      <div className="drop-zone-title">
                        ここにドラッグ&ドロップまたは
                      </div>
                      <Button
                        mih={32}
                        h={32}
                        maw={129}
                        w={129}
                        onClick={() => openRef.current()}
                        className="upload-file-button"
                      >
                        <Text truncate="end">
                          {form.values.file
                            ? form.values.file.name
                            : "ファイルを選択"}
                        </Text>
                      </Button>
                      <div className="drop-zone-title">
                        推奨サイズ(1500px × 500px)
                      </div>
                    </div>
                  </Dropzone>
                </>
              )}
            </div>
            <div className="modal-content-actions">
              <Button
                className="modal-button modal-button-cancel"
                type="button"
                onClick={closeModal}
                disabled={isUploading}
              >
                キャンセル
              </Button>
              <Button
                className="modal-button modal-button-submit"
                type="button"
                onClick={onSubmit}
                disabled={isUploading}
              >
                {isUploading ? "アップロード中..." : "保存する"}
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

const Styled = styled(Presentation)`
  color: ${propcolors.blackLight};
  boder-radius: 8px;

  .upload-file-button {
    border: 1px solid ${propcolors.gray[200]};
    min-height: 42px;
    background-color: white;
    color: #222222;
    padding: 0px 12px;
    font-size: 14px;
    font-weight: 500 !important;
    font-family: "Inter", "system-ui";
    border: 1px solid #e8eaed;
    border-radius: 6px;
  }

  .drop-zone {
    display: flex;
    height: 150px;
    border-radius: 8px;
    border: 0.12px dashed #e8eaed;
    background-color: #f7f8f9;
    margin-top: 8px;
    justify-content: center;
    font-family: "Inter", "system-ui";
    align-items: center;
    flex-direction: column;
    &-title {
      font-size: 14px;
      color: #666666;
    }
    &-inner {
      display: flex;
      justify-content: center;
      gap: 16px;
      align-items: center;
      flex-direction: column;
    }
  }

  .modal {
    &-wrap {
      background: ${propcolors.white};
    }
    &-title {
      color: #666666;
      font-size: 12px;
      font-weight: 400;
    }
    &-header {
      color: ${propcolors.blackLight};
      padding: 16px 40px;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      border-bottom: 1px solid ${propcolors.gray[200]};
    }
    &-loading-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16px;
      padding: 24px;
    }
    &-content {
      flex-direction: column;
      display: flex;
      &-input {
        padding: 24px;
        input {
          margin-top: 8px;
          height: 48px;
        }
      }

      &-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 24px 40px;
        gap: 24px;
        background: #f7f8f9;
        border-top: 1px solid ${propcolors.gray[200]};
      }
    }
    &-button {
      width: 284px;
      height: 42px;
      font-size: 14px;
      &-cancel {
        background-color: ${propcolors.greyDefault};
      }
      &-submit {
        background-color: ${propcolors.blackLight};
      }
    }
  }
`;

export const DriveEditHeaderModal: React.FC<DriveEditPortalListProps> = ({
  mutate,
}) => {
  const [isModalOpen, toggleModal] = useState<boolean>(false);
  const closeModal = () => toggleModal(false);
  const openModal = () => toggleModal(true);
  const [isUploading, setIsUploading] = useState<boolean>(false);

  const uploadForm = useForm<{ file: File | null }>({
    initialValues: {
      file: null,
    },
    validateInputOnChange: true,
    validate: {
      file: (value) => {
        if (value!.size >= 50000000) {
          return "size";
        }
        if (value?.type !== "image/jpeg" && value?.type !== "image/png") {
          return "type";
        }
        return null;
      },
    },
  });

  const onSubmit = () => {
    if (!uploadForm.values.file || uploadForm.errors.file) {
      switch (uploadForm.errors.file) {
        case "size":
          return notifications.show({
            title: "エラー",
            message: "50MB以下のファイルを選択してください。",
            icon: <IconNotiFailed />,
          });
        case "type":
          return notifications.show({
            title: "エラー",
            message: "画像ファイルはpngまたはjpegで選択してください。",
            icon: <IconNotiFailed />,
          });
        default:
          return notifications.show({
            title: "エラー",
            message: "ヘッダーを登録してください",
            icon: <IconNotiFailed />,
          });
      }
    }
    setIsUploading(true);
    const formData = new FormData();
    formData.append("header", uploadForm.values.file!);
    ax.post("api/v1/drive/header", formData)
      .then((res) => {
        notifications.show({
          title: "成功",
          message: "ヘッダーを追加しました",
          icon: <IconNotiSuccess />,
        });
        mutate();
        closeModal();
      })
      .catch((error) => {
        notifications.show({
          title: "エラー",
          message: "ヘッダーを追加できませんでした",
          icon: <IconNotiFailed />,
        });
      })
      .finally(() => {
        setIsUploading(false);
      });
  };

  return (
    <Styled
      isModalOpen={isModalOpen}
      isUploading={isUploading}
      openModal={openModal}
      closeModal={closeModal}
      onSubmit={onSubmit}
      form={uploadForm}
    />
  );
};
