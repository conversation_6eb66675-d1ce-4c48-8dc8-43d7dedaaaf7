import styled from "@emotion/styled";
import { TextInput, Button } from "@mantine/core";
import { UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { ax } from "utils/axios";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";

type PresentationProps = {
  className?: string;
  handleSubmit: () => void;
  form: UseFormReturnType<DriveNewPortalForm>;
  close: () => void;
};

type DriveNewPortalProps = {
  close: () => void;
  mutate?: () => void;
};

type DriveNewPortalForm = {
  name: string;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  handleSubmit,
  form,
  close,
}) => {
  return (
    <div className={className}>
      <TextInput
        label="ポータル名"
        className="input"
        placeholder="ポータル名"
        {...form.getInputProps("name")}
      />

      <div className="button-group">
        <Button type="button" className="button button-close" onClick={close}>
          キャンセル
        </Button>
        <Button
          type="submit"
          className="button button-submit"
          onClick={handleSubmit}
        >
          追加する
        </Button>
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  .input {
    padding: 24px 24px 0 24px;
    input {
      margin-top: 8px;
      height: 48px;
    }
  }

  .button-group {
    display: flex;
    padding: 24px;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
  }

  .button {
    width: 50%;
    height: 42px;
    border-radius: 8px;
    &-submit {
      background: #222222;
      color: white;
    }
    &-close {
      background: #8992a0;
      color: white;
    }
  }
`;

export const DriveNewPortalModal: React.FC<DriveNewPortalProps> = ({
  close,
  mutate,
}) => {
  const uploadForm = useForm<DriveNewPortalForm>({
    initialValues: {
      name: "",
    },
    validateInputOnChange: true,
    validate: {
      name: (value) => (value === "" ? "ポータル名を入力してください" : null),
    },
  });
  const handleSubmit = () => {
    if (!uploadForm.isValid()) return;
    ax.post(`api/v1/drive/portals/`, { name: uploadForm.values.name })
      .then((res) => {
        notifications.show({
          title: "成功",
          message: "ファイルを追加しました",
          icon: <IconNotiSuccess />,
        });
        mutate && mutate();
        close();
      })
      .catch((error) => {
        notifications.show({
          title: "エラー",
          message: "ファイルを追加できませんでした",
          icon: <IconNotiFailed />,
        });
      });
  };
  return <Styled handleSubmit={handleSubmit} form={uploadForm} close={close} />;
};
