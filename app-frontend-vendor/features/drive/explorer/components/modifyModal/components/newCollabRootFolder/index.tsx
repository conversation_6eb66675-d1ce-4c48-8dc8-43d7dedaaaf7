import styled from "@emotion/styled";
import { Button, TextInput } from "@mantine/core";
import { type UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { useRouter } from "next/router";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import useSWR from "swr";
import { ax } from "utils/axios";
import { SelectShare } from "./find-share";

type PresentationProps = {
  className?: string;
  form: UseFormReturnType<{ name: string; partner_id: number }>;
  partnerList?: LinkActivePartner[];
  handleSubmit: () => void;
  close: () => void;
};

type DriveNewCollabFolderModal = {
  path: number | undefined;
  close: () => void;
  mutate: () => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  form,
  partnerList,
  handleSubmit,
  close,
}) => {
  const setSelectedPartnerId = (partnerId: number) => {
    form.setValues({
      partner_id: partnerId,
    });
  };

  return (
    <form
      className={className}
      onSubmit={(e) => {
        e.preventDefault();
        handleSubmit();
      }}
    >
      <TextInput
        label="共同編集フォルダ名"
        className="newcollaborativefolder-input"
        required
        {...form.getInputProps("name")}
      />
      <SelectShare
        data={partnerList}
        placeholder="パートナー名を入力"
        setCurrentPartnerId={setSelectedPartnerId}
      />
      <div className="button-group">
        <Button type="button" className="button button-close" onClick={close}>
          キャンセル
        </Button>
        <Button
          type="submit"
          className="button button-submit"
          disabled={form.isValid() ? false : true}
        >
          追加する
        </Button>
      </div>
    </form>
  );
};

const Styled = styled(Presentation)`
  padding: 0px 24px;
  .button-group {
    display: flex;
    padding: 24px;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
  }
  .button {
    width: 50%;
    height: 42px;
    border-radius: 8px;
    &-submit {
      background: #222222;
      color: white;
    }
    &-close {
      background: #8992a0;
      color: white;
    }
  }
  .newcollaborativefolder-input {
    padding: 24px 0px;
    input {
      height: 48px;
      margin-top: 8px;
    }
  }
`;

export const DriveNewCollabRootFolderModal: React.FC<
  DriveNewCollabFolderModal
> = ({ path, close, mutate }) => {
  const { query } = useRouter();
  const { portal_id } = query;
  const newCollabFolderForm = useForm<{ name: string; partner_id: number }>({
    initialValues: {
      name: "",
      partner_id: -1,
    },
    validateInputOnChange: true,
    validate: {
      name: (value) => {
        if (value.length < 2)
          return "共同編集フォルダ名は2文字以上で入力してください";
        if (value.match(/[/.*+?^${}()|<>\\=!¥:;@]/g))
          return "特殊文字は利用できません";
        return null;
      },
      partner_id: (value) => {
        if (!value || value <= 0)
          return "共同編集フォルダ名は2文字以上で入力してください";
        return null;
      },
    },
  });
  const partnerListFetcher = () =>
    ax
      .get("api/v1/partners/link_active")
      .then((res) => res.data as LinkActivePartner[]);

  const { data: partnerList, mutate: mutatePartnerList } = useSWR(
    "api/v1/partners/link_active",
    partnerListFetcher,
  );

  const handleSubmit = () => {
    if (newCollabFolderForm.isValid()) {
      ax.post(`api/v1/drive/collab_file/${path ? path : 0}`, {
        type: "directory",
        name: newCollabFolderForm.values.name,
        partner_id: newCollabFolderForm.values.partner_id,
        portal_id: portal_id!,
      })
        .then((res) => {
          notifications.show({
            message: "共同編集フォルダを作成しました",
            icon: <IconNotiSuccess />,
          });
          mutate();
          close();
        })
        .catch((error) => {
          let errorTitle = "エラー";
          let errorMessage = "共同編集フォルダを作成できませんでした";
          if (
            error.response.data?.message ===
            "Error: The file or directory name already exists."
          ) {
            errorTitle = "共同編集フォルダを作成できませんでした";
            errorMessage =
              "フォルダーはすでに存在します。別の名称を入力してください。";
          }
          notifications.show({
            title: errorTitle,
            message: errorMessage,
            icon: <IconNotiFailed />,
          });
        });
    }
  };
  return (
    <Styled
      handleSubmit={handleSubmit}
      form={newCollabFolderForm}
      partnerList={partnerList}
      close={close}
    />
  );
};
