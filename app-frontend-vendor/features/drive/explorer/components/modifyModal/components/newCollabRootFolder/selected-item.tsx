import { propcolors } from "styles/colors";

interface SelectItemProps {
  value: number;
  label: string;
  setValue: (item: number) => void;
}

export const SelectItemComponent = ({
  label,
  value,
  setValue,
}: SelectItemProps) => {
  return (
    <div
      css={{
        display: 'flex',
        height: 'auto',
        minHeight: '48px',
        padding: '8px 16px',
        gap: '16px',
        cursor: 'pointer',
        alignItems: 'center',
        '&:hover': {
          backgroundColor: propcolors.gray[150],
        },
      }}
      onClick={() => setValue(value)}
    >
      <p css={{ fontSize: 14 }}>{label}</p>
    </div>
  );
};
