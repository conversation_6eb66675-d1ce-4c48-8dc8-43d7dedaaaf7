import { propcolors } from "styles/colors";
import React, { useEffect, useState } from "react";
import { Loader } from "@mantine/core";
import { SelectItemComponent } from "./selected-item";
import { RiArrowDropDownLine, RiArrowDropUpLine } from "@remixicon/react";

interface SelectShareProps {
  data?: LinkActivePartner[];
  placeholder: string;
  setCurrentPartnerId: (item: number) => void;
}

export const SelectShare = ({
  data,
  placeholder,
  setCurrentPartnerId,
}: SelectShareProps) => {
  const [dataShow, setDataShow] = useState<LinkActivePartner[] | undefined>([]);
  const [showToggle, setShowToggle] = useState<boolean>(false);
  const [value, setValue] = useState<string>("");

  useEffect(() => {
    const filterData = data?.filter((item) =>
      item.partner_name.toLowerCase().includes(value.toLowerCase())
    );
    setDataShow(filterData);
  }, [data, value]);

  const onChangeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    const filterData = data?.filter((item) =>
      item.partner_name.toLowerCase().includes(e.target.value.toLowerCase())
    );
    setDataShow(filterData);
    setShowToggle(true);
    setCurrentPartnerId(0);
    setValue(e.target.value);
  };

  const handlerOnclickInput = () => {
    setShowToggle(!showToggle);
  };

  const handleSetValue = (value: number) => {
    setValue(data?.find((d) => d.vendor_linked_partner_id == value)?.partner_name ?? "");
    setShowToggle(false);
    setCurrentPartnerId(value);
  };

  return (
    <div
      css={{
        width: '100%',
        paddingBottom: '24px',
      }}
    >
      <label
        css={{
          display: 'inline-block',
          fontSize: '0.875rem',
          fontWeight: 500,
          color: '#212529',
          wordBreak: 'break-word',
          cursor: 'default',
        }}
      >
        共同編集パートナーを選択
        <span
          css={{
            color: '#fa5252',
          }}
        >
          *
        </span>
      </label>
      <label
        css={{
          paddingBottom: '8px',
          display: 'inline-block',
          fontSize: '0.875rem',
          color: '#fa5252',
        }}
      >
        このフォルダへのファイルのアップロード、および配下のフォルダ追加が可能となります
      </label>
      <div
        css={{
          display: 'block',
          padding: '0',
          borderRadius: '8px',
          border: `1px solid ${propcolors.gray[200]}`,
        }}
      >
        <div
          css={{
            position: 'relative',
            width: '100%',
          }}
        >
          <input
            type="text"
            placeholder={placeholder}
            css={{
              width: '100%',
              height: '48px',
              padding: '0 32px 0 16px',
              fontSize: '14px',
              background: 'none',
              border: 'none',
              color: '#212529',
              '::placeholder': {
                color: '#8992a0',
              },
              ':disabled': {
                background: '#f1f3f5',
                '::placeholder': {
                  color: '#ced4da',
                },
              },
            }}
            onChange={(e) => onChangeHandler(e)}
            value={value}
            onClick={handlerOnclickInput}
            disabled={!data}
          />
          <div
            css={{
              position: 'absolute',
              top: '14px',
              right: '8px',
              display: 'flex',
              flexDirection: 'column',
              color: propcolors.gray[500],
              cursor: 'pointer',
            }}
            onClick={handlerOnclickInput}
          >
            {showToggle ? <RiArrowDropUpLine /> : <RiArrowDropDownLine /> }
          </div>
        </div>

        {!data && (
          <div
            css={{
              width: '100%',
              height: '144px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Loader size="lg" />
          </div>
        )}
        
        <div
          css={{
            maxHeight: showToggle ? '200px' : '0',
            overflow: 'hidden',
            transition: 'max-height 0.2s ease-in-out',
            borderTop: 'none',
            overflowY: 'scroll',
            scrollbarWidth: 'none',
            '-ms-overflow-style': 'none',
            '::-webkit-scrollbar': {
              display: 'none',
            },
          }}
        >
          {dataShow?.map((item) => (
            <SelectItemComponent
              key={item.vendor_linked_partner_id}
              value={item.vendor_linked_partner_id}
              label={item.partner_name}
              setValue={handleSetValue}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
