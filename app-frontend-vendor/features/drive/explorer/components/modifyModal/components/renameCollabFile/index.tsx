import styled from "@emotion/styled";
import { But<PERSON>, Group, Text, TextInput } from "@mantine/core";
import { Dropzone } from "@mantine/dropzone";
import { type UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import IconCheck from "public/icons/check.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import { useRef } from "react";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import { getApiErrorMessage } from "utils/values/errorMessages";

type PresentationProps = {
  className?: string;
  handleSubmit: () => void;
  form: UseFormReturnType<{ name: string; thumbnail: File | null }>;
  close: () => void;
};

type DriveRenameModal = {
  path: number | undefined;
  fileName: string;
  close: () => void;
  mutate: () => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  form,
  handleSubmit,
  close,
}) => {
  const openRef = useRef<() => void>(() => {});
  const checkIcon = <IconCheck color={propcolors.white} />;
  return (
    <div className={className}>
      <TextInput
        className="input"
        label="ファイル名"
        required
        {...form.getInputProps("name")}
      />
      <div className="modal-thumbnail">
        <div className="modal-thumbnail-title">サムネイルを選択</div>
        <Dropzone
          openRef={openRef}
          multiple={false}
          onDrop={(thumbnails) =>
            form.setFieldValue("thumbnail", thumbnails[0])
          }
          className="drop-zone-thumbnail"
          activateOnClick={false}
          {...form.getInputProps("thumbnail")}
          h={117}
          bg="#F7F8F9"
          radius={8}
          styles={{ inner: { pointerEvents: "all" } }}
          accept={["image/png", "image/jpeg", "image/jpg"]}
        >
          <Group className="drop-zone-inner" position="center">
            <div className="drop-zone-title">ここにドラッグ&ドロップまたは</div>
            <Button
              mih={32}
              h={32}
              maw={129}
              w={129}
              onClick={() => openRef.current()}
              className="upload-file-button"
            >
              <Text truncate="end">
                {form.values.thumbnail
                  ? form.values.thumbnail.name
                  : "ファイルを選択"}
              </Text>
            </Button>
            <div className="drop-zone-title">推奨サイズ(1280px × 720px)</div>
          </Group>
        </Dropzone>
      </div>
      <div className="button-group">
        <Button type="button" className="button button-close" onClick={close}>
          キャンセル
        </Button>
        <Button
          type="submit"
          className="button button-submit"
          onClick={handleSubmit}
        >
          保存する
        </Button>
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  .input {
    padding: 24px 24px 0 24px;
    margin-bottom: 8px;

    .mantine-TextInput-label {
      font-family: "Inter", "system-ui";
      font-size: 14px;
      font-weight: 400;
      color: #666666;
    }
    input {
      margin-top: 8px;
      height: 48px;
    }
  }

  .upload-file-button {
    border: 1px solid ${propcolors.gray[200]};
    min-height: 42px;
    background-color: white;
    color: #222222;
    font-size: 14px;
    padding: 0 12px;
    font-weight: 500 !important;
    font-family: "Inter", "system-ui";
    border: 1px solid #e8eaed;
    border-radius: 6px;
  }

  .drop-zone-thumbnail {
    display: flex;
    height: 150px;
    border-radius: 8px;
    border: 0.12px dashed #e8eaed;
    background-color: #f7f8f9;
    margin-top: 8px;
    justify-content: center;
    font-family: "Inter", "system-ui";
    align-items: center;
    flex-direction: column;
  }

  .drop-zone-inner {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .drop-zone-title {
    font-size: 14px;
    color: #666666;
  }

  .modal-thumbnail {
    padding: 24px;
    &-title {
      font-size: 14px;
      color: #666666;
      &.required {
        position: relative;
        &::after {
          content: "*";
          color: #fa5252;
          font-size: 12px;
          line-height: 1;
          margin-left: 4px;
        }
      }
      &-thumbnail {
        font-size: 14px;
        color: #666666;
        padding: 8px 0px 0px 0px;
      }
    }
  }
  .file-download {
    padding: 0 24px;
    .file-download-wrap {
      border-top: 1px solid #e8eaed;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 17.5px 16px;

      .file-download-label {
        font-family: Inter;
        font-size: 14px;
        font-weight: 400;
        line-height: 16.94px;
        text-align: left;
        color: #222222;
      }
      .download-switch {
        cursor: pointer;
        input:checked + .mantine-Switch-track {
          background-color: ${propcolors.black};
          border-color: ${propcolors.black};
          .mantine-Switch-thumb {
            background-color: ${propcolors.white};
          }
        }
        .mantine-Switch-track {
          height: 20px;
          background-color: ${propcolors.white};
          border-color: #e8eaed;
          .mantine-Switch-thumb {
            background-color: ${propcolors.switchOff};
          }
        }
      }
    }
  }

  .button-group {
    display: flex;
    padding: 16px 24px 24px;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
  }
  .button {
    width: 50%;
    height: 42px;
    border-radius: 8px;
    &-submit {
      background: #222222;
      color: white;
    }
    &-close {
      background: #8992a0;
      color: white;
    }
  }
`;

export const DriveRenameCollabFileModal: React.FC<DriveRenameModal> = ({
  path,
  close,
  mutate,
  fileName,
}) => {
  const lastIndex = fileName.lastIndexOf(".");
  const filenames = [
    fileName.substring(0, lastIndex), // ファイル名部分
    fileName.substring(lastIndex + 1), // 拡張子部分
  ];
  const MAX_THUMBNAIL_SIZE = 3 * 1024 * 1024; // 3MB
  const renameForm = useForm<{ name: string; thumbnail: File | null }>({
    initialValues: {
      name: filenames[0],
      thumbnail: null,
    },
    validateInputOnChange: true,
    validate: {
      name: (value) => {
        if (value.length < 2) return "は2文字以上で入力してください";
        if (value.match(/[/*+?^${}()|<>\\=!¥:;@]/g))
          return "特殊文字は利用できません";
        return null;
      },
    },
  });

  const handleSubmit = async () => {
    if (renameForm.values.name.length < 2) {
      return notifications.show({
        title: "名前が短すぎます",
        message: "名前は2文字以上である必要があります",
        icon: <IconNotiFailed />,
      });
    }

    // ファイル名の変更を処理
    if (renameForm.values.name !== filenames[0]) {
      await handleRename();
    }

    // サムネイルが選択されている場合、サムネイルを更新
    if (renameForm.values.thumbnail) {
      if (renameForm.values.thumbnail.size >= MAX_THUMBNAIL_SIZE) {
        return notifications.show({
          title: "エラー",
          message: "3MB以下のサムネイルを選択してください。",
          icon: <IconNotiFailed />,
        });
      } else {
        await handleThumbnailUpdate();
      }
    }

    mutate();
    close();
  };

  const handleRename = async () => {
    ax.put(`api/v1/drive/collab_file/${path}`, {
      name: filenames[1]
        ? renameForm.values.name + "." + filenames[1]
        : renameForm.values.name,
    })
      .then((res) => {
        notifications.show({
          message: "名称を変更しました",
          icon: <IconNotiSuccess />,
        });
        mutate();
        close();
      })
      .catch((error) => {
        let errorTitle = "名称を変更できませんでした";
        let errorMessage = getApiErrorMessage(error.response.data.message);
        if (
          error.response.data?.message ===
          "Error: The file or directory name already exists."
        ) {
          errorMessage =
            "ファイルはすでに存在します。別の名称を入力してください。";
        }

        notifications.show({
          title: errorTitle,
          message: errorMessage,
          icon: <IconNotiFailed />,
        });
      });
  };

  // サムネイル更新用の関数
  const handleThumbnailUpdate = async () => {
    const formData = new FormData();
    formData.append("thumbnail", renameForm.values.thumbnail!);

    ax.post(`/api/v1/drive/collab_file/${path}/thumbnail`, formData)
      .then((res) => {
        notifications.show({
          message: "サムネイルを更新しました",
          icon: <IconNotiSuccess />,
        });
        mutate();
        close();
      })
      .catch((error) => {
        notifications.show({
          title: "サムネイルを更新できませんでした",
          message: getApiErrorMessage(error.response.data.message),
          icon: <IconNotiFailed />,
        });
      });
  };

  return <Styled handleSubmit={handleSubmit} form={renameForm} close={close} />;
};
