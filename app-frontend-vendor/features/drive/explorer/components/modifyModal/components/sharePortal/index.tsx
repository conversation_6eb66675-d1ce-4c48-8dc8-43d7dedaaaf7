import styled from "@emotion/styled";
import { SelectItem, Switch, Loader } from "@mantine/core";
import PermissionList from "../share/permisionList";
import { useSetChange } from "utils/recoil/drive/changeDropDownHook";
import { notifications } from "@mantine/notifications";
import { useCallback, useEffect, useState } from "react";
import { propcolors } from "styles/colors";
import useSWR from "swr";
import { ax } from "utils/axios";
import { getApiErrorMessage } from "utils/values/errorMessages";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { SelectShare } from "../share/find-share";
import { NOTIFICATION_MSG } from "constants/ja/common";

type fetchMode = "partner" | "partner_user";

type DriveInput = {
  ID: number;
  type: "partner" | "partner_user" | "all";
};

type PresentationProps = {
  className?: string;
  permissionList: DrivePermissionPartner[] | undefined;
  partnerPermissionList: DrivePermissionPartner[] | undefined;
  mutatePermisionList: () => void;
  userPartnerPermissionList: DrivePermissionPartner[] | undefined;
  isPartnerPermissionListFetching: boolean;
  isPermissionListFetching: boolean;
  handleFetchMode: (mode: fetchMode) => void;
  fetchMode: fetchMode;
  partnerList: LinkActivePartner[] | undefined;
  partnerUserList: PartnerUser[] | undefined;
  mutateSelectInput: () => void;
  handleAddPermission: (value: any) => void;
  handleSelectInput: (value: string | null) => void;
  handlePermission: (
    mode: boolean,
    type: "partner" | "partner_user" | "all_partner",
    id: number,
    isMutateRefetchPartner: boolean,
    isMutateRefetchUserPartner: boolean,
    partner_id?: number
  ) => void;
  isSharedToAllPartners: boolean | null;
  selectedItem: DriveInput | null;
};

type DriveNewFolderModal = {
  path: number | undefined;
  close: () => void;
  mutate: () => void;
  opened: boolean;
};

interface ItemProps extends React.ComponentPropsWithoutRef<"div"> {
  image: string;
  label: string;
  description: string;
}

const Presentation: React.FC<PresentationProps> = ({
  className,
  permissionList,
  partnerPermissionList,
  isPermissionListFetching,
  handleFetchMode,
  fetchMode,
  partnerList,
  partnerUserList,
  handleAddPermission,
  mutateSelectInput,
  handlePermission,
  isSharedToAllPartners,
  userPartnerPermissionList,
}) => {
  const allPartner: SelectItem = {
    label: "全てのパートナー",
    value: JSON.stringify({
      ID: "", // 全てのパートナーが対象なので、IDは空
      type: "all",
    }),
    group: "全体",
  };

  const parsedPartnerList: SelectItem[] = partnerList
    ? partnerList.map((item) => {
        const isEnable = partnerPermissionList?.find(
          (itemP: any) => itemP.id === item.vendor_linked_partner_id
        );

        const result: SelectItem = {
          label: item.partner_name,
          value: JSON.stringify({
            ID: item.vendor_linked_partner_id,
            type: "partner",
            have_access: isEnable ? isEnable.has_access : false,
          }),
          group: "パートナー",
        };
        return result;
      })
    : [];

  const parsedPartnerUserList: SelectItem[] = partnerUserList
    ? partnerUserList.map((item) => {
        const isEnable = userPartnerPermissionList?.find(
          (itemUP: any) => itemUP.id === item.id
        );
        const result: SelectItem = {
          label: `${item.name} (${item.email})`,
          value: JSON.stringify({
            ID: item.id,
            type: "partner_user",
            have_access: isEnable ? isEnable.has_access : false,
          }),
          group: "ユーザー",
        };
        return result;
      })
    : [];

  const dataForSelect: any = [
    allPartner,
    ...parsedPartnerList,
    ...parsedPartnerUserList,
  ];

  if (isSharedToAllPartners === null) {
    return (
      <div className={className}>
        <div className="loading-share">
          <Loader size="lg" />
        </div>
      </div>
    );
  } else {
    return (
      <div className={className}>
        <div className="share-modal-header">
          <h1 className="share-title"> パートナー・ユーザーを選択 </h1>
          <span className="share-notifications">
            権限を変更すると該当フォルダに存在する「上層/下層のフォルダ」の権限も変更されます
          </span>
          <br />
          <p className="share-main-label">入力して検索できます</p>
        </div>
        <SelectShare
          data={dataForSelect}
          placeholder="パートナー名、ユーザーのメールアドレスを入力"
          fetchMode={fetchMode}
          isShareAll={isSharedToAllPartners}
          onSubmit={handleAddPermission}
          mutateSelectInput={mutateSelectInput}
          userPartnerPermissionList={userPartnerPermissionList}
          onRemoveAll={() =>
            handlePermission(false, "all_partner", 0, false, false)
          }
          handerPermission={handlePermission}
        />
        <section className="permission">
          {!isSharedToAllPartners && (
            <>
              <div className="segmented-controller">
                <div
                  className={`segmented-controller-item ${fetchMode === "partner" && "active"}`}
                  onClick={() => {
                    handleFetchMode("partner");
                  }}
                >
                  パートナー
                </div>
                <div className="segmented-controller-sperator"></div>
                <div
                  className={`segmented-controller-item ${fetchMode === "partner_user" && "active"}`}
                  onClick={() => {
                    handleFetchMode("partner_user");
                  }}
                >
                  ユーザー
                </div>
              </div>
              <PermissionList
                permissionList={permissionList}
                fetchMode={fetchMode}
                handlePermission={handlePermission}
                permissionListFetching={isPermissionListFetching}
                partnerList={partnerList}
              />
            </>
          )}
          {isSharedToAllPartners && (
            <>
              <section className="permission-list">
                {isPermissionListFetching ? (
                  <div>loading...</div>
                ) : isSharedToAllPartners ? (
                  <div className="permission-list-item">
                    <p>全てのパートナー</p>
                    <Switch
                      defaultChecked={isSharedToAllPartners}
                      onChange={(e) =>
                        handlePermission(
                          e.target.checked,
                          "all_partner",
                          0,
                          false,
                          true
                        )
                      }
                    />
                  </div>
                ) : (
                  <div className="no-data">no data</div>
                )}
              </section>
            </>
          )}
        </section>
      </div>
    );
  }
};

const Styled = styled(Presentation)`
  .share-modal-header {
    padding: 24px 24px 0px 24px;
  }

  .fetching-partner-permission-list {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 96px;
  }

  .no-data {
    font-size: 16px;
    font-weight: 600;
    min-height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .user-list-controller {
    padding: 0 24px 24px 24px;
  }

  .partner-list {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid ${propcolors.gray[200]};
    &-header {
      height: 48px;
      font-size: 14px;
      display: flex;
      color: #222222;
      justify-content: flex-start;
      background: ${propcolors.inputBackground};
      align-items: center;
      border-bottom: 1px solid ${propcolors.gray[200]};
      padding: 10px 16px 10px 16px;
      cursor: default;
    }
    &-item {
      height: 48px;
      padding: 10px 16px 10px 16px;
      display: flex;
      cursor: default;
      font-size: 14px;
      color: #222222;
      transition: 0.2s;
      justify-content: space-between;
      align-items: center;
      background: ${propcolors.white};
      :hover {
        background: ${propcolors.inputBackground};
      }
    }
    &-button {
      height: 32px;
      cursor: pointer;
      border-radius: 6px;
      transition: 0.2s;
      border: 1px solid ${propcolors.blackLight};
      background: ${propcolors.blackLight};
      color: ${propcolors.white};
      padding: 9px 12px 9px 12px;
      &-enable {
        border-color: #c13515;
        background-color: white;
        color: #c13515;
        font-size: 14px;
      }
    }
  }

  height: 100%;
  display: grid;
  grid-template-rows: auto 1fr;
  tr {
    display: grid;
    grid-template-columns: 1fr 10% 15% 15% 15%;
  }

  .loading-share {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 12vh;
  }

  .segmented-controller {
    width: 100%;
    height: 46px;
    border: 1px solid ${propcolors.gray[200]};
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    &-sperator {
      width: 1px;
      height: 100%;
      background: ${propcolors.gray[200]};
    }
    &-item {
      transition: 0.3s;
      flex: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: ${propcolors.blackLight};
      height: 100%;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .active {
    background: ${propcolors.blackLight};
    color: ${propcolors.white};
  }

  .file {
    &-title {
      display: flex;
      align-items: center;
      gap: 1rem;
    }
  }
  .permission {
    display: grid;
    padding: 24px;
    grid-template-rows: auto 1fr;
    border-top: 1px solid ${propcolors.gray[200]};
    gap: 24px;
    &-list {
      border-radius: 5px;
      &-item {
        max-width: 100%;
        transition: 0.2s;
        border-bottom: 1px solid ${propcolors.gray[200]};
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 16px;
        padding: 16px;
        :hover {
          background-color: ${propcolors.greyBreadcrumb};
        }
        &-switch {
          input:checked + .mantine-Switch-track {
            background-color: ${propcolors.black};
            border-color: ${propcolors.black};
            .mantine-Switch-thumb {
              background-color: ${propcolors.white};
            }
          }
          .mantine-Switch-track {
            height: 20px;
            background-color: ${propcolors.white};
            border-color: #e8eaed;
            .mantine-Switch-thumb {
              background-color: ${propcolors.switchOff};
            }
          }
        }
        &-logo {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          background: ${propcolors.inputBackground};
        }
        &-info {
          flex-grow: 1;
          display: flex;
          justify-content: flex-start;
          items: center;
          flex-direction: column;
          cursor: default;
          gap: 8px;
          &-name {
            font-size: 14px;
            color: #222222;
            font-weight: 400;
            cursor: normal;
            color: ${propcolors.blackLight};
          }
          &-owner {
            font-size: 14px;
            color: #222222;
            font-weight: 400;
            color: ${propcolors.blackLight};
          }
        }
        &-remove {
          display: flex;
          justify-content: center;
          cursor: pointer;
          align-items: center;
        }
        &-switch {
          width: 36px;
          height: 20px;
          input:checked + label {
            background-color: #222222;
            border-color: #222222;
          }
        }
      }
    }
  }
  .share {
    &-notifications {
      color: #f93832;
      margin-bottom: 8px;
      font-weight: 300;
      font-size: 12px;
      display: inline-block;
    }
    &-select {
      padding: 0 24px 24px 24px;
      input {
        height: 48px;
      }
    }
    &-main-label {
      margin-bottom: 8px;
      font-size: 12px;
      color: #666666;
      font-weight: 300;
    }
    &-title {
      font-size: 18px;
      font-bold: 600;
      color: #222222;
      margin-bottom: 16px;
    }
  }
`;

export const DriveSharePortalModal: React.FC<DriveNewFolderModal> = ({
  path,
  mutate: MutateAll,
  opened,
}) => {
  const [fetchMode, setFetchMode] = useState<fetchMode>("partner");
  const [selectedItem, setSelectedItem] = useState<DriveInput | null>(null);
  const setChange = useSetChange();
  const [isSharedToAllPartners, setIsSharedToAllPartners] = useState<
    boolean | null
  >(null);
  const listFetcher = (fileID: number, mode: string) =>
    ax
      .get(`api/v1/drive/file/${fileID}/permission/${mode}?share_portal_flg=1`)
      .then((res) => res.data);

  const {
    data: permissionList,
    mutate: mutatePermissionList,
    isValidating: isPermissionListFetching,
  } = useSWR([path, fetchMode], ([fileID, mode]) => listFetcher(fileID!, mode));

  const {
    data: partnerPermissionList,
    mutate: mutatePartnerPermissionList,
    isValidating: isPartnerPermissionListFetching,
  } = useSWR([path, "partner"], ([fileID, mode]) => listFetcher(fileID!, mode));

  const {
    data: userPartnerPermissionList,
    mutate: mutateUserPartnerPartnerPermissionList,
    isValidating: isUserPartnerPermissionListFetching,
  } = useSWR([path, "partner_user"], ([fileID, mode]) =>
    listFetcher(fileID!, mode)
  );

  const handleFetchMode = useCallback((mode: fetchMode) => {
    setFetchMode(mode);
  }, []);

  const partnerListFetcher = () =>
    ax
      .get("api/v1/partners/link_active")
      .then((res) => res.data as LinkActivePartner[]);

  const { data: partnerList, mutate: mutatePartnerList } = useSWR(
    "api/v1/partners/link_active",
    partnerListFetcher
  );

  const partnerUserListFetcher = () =>
    ax
      .get("api/v1/partners/link_active/users")
      .then((res) => res.data as PartnerUser[]);

  const { data: partnerUserList, mutate: mutatePartnerUserList } = useSWR(
    "api/v1/partners/link_active/users",
    partnerUserListFetcher
  );

  const handleAddPermission = (item: any) => {
    let selectedItem = item;
    if (!selectedItem) {
      return;
    }
    if (!item)
      return notifications.show({
        title: "エラー",
        message: "パートナー・ユーザーを選択してください",
        icon: <IconNotiFailed />,
      });
    selectedItem = JSON.parse(item);

    if (selectedItem.type === "all") {
      ax.post(
        `api/v1/drive/file/${path}/share-to/all_partner?share_portal_flg=1`
      )
        .then((res) => {
          notifications.show({
            icon: <IconNotiSuccess />,
            title: NOTIFICATION_MSG.SHARE_ALL_TITLE,
            message: NOTIFICATION_MSG.SHARE_ALL_MESSAGE,
          });
          setIsSharedToAllPartners(true);
          // MutateAll();
          // mutatePermissionList([path!, fetchMode]);
        })
        .catch((error) => {
          notifications.show({
            title: "共有を追加できませんでした",
            message: getApiErrorMessage(error.response.data.message),
            icon: <IconNotiFailed />,
          });
        });
    } else {
      ax.post(
        `api/v1/drive/file/${path}/share-to/${selectedItem.type}?share_portal_flg=1`,
        selectedItem.type === "partner"
          ? {
              partner_id: selectedItem.ID,
            }
          : {
              partner_team_member_id: selectedItem.ID,
            }
      )
        .then((res) => {
          notifications.show({
            icon: <IconNotiSuccess />,
            message: NOTIFICATION_MSG.SHARE_SETTING_CHANGE,
          });
          MutateAll();
          mutatePermissionList([path!, fetchMode]);
        })
        .catch((error) => {
          notifications.show({
            title: "共有を追加できませんでした",
            message: getApiErrorMessage(error.response.data.message),
            icon: <IconNotiFailed />,
          });
        });
    }
  };

  const handlePermission = (
    mode: boolean,
    type: "partner" | "partner_user" | "all_partner",
    id: number,
    isMutateRefetchPartner: boolean,
    isMutateRefetchUserPartner: boolean
  ) => {
    // 全てのパートナーが対象の場合はペイロードは空
    let payload = {};
    if (type === "partner") {
      payload = { partner_id: id };
    } else if (type === "partner_user") {
      payload = { partner_team_member_id: id };
    }

    ax.post(
      `api/v1/drive/file/${path}/${mode ? "share-to" : "unshare-from"}/${type}?share_portal_flg=1`,
      payload
    )
      .then((res) => {
        notifications.show({
          icon: <IconNotiSuccess />,
          message: NOTIFICATION_MSG.SHARE_SETTING_CHANGE,
        });
        if (type === "all_partner") {
          if (mode) {
            setIsSharedToAllPartners(true);
          } else {
            setIsSharedToAllPartners(false);
          }
        }
        // MutateAll();
        if (isMutateRefetchPartner) {
          mutatePartnerPermissionList();
          mutateUserPartnerPartnerPermissionList();
          mutatePermissionList();
        }
        if (isMutateRefetchUserPartner) {
          mutateUserPartnerPartnerPermissionList();
          mutatePermissionList();
        }
      })
      .catch((error) => {
        notifications.show({
          icon: <IconNotiFailed />,
          title: NOTIFICATION_MSG.ERROR_SHARE_SETTING,
          message: getApiErrorMessage(error.response.data.message),
        });
      });
  };

  const handleSelectInput = useCallback((value: string | null) => {
    if (!value)
      return notifications.show({
        title: "エラー",
        message: "パートナー名、ユーザーのメールアドレスを入力",
        icon: <IconNotiFailed />,
      });
    const result = JSON.parse(value);
    setSelectedItem(result);
  }, []);

  const mutateSelect = () => {
    mutatePartnerPermissionList();
    mutateUserPartnerPartnerPermissionList();
  };

  useEffect(() => {
    mutatePermissionList();
    mutatePartnerPermissionList();
    mutateUserPartnerPartnerPermissionList();
    setChange(true);
    setSelectedItem(null);
  }, [fetchMode]);

  // モーダル表示ごとに「全共有」が設定済みかを確認
  useEffect(() => {
    // 非同期処理のクリーンアップ用
    // モーダル自体がアンマウントされた際のAPI実行を防止
    let isMounted = true;
    MutateAll();

    if (path) {
      ax.get(
        `api/v1/drive/file/${path}/permission/all_partner?share_portal_flg=1`
      )
        .then((res) => {
          const result = res.data.data.allPartnersAuthorized;
          if (typeof result === "boolean") {
            setIsSharedToAllPartners(result);
            MutateAll();
          } else {
            // resultがbooleanでない場合のエラーハンドリング
            console.error(
              "allPartnersAuthorized should be boolean, received:",
              result
            );
          }
        })
        .catch((err) => {
          // エラーハンドリング
          console.error("Error fetching all partners authorized:", err);
        });
    }

    return () => {
      isMounted = false;
    };
  }, [opened]);

  return (
    <Styled
      permissionList={permissionList}
      isPermissionListFetching={isPermissionListFetching}
      isPartnerPermissionListFetching={isPartnerPermissionListFetching}
      partnerPermissionList={partnerPermissionList}
      fetchMode={fetchMode}
      mutateSelectInput={mutateSelect}
      userPartnerPermissionList={userPartnerPermissionList}
      handleFetchMode={handleFetchMode}
      mutatePermisionList={mutatePermissionList}
      partnerList={partnerList}
      partnerUserList={partnerUserList}
      handleAddPermission={handleAddPermission}
      handleSelectInput={handleSelectInput}
      handlePermission={handlePermission}
      isSharedToAllPartners={isSharedToAllPartners}
      selectedItem={selectedItem}
    />
  );
};
