import styled from "@emotion/styled";
import { Button, TextInput } from "@mantine/core";
import { type UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import { ax } from "utils/axios";
import { getApiErrorMessage } from "utils/values/errorMessages";

type PresentationProps = {
  className?: string;
  handleSubmit: () => void;
  form: UseFormReturnType<{ name: string }>;
  close: () => void;
};

type DriveRenameModal = {
  path: number | undefined;
  folderName: string;
  type?: string | undefined;
  close: () => void;
  mutate: () => void;
  isCollab: boolean;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  form,
  handleSubmit,
  close,
}) => {
  return (
    <div className={className}>
      <TextInput
        className="input"
        label="フォルダ名"
        required
        {...form.getInputProps("name")}
      />
      <div className="button-group">
        <Button type="button" className="button button-close" onClick={close}>
          キャンセル
        </Button>
        <Button
          type="submit"
          className="button button-submit"
          onClick={handleSubmit}
        >
          変更
        </Button>
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  .input {
    padding: 24px 24px 0 24px;
    input {
      margin-top: 8px;
      height: 48px;
    }
  }

  .button-group {
    display: flex;
    padding: 24px;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
  }
  .button {
    width: 50%;
    height: 42px;
    border-radius: 8px;
    &-submit {
      background: #222222;
      color: white;
    }
    &-close {
      background: #8992a0;
      color: white;
    }
  }
`;

export const DriveRenameFolderModal: React.FC<DriveRenameModal> = ({
  path,
  close,
  mutate,
  folderName,
  isCollab,
}) => {
  const renameForm = useForm<{ name: string }>({
    initialValues: {
      name: folderName,
    },
    validateInputOnChange: true,
    validate: {
      name: (value) => {
        if (value.length < 2) return "は2文字以上で入力してください";
        if (value.match(/[/*+?^${}|<>\\=!¥:;@]/g))
          return "特殊文字は利用できません";
        return null;
      },
    },
  });
  const handleSubmit = () => {
    if (renameForm.values.name.length < 2) {
      return notifications.show({
        title: "名前が短すぎます",
        message: "名前は2文字以上である必要があります",
        icon: <IconNotiFailed />,
      });
    }
    ax.put(
      isCollab
        ? `api/v1/drive/collab_file/${path}`
        : `api/v1/drive/file/${path}`,
      {
        name: renameForm.values.name,
      },
    )
      .then((res) => {
        notifications.show({
          message: "名称を変更しました",
          icon: <IconNotiSuccess />,
        });
        mutate();
        close();
      })
      .catch((error) => {
        let errorTitle = "名称を変更できませんでした";
        let errorMessage = getApiErrorMessage(error.response.data.message);
        switch (error.response.data?.message) {
          case "Error: The file or directory name already exists.":
            errorTitle = "名称を変更できませんでした";
            errorMessage =
              "フォルダーはすでに存在します。別の名称を入力してください。";
            break;
          default:
            break;
        }
        notifications.show({
          title: errorTitle,
          message: errorMessage,
          icon: <IconNotiFailed />,
        });
      });
  };
  return <Styled handleSubmit={handleSubmit} form={renameForm} close={close} />;
};
