import styled from "@emotion/styled";
import { Button } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { ax } from "utils/axios";
import { getApiErrorMessage } from "utils/values/errorMessages";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { useRouter } from "next/router";

type PresentationProps = {
  className?: string;
  handleSubmit: () => void;
  close: () => void;
};

type DriveRenameModal = {
  path: number | undefined;
  fileName: string;
  close: () => void;
  mutate: () => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  handleSubmit,
  close,
}) => {
  return (
    <div className={className}>
      <div className="wrapper">
        <div className="title">本当にこのファイルを削除しますか？</div>
        <p className="caution">※この操作は取り消せません。</p>
      </div>
      <div className="button-group">
        <Button type="button" className="button button-close" onClick={close}>
          キャンセル
        </Button>
        <Button
          type="submit"
          className="button button-submit"
          onClick={handleSubmit}
        >
          削除する
        </Button>
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  .wrapper {
    padding: 24px 24px 0 24px;
  }

  .title {
    font-size: 18px;
    font-weight: 600;
    color: #222222;
  }

  .caution {
    font-size: 12px;
    color: #23221e;
    margin-top: 16px;
  }

  .input {
    padding: 24px 24px 0 24px;
    input {
      margin-top: 8px;
      height: 42px;
    }
  }
  .button-group {
    display: flex;
    padding: 24px;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
  }
  .button {
    width: 50%;
    height: 42px;
    border-radius: 8px;
    &-submit {
      background: #222222;
      color: white;
    }
    &-close {
      background: #8992a0;
      color: white;
    }
  }
`;

export const DriveDeleteFile: React.FC<DriveRenameModal> = ({
  path,
  close,
  mutate,
  fileName,
}) => {
  const {pathname} = useRouter();
  const handleSubmit = () => {
    ax.delete(pathname == "/drive/collab_files/[file_id]" ? `api/v1/drive/collab_file/${path}` : `api/v1/drive/file/${path}`)
      .then(() => {
        mutate();
        close();
        return notifications.show({
          message: `${fileName}を削除しました`,
          icon: <IconNotiSuccess />,
        });
      })
      .catch((err) => {
        if (err.response.status === 400) {
          switch (err.response.data.error) {
            case "Error: cannot delete the file related to training":
              close();
              return notifications.show({
                title: `${fileName}を削除できませんでした。`,
                message:
                  "このファイルは公開または非公開のトレーニングに使用されているため削除できません。",
                icon: <IconNotiFailed />,
              });
              break;

            default:
              break;
          }
        }
        close();
        return notifications.show({
          title: `${fileName}を削除できませんでした。`,
          message: getApiErrorMessage(err.response.data.message),
          icon: <IconNotiFailed />,
        });
      });
  };
  return <Styled handleSubmit={handleSubmit} close={close} />;
};
