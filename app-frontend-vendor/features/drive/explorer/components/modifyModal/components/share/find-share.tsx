import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import React, { useEffect, useState } from "react";
import { Loader } from "@mantine/core";
import { SelectItemComponent } from "./selected-item";
import { PermissionItem } from ".";

interface SelectItemData {
  label: string;
  value: PermissionItem;
  group: "全体" | "パートナー" | "ユーザー";
  have_access: boolean;
  subLabel: string;
}

interface SelectShareProps {
  data: SelectItemData[];
  placeholder: string;
  fetchMode: "partner" | "partner_user";
  onRemoveAll: () => void;
  onSubmit: (value: string) => void;
  isShareAll: boolean;
  userPartnerPermissionList: DrivePermissionPartner[] | undefined;
  mutateSelectInput: () => void;
  handerPermission: (
    value: boolean,
    type: "partner" | "partner_user" | "all_partner",
    id: number,
    isShareAll: boolean,
    isShare: boolean,
    partner_id?: number
  ) => void;
}

interface PresentationProps extends SelectShareProps {
  className?: string;
}

const Presentation: React.FC<PresentationProps> = ({
  data,
  placeholder,
  className,
  fetchMode,
  isShareAll,
  userPartnerPermissionList,
  onRemoveAll,
  onSubmit,
  handerPermission,
  mutateSelectInput,
}) => {
  const [dataShow, setDataShow] = useState<SelectItemData[]>([]);
  const [showToggle, setShowToggle] = useState<boolean>(false);
  const [value, setValue] = useState<string>("");
  const duplicateTitle: string[] = [];

  useEffect(() => {
    mutateSelectInput();
  }, [fetchMode]);

  useEffect(() => {
    setShowToggle(false);
    if (isShareAll) {
      setShowToggle(false);
    }
  }, [isShareAll]);

  useEffect(() => {
    const filterData = data.filter((item) =>
      item.label.toLowerCase().includes(value.toLowerCase())
    );
    setDataShow(filterData);
  }, [data, value]);

  const onChangeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    setShowToggle(true);
    setValue(e.target.value);
  };

  const handlerOnclickInput = () => {
    setShowToggle(!showToggle);
  };

  return (
    <div className={className}>
      <div className="share-wrap">
        <input
          type="text"
          placeholder={placeholder}
          className={`share-input ${showToggle ? "active-share-select" : "disable-share-select"}`}
          onChange={(e) => onChangeHandler(e)}
          disabled={isShareAll}
          value={value}
          onClick={() => handlerOnclickInput()}
          style={{ fontSize: 14 }}
        />
        <>
          {data === null && (
            <div className="loading-share">
              <Loader size="lg" />
            </div>
          )}
          <div className={`share-show ${showToggle ? "open" : "close"}`}>
            {dataShow &&
              dataShow.map((item) => {
                const component = (
                  <>
                    {!duplicateTitle.includes(item.group) && (
                      <div className="share-show-header">
                        <div className="share-show-header-text">
                          {item.group}
                        </div>
                        <div className="share-show-header-line"></div>
                      </div>
                    )}
                    {duplicateTitle.push(item.group) && <></>}
                    <SelectItemComponent
                      value={item.value}
                      key={item.value + item.group}
                      label={item.label}
                      subLabel={item.subLabel && item.subLabel}
                      group={item.group}
                      isShareAll={isShareAll}
                      onSubmit={onSubmit}
                      onRemoveAll={() =>
                        handerPermission(false, "all_partner", 0, false, false)
                      }
                      handerPermission={handerPermission}
                    />
                  </>
                );
                return component;
              })}
          </div>
        </>
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  width: 100%;
  padding: 0 24px 24px 24px;

  .active-share-select {
    border-bottom: 1px solid ${propcolors.gray[200]} !important;
  }

  .disable-share-select {
    border-radius: 6px !important;
  }

  .open {
    max-height: 200px !important;
    transition: max-height 0.2s ease-in-out;
    scrollbar-width: none;
    -ms-overflow-style: none;
    ::-webkit-scrollbar {
      display: none;
    }
  }

  .close {
    transition: max-height 0.2s ease;
    transition-delay: max-height 0.15s;
    border: 1px transparent !important;
  }

  .share {
    &-wrap {
      display: block;
      padding: 0;
    }
    &-show {
      border-radius: 0 0 6px 6px;
      border: 1px solid ${propcolors.gray[200]};
      border-top: none;
      overflow: hidden;
      max-height: 0;
      overflow-y: scroll;
      &-header {
        display: flex;
        gap: 8px;
        justify-content: space-between;
        cursor: default;
        align-items: center;
        padding: 8px 16px;
        &-text {
          font-size: 12px;
          white-space: nowrap;
          color: #868e96;
        }
        &-line {
          width: 100%;
          height: 1px;
          background: ${propcolors.gray[200]};
        }
      }
    }
    &-input {
      all: unset;
      box-sizing: border-box;
      font-family: inherit;
      font-size: inherit;
      color: inherit;
      background: none;
      border: none;
      padding-block: 0;
      padding-inline: 0;
      padding: 0;
      margin: 0;
      outline: none;
      width: 100%;
      height: 48px;
      padding: 0 16px;
      border: 0px;
      outline: none;
      border: 1px solid ${propcolors.gray[200]};
      border-radius: 6px 6px 0 0;
      ::placeholder {
        border: none;
        color: #8992a0;
      }
      :disabled {
        background: #f1f3f5;
        ::placeholder {
          color: #ced4da;
        }
      }
    }
  }

  .active {
    border-bottom: 1px solid ${propcolors.gray[200]} !important;
  }

  .loading-share {
    width: 100%;
    height: 144px;
  }
`;

export const SelectShare = ({
  data,
  placeholder,
  fetchMode,
  isShareAll,
  handerPermission,
  onRemoveAll,
  mutateSelectInput,
  userPartnerPermissionList,
  onSubmit,
}: SelectShareProps) => {
  return (
    <Styled
      data={data}
      placeholder={placeholder}
      fetchMode={fetchMode}
      isShareAll={isShareAll}
      handerPermission={handerPermission}
      onRemoveAll={onRemoveAll}
      userPartnerPermissionList={userPartnerPermissionList}
      mutateSelectInput={mutateSelectInput}
      onSubmit={onSubmit}
    />
  );
};
