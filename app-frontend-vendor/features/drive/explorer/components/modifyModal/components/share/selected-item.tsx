import styled from "@emotion/styled";
import { <PERSON><PERSON>, Stack } from "@mantine/core";
import React from "react";
import { useSetChange } from "utils/recoil/drive/changeDropDownHook";
import { PermissionItem } from ".";

interface SelectItemProps {
  value: PermissionItem;
  onRemoveAll: () => void;
  onSubmit: (value: string) => void;
  group: string;
  isShareAll: boolean;
  label: string;
  subLabel?: string;
  handerPermission: (
    value: boolean,
    type: "partner" | "partner_user" | "all_partner",
    id: number,
    isShareAll: boolean,
    isShare: boolean,
    partner_id?: number
  ) => void;
}

interface PresentationProps extends SelectItemProps {
  className?: string;
}

const Presentation: React.FC<PresentationProps> = ({
  value,
  isShareAll,
  onRemoveAll,
  onSubmit,
  handerPermission,
  group,
  className,
  label,
  subLabel,
}) => {
  const setChangePermission = useSetChange();
  const handlerOnClickToogleAll = () => {
    if (isShareAll) {
      onRemoveAll();
      return;
    }
    handerPermission(!value.have_access, "all_partner", 0, true, true);
  };
  const partnerChangePermission = () => {
    setChangePermission(true);
    handerPermission(!value.have_access, "partner", value.ID, true, true);
  };

  const partnerUserChangePermission = () => {
    setChangePermission(true);
    handerPermission(
      !value.have_access,
      "partner_user",
      value.ID,
      true,
      true,
      value.partner_id ?? 0
    );
  };

  return (
    <div className={className}>
      <Stack spacing={1}>
        <p style={{ fontSize: 14 }}>{label}</p>
        {subLabel && (
          <p style={{ fontSize: 12, color: "rgba(137, 146, 160, 1)" }}>
            {subLabel}
          </p>
        )}
      </Stack>
      {group === "全体" && (
        <Button
          className={`turn-on-share-all ${!isShareAll ? "on" : "off"}`}
          onClick={handlerOnClickToogleAll}
        >
          {!isShareAll ? "追加" : "削除"}
        </Button>
      )}
      {group === "パートナー" && (
        <Button
          className={`turn-on-share-all ${value.have_access === true ? "off" : "on"}`}
          onClick={() => partnerChangePermission()}
        >
          {value.have_access === true ? "削除" : "追加"}
        </Button>
      )}
      {group === "ユーザー" && (
        <Button
          className={`turn-on-share-all ${value.have_access === true ? "off" : "on"}`}
          onClick={() => partnerUserChangePermission()}
        >
          {value.have_access === true ? "削除" : "追加"}
        </Button>
      )}
    </div>
  );
};

const Styled = styled(Presentation)`
  display: flex;
  height: auto;
  min-height: 48px;
  padding: 8px 16px;
  gap: 16px;
  justify-content: space-between;
  cursor: default;
  align-items: center;

  .turn-on-share-all {
    width: 52px;
    min-width: 52px;
    padding: 0;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 400 !important;
    border 1px solid transparent;
  }

  .on {
    color: #ffffff;
    background-color: #222222;
  }

  .off {
  color: #C13515;
  border-color: #C13515;
  background-color: #ffffff;
  }
`;

export const SelectItemComponent = ({
  onRemoveAll,
  onSubmit,
  isShareAll,
  handerPermission,
  value,
  group,
  label,
  subLabel,
}: SelectItemProps) => {
  return (
    <>
      <Styled
        onRemoveAll={onRemoveAll}
        onSubmit={onSubmit}
        isShareAll={isShareAll}
        handerPermission={handerPermission}
        value={value}
        group={group}
        label={label}
        subLabel={subLabel}
      />
    </>
  );
};
