import Image from "next/image";
import { Switch, Loader, rem } from "@mantine/core";
import { propcolors } from "styles/colors";
import IconCheck from "public/icons/check.svg";
import { useChange, useSetChange } from "utils/recoil/drive/changeDropDownHook";
import { useEffect, useState } from "react";

export default function PermissionList({
  permissionList,
  handlePermission,
  fetchMode,
  permissionListFetching,
  partnerList,
}: {
  permissionList: DrivePermissionPartner[] | undefined;
  handlePermission: (
    value: boolean,
    type: "partner" | "partner_user" | "all_partner",
    id: number,
    isShareAll: boolean,
    isShare: boolean,
    partner_id?: number
  ) => void;
  permissionListFetching: boolean;
  fetchMode: "partner" | "partner_user";
  partnerList: LinkActivePartner[] | undefined;
}) {
  const changePermission = useChange();
  const setChangePermission = useSetChange();
  const [permisionListState, setPermissionListState] = useState<
    DrivePermissionPartner[] | undefined
  >(undefined);

  const handerChangePermission = (
    toggle: boolean,
    fetchMode: "partner" | "partner_user",
    id: number,
    partner_id?: number
  ) => {
    setChangePermission(false);
    handlePermission(toggle, fetchMode, id, true, true, partner_id);
  };

  useEffect(() => {
    if (!changePermission) return;
    if (permissionListFetching) {
      setPermissionListState(undefined);
      return;
    }

    setPermissionListState(permissionList);
  }, [changePermission, fetchMode, permissionListFetching, permissionList]);

  const checkIcon = <IconCheck color={propcolors.white} />;

  return (
    <>
      <section className="permission-list">
        {permisionListState === undefined ? (
          <div className="loading-share">
            <Loader size="lg" />
          </div>
        ) : permisionListState !== undefined &&
          permisionListState.length > 0 ? (
          permisionListState
            .sort((a, b) => a.id - b.id)
            .map((item) => (
              <div className={`permission-list-item`} key={item.id}>
                <div className="permission-list-item-logo">
                  {fetchMode === "partner" ? (
                    <>
                      {item.partner_avatar_url ? (
                        <Image
                          width={40}
                          height={40}
                          style={{ borderRadius: "40px" }}
                          src={item.partner_avatar_url}
                          alt=""
                        />
                      ) : (
                        <Image
                          width={18}
                          height={18}
                          src="/icons/company.svg"
                          alt="company-logo"
                        />
                      )}
                    </>
                  ) : (
                    <>
                      {item.partner_avatar_url ? (
                        <Image
                          width={40}
                          height={40}
                          style={{ borderRadius: "40px" }}
                          src={item.partner_avatar_url}
                          alt=""
                        />
                      ) : (
                        <Image
                          width={18}
                          height={18}
                          src="/icons/user.svg"
                          alt="company-logo"
                        />
                      )}
                    </>
                  )}
                </div>
                <div className="permission-list-item-info">
                  <div className="permission-list-item-info-name">
                    {item.name}
                  </div>
                  {fetchMode === "partner_user" && (
                    <div className="permission-list-item-info-owner">
                      {item.partner_name ? item.partner_name : ""}
                    </div>
                  )}
                </div>
                <div className="permission-list-item-remove">
                  <Switch
                    className="permission-list-item-switch"
                    onLabel={checkIcon}
                    color="dark"
                    sx={{
                      width: rem(34),
                    }}
                    size="sm"
                    defaultChecked={item.has_access}
                    onChange={(e) =>
                      handerChangePermission(
                        e.target.checked,
                        fetchMode,
                        item.id,
                        item.partner_id ?? 0
                      )
                    }
                  />
                </div>
              </div>
            ))
        ) : (
          <div className="no-data">no data</div>
        )}
      </section>
    </>
  );
}
