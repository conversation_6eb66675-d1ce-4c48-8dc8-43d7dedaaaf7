import styled from "@emotion/styled";
import { Button, TextInput } from "@mantine/core";
import { type UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { useRouter } from "next/router";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import { ax } from "utils/axios";

type PresentationProps = {
  className?: string;
  form: UseFormReturnType<{ name: string }>;
  handleSubmit: () => void;
  close: () => void;
};

type DriveNewCollabSubFolderModal = {
  path: number | undefined;
  close: () => void;
  mutate: () => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  form,
  handleSubmit,
  close,
}) => {
  return (
    <form
      className={className}
      onSubmit={(e) => {
        e.preventDefault();
        handleSubmit();
      }}
    >
      <TextInput
        label="フォルダ名"
        className="newfolder-input"
        required
        {...form.getInputProps("name")}
      />
      <div className="button-group">
        <Button type="button" className="button button-close" onClick={close}>
          キャンセル
        </Button>
        <Button
          type="submit"
          className="button button-submit"
          disabled={form.isValid() ? false : true}
        >
          追加する
        </Button>
      </div>
    </form>
  );
};

const Styled = styled(Presentation)`
  .button-group {
    display: flex;
    padding: 24px;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
  }
  .button {
    width: 50%;
    height: 42px;
    border-radius: 8px;
    &-submit {
      background: #222222;
      color: white;
    }
    &-close {
      background: #8992a0;
      color: white;
    }
  }

  .newfolder-input {
    padding: 24px 24px 0 24px;
    input {
      height: 48px;
      margin-top: 8px;
    }
  }

`;

export const DriveNewCollabSubFolderModal: React.FC<
  DriveNewCollabSubFolderModal
> = ({ path, close, mutate }) => {
  const { query } = useRouter();
  const { portal_id } = query;
  const newFolderForm = useForm<{ name: string }>({
    initialValues: {
      name: "",
    },
    validateInputOnChange: true,
    validate: {
      name: (value) => {
        if (value.length < 2) return "フォルダ名は2文字以上で入力してください";
        if (value.match(/[/.*+?^${}()|<>\\=!¥:;@]/g))
          return "特殊文字は利用できません";
        return null;
      },
    },
  });
  const handleSubmit = () => {
    if (newFolderForm.isValid()) {
      ax.post(`api/v1/drive/collab_file/${path ? path : 0}`, {
        type: "directory",
        name: newFolderForm.values.name,
        portal_id: portal_id!,
      })
        .then((res) => {
          notifications.show({
            title: "フォルダを作成しました",
            message: "フォルダを作成しました",
            icon: <IconNotiSuccess />,
          });
          mutate();
          close();
        })
        .catch((error) => {
          let errorTitle = "エラー";
          let errorMessage = "フォルダを作成できませんでした";
          switch (error.response.data?.message) {
            case "Error: depth limit exceeds":
              errorTitle = "共同編集フォルダの作成に失敗しました";
              errorMessage =
                "フォルダの階層が上限を超えます。このフォルダの配下にはフォルダを作成できません。";
              break;
            case "Error: The file or directory name already exists.":
              errorTitle = "共同編集フォルダの作成に失敗しました";
              errorMessage =
                "フォルダーはすでに存在します。別の名称を入力してください。";
              break;
            default:
              break;
          }
          notifications.show({
            title: errorTitle,
            message: errorMessage,
            icon: <IconNotiFailed />,
          });
        });
    }
  };
  return (
    <Styled handleSubmit={handleSubmit} form={newFolderForm} close={close} />
  );
};
