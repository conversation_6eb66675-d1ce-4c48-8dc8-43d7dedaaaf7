import styled from "@emotion/styled";
import { <PERSON><PERSON>, Group, Loader, Text } from "@mantine/core";
import { Dropzone } from "@mantine/dropzone";
import { type UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { useRouter } from "next/router";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import { useRef, useState } from "react";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";

type PresentationProps = {
  className?: string;
  handleSubmit: () => void;
  form: UseFormReturnType<{ file: File | null; thumbnail: File | null }>;
  isUploading: boolean;
  close: () => void;
};

type DriveNewFolderModal = {
  path: number | undefined;
  close: () => void;
  mutate: () => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  handleSubmit,
  form,
  isUploading,
  close,
}) => {
  const openRef = useRef<() => void>(() => {});
  return (
    <div className={className}>
      <div className="modal-wrap">
        <div className="modal-content">
          <div className="modal-content-input">
            {isUploading ? (
              <div className="modal-loading-container">
                <Text size="sm" mt="md" color="dimmed">
                  ファイルをアップロード中...
                </Text>
                <Loader size="md" />
              </div>
            ) : (
              <>
                <div className="modal-title required">ファイルを選択</div>
                <Dropzone
                  openRef={openRef}
                  multiple={false}
                  onDrop={(files) => form.setFieldValue("file", files[0])}
                  className="drop-zone"
                  activateOnClick={false}
                  {...form.getInputProps("file")}
                  h={117}
                  bg="#F7F8F9"
                  radius={8}
                  styles={{ inner: { pointerEvents: "all" } }}
                >
                  <Group className="drop-zone-inner" position="center">
                    <div className="drop-zone-title">
                      ここにドラッグ&ドロップまたは
                    </div>
                    <Button
                      mih={32}
                      h={32}
                      maw={129}
                      w={129}
                      onClick={() => openRef.current()}
                      className="upload-file-button"
                    >
                      <Text truncate="end">
                        {form.values.file
                          ? form.values.file.name
                          : "ファイルを選択"}
                      </Text>
                    </Button>
                  </Group>
                </Dropzone>
                <div className="modal-title-thumbnail">サムネイルを選択</div>
                <Dropzone
                  openRef={openRef}
                  multiple={false}
                  onDrop={(thumbnails) =>
                    form.setFieldValue("thumbnail", thumbnails[0])
                  }
                  className="drop-zone-thumbnail"
                  activateOnClick={false}
                  {...form.getInputProps("thumbnail")}
                  h={117}
                  bg="#F7F8F9"
                  radius={8}
                  styles={{ inner: { pointerEvents: "all" } }}
                  accept={["image/png", "image/jpeg", "image/jpg"]}
                >
                  <Group className="drop-zone-inner" position="center">
                    <div className="drop-zone-title">
                      ここにドラッグ&ドロップまたは
                    </div>
                    <Button
                      mih={32}
                      h={32}
                      maw={129}
                      w={129}
                      onClick={() => openRef.current()}
                      className="upload-file-button"
                    >
                      <Text truncate="end">
                        {form.values.thumbnail
                          ? form.values.thumbnail.name
                          : "ファイルを選択"}
                      </Text>
                    </Button>
                    <div className="drop-zone-title">
                      推奨サイズ(1280px × 720px)
                    </div>
                  </Group>
                </Dropzone>
              </>
            )}
          </div>
          <div className="modal-content-actions">
            <Button
              className="modal-button modal-button-cancel"
              type="button"
              onClick={close}
              loading={isUploading}
              loaderProps={{ size: 0 }}
              disabled={isUploading}
            >
              キャンセル
            </Button>
            <Button
              className="modal-button modal-button-submit"
              type="button"
              onClick={handleSubmit}
              loading={isUploading}
              loaderProps={{ size: 0 }}
              disabled={isUploading}
            >
              {isUploading ? "アップロード中..." : "保存する"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  color: ${propcolors.blackLight};
  boder-radius: 8px;
  border: 1px solid ${propcolors.gray[200]};

  .upload-file-button {
    border: 1px solid ${propcolors.gray[200]};
    min-height: 42px;
    background-color: white;
    color: #222222;
    font-size: 14px;
    padding: 0 12px;
    font-weight: 500 !important;
    font-family: "Inter", "system-ui";
    border: 1px solid #e8eaed;
    border-radius: 6px;
  }

  .drop-zone {
    display: flex;
    height: 117px;
    border-radius: 8px;
    border: 0.12px dashed #e8eaed;
    background-color: #f7f8f9;
    margin-top: 8px;
    justify-content: center;
    font-family: "Inter", "system-ui";
    align-items: center;
    flex-direction: column;
    &-thumbnail {
      display: flex;
      height: 150px;
      border-radius: 8px;
      border: 0.12px dashed #e8eaed;
      background-color: #f7f8f9;
      margin-top: 8px;
      justify-content: center;
      font-family: "Inter", "system-ui";
      align-items: center;
      flex-direction: column;
    }
    &-inner {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    &-title {
      font-size: 14px;
      color: #666666;
    }
  }

  .modal {
    &-wrap {
      background: ${propcolors.white};
      border-radius: 8px;
    }
    &-title {
      font-size: 14px;
      color: #666666;
      // 「ファイルを選択」後の「*」用
      &.required {
        position: relative;
        &::after {
          content: "*";
          color: #fa5252;
          font-size: 12px;
          line-height: 1;
          margin-left: 4px;
        }
      }
      &-thumbnail {
        font-size: 14px;
        color: #666666;
        padding: 8px 0px 0px 0px;
      }
    }
    &-header {
      color: ${propcolors.blackLight};
      padding: 16px 40px;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      border-bottom: 1px solid ${propcolors.gray[200]};
    }
    &-loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 329px;
      gap: 16px;
      padding: 24px;
    }
    &-content {
      flex-direction: column;
      display: flex;
      &-input {
        padding: 24px;
        input {
          margin-top: 8px;
          height: 48px;
        }
      }

      &-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 24px 40px;
        gap: 24px;
        background: #f7f8f9;
        border-top: 1px solid ${propcolors.gray[200]};
      }
    }
    &-button {
      width: 284px;
      height: 42px;
      font-size: 14px;
      &-cancel {
        background-color: ${propcolors.greyDefault};
      }
      &-submit {
        background-color: ${propcolors.blackLight};
      }
    }
  }
`;

export const DriveNewFileModal: React.FC<DriveNewFolderModal> = ({
  path,
  close,
  mutate,
}) => {
  const router = useRouter();
  const { portal_id } = router.query;
  const MAX_FILE_SIZE = 500000000;
  const MAX_THUMBNAIL_SIZE = 3 * 1024 * 1024; // 3MB

  const [isUploading, setIsUploading] = useState(false);

  const uploadForm = useForm<{ file: File | null; thumbnail: File | null }>({
    initialValues: {
      file: null,
      thumbnail: null,
    },
    validateInputOnChange: true,
    validate: {
      file: (value) =>
        value && value!.size >= MAX_FILE_SIZE
          ? "ファイルサイズが大きすぎます"
          : null,
      thumbnail: (value) =>
        value && value.size >= MAX_THUMBNAIL_SIZE
          ? "3MB以上のサムネイルは登録できません"
          : null,
    },
  });

  const handleSubmit = async () => {
    if (!uploadForm.values.file) {
      return notifications.show({
        title: "エラー",
        message: "ファイルを登録してください",
        icon: <IconNotiFailed />,
      });
    } else if (uploadForm.values.file.size >= MAX_FILE_SIZE) {
      return notifications.show({
        title: "エラー",
        message: "500MB以下のファイルを選択してください。",
        icon: <IconNotiFailed />,
      });
    } else if (
      uploadForm.values.thumbnail &&
      uploadForm.values.thumbnail.size >= MAX_THUMBNAIL_SIZE
    ) {
      return notifications.show({
        title: "エラー",
        message: "3MB以下のサムネイルを選択してください。",
        icon: <IconNotiFailed />,
      });
    }

    setIsUploading(true);

    const formData = new FormData();
    formData.append("file", uploadForm.values.file!);
    formData.append("type", "normal");
    formData.append("portal_id", portal_id!.toString());

    // ファイル作成のエンドポイントを決定（collab_fileかどうか）
    let baseEndpoint =
      router.pathname === "/drive/collab_files/[file_id]"
        ? `api/v1/drive/collab_file/${path}`
        : `api/v1/drive/file/${path ? path : 0}`;

    // サムネイルがある場合はサムネイルを追加
    if (uploadForm.values.thumbnail) {
      formData.append("thumbnail", uploadForm.values.thumbnail!);
    }

    // ファイルのアップロード
    ax.post(baseEndpoint, formData)
      .then((res) => {
        notifications.show({
          title: "成功",
          message: "ファイルを追加しました",
          icon: <IconNotiSuccess />,
        });
        mutate();
        close();
      })
      .catch((error) => {
        let errorTitle = "エラー";
        let errorMessage = "ファイルを追加できませんでした";

        if (
          error.response.data?.message ===
          "Error: The file or directory name already exists."
        ) {
          errorTitle = "ファイルを追加できませんでした";
          errorMessage =
            "このフォルダには同じ名称のファイルがすでに存在します。";
        }
        notifications.show({
          title: errorTitle,
          message: errorMessage,
          icon: <IconNotiFailed />,
        });
      })
      .finally(() => {
        setIsUploading(false);
      });
  };
  return (
    <Styled
      handleSubmit={handleSubmit}
      form={uploadForm}
      isUploading={isUploading}
      close={close}
    />
  );
};
