import { DriveNewFolderModal } from "./components/newFolder";
import { DriveRenameFileModal } from "./components/renameFile";
import { DriveRenameFolderModal } from "./components/renameFolder";
import { DriveNewFileModal } from "./components/newFile";
import { DriveShareModal } from "./components/share";
import { DriveSharePortalModal } from "./components/sharePortal";
import { DriveDeleteFolder } from "./components/deleteFolder";
import { DriveDeleteFile } from "./components/deleteFile";
import { DriveNewPortalModal } from "./components/newPortal";
import { DriveModal } from "components/Modals/foundation/driveModal";
import { DriveNewCollabRootFolderModal } from "./components/newCollabRootFolder";
import { DriveNewCollabSubFolderModal } from "./components/newCollabSubFolder";
import { DriveRenameCollabFileModal } from "./components/renameCollabFile";

export const DriveModifierModal: React.FC<DriveModifier> = ({
  fileID,
  filename,
  isLimitDownloadAll,
  mode,
  close,
  opened,
  mutate,
  mutatePortal,
  type,
}) => {
  let title = "";
  let closeMarkEnable = false;

  switch (mode) {
    case "newFolder":
      title = "新規フォルダを追加";
      break;
    case "newCollabRootFolder":
    case "newCollabSubFolder":
      title = "共同編集フォルダを追加";
      break;
    case "newFile":
      title = "ファイルをアップロード";
      break;
    case "renameFile":
    case "renameCollabFile":
      title = "ファイル設定";
      break;
    case "renameFolder":
      title = "フォルダ名を変更";
      break;
    case "renameCollabFolder":
      title = "共同編集フォルダ名を変更";
      break;
    case "share":
      title = "共有する";
      closeMarkEnable = true;
      break;
    case "newPortal":
      title = "新規ポータルを追加";
      break;
    case "deleteFolder":
      title = "フォルダを削除";
      closeMarkEnable = true;
      break;
    case "deleteCollabFolder":
      title = "共同編集フォルダを削除";
      closeMarkEnable = true;
      break;
    case "sharePortal":
      title = "共有する";
      closeMarkEnable = true;
      break;
    case "deleteFile":
      title = "ファイルを削除";
      closeMarkEnable = true;
      break;
  }

  return (
    <DriveModal
      close={close}
      opened={opened}
      title={title}
      mode={mode}
      closeMarkEnable={closeMarkEnable}
    >
      {mode === "share" && (
        <DriveShareModal
          path={fileID}
          close={close}
          mutate={mutate}
          opened={opened}
          isLimitDownloadAll={isLimitDownloadAll ?? false}
          type={type}
        />
      )}
      {mode === "sharePortal" && (
        <DriveSharePortalModal
          path={fileID}
          close={close}
          mutate={mutate}
          opened={opened}
        />
      )}
      {mode === "deleteFolder" && (
        <DriveDeleteFolder
          path={fileID}
          close={close}
          mutate={mutate}
          fileName={filename ?? ""}
          isCollab = {false}
        />
      )}
      {mode === "deleteCollabFolder" && (
        <DriveDeleteFolder
          path={fileID}
          close={close}
          mutate={mutate}
          fileName={filename ?? ""}
          isCollab = {true}
        />
      )}
      {mode === "deleteFile" && (
        <DriveDeleteFile
          path={fileID}
          close={close}
          mutate={mutate}
          fileName={filename ?? ""}
        />
      )}
      {mode === "newFolder" && (
        <DriveNewFolderModal path={fileID} close={close} mutate={mutate} />
      )}
      {mode === "newCollabRootFolder" && (
        <DriveNewCollabRootFolderModal path={fileID} close={close} mutate={mutate} />
      )}
      {mode === "newCollabSubFolder" && (
        <DriveNewCollabSubFolderModal path={fileID} close={close} mutate={mutate} />
      )}
      {mode === "newFile" && (
        <DriveNewFileModal path={fileID} close={close} mutate={mutate} />
      )}
      {mode === "renameFile" && (
        <DriveRenameFileModal
          close={close}
          path={fileID}
          fileName={filename ?? ""}
          mutate={mutate}
          isLimitDownloadAll={isLimitDownloadAll ?? false}
        />
      )}
      {mode === "renameCollabFile" && (
        <DriveRenameCollabFileModal
          close={close}
          path={fileID}
          fileName={filename ?? ""}
          mutate={mutate}
        />
      )}
      {mode === "renameFolder" && (
        <DriveRenameFolderModal
          close={close}
          path={fileID}
          folderName={filename ?? ""}
          mutate={mutate}
          isCollab={false}
        />
      )}
      {mode === "renameCollabFolder" && (
        <DriveRenameFolderModal
          close={close}
          path={fileID}
          folderName={filename ?? ""}
          mutate={mutate}
          isCollab={true}
        />
      )}
      {mode === "newPortal" && (
        <DriveNewPortalModal close={close} mutate={mutatePortal} />
      )}
    </DriveModal>
  );
};
