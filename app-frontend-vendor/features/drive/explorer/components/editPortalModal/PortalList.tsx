import styled from "@emotion/styled";
import { <PERSON><PERSON>, Stack } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { ax } from "utils/axios";
import { PortalItem } from "./PortalItem";
import { propcolors } from "styles/colors";
import {
  usePortalListState,
  useSetPortalListState,
} from "utils/recoil/drive/portalListState";
import { useEffect, useState } from "react";
import { modals } from "@mantine/modals";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";

type PresentationProps = {
  className?: string;
  onSubmit: () => void;
  deletePortal: (id: number) => void;
  changePortalName: (id: number, name: string) => void;
  currentPortalList: DrivePortal[] | null;
  setCurrentPortalList: React.Dispatch<
    React.SetStateAction<DrivePortal[] | null>
  >;
} & PortalListProps;

type PortalListProps = {
  closeModal: () => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  closeModal,
  onSubmit,
  deletePortal,
  changePortalName,
  currentPortalList,
  setCurrentPortalList,
}) => {
  return (
    <div className={className}>
      <Stack spacing="0" className="portal_list">
        {currentPortalList &&
          currentPortalList.map((portal, index) => (
            <PortalItem
              key={portal.id}
              portal={portal}
              index={index}
              deletePortal={deletePortal}
              changePortalName={changePortalName}
              portalList={currentPortalList}
              setPortalList={setCurrentPortalList}
            />
          ))}
      </Stack>
      <Stack spacing={2} className="editPortal_buttons">
        <Button
          onClick={closeModal}
          className="type-discard action-button"
          aria-label="Close"
          fullWidth
        >
          キャンセル
        </Button>
        <Button
          fullWidth
          className="type-submit action-button"
          onClick={onSubmit}
        >
          保存する
        </Button>
      </Stack>
    </div>
  );
};

const ModalTitle = styled.div`
  width: 100%;
  text-align: center;
  border-bottom: 1px solid ${propcolors.gray[200]};
  padding-bottom: 1rem;
  top: 1.3rem;
  position: absolute;
  margin: 0 -1rem;
  font-weight: 600;
  font-size: 16px;
  line-height: 27px;
`;
const ModalBody = styled.div`
  margin: 1rem 0;
  font-weight: 600;
  font-size: 18px;
  color: #222222;
`;
const ModalBottom = styled.div`
  font-size: 12px;
`;

const Styled = styled(Presentation)`
  .portal_list {
    padding: 24px;
    display: flex;
    gap: 16px;
    flex-direction: column;
    cursor: grab;
  }

  overflow-y: unset;
  .editPortal_buttons {
    border-top: 1px solid ${propcolors.gray[200]};
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 24px 40px;
    gap: 16px;
    background-color: #f7f8f9;
    .type-submit {
      background-color: ${propcolors.blackLight};
    }
    .type-discard {
      background-color: #8992a0;
    }
    .action-button {
      height: 42px;
      font-size: 14px;
      font-weight: 400 !important;
      color: #ffffff;
      border-radius: 8px;
      color: white;
    }
  }
`;

export const PortalList: React.FC<PortalListProps> = ({ closeModal }) => {
  const portalList = usePortalListState();
  const setPortalList = useSetPortalListState();
  const [currentPortalList, setCurrentPortalList] = useState<
    DrivePortal[] | null
  >(null);
  const fetchPortalList = () => {
    ax.get("api/v1/drive/portals").then((res) => {
      setPortalList(res.data);
    });
  };
  useEffect(() => {
    if (portalList) {
      setCurrentPortalList(portalList);
    }
  }, [portalList]);
  useEffect(() => {}, [currentPortalList]);

  const onSubmit = () => {
    ax.post("api/v1/drive/portals/bulk", { portal_list: currentPortalList })
      .then((res) => {
        notifications.show({
          title: "ポータルを更新しました",
          message: "ポータルの変更を保存しました",
          icon: <IconNotiSuccess />,
          autoClose: 3000,
        });
        fetchPortalList();
        closeModal();
      })
      .catch((err) => {
        notifications.show({
          title: "ポータルの更新に失敗しました",
          message: "ポータルの変更を保存できませんでした",
          icon: <IconNotiFailed />,
          autoClose: 3000,
        });
      });
  };
  const changePortalName = (id: number, name: string) => {
    setCurrentPortalList((prev) => {
      if (prev) {
        return prev.map((portal) => {
          if (portal.id === id) {
            return {
              ...portal,
              name: name,
            };
          } else {
            return portal;
          }
        });
      } else {
        return prev;
      }
    });
  };
  const deletePortal = (id: number) => {
    modals.openConfirmModal({
      title: <ModalTitle>ポータルの削除</ModalTitle>,
      labels: {
        confirm: "削除する",
        cancel: "キャンセル",
      },
      size: "640px",
      children: (
        <div style={{ padding: "0.5rem" }}>
          <ModalBody>本当にこのポータルを削除しますか？</ModalBody>
          <ModalBottom>
            ポータルを削除すると、そのポータルに関連するフォルダ・ファイルも削除されます。<br/>
            ※この変更は確定後に反映されます。
          </ModalBottom>
        </div>
      ),
      confirmProps: {
        sx: {
          width: "284px",
          borderRadius: "8px",
          fontSize: "14px",
          lineHeight: "14px",
          paddingTop: "13px",
          paddingBottom: "13px",
          marginRight: "8px",
          marginBottom: "8px",
          backgroundColor: `${propcolors.black}`,
          color: `${propcolors.white}`,
          height: "auto",
          "&:hover": {
            backgroundColor: `${propcolors.black}`,
          },
        },
      },
      cancelProps: {
        // variant: "outline",
        sx: {
          borderRadius: "8px",
          width: "284px",
          left: "24px",
          paddingTop: "13px",
          paddingBottom: "13px",
          position: "absolute",
          marginBottom: "8px",
          height: "auto",
          lineHeight: "14px",
          fontSize: "14px",
          borderColor: `${propcolors.greyDefault}`,
          backgroundColor: `${propcolors.greyDefault}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.greyDefault}`,
          },
        },
      },
      onConfirm: () => {
        setCurrentPortalList((prev) => {
          if (prev) {
            const newPortalList = prev.filter((portal) => portal.id !== id);
            return newPortalList.map((item, index) => {
              return { ...item, sort_order: index }; // sort_orderを0から振りなおす
            });
          } else {
            return prev;
          }
        });
      },
    });
  };
  return (
    <Styled
      closeModal={closeModal}
      onSubmit={onSubmit}
      deletePortal={deletePortal}
      changePortalName={changePortalName}
      currentPortalList={currentPortalList}
      setCurrentPortalList={setCurrentPortalList}
    />
  );
};
