import styled from "@emotion/styled";
import { <PERSON><PERSON>, Mo<PERSON> } from "@mantine/core";
import { useState } from "react";
import { PortalList } from "./PortalList";
import { useDisclosure } from "@mantine/hooks";
import { propcolors } from "styles/colors";

type PresentationProps = {
  className?: string;
} & DriveEditPortalListProps;

type DriveEditPortalListProps = {};

type DriveNewPortalForm = {
  name: string;
};

const Presentation: React.FC<PresentationProps> = ({ className }) => {
  const [opened, { open, close }] = useDisclosure(false);
  return (
    <div className={className}>
      <Button
        color="gray"
        variant="outline"
        onClick={open}
        className="edit-portal-button"
      >
        ポータル設定
      </Button>
      <Modal
        opened={opened}
        onClose={close}
        withCloseButton={false}
        className={className}
        padding={0}
        size={640}
        maw={640}
        miw={320}
      >
        <div className="modal-wrap">
          <div className="modal-header">
            <p>ポータルを編集する</p>
          </div>
          <div className="modal-content">
            <PortalList closeModal={close} />
          </div>
        </div>
      </Modal>
    </div>
  );
};

const Styled = styled(Presentation)`
  .edit-portal-button {
    height: 42px;
    color: ${propcolors.blackLight};
    boder-radius: 8px;
    font-size: 14px;
    font-weight: 500 !important;
    font-family: "Inter", "system-ui";
    color: #222222;
    border: 1px solid ${propcolors.gray[200]};
  }

  .modal {
    &-wrap {
      background: ${propcolors.white};
      border-radius: 6px;
    }
    &-header {
      color: ${propcolors.blackLight};
      padding: 16px 40px;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      border-bottom: 1px solid ${propcolors.gray[200]};
    }

    &-content {
      flex-direction: column;
      display: flex;
      gap: 16px;
      &-input input {
        margin-top: 8px;
        height: 48px;
      }
      &-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 24px;
      }
    }
`;

export const DriveEditPortalListModal: React.FC<
  DriveEditPortalListProps
> = ({}) => {
  const [isModalOpen, toggleModal] = useState<boolean>(false);
  return <Styled />;
};
