import styled from "@emotion/styled";
import { TextInput, ActionIcon } from "@mantine/core";
import React from "react";
import Image from "next/image";
import { useDrop, useDrag } from "react-dnd";

type PresentationProps = {
  portal: DrivePortal;
  index: number;
  deletePortal: (id: number) => void;
  changePortalName: (id: number, name: string) => void;
  className?: string;
  moveItem: (fromIndex: number, toIndex: number) => void;
};

type PortalItemProps = {
  portal: DrivePortal;
  index: number;
  deletePortal: (id: number) => void;
  changePortalName: (id: number, name: string) => void;
  portalList: DrivePortal[] | null;
  setPortalList: React.Dispatch<React.SetStateAction<DrivePortal[] | null>>;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  portal,
  index,
  deletePortal,
  changePortalName,
  moveItem,
}) => {
  return (
    <DraggableItem
      className={className}
      key={portal.id}
      index={portal.sort_order}
      item={portal}
      moveItem={moveItem}
    >
      <div className="portal_item_index">{index + 1}</div>
      <div className="portal_item_name">
        <TextInput
          className="portal_item_name_input"
          value={portal.name}
          onChange={(e) => changePortalName(portal.id, e.target.value)}
        />
      </div>
      <div className="portal_item_delete">
        <ActionIcon onClick={() => deletePortal(portal.id)}>
          <Image
            src="/icons/bin-black.svg"
            width={16.67}
            height={16.67}
            alt="del-portal"
          />
        </ActionIcon>
      </div>
    </DraggableItem>
  );
};

const Styled = styled(Presentation)`
  .portal_item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 0.25rem;
    box-sizing: border-box;
    gap: 8px;
    &_handle {
      color: #999;
    }
    &_index {
    display: block;
    font-size: 12px; 
    color: #666666;
    font-weight: 400 !important;
    margin: 0 8px;
    }
    &_name {
      flex: 1;
      text-align: center;
      &_input {
        input {
          min-height: 48px;
          padding: 10px 16px;
        }
      }
    }
    &_delete {
      color: #999;
      cursor: pointer;
    }
  }
`;

export const PortalItem: React.FC<PortalItemProps> = ({
  portal,
  index,
  deletePortal,
  changePortalName,
  portalList,
  setPortalList,
}) => {
  const moveItem = (fromIndex: number, toIndex: number) => {
    if (portalList) {
      const items = portalList.map((item) => ({ ...item }));
      const [movedItem] = items.splice(fromIndex, 1);
      items.splice(toIndex, 0, movedItem);
      // 並び替えたあとのsort_orderを更新
      items.forEach((item, index) => {
        item.sort_order = index;
      });
      setPortalList(items);
    }
  };
  return (
    <Styled
      portal={portal}
      index={index}
      deletePortal={deletePortal}
      changePortalName={changePortalName}
      moveItem={moveItem}
    />
  );
};
// カスタムカラムの並び替え用の型を定義
type DraggableItemProps = {
  item: DrivePortal;
  index: number;
  moveItem: (fromIndex: number, toIndex: number) => void;
  [key: string]: any;
};
// ドラッグアイタムの型を定義
type DraggedItem = {
  type: string;
  index: number;
  item: DrivePortal;
};

const ItemType = {
  ITEM: "ITEM",
};

const DraggableItem: React.FC<DraggableItemProps> = ({
  item,
  index,
  moveItem,
  ...props
}) => {
  const ref = React.useRef<HTMLTableDataCellElement | null>(null);

  const [, drop] = useDrop<DraggedItem>({
    accept: ItemType.ITEM,
    hover(draggedItem, monitor) {
      // ref.currentが存在しない場合は何もしない
      if (!ref.current) {
        return;
      }
      const draggedItemIndex = item.sort_order;
      const targetItemIndex = draggedItem.index;

      // 自分自身との入れ替えは行わない
      if (draggedItemIndex === targetItemIndex) {
        return;
      }

      // 現在の要素の位置とサイズに関する情報を取得する
      const targetElementRect = ref.current?.getBoundingClientRect();

      // 現在の要素の縦の中心位置Yを計算する
      const targetElementMiddleY =
        (targetElementRect.bottom - targetElementRect.top) / 2;

      // マウスの現在の位置を取得する
      const mousePosition = monitor.getClientOffset();

      // マウスの現在の位置から、要素の上端までの距離を計算する
      const mousePositionFromTargetElementTop = mousePosition
        ? mousePosition.y - targetElementRect.top
        : 0;

      if (
        // indexを比較して下にドラッグする場合と判定
        draggedItemIndex < targetItemIndex &&
        // 入れ替え対象のTopからマウスまでの位置が入れ替え対象の中心より上にある場合入れ替えない
        mousePositionFromTargetElementTop > targetElementMiddleY
      ) {
        return;
      }

      // 上にドラッグする場合
      if (
        draggedItemIndex > targetItemIndex &&
        mousePositionFromTargetElementTop < targetElementMiddleY
      ) {
        return;
      }
      // 実際にアクションを実行する時間
      moveItem(draggedItem.index, index);
      draggedItem.index = index;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: ItemType.ITEM,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(drop(ref));

  return (
    <div
      style={{
        opacity: isDragging ? 0.5 : 1,
      }}
      {...props}
    >
      <div className="portal_item" ref={ref}>
        <Image src="/icons/burger.svg" width={16} height={16} alt="drag-item" />
        {props.children}
      </div>
    </div>
  );
};
