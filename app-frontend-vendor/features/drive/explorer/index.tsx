import styled from "@emotion/styled";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Skeleton, Text, Tooltip } from "@mantine/core";
import { notifications } from "@mantine/notifications";
import { CustomBreadcrumb } from "components/breadcrumb";
import { CommonListLayout } from "components/layouts/commonListLayout";
import dayjs from "dayjs";
import saveAs from "file-saver";
import Head from "next/head";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/router";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { useCallback, useEffect, useMemo, useState } from "react";
import { propcolors } from "styles/colors";
import useSWR from "swr";
import { ax } from "utils/axios";
import {
  usePortalListState,
  useSetPortalListState,
} from "utils/recoil/drive/portalListState";
import { DriveEditHeaderModal } from "./components/editHeaderModal";
import { SearchFilesModal } from "./components/searchFilesModal";
import { DriveEditPortalListModal } from "./components/editPortalModal";
import { DriveModifierModal } from "./components/modifyModal";
import { DriveExplorerTable } from "./components/table";
import { FilesExplorerTable } from "./components/table/components/file-list";
import { CollabDriveExplorerTable } from "./components/collabTable";
import { CollabFilesExplorerTable } from "./components/collabTable/components/file-list";

type DriveContent =
  | {
      type: "loading";
    }
  | {
      type: "files";
      files: DriveExplorerData[];
      folders: DriveExplorerData[];
      collabFiles: DriveExplorerData[];
      collabFolders: DriveExplorerData[];
    }
  | {
      type: "no_files";
    }
  | {
      type: "no_portal";
    };

type DriveExplorerProps = {};

type PresentationProps = {
  className?: string;
  data: driveDirectory | null;
  openModal: (
    mode: DriveModifierMode,
    filename?: string,
    fileID?: number
  ) => void;
  fileID: number | undefined;
  isValidating: boolean;
  isValidatingPortalList: boolean;
  headerFetching: boolean;
  mutate: () => void;
  downloadLogData: () => void;
  portalList: DrivePortal[] | null;
  selectedPortalID: number;
  headerData: { url: string; contentType: string };
  mutateHeader: () => void;
  isCollab: boolean;
} & DriveExplorerProps;
const Presentation: React.FC<PresentationProps> = ({
  className,
  data,
  openModal,
  fileID,
  isValidating,
  isValidatingPortalList,
  mutate,
  downloadLogData,
  portalList,
  selectedPortalID,
  headerData,
  mutateHeader,
  headerFetching,
  isCollab,
}) => {
  const router = useRouter();
  const { file_id } = router.query;
  const [headerImageError, setHeaderImageError] = useState<boolean>(false);
  const MAX_TEXT_DISPLAY_LENGTH = 20;

  const isRoot = file_id === undefined || file_id === "0";

  const currentPortal = portalList?.find(
    (portal) => portal.id === selectedPortalID
  );

  const breadcrumbs = useMemo(
    () => [
      {
        id: "drive-screen-root",
        title: "ホーム",
        href: "/drive" + "?" + `portal_id=${selectedPortalID}`,
      },
      ...(isValidating
        ? [
            {
              id: "drive-loading",
              title: "loading",
              href: "/drive" + "?" + `portal_id=${selectedPortalID}`,
            },
          ]
        : [
            ...(currentPortal
              ? [
                  {
                    id: "drive-main-portal",
                    title: currentPortal.name,
                    href: "/drive" + "?" + `portal_id=${selectedPortalID}`,
                  },
                ]
              : []),
            ...(data && Array.isArray(data?.breadcrumbs)
              ? data.breadcrumbs.map((item, index) => {
                  const basePageUrl = isCollab
                    ? `/drive/collab_files/${item.id}`
                    : `/drive/${item.id}`;
                  if (index === 0) {
                    return {
                      id: `drive-${item.id}-${index}`,
                      title: item.name,
                      href: basePageUrl + "?" + `portal_id=${selectedPortalID}`,
                    };
                  } else {
                    return {
                      id: `drive-${item.id}`,
                      title: item.name,
                      href: `${basePageUrl}?portal_id=${selectedPortalID}&file_id=${data?.breadcrumbs[index - 1].id}`,
                    };
                  }
                })
              : []),
          ]),
    ],
    [isValidating, data]
  );

  useEffect(() => {
    if (headerFetching) {
      setHeaderImageError(false);
    }
    if (!headerFetching && (!headerData || headerData?.url == "")) {
      setHeaderImageError(true);
    }
  }, [headerFetching, headerData]);

  const handerImageError = () => {
    if (headerFetching) return;
    setHeaderImageError(true);
  };

  const content = useMemo<DriveContent>(() => {
    if (isValidating || isValidatingPortalList) {
      return {
        type: "loading",
      };
    } else if (
      (data?.files && data.files.length > 0) ||
      (data?.collab_files && data.collab_files.length > 0)
    ) {
      return {
        type: "files",
        folders: data.files
          ? (data.files as DriveExplorerData[])
              .map((item) => {
                if (item.type === "directory") {
                  return item;
                }
              })
              .filter((item): item is DriveExplorerData => item !== undefined)
          : [],
        files: data.files
          ? (data.files as DriveExplorerData[])
              .map((item) => {
                if (item.type === "normal") {
                  return item;
                }
              })
              .filter((item): item is DriveExplorerData => item !== undefined)
          : [],
        collabFolders: data.collab_files
          ? (data.collab_files as DriveExplorerData[])
              .map((item) => {
                if (item.type === "directory") {
                  return item;
                }
              })
              .filter((item): item is DriveExplorerData => item !== undefined)
          : [],
        collabFiles: data.collab_files
          ? (data.collab_files as DriveExplorerData[])
              .map((item) => {
                if (item.type === "normal") {
                  return item;
                }
              })
              .filter((item): item is DriveExplorerData => item !== undefined)
          : [],
      };
    } else if (currentPortal) {
      return {
        type: "no_files",
      };
    } else {
      return {
        type: "no_portal",
      };
    }
  }, [isValidating, isValidatingPortalList, data, portalList]);

  return (
    <>
      <CommonListLayout className={className} style={{ overflow: "hidden" }}>
        <Head>
          <title>ポータル | PartnerProp</title>
        </Head>
        <header>
          <CustomBreadcrumb
            title="ポータル"
            list={breadcrumbs}
            isMultipleLongText={true}
          />
          <div className="header-buttons">
            <SearchFilesModal />
            <DriveEditHeaderModal mutate={mutateHeader} />
            <DriveEditPortalListModal />
            <Button
              onClick={downloadLogData}
              color="gray"
              variant="outline"
              className="header-buttons-download"
            >
              ログデータ出力
            </Button>
          </div>
        </header>
        <div className="explorer_tables">
          <div className="commonList-header-image">
            <Image
              src={
                !headerFetching &&
                (!headerData || headerData?.url == "" || headerImageError)
                  ? "/images/drive/header-image.png"
                  : headerData?.url
              }
              alt=""
              className={`commonList-header-image-item ${headerFetching ? `hidden` : ""}`}
              onError={() => handerImageError()}
              layout="fill"
              objectFit="contain"
            />
            {headerFetching && (
              <Skeleton
                height={150}
                className="commonList-header-image-skeleton"
              />
            )}
          </div>
          <div className="explorer_tables_tab_list">
            <div className="explorer_tables_tab_list-inner">
              {portalList &&
                portalList.length > 0 &&
                portalList.map((portal, index) => (
                  <Link
                    href={`/drive?portal_id=${portal.id}`}
                    className={`explorer_tables_tab_list-tab ${
                      selectedPortalID === portal.id && "active"
                    }`}
                    key={portal.id}
                  >
                    <div className="explorer_tables_tab_list-tab-text">
                      {portal.name.length > MAX_TEXT_DISPLAY_LENGTH ? (
                        <Tooltip
                          label={portal.name}
                          multiline={true}
                          withArrow
                          position="bottom"
                          className="explorer_tables_tab_list-tab-tooltip"
                          maw={233.4}
                          transitionProps={{ duration: 200 }}
                        >
                          <Text>{`${portal.name.slice(0, MAX_TEXT_DISPLAY_LENGTH)}...`}</Text>
                        </Tooltip>
                      ) : (
                        <Text>{portal.name}</Text>
                      )}
                    </div>
                  </Link>
                ))}
            </div>
            <div className="explorer_tables_tab_list-add_menu">
              <Menu shadow="md" width={240}>
                <Menu.Target>
                  <Button size="md" radius="md">
                    新規追加
                  </Button>
                </Menu.Target>
                <Menu.Dropdown style={{ textAlignLast: "center" }}>
                  <Menu.Item
                    onClick={() => {
                      openModal("newPortal");
                    }}
                  >
                    ポータルを追加
                  </Menu.Item>
                  {!!currentPortal && (
                    <>
                      {(isRoot ||
                        router.pathname != "/drive/collab_files/[file_id]") && (
                        <Menu.Item
                          onClick={() => {
                            openModal("newFolder", undefined, fileID);
                          }}
                        >
                          フォルダを追加
                        </Menu.Item>
                      )}
                      {isRoot && (
                        <Menu.Item
                          onClick={() => {
                            openModal("newCollabRootFolder", undefined, fileID);
                          }}
                        >
                          共同編集フォルダを追加
                        </Menu.Item>
                      )}
                      {router.pathname === "/drive/collab_files/[file_id]" && (
                        <Menu.Item
                          onClick={() => {
                            openModal("newCollabSubFolder", undefined, fileID);
                          }}
                        >
                          共同編集フォルダを追加
                        </Menu.Item>
                      )}
                      <Menu.Item
                        disabled={!currentPortal}
                        onClick={() => {
                          openModal("newFile", undefined, fileID);
                        }}
                      >
                        ファイルを追加
                      </Menu.Item>
                      <Menu.Item
                        onClick={() => {
                          router.push(
                            `/drive/document?portal_id=${selectedPortalID}`,
                          );
                        }}
                      >
                        ドキュメントを作成
                      </Menu.Item>
                    </>
                  )}
                </Menu.Dropdown>
              </Menu>
            </div>
          </div>
          {(() => {
            switch (content.type) {
              case "files":
                return (
                  <div className="explorer_tables_inner">
                    {content.folders.length > 0 && (
                      <DriveExplorerTable
                        mutate={mutate}
                        data={content.folders}
                        path={fileID}
                        openModal={openModal}
                        isRoot={isRoot}
                        share={() => openModal("share", undefined, fileID)}
                        sharePortal={() =>
                          openModal("sharePortal", undefined, selectedPortalID)
                        }
                        type="directory"
                      />
                    )}
                    {content.collabFolders.length > 0 && (
                      <CollabDriveExplorerTable
                        mutate={mutate}
                        data={content.collabFolders}
                        path={fileID}
                        openModal={openModal}
                        isRoot={isRoot}
                        share={() => openModal("share", undefined, fileID)}
                        sharePortal={() =>
                          openModal("sharePortal", undefined, selectedPortalID)
                        }
                        type="directory"
                      />
                    )}
                    {content.files.length > 0 && (
                      <FilesExplorerTable
                        mutate={mutate}
                        data={content.files}
                        path={fileID}
                        openModal={openModal}
                        isRoot={isRoot}
                        type="normal"
                        share={() => openModal("share", undefined, fileID)}
                      />
                    )}
                    {content.collabFiles.length > 0 && (
                      <CollabFilesExplorerTable
                        mutate={mutate}
                        data={content.collabFiles}
                        path={fileID}
                        openModal={openModal}
                        isRoot={isRoot}
                        type="normal"
                        share={() => openModal("share", undefined, fileID)}
                      />
                    )}
                  </div>
                );
              case "no_files":
                return (
                  <div className="explorer_tables_empty">
                    <Image
                      src="/images/drive/empty.png"
                      alt="empty"
                      unoptimized
                      width={230}
                      height={230}
                    />
                    <div className="explorer_tables_text">
                      <Text size="xl" weight="bold">
                        新規追加ボタンからファイルをアップロード
                      </Text>
                      <Text size="sm" color="dimmed">
                        新規加ボタンを押すとフォルダもしくはファイルを追加できます。
                      </Text>
                    </div>
                  </div>
                );
              case "no_portal":
                return (
                  <div className="explorer_tables_empty">
                    <Image
                      src="/images/drive/empty.png"
                      alt="empty"
                      unoptimized
                      width={230}
                      height={230}
                    />
                    <div className="explorer_tables_text">
                      <Text size="xl" weight="bold">
                        新規追加ボタンからポータルを追加
                      </Text>
                      <Text size="sm" color="dimmed">
                        新規加ボタンを押すとポータルを追加できます。
                      </Text>
                    </div>
                  </div>
                );
              case "loading":
                return (
                  <div className="drive-loading">
                    <Loader size="lg" />
                  </div>
                );
              default:
                return (
                  <div className="drive-loading">
                    {/* <Loader size="lg" /> */}
                  </div>
                );
            }
          })()}
        </div>
      </CommonListLayout>
    </>
  );
};

const Styled = styled(Presentation)`
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
  ::-webkit-scrollbar {
    display: none;
  }

  .tooltip-content {
    .mantine-Tooltip-tooltip {
      height: auto;
      font-size: 12px;
      line-height: 14px;
      font-weight: 400;
      top: -28px !important;
    }
    .mantine-Tooltip-arrow {
      border-left: 4px solid transparent !important;
      border-right: 4px solid transparent !important;
      border-bottom: 4px solid #212529 !important;
      -moz-transform: rotate(180deg) !important;
      -ms-transform: rotate(180deg) !important;
      transform: rotate(180deg) !important;
      position: absolute !important;
      top: 25px !important;
      background-color: transparent;
    }
  }

  .image-loaded {
    z-index: 21 !important;
  }

  .drive-screen-breadcrumb {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    &-chevron {
      margin-left: 8px;
    }
    &-item {
      display: flex;
      align-items: center;
      p {
        font-size: 14px;
        font-weight: 400;
        min-height: 20px;
        color: #222222;
        cursor: default;
      }
      a {
        font-size: 14px;
        display: flex;
        min-height: 20px;
        font-weight: 400;
        color: #222222;
      }
    }
  }

  header {
    flex-wrap: wrap;
    gap: 10px;
  }

  .drive-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 70%;
  }

  .commonList-header {
    display: flex;
    align-items: center;
    gap: 24px !important;
    &-image {
      width: calc(100% - 48px);
      max-width: 1920px;
      height: calc(100vw * (150 / 1920)); /* 画面幅に基づいて高さを計算 */
      max-height: 150px;
      position: relative;
      margin: 0 auto;
      border-radius: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      background-color: transparent;
      overflow: hidden;

      &-item {
        border-radius: 12px;
        width: calc(100% - 48px);
        height: auto;
        max-height: 150px;
        position: relative;
        z-index: 19;
      }
      &-item.hidden {
        visibility: hidden;
      }
      &-skeleton {
        border-radius: 12px;
        max-height: 150px;
        position: absolute;
        top: 0;
        z-index: 20;
        left: 24px;
        width: calc(100% - 48px);
      }
    }
  }

  .header-buttons {
    &-download {
      height: 42px;
      color: ${propcolors.blackLight};
      font-family: "Inter", "system-ui";
      font-weight: 500;
      boder-radius: 8px;
      border: 1px solid ${propcolors.gray[200]};
    }
  }

  .navbar {
    &-title {
      display: flex;
      align-items: center;
      gap: 2rem;
    }
    &-buttons {
      display: flex;
      gap: 10px;
    }
  }
  .explorer_tables {
    max-width: calc(100vw - 80px);
    &_create {
      margin-left: 16px;
      color: ${propcolors.blackLight};
      font-weight: 600 !important;
    }
    &_header {
      height: 40px;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      align-items: center;
      &-buttons {
        display: flex;
        gap: 10px;
      }
    }
    &_inner {
      display: grid;
      max-height: calc(100vh - 436px);
      overflow-y: scroll;
      scrollbar-width: none;
      -ms-overflow-style: none;
      ::-webkit-scrollbar {
        display: none;
      }
    }
    &_tab_list {
      display: flex;
      border-bottom: 1px solid ${propcolors.gray[200]};
      height: fit-content;
      align-items: center;
      padding: 16px 24px;
      gap: 16px;
      &-inner {
        overflow-x: auto;
        flex-grow: 1;
        display: grid;
        grid-auto-flow: column;
        align-items: center;
        justify-content: start;
        box-sizing: border-box;
      }
      &-tab {
        width: 233.4px;
        height: 53px;
        padding: 8px 16px;
        text-align: center;
        border: 1px solid ${propcolors.gray[200]};
        border-right: none;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        background-color: ${propcolors.white};
        color: ${propcolors.blackLight};
        display: flex;
        justify-content: center;
        align-items: center;
        &-tooltip {
          max-width: 233.4px;
        }
        &.active {
          background-color: ${propcolors.blackLight};
          color: ${propcolors.white};
          border: 1px transparent;
        }
        &:first-child {
          border-start-start-radius: 12px;
          border-end-start-radius: 12px;
        }
        &:last-child {
          border-start-end-radius: 12px;
          border-end-end-radius: 12px;
          border-right: 1px solid ${propcolors.gray[200]};
        }
      }
      &-add_menu {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
      }
    }
  }

  .explorer_tables_empty {
    padding: 80px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 32px;
  }
  .explorer_tables_text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }
`;

export const DriveExplorer: PageWithTransitionKeyGetter<React.FC> = () => {
  const [modalInnerProps, setModalInnerProps] =
    useState<ModalInnerProps | null>(null);
  const router = useRouter();
  const { query, push } = router;
  const { portal_id } = query;
  const portalList = usePortalListState();
  const setPortalList = useSetPortalListState();
  const isCollab = router.pathname === "/drive/collab_files/[file_id]";
  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => {
        return res.data;
      })
      .catch((err) => {
        // test環境デバック用
        switch (err.response.data.error) {
          case "Error: the directory does not exist":
          case "Error: cannot access to the file":
            notifications.hide("drive-folder-not-found");
            notifications.show({
              id: "drive-detail-not-found",
              title: "フォルダが存在しませんでした",
              message:
                "該当フォルダが削除された、またはアクセス権がない可能性があります",
              icon: <IconNotiFailed />,
              autoClose: 5000,
            });
            router.push(`/drive`);
            break;

          case "Error: cannot get list from non-directory":
            notifications.hide("drive-invalid-url");
            notifications.show({
              id: "drive-invalid-url",
              title: "無効なURLです",
              message: "ディレクトリまたはファイルが存在しない可能性があります",
              icon: <IconNotiFailed />,
              autoClose: 5000,
            });
            router.push(`/drive?portal_id=${portal_id}`);
            break;

          default:
            break;
        }
      });
  const pushPortal = (portal_id: number | null) => {
    if (router.pathname === "/drive") {
      push({
        query: portal_id
          ? {
              portal_id: portal_id,
            }
          : {},
      });
    }
  };
  const {
    data: portalListData,
    mutate: mutatePortalList,
    isValidating: isValidatingPortalList,
  } = useSWR("api/v1/drive/portals", fetcher, { revalidateOnFocus: false });
  const {
    data: headerData,
    mutate: mutateHeader,
    isValidating: isValidatingHeader,
  } = useSWR("api/v1/drive/header", fetcher, { revalidateOnFocus: false });
  const { data, mutate, isValidating } = useSWR(
    () => {
      // URLがドライブページか判定（/drive/detail/[file_id]の場合にfetchしないように）
      if (
        router.pathname === "/drive/[file_id]" ||
        router.pathname === "/drive"
      ) {
        // URLパラメータがReady状態かつportal_idが存在するかチェック
        if (router.isReady && portal_id) {
          return query.file_id
            ? `api/v1/drive/file/${query.file_id}/list/${portal_id}` //file_idが存在すればリストを取得
            : `api/v1/drive/file/${0}/list/${portal_id}`; //file_idが存在しなければルートディレクトリを取得
        }
      } else if (router.pathname === "/drive/collab_files/[file_id]") {
        // URLパラメータがReady状態かつportal_idが存在するかチェック
        if (router.isReady && portal_id) {
          return query.file_id
            ? `api/v1/drive/collab_file/${query.file_id}/list/${portal_id}` //file_idが存在すればリストを取得
            : `api/v1/drive/file/${0}/list/${portal_id}`; //file_idが存在しなければルートディレクトリを取得
        }
      }
      return null;
    },
    fetcher,
    { revalidateOnFocus: false }
  );

  useEffect(() => {
    setPortalList(portalListData);
  }, [portalListData]);

  const openModal = useCallback(
    (
      mode: DriveModifierMode,
      filename?: string,
      fileID?: number,
      isLimitDownloadAll: boolean = false
    ) => {
      setModalInnerProps({
        mode,
        filename,
        fileID,
        opened: true,
        isLimitDownloadAll,
      });
    },
    []
  );

  const mutateData = useCallback(() => {
    if (isCollab) {
      mutate(
        `api/v1/drive/collab_file/${query.file_id ? query.file_id : 0}/list`
      );
    } else {
      mutate(`api/v1/drive/file/${query.file_id ? query.file_id : 0}/list`);
    }
  }, []);

  const mutatePortalData = useCallback(() => mutatePortalList(), []);

  useEffect(() => {
    if (portalList && portalList.length > 0) {
      if (router.isReady && portal_id === undefined) {
        pushPortal(portalList[0].id);
      }
    }
  }, [portalList, router]);

  useEffect(() => {
    const layouttDiv = document.querySelector(".layout-content");
    //add overflow to this div
    if (layouttDiv) {
      layouttDiv.classList.add("disable-scroll");
    }

    return () => {
      if (layouttDiv) {
        layouttDiv.classList.remove("disable-scroll");
      }
    };
  });

  useEffect(() => {
    const portalIdExists = portal_id !== null && portal_id !== undefined;
    const portalListExists = portalList && portalList.length > 0;
    if (portalIdExists && portalListExists) {
      // portalListにportal_idと一致するポータルが存在するか確認
      const isExist = portalList?.some(
        (portal) => portal.id === Number(portal_id)
      );
      if (!isExist) {
        pushPortal(portalList[0].id);
        return;
      }
      mutate(
        isCollab
          ? `api/v1/drive/collab_file/${query.file_id ? query.file_id : 0}/list/${
              portal_id ? portal_id : 0
            }`
          : `api/v1/drive/file/${query.file_id ? query.file_id : 0}/list/${
              portal_id ? portal_id : 0
            }`
      );
    } else if (portalIdExists && !portalListExists) {
      pushPortal(null);
    }
  }, [portal_id, portalList]);

  // const changeSelectedPortal = (newPortal: DrivePortal) => {
  //   pushPortal(newPortal.id);
  // };

  const downloadFile = () => {
    ax.get(`api/v1/drive/file/export_logs`, {
      responseType: "blob",
    }).then((res) => {
      let name = "";
      name = `pp-${dayjs().format("YYYYMMDD")}-drive-logs`;
      let mineType = res.headers["content-type"];
      const blob = new Blob([res.data], { type: mineType });
      saveAs(blob, name);
    });
  };

  return (
    <>
      <DriveModifierModal
        close={() => setModalInnerProps(null)}
        mode={modalInnerProps?.mode}
        fileID={modalInnerProps?.fileID}
        filename={modalInnerProps?.filename}
        opened={modalInnerProps ? true : false}
        mutate={mutateData}
        mutatePortal={mutatePortalData}
        isLimitDownloadAll={modalInnerProps?.isLimitDownloadAll}
        type="directory"
      />
      <Styled
        data={data}
        isValidating={isValidating}
        fileID={query.file_id ? Number(query.file_id) : 0}
        headerFetching={isValidatingHeader}
        isValidatingPortalList={isValidatingPortalList}
        openModal={openModal}
        mutate={mutateData}
        downloadLogData={downloadFile}
        portalList={portalList}
        selectedPortalID={portal_id ? Number(portal_id) : 0}
        headerData={headerData}
        mutateHeader={mutateHeader}
        isCollab={isCollab}
      />
    </>
  );
};

DriveExplorer.getTransitionKey = () => {
  return "/drive";
};
