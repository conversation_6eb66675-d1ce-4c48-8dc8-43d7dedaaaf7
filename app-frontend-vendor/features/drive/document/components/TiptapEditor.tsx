import Bold from '@tiptap/extension-bold';
import BulletList from '@tiptap/extension-bullet-list';
import DropCursor from '@tiptap/extension-dropcursor';
import GapCursor from '@tiptap/extension-gapcursor';
import HardBreak from '@tiptap/extension-hard-break';
import Heading from '@tiptap/extension-heading';
import History from '@tiptap/extension-history';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import Italic from '@tiptap/extension-italic';
import Link from '@tiptap/extension-link';
import ListItem from '@tiptap/extension-list-item';
import OrderedList from '@tiptap/extension-ordered-list';
import Paragraph from '@tiptap/extension-paragraph';
import Strike from '@tiptap/extension-strike';
import Text from '@tiptap/extension-text';
import {
  BubbleMenu,
  type Editor,
  EditorContent,
  useEditor,
} from '@tiptap/react';
import { DBlock } from './extensions/d-block';
import { Document } from './extensions/doc';
import { TrailingNode } from './extensions/trailing-node';
import { debounce } from "../../../../constants/debounce";
import { Code } from "@tiptap/extension-code";
import Blockquote from '@tiptap/extension-blockquote';
import { CodeBlock } from "@tiptap/extension-code-block";
import Image from "@tiptap/extension-image";
import { FileHandler } from "@tiptap/extension-file-handler";
import { mergeAttributes } from '@tiptap/core';
import { useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import React from 'react';

// Custom Image extension with data-id support
const CustomImageExtension = Image.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      'data-id': {
        default: null,
        parseHTML: element => element.getAttribute('data-id'),
        renderHTML: attributes => {
          if (!attributes['data-id']) {
            return {}
          }
          return {
            'data-id': attributes['data-id'],
          }
        },
      },
    }
  },

  renderHTML({ HTMLAttributes }) {
    return ['img', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]
  },
});
// import Underline from '@tiptap/extension-underline';
// import TextStyle from '@tiptap/extension-text-style';
// import Color from '@tiptap/extension-color';
// import Focus from '@tiptap/extension-focus';

interface BlockContent {
  type: string;
  text: string;
  styles: Record<string, unknown>;
}

interface BlockProps {
  textColor: string;
  backgroundColor: string;
  textAlignment: string;
  level?: number;
  language?: string;
  name?: string;
  url?: string;
  caption?: string;
  showPreview?: boolean;
  previewWidth?: number;
}

interface Block {
  id: string;
  type: string;
  props: BlockProps;
  content: BlockContent[];
  children: Block[];
}

interface TiptapEditorProps {
  content?: any; // JSON content instead of HTML string
  onChange?: (content: any) => void; // JSON content instead of HTML
  onJSONChange?: (blocks: Block[]) => void;
  onEditorReady?: (editor: any) => void;
  placeholder?: string;
  disabled?: boolean;
}

// Utility function to convert Tiptap JSON to block format
const convertToBlocks = (tiptapJSON: any): Block[] => {
  if (!tiptapJSON || !tiptapJSON.content) return [];

  const blocks: Block[] = [];

  tiptapJSON.content.forEach((dBlock: any) => {
    if (!dBlock) return;
    
    // Xử lý cả trường hợp dBlock là một block thông thường hoặc một dBlock
    if (dBlock.type === 'dBlock' && dBlock.content) {
      // Xử lý nội dung trong dBlock
      dBlock.content.forEach((node: any) => {
        if (!node) return;
        
        const block: Block = {
          id: node.attrs?.id || uuidv4(),
          type: getBlockType(node),
          props: getBlockProps(node),
          content: node.type !== 'image' ? getBlockContent(node) : [],
          children: getBlockChildren(node)
        };
        blocks.push(block);
      });
    } else if (dBlock.type) {
      // Xử lý trường hợp block trực tiếp không nằm trong dBlock
      const block: Block = {
        id: dBlock.attrs?.id || uuidv4(),
        type: getBlockType(dBlock),
        props: getBlockProps(dBlock),
        content: dBlock.type !== 'image' ? getBlockContent(dBlock) : [],
        children: getBlockChildren(dBlock)
      };
      blocks.push(block);
    }
  });

  return blocks;
};

const getBlockChildren = (node: any): Block[] => {
  if (!node) return [];
  if (node.type === 'bulletList' || node.type === 'orderedList') {
    return processListItems(node.content || []);
  }
  return [];
};

const processListItems = (items: any[]): Block[] => {
  if (!items || !Array.isArray(items)) return [];
  
  return items.map(item => {
    if (!item) return null;
    
    const listItemBlock: Block = {
      id: uuidv4(),
      type: 'listItem',
      props: {
        textColor: 'default',
        backgroundColor: 'default',
        textAlignment: 'left'
      },
      content: getBlockContent(item),
      children: []
    };


    // Process nested lists
    if (item.content && Array.isArray(item.content)) {
      for (const child of item.content) {
        if (child && (child.type === 'bulletList' || child.type === 'orderedList')) {
          listItemBlock.children = processListItems(child.content || []);
        }
      }
    }

    return listItemBlock;
  }).filter((item): item is Block => item !== null);
};

const getBlockType = (node: any): string => {
  switch (node.type) {
    case 'heading':
      return 'heading';
    case 'paragraph':
      return 'paragraph';
    case 'bulletList':
      return 'bulleted_list';
    case 'orderedList':
      return 'numbered_list';
    case 'horizontalRule':
      return 'divider';
    case 'codeBlock':
      return 'code';
    case 'blockquote':
      return 'quote';
    case 'image':
      return 'image';
    case 'listItem':
      return 'list_item';
    default:
      return 'paragraph';
  }
};

const getBlockProps = (node: any): any => {
  const baseProps = {
    textColor: 'default',
    backgroundColor: 'default',
    textAlignment: 'left'
  };

  if (node.type === 'heading') {
    return {
      ...baseProps,
      level: node.attrs?.level || 1
    };
  }

  if (node.type === 'image') {
    return {
      ...baseProps,
      textAlignment: 'center',
      name: node.attrs?.alt || 'image.jpg',
      url: node.attrs?.src || '',
      caption: node.attrs?.caption || '',
      showPreview: true,
      previewWidth: node.attrs?.width || 454
    };
  }

  if (node.type === 'codeBlock') {
    return {
      ...baseProps,
      language: node.attrs?.language || 'plain'
    };
  }

  return baseProps;
};

const getBlockContent = (node: any): BlockContent[] => {
  if (!node.content) return [];

  const content: BlockContent[] = [];

  const processContent = (nodes: any[]) => {
    nodes.forEach((child: any) => {
      if (child.type === 'text') {
        content.push({
          type: 'text',
          text: child.text || '',
          styles: getTextStyles(child.marks || [])
        });
      } else if (child.type === 'hardBreak') {
        // Handle line breaks
        content.push({
          type: 'text',
          text: '\n',
          styles: {}
        });
      } else if (child.content && child.type !== 'bulletList' && child.type !== 'orderedList') {
        // Skip nested lists as they're handled in children
        processContent(child.content);
      }
    });
  };

  processContent(node.content);
  return content;
};

const getTextStyles = (marks: any[]): Record<string, unknown> => {
  const styles: Record<string, unknown> = {};

  marks.forEach((mark: any) => {
    switch (mark.type) {
      case 'bold':
        styles.bold = true;
        break;
      case 'italic':
        styles.italic = true;
        break;
      case 'strike':
        styles.strikethrough = true;
        break;
      case 'code':
        styles.code = true;
        break;
      case 'link':
        styles.link = mark.attrs?.href || '';
        break;
      // case 'underline':
      //   styles.underline = true;
      //   break;
      // case 'textStyle':
      //   if (mark.attrs?.color) {
      //     styles.textColor = mark.attrs.color;
      //   }
      //   break;
    }
  });

  return styles;
};

// Cấu trúc content mặc định cho Tiptap Editor
const defaultContent = {
  type: 'doc',
  content: [
    {
      type: 'dBlock',
      content: [
        {
          type: 'paragraph',
          content: []
        }
      ]
    }
  ]
};

// Helper function to upload image to S3 and update URL
const uploadImageToS3 = async (file: File, imageId: string): Promise<string | null> => {
  try {
    // Thực hiện upload ảnh lên S3 và trả về URL
    // Đây là phần giả lập, bạn cần thay thế bằng code thực tế
    return URL.createObjectURL(file);
  } catch (error) {
    return null;
  }
};

// Helper function to update image URL in editor by data-id
const updateImageUrlById = (editor: any, imageId: string, newUrl: string) => {
  if (!editor || !imageId || !newUrl) return;

  // Tìm tất cả các hình ảnh có data-id tương ứng
  const images = editor.view.dom.querySelectorAll(`img[data-id="${imageId}"]`);
  
  // Cập nhật src cho tất cả các hình ảnh tìm thấy
  images.forEach((img: HTMLImageElement) => {
    img.src = newUrl;
  });

  // Cập nhật trong editor state
  editor.commands.setImage({ src: newUrl });
};

// Helper function to handle image upload
const handleImageUpload = async (editor: any, file: File, pos?: number) => {
  if (!file.type.startsWith('image/')) {
    return;
  }

  const fileReader = new FileReader();

  fileReader.onload = () => {
    try {
      // Generate unique ID for the image
      const imageId = uuidv4();
      const imageSrc = fileReader.result as string;


      // Method 1: Try using setImage command (most reliable)
      try {
        if (pos !== undefined && pos >= 0) {
          // For drag and drop - set cursor position first
          const { state } = editor;
          const { doc } = state;

          if (pos <= doc.content.size) {
            // Set cursor to drop position
            editor.chain().focus().setTextSelection(pos).run();
          }
        }

        // Insert image using setImage command
        editor.chain()
          .focus()
          .setImage({
            src: imageSrc,
            alt: file.name || 'Uploaded image',
            title: file.name || 'Uploaded image',
            'data-id': imageId,
          })
          .run();

        return;
      } catch (setImageError) {
      }

      // Method 2: Try insertContent with dBlock wrapper
      try {
        const imageHTML = `<div data-type="d-block"><img src="${imageSrc}" alt="${file.name || 'Uploaded image'}" title="${file.name || 'Uploaded image'}" data-id="${imageId}" class="editor-image" /></div>`;

        if (pos !== undefined && pos >= 0) {
          const { state } = editor;
          const { doc } = state;

          if (pos <= doc.content.size) {
            // Find the nearest valid position
            const resolvedPos = doc.resolve(Math.min(pos, doc.content.size));
            let insertPos = pos;

            // If we're in the middle of a block, move to the end
            if (resolvedPos.parent.type.name !== 'doc') {
              insertPos = resolvedPos.after();
            }

            editor.chain().focus().insertContentAt(insertPos, imageHTML).run();
          } else {
            editor.chain().focus().insertContent(imageHTML).run();
          }
        } else {
          editor.chain().focus().insertContent(imageHTML).run();
        }

        return;
      } catch (htmlError) {
      }

      // Method 3: Try simple HTML insertion
      try {
        const simpleImageHTML = `<img src="${imageSrc}" alt="${file.name || 'Uploaded image'}" data-id="${imageId}" />`;

        if (pos !== undefined && pos >= 0) {
          const { state } = editor;
          const { doc } = state;

          if (pos <= doc.content.size) {
            editor.chain().focus().setTextSelection(pos).insertContent(simpleImageHTML).run();
          } else {
            editor.chain().focus().insertContent(simpleImageHTML).run();
          }
        } else {
          editor.chain().focus().insertContent(simpleImageHTML).run();
        }

        return;
      } catch (simpleError) {
      }

      // Method 4: Final fallback - force insert at end of document
      try {
        const { state } = editor;
        const { doc } = state;
        const endPos = doc.content.size;

        // Insert at the very end of the document
        editor.chain()
          .focus()
          .setTextSelection(endPos)
          .insertContent(`<img src="${imageSrc}" alt="${file.name || 'Uploaded image'}" data-id="${imageId}" />`)
          .run();

      } catch (fallbackError) {

        // Last resort: try to replace selection
        try {
          editor.chain().focus().deleteSelection().insertContent(`<img src="${imageSrc}" alt="image" />`).run();
        } catch (lastResortError) {
          console.error('Even last resort failed:', lastResortError);
        }
      }

    } catch (error) {
      console.error('Error in handleImageUpload:', error);
    }
  };

  fileReader.onerror = () => {
    console.error('Error reading file:', file.name);
  };

  fileReader.readAsDataURL(file);
};

export const TiptapEditor: React.FC<TiptapEditorProps> = ({
  content = defaultContent,
  onChange = () => {},
  onJSONChange = () => {},
  onEditorReady = () => {},
  placeholder = "内容を入力してください...",
  disabled = false,
}) => {
  const editor = useEditor({
    // Khởi tạo với nội dung mặc định
    content: content || defaultContent,
    onUpdate: debounce(({ editor }) => {
      const json = editor.getJSON();

      // Convert to blocks format
      const blocks = convertToBlocks(json);

      if (onChange) {
        onChange(json);
      }
      if (onJSONChange) {
        onJSONChange(blocks);
      }
    }, 300),
    extensions: [
      Document,
      DBlock,
      TrailingNode,
      Paragraph,
      Text,
      HardBreak,
      Link.configure({
        autolink: true,
        openOnClick: true,
        linkOnPaste: true,
        protocols: ['http', 'https', 'mailto'],
      }),

      DropCursor,
      GapCursor,
      History,
      // Focus.configure({
      //   className: 'has-focus',
      //   mode: 'all',
      // }),
      // mark
      Bold,
      Italic,
      Strike,
      Code,
      // Underline,
      // TextStyle,
      // Color,
      // node
      Blockquote,
      CodeBlock.configure({
        HTMLAttributes: {
          class: 'code-block',
        },
      }),
      // Configure ListItem with enhanced settings for Notion-like behavior
      ListItem.configure({
        HTMLAttributes: {
          class: 'list-item',
        },
      }),
      // Enhanced BulletList for Notion-like behavior
      BulletList.configure({
        HTMLAttributes: {
          class: 'bullet-list',
        },
        itemTypeName: 'listItem',
        keepMarks: true,
        keepAttributes: true,
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'ordered-list',
          start: 1,
        },
        itemTypeName: 'listItem',
        keepMarks: true,
        keepAttributes: true,
      }),
      Heading.configure({ levels: [1, 2, 3] }),
      HorizontalRule,
      CustomImageExtension.configure({
        inline: false,
        allowBase64: true,
        HTMLAttributes: {
          class: 'editor-image',
        },
      }),
      FileHandler.configure({
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp'],
        onDrop: (currentEditor, files, pos) => {
          files.forEach(file => {
            const fileReader = new FileReader()

            fileReader.readAsDataURL(file)
            fileReader.onload = () => {
              currentEditor.chain().insertContentAt(pos, {
                type: 'image',
                attrs: {
                  src: fileReader.result,
                },
              }).focus().run()
            }
          })
        },
        onPaste: (currentEditor, files, htmlContent) => {
          files.forEach(file => {
            if (htmlContent) {
              // if there is htmlContent, stop manual insertion & let other extensions handle insertion via inputRule
              // you could extract the pasted file from this url string and upload it to a server for example
              return false
            }

            const fileReader = new FileReader()

            fileReader.readAsDataURL(file)
            fileReader.onload = () => {
              currentEditor.chain().insertContentAt(currentEditor.state.selection.anchor, {
                type: 'image',
                attrs: {
                  src: fileReader.result,
                },
              }).focus().run()
            }
          })
        },
      }),
    ],
    editable: !disabled,
    editorProps: {
      attributes: {
        class: 'editor',
      },
    },
  });

  // Call onEditorReady when editor is ready
  useEffect(() => {
    if (editor && onEditorReady) {
      onEditorReady(editor);
    }
  }, [editor, onEditorReady]);

  // Update content when prop changes
  useEffect(() => {
    if (!editor) return;

    try {
      const currentJSON = editor.getJSON();

      // Improved content validation
      const newContent = content &&
                        typeof content === 'object' &&
                        content.content &&
                        Array.isArray(content.content) &&
                        content.content.length > 0
        ? content
        : defaultContent;

      // Only update if content is actually different
      const currentJSONString = JSON.stringify(currentJSON);
      const newContentString = JSON.stringify(newContent);

      if (newContentString !== currentJSONString) {

        try {
          // Enhanced content structure validation
          const normalizedContent = {
            type: 'doc',
            content: Array.isArray(newContent?.content)
              ? newContent.content.filter((item: any) => item && typeof item === 'object')
              : [{ type: 'dBlock', content: [{ type: 'paragraph' }] }]
          };

          // Validate each content item has required properties
          const validatedContent = {
            type: 'doc',
            content: normalizedContent.content.map((item: { type: string; content: string | any[]; }) => {
              if (!item || typeof item !== 'object') {
                return { type: 'dBlock', content: [{ type: 'paragraph' }] };
              }

              // Ensure dBlock has proper structure
              if (item.type === 'dBlock') {
                return {
                  ...item,
                  content: Array.isArray(item.content) && item.content.length > 0
                    ? item.content
                    : [{ type: 'paragraph' }]
                };
              }

              return item;
            })
          };

          // Use a timeout to avoid race conditions with keyboard shortcuts
          setTimeout(() => {
            if (editor && !editor.isDestroyed) {
              editor.commands.setContent(validatedContent);
            }
          }, 0);

        } catch (error) {
          // Fallback to default content with timeout
          setTimeout(() => {
            if (editor && !editor.isDestroyed) {
              editor.commands.setContent(defaultContent);
            }
          }, 0);
        }
      }
    } catch (error) {
    }
  }, [content, editor]);


  return (
    <div
      css={{
        marginTop: '20px',
        border: 'none',
        borderRadius: '0px',
        padding: '0px',
        backgroundColor: 'transparent',
        '.editor': {
          outline: 'none',
        },
        '.tiptap': {
          fontSize: '16px',
          lineHeight: '1.6',
          color: '#374151',
          minHeight: '200px',
          padding: '0px',
          border: 'none',
          borderRadius: '0px',
          backgroundColor: 'transparent',
          '&:focus': {
            outline: 'none',
          },
          // Notion-like focus styles
          '.has-focus': {
            borderRadius: '3px',
            backgroundColor: 'rgba(35, 131, 226, 0.07)',
          },
          // Improve cursor visibility
          '.ProseMirror-gapcursor:after': {
            borderTop: '1px solid #3b82f6',
            margin: '0 -4px',
          },
          'ul' : {
            listStyleType: "disc",
            color: '#374151'
          },
          'ol': {
            listStyleType: "decimal"
          },
          ":first-of-type": {
            marginTop: 0
          },
          'ul, ol ': {
            padding: '0 0 0 1.5rem',
            margin: '0.5rem 0',

            "li": {
              marginBottom: '0.25rem',
              position: 'relative',

              "p": {
                marginTop: '0.25em',
                marginBottom: '0.25em',
              },

              // Nested list styling
              "ul, ol": {
                marginTop: '0.25rem',
                marginBottom: '0.25rem',
                paddingLeft: '1.5rem',
              }
            }
          },

          // Enhanced list item styling for better UX
          '.bullet-list': {
            listStyleType: 'disc',

            'li': {
              '&::marker': {
                color: '#6b7280',
              }
            }
          },

          '.ordered-list': {
            listStyleType: 'decimal',

            'li': {
              '&::marker': {
                color: '#6b7280',
                fontWeight: '500',
              }
            }
          },
          'h1, h2, h3, h4, h5, h6' : {
            lineHeight: 1.1,
            marginTop: '2.5rem',
            textWrap: "pretty",
          },
          'h1, h2' : {
            marginTop: '3.5rem',
            marginBottom: '1.5rem',
          },
          'h1' : {
            fontSize: '1.4rem',
          },
          'h2': {
            fontSize: '1.2rem',
          },
          'h3': {
            fontSize: '1.1rem',
          },
          'h4, h5, h6': {
            fontSize: '1rem',
          },
          // Image styles
          'img, .editor-image': {
            maxWidth: '100%',
            height: 'auto',
            borderRadius: '8px',
            margin: '16px 0',
            display: 'block',
            cursor: 'pointer',
            transition: 'opacity 0.2s ease',
            '&:hover': {
              opacity: 0.8,
            },
            '&.ProseMirror-selectednode': {
              outline: '2px solid #3b82f6',
              outlineOffset: '2px',
            },
          },
          'code': {
            backgroundColor:`var(--purple-light)`,
            borderRadius: '0.4rem',
            color: `var(--black)`,
            fontSize: '0.85rem',
            padding: '0.25em 0.3em',
          },
          'pre': {
            background: '#1e1e1e',
            borderRadius: '8px',
            color: '#d4d4d4',
            fontFamily: '"JetBrains Mono", "Fira Code", Consolas, monospace',
            margin: '1.5rem 0',
            padding: '1rem',
            overflow: 'auto',
            fontSize: '14px',
            lineHeight: '1.5',

            "code": {
              background: "none",
              color: "inherit",
              fontSize: 'inherit',
              padding: 0
            },
          },
          '.code-block': {
            background: '#1e1e1e',
            borderRadius: '8px',
            color: '#d4d4d4',
            fontFamily: '"JetBrains Mono", "Fira Code", Consolas, monospace',
            margin: '1.5rem 0',
            padding: '1rem',
            overflow: 'auto',
            fontSize: '14px',
            lineHeight: '1.5',
          },
          'blockquote': {
            borderLeft: '3px solid #e5e7eb',
            margin: '1.5rem 0',
            paddingLeft: '1rem',
            color: '#6b7280',
            fontStyle: 'italic',
            backgroundColor: '#f9fafb',
            padding: '1rem',
            borderRadius: '0 8px 8px 0',
          },

          'hr': {
            borderTop: '1px solid gray',
            margin: '2rem 0',
          },

          'a': {
            "color": `rgb(138, 80, 23)`,
            cursor: "pointer",

            '&:hover': {
              color:` var(--purple-contrast)`
            }
          }
        }
      }}
    >
      {editor && <CustomBubbleMenu editor={editor} />}
      <EditorContent editor={editor} />
    </div>
  );
}

const CustomBubbleMenu = ({ editor }: { editor: Editor }) => {
  return (
    <BubbleMenu
      editor={editor}
      shouldShow={({ editor, view, state, oldState, from, to }) => {
        // Only visible when text is selected (not empty selection) or double clicked
        const { selection } = state;
        const { empty } = selection;

        // Not displayed if selection is empty
        if (empty) return false;

        // Not visible if dragging is in progress
        if (view.dragging) return false;

        // Only visible when text is selected
        return from !== to;
      }}
      tippyOptions={{
        duration: 100,
        placement: 'top',
      }}
    >
      <div
        role="group"
        css={{
          display: 'flex',
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          border: '1px solid rgba(0, 0, 0, 0.1)',
          padding: '4px',
          gap: '2px',
          flexWrap: 'wrap',
          maxWidth: '400px',
        }}
      >
        {/* Text formatting buttons */}
        <div css={{ display: 'flex', gap: '2px', alignItems: 'center' }}>
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleBold().run()}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '6px 8px',
              borderRadius: '4px',
              border: 'none',
              backgroundColor: editor.isActive('bold') ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
              color: editor.isActive('bold') ? '#000' : '#666',
              fontSize: '14px',
              fontWeight: editor.isActive('bold') ? 'bold' : 'normal',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: editor.isActive('bold') ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
              },
            }}
            title="Bold (Ctrl+B)"
          >
            <strong>B</strong>
          </button>
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleItalic().run()}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '6px 8px',
              borderRadius: '4px',
              border: 'none',
              backgroundColor: editor.isActive('italic') ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
              color: editor.isActive('italic') ? '#000' : '#666',
              fontSize: '14px',
              fontStyle: 'italic',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: editor.isActive('italic') ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
              },
            }}
            title="Italic (Ctrl+I)"
          >
            I
          </button>
          {/* Underline button - commented out until extension is installed
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '6px 8px',
              borderRadius: '4px',
              border: 'none',
              backgroundColor: editor.isActive('underline') ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
              color: editor.isActive('underline') ? '#000' : '#666',
              fontSize: '14px',
              textDecoration: editor.isActive('underline') ? 'underline' : 'none',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: editor.isActive('underline') ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
              },
            }}
            title="Underline (Ctrl+U)"
          >
            U
          </button>
          */}
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleStrike().run()}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '6px 8px',
              borderRadius: '4px',
              border: 'none',
              backgroundColor: editor.isActive('strike') ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
              color: editor.isActive('strike') ? '#000' : '#666',
              fontSize: '14px',
              textDecoration: editor.isActive('strike') ? 'line-through' : 'none',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: editor.isActive('strike') ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
              },
            }}
            title="Strikethrough"
          >
            S
          </button>
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleCode().run()}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '6px 8px',
              borderRadius: '4px',
              border: 'none',
              backgroundColor: editor.isActive('code') ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
              color: editor.isActive('code') ? '#000' : '#666',
              fontSize: '14px',
              fontFamily: 'monospace',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: editor.isActive('code') ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
              },
            }}
            title="Code"
          >
            {'</>'}
          </button>
        </div>

        <div css={{ width: '1px', height: '20px', backgroundColor: 'rgba(0, 0, 0, 0.1)', margin: '4px 2px' }} />

        {/* Link button */}
        <button
          type="button"
          onClick={() => {
            const url = window.prompt('Enter URL:');
            if (url) {
              editor.chain().focus().setLink({ href: url }).run();
            }
          }}
          css={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '6px 8px',
            borderRadius: '4px',
            border: 'none',
            backgroundColor: editor.isActive('link') ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
            color: editor.isActive('link') ? '#000' : '#666',
            fontSize: '14px',
            cursor: 'pointer',
            transition: 'all 0.2s',
            '&:hover': {
              backgroundColor: editor.isActive('link') ? 'rgba(0, 0, 0, 0.15)' : 'rgba(0, 0, 0, 0.05)',
            },
          }}
          title="Add Link"
        >
          🔗
        </button>

        {/* Color picker - commented out until extension is installed
        <div css={{ position: 'relative' }}>
          <button
            type="button"
            onClick={() => {
              // Simple color application - in a real app, you'd use a color picker
              editor.chain().focus().setColor('#3b82f6').run();
            }}
            css={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '6px 8px',
              borderRadius: '4px',
              border: 'none',
              backgroundColor: 'transparent',
              color: '#666',
              fontSize: '14px',
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.05)',
              },
            }}
            title="Text Color"
          >
            A<span style={{ color: '#3b82f6', fontSize: '10px', marginLeft: '2px' }}>▼</span>
          </button>
        </div>
        */}
      </div>
    </BubbleMenu>
  );
};
