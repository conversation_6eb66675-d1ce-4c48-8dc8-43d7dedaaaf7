import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { TextSelection } from 'prosemirror-state';
import { DBlockNodeView } from './d-block-node-view';

export interface DBlockOptions {
  HTMLAttributes: Record<string, string | number>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    dBlock: {
      /**
       * Toggle a dBlock
       */
      setDBlock: (position?: number) => ReturnType;
    };
  }
}

export const DBlock = Node.create<DBlockOptions>({
  name: 'dBlock',
  priority: 1000,
  group: 'dBlock',
  content: 'block+',
  draggable: true,
  defining: true,
  selectable: false,
  inline: false,

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  parseHTML() {
    return [{ tag: 'div[data-type="d-block"]' }];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(HTMLAttributes, { 'data-type': 'd-block' }),
      0,
    ];
  },

  addCommands() {
    return {
      setDBlock:
        (position) =>
        ({ state, chain }) => {
          const {
            selection: { from },
            doc,
          } = state;

          const pos = position !== undefined && position !== null ? position : from;
          
          // Create a new dBlock with a paragraph
          const dBlock = state.schema.nodes.dBlock.create(
            {},
            state.schema.nodes.paragraph.create()
          );
          
          // Insert the dBlock at the specified position
          const tr = state.tr.insert(pos, dBlock);
          
          // Set the selection to the new paragraph
          const resolvedPos = doc.resolve(pos + 1); // +1 to move inside the new block
          const selection = TextSelection.near(resolvedPos);
          tr.setSelection(selection);
          
          // Apply the transaction
          return chain().setMeta('addToHistory', true).command(({ tr, dispatch }) => {
            if (dispatch) {
              dispatch(tr);
              return true;
            }
            return false;
          }).run();
        },
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(DBlockNodeView);
  },

  // Add any necessary ProseMirror plugins
  addProseMirrorPlugins() {
    return [];
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Alt-0': () => this.editor.commands.setDBlock(),
      Enter: ({ editor }) => {
        const { state } = editor;
        const { selection } = state;
        const { $head, from } = selection;
        const { depth } = $head;

        // Get current node and its parent
        const currentNode = $head.node(depth);
        const parentNode = depth > 0 ? $head.node(depth - 1) : null;

        // Check if we're in a list
        const isInList = parentNode?.type.name === 'listItem' ||
                        parentNode?.type.name === 'taskItem';

        // If not in a dBlock and not in a list, use default behavior
        if (parentNode?.type.name !== 'dBlock' && !isInList) {
          return false;
        }

        // Handle list items
        if (isInList) {
          const isListEmpty = currentNode.textContent === '' &&
                            currentNode.type.name === 'paragraph';

          // If list item is empty, convert to dBlock
          if (isListEmpty) {
            // Create a new dBlock with a paragraph
            const tr = state.tr;
            const node = state.schema.nodes.dBlock.create(
              {},
              state.schema.nodes.paragraph.create()
            );

            // Replace the current list item with the new dBlock
            tr.replaceWith(from - 1, from + 1, node);

            // Set the selection to the new paragraph inside the dBlock
            const resolvedPos = tr.doc.resolve(from);
            const selection = TextSelection.near(resolvedPos);
            tr.setSelection(selection);

            // Apply the transaction
            editor.view.dispatch(tr);

            return true;
          }

          // Let the default list behavior handle non-empty items
          return false;
        }

        // Handle dBlock specific behavior
        const isAtEnd = $head.parentOffset === $head.parent.content.size;
        const isEmpty = $head.parent.content.size === 0;

        // If we're in a dBlock, handle different scenarios
        if (parentNode?.type.name === 'dBlock') {
          // If we're at the end of the current paragraph or it's empty, create new dBlock
          if (isAtEnd || isEmpty) {
            try {
              // Use a simpler and safer approach with editor commands
              // Find the position after the current dBlock
              const dBlockDepth = depth - 1; // Parent is the dBlock
              const dBlockEnd = $head.after(dBlockDepth);

              // Validate position before proceeding
              if (dBlockEnd > state.doc.content.size) {
                return editor.chain()
                  .splitBlock()
                  .focus()
                  .run();
              }

              // Use insertContentAt which handles position validation internally
              const success = editor.chain()
                .insertContentAt(dBlockEnd, {
                  type: 'dBlock',
                  content: [{ type: 'paragraph' }]
                })
                .focus(dBlockEnd + 1) // Focus inside the new paragraph
                .run();

              if (!success) {
                // Fallback to split block if insertion failed
                return editor.chain()
                  .splitBlock()
                  .focus()
                  .run();
              }

              return true;
            } catch (error) {
              // Fallback to default split behavior
              return editor.chain()
                .splitBlock()
                .focus()
                .run();
            }
          } else {
            // If we're in the middle of text, use default split behavior
            return editor.chain()
              .splitBlock()
              .focus()
              .run();
          }
        }

        // Fallback to default behavior
        return false;
      },
    };
  },
});
