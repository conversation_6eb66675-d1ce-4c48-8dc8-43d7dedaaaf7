"use client";
import { useRouter } from "next/router";
import { ax } from "../../../utils/axios";
import useSWR from "swr";
import { Box, Button, Text, FileInput, TextInput } from "@mantine/core";
import { propcolors } from "styles/colors";
import { CustomBreadcrumb } from "../../../components/breadcrumb";
import { useState } from "react";
import { ImagePreview } from "./components/ImagePreview";
import { notifications } from "@mantine/notifications";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import { TiptapEditor } from "./components/TiptapEditor";

export default function DocumentEditor() {
  const router = useRouter();
  const { query } = router;
  const { portal_id } = query;
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileError, setFileError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [title, setTitle] = useState<string>("");
  const [content, setContent] = useState<any>(null); // JSON content instead of HTML
  const [contentBlocks, setContentBlocks] = useState<any[]>([]);

  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch((_err) => {});

  const { data: portalList } = useSWR("api/v1/drive/portals", fetcher, {
    revalidateOnFocus: false,
  });

  const portal = portalList?.find(
    (item: DrivePortal) => item.id === Number(portal_id),
  );

  const BREADCRUMB_LIST = [
    {
      title: "ホーム",
      href: `/drive${portal_id && `?portal_id=${portal_id}`}`,
      id: "root-nav",
    },
    {
      title: portal?.name,
      href: `/drive${portal_id && `?portal_id=${portal_id}`}`,
      id: "portal-nav-portal",
    },
    {
      title: "名称未設定",
      id: "document",
    },
  ];

  // Image file validation constants
  const ALLOWED_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp'
  ];
  const MIN_FILE_SIZE = 1024; // 1KB
  const MAX_THUMBNAIL_SIZE = 3 * 1024 * 1024; // 3MB

  const validateFile = (file: File): string | null => {
    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
      return "画像ファイル（JPEG、PNG、GIF、WebP）のみアップロード可能です";
    }
    if (file.size > MAX_THUMBNAIL_SIZE) {
      return "サムネイルは3MB以下にしてください";
    }
    if (file.size < MIN_FILE_SIZE) {
      return "ファイルサイズが小さすぎます";
    }
    return null;
  };

  const handleFileChange = (file: File | null) => {
    setFileError(null);

    if (!file) {
      setSelectedFile(null);
      return;
    }

    const error = validateFile(file);
    if (error) {
      setFileError(error);
      setSelectedFile(null);
      return;
    }

    setSelectedFile(file);
  };

  const handleRemoveImage = () => {
    setSelectedFile(null);
    setFileError(null);
  };

  const handleSave = async () => {
    // Validation for content - required
    const isContentEmpty = !contentBlocks ||
      contentBlocks.length === 0 ||
      contentBlocks.every(block =>
        !block.content ||
        block.content.length === 0 ||
        block.content.every((item: any) => !item.text || item.text.trim() === '')
      );

    if (isContentEmpty) {
      return notifications.show({
        title: "エラー",
        message: "内容を入力してください",
        icon: <IconNotiFailed />,
      });
    }

    if (!selectedFile) {
      return notifications.show({
        title: "エラー",
        message: "サムネイルを選択してください",
        icon: <IconNotiFailed />,
      });
    }

    // Validation for thumbnail
    if (selectedFile.size >= MAX_THUMBNAIL_SIZE) {
      return notifications.show({
        title: "エラー",
        message: "3MB以下のサムネイルを選択してください",
        icon: <IconNotiFailed />,
      });
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append("name", title.trim() || "名称未設定"); // Use title input value or default
      formData.append("content", JSON.stringify(contentBlocks)); // Use JSON blocks from TiptapEditor
      formData.append("thumbnail", selectedFile); // This will be saved to thumbnail_path
      formData.append("type", "normal");
      formData.append("portal_id", portal_id!.toString());
      formData.append("is_document", "true");

      const path = 0; // Default path as 0 like in the reference
      await ax.post(`api/v1/drive/file/${path}`, formData);

      notifications.show({
        title: "成功",
        message: "ファイルを保存しました",
        icon: <IconNotiSuccess />,
      });

    } catch (error: any) {
      let errorTitle = "エラー";
      let errorMessage = "ファイルを保存できませんでした";

      if (
        error.response?.data?.message ===
        "Error: The file or directory name already exists."
      ) {
        errorTitle = "ファイルを保存できませんでした";
        errorMessage =
          "このフォルダには同じ名称のファイルがすでに存在します。";
      }

      notifications.show({
        title: errorTitle,
        message: errorMessage,
        icon: <IconNotiFailed />,
      });
    } finally {
      setIsUploading(false);
    }
  };


  return (
    <Box css={{
      overflowY: "scroll",
      scrollbarWidth: "none",
      msOverflowStyle: "none",
      "::-webkit-scrollbar": {
        display: "none",
      },
      header: {
        display: "flex",
        justifyContent: "space-between",
        padding: "10px 20px",
        borderTop: `solid 1px ${propcolors.gray[200]}`,
        borderBottom: `solid 1px ${propcolors.gray[200]}`,
      },
      ".header-button-container": {
        display: "flex",
        gap: "15px",
      },

      ".main": {
        display: "flex",
      },
    }}>
      <header>
        <CustomBreadcrumb title="ポータル" list={BREADCRUMB_LIST} />
        <Box className="header-button-container">
          <Button
            variant="default"
            onClick={() => router.push("/mail")}
            disabled={isUploading}
          >
            キャンセル
          </Button>
          <Button
            onClick={handleSave}
            loading={isUploading}
            disabled={isUploading}
          >
            {isUploading ? "保存中..." : "保存する"}
          </Button>
        </Box>
      </header>
      <Box css={{ width: "60%", margin: "0 auto", padding:"60px" }}>
        <Box css={{ width: "100%", maxWidth: "500px" }}>
          {!selectedFile ? (
            <Box css={{ display: "flex", flexDirection: "column", alignItems: "flex-start", gap: "16px" }}>
              <FileInput
                placeholder="ファイルを選択"
                accept="image/*"
                onChange={handleFileChange}
                disabled={isUploading}
                css={{
                  ".mantine-FileInput-placeholder": {
                  color: propcolors.blackLight
                  },
                }}
              />
              {fileError && (
                <Text
                  size="sm"
                  css={{
                    color: propcolors.red[500],
                    textAlign: "center",
                  }}
                >
                  {fileError}
                </Text>
              )}
            </Box>
          ) : selectedFile ? (
            <ImagePreview
              file={selectedFile}
              onFileChange={handleFileChange}
              onRemove={handleRemoveImage}
              maxWidth={460}
              showChangeButton={true}
              showRemoveButton={true}
            />
          ) : (
            <ImagePreview
              onFileChange={handleFileChange}
              maxWidth={460}
              showChangeButton={true}
              showRemoveButton={false}
            />
          )}
        </Box>
        <TextInput
          placeholder="タイトルを入力"
          variant="unstyled"
          className="mt-2"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          disabled={isUploading}
          styles={{
            input: {
              border: 'none',
              backgroundColor: 'transparent',
              boxShadow: 'none',
              fontSize: '16px',
              marginTop: "20px",
              borderStyle: "none !important",
            },
          }}
        />

        <TiptapEditor
          content={content}
          onChange={setContent}
          onJSONChange={setContentBlocks}
          placeholder="内容を入力してください..."
          disabled={isUploading}
        />
      </Box>
    </Box>
  );
}
