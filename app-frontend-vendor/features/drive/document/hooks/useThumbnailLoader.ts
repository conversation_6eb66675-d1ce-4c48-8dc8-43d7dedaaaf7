import { useState, useEffect } from 'react';
import { ax } from '../../../../utils/axios';

type UseThumbnailLoaderProps = {
  itemId?: number;
  thumbnailPath?: string;
  enabled?: boolean;
};

export const useThumbnailLoader = ({
  itemId,
  thumbnailPath,
  enabled = true,
}: UseThumbnailLoaderProps) => {
  const [thumbnailUrl, setThumbnailUrl] = useState<string>("/icons/file.svg");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!enabled || !itemId) {
      setThumbnailUrl("/icons/file.svg");
      return;
    }

    const loadThumbnail = async () => {
      if (!thumbnailPath) {
        setThumbnailUrl("/icons/file.svg");
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await ax.get(
          `/api/v1/drive/file/${itemId}/thumbnail`
        );
        
        // レスポンスデータがURLの文字列なので、それを直接使用
        if (response.data) {
          setThumbnailUrl(response.data);
        } else {
          setThumbnailUrl("/icons/file.svg");
        }
      } catch (error: any) {
        console.error("Failed to load thumbnail:", error);
        setError(error.message || "Failed to load thumbnail");
        setThumbnailUrl("/icons/file.svg");
      } finally {
        setIsLoading(false);
      }
    };

    loadThumbnail();
  }, [itemId, thumbnailPath, enabled]);

  const reloadThumbnail = async () => {
    if (!enabled || !itemId || !thumbnailPath) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await ax.get(
        `/api/v1/drive/file/${itemId}/thumbnail`
      );
      
      if (response.data) {
        setThumbnailUrl(response.data);
      } else {
        setThumbnailUrl("/icons/file.svg");
      }
    } catch (error: any) {
      console.error("Failed to reload thumbnail:", error);
      setError(error.message || "Failed to reload thumbnail");
      setThumbnailUrl("/icons/file.svg");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    thumbnailUrl,
    isLoading,
    error,
    reloadThumbnail,
  };
};
