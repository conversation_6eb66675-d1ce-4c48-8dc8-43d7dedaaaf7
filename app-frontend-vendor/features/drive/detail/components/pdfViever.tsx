import {
  LocalizationMap,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Viewer,
  Worker,
} from "@react-pdf-viewer/core";
import {
  defaultLayoutPlugin,
  ToolbarProps,
} from "@react-pdf-viewer/default-layout";
import type { ToolbarSlot } from "@react-pdf-viewer/toolbar";

// Import the localization file
import jp_JP from "@react-pdf-viewer/locales/lib/jp_JP.json";
// Import the styles
import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/default-layout/lib/styles/index.css";
// import "@react-pdf-viewer/toolbar/lib/styles/index.css";
import styled from "@emotion/styled";
import React, { ReactElement } from "react";

// pdfjs-distからバージョン番号を取得
import * as pdfjs from "pdfjs-dist";

// cMapUrlとworkerUrlを動的に構築
const cMapUrl = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/cmaps/`;

// Define the properties (props) for the PDFViewer component
type PDFViewerProps = {
  fileUrl: string; // URL of the PDF file to be displayed
  isDownloadable: boolean; // Flag indicating whether the PDF can be downloaded and printed
};

/**
 * Presentation component renders a PDF viewer with customizable toolbar.
 * It allows zooming, page navigation, and optionally downloading and printing the PDF file.
 *
 * @param {PDFViewerProps} props - The properties for the PDF viewer component.
 * @returns {ReactElement} The rendered PDF viewer component.
 */
const Presentation: React.FC<PDFViewerProps> = ({
  fileUrl,
  isDownloadable,
}: PDFViewerProps): ReactElement => {
  /**
   * Renders the custom toolbar for the PDF viewer.
   *
   * @param {(props: ToolbarProps) => ReactElement} Toolbar - The toolbar component from `@react-pdf-viewer/toolbar`.
   * @param {boolean} isDownloadable - Determines whether the download and print buttons are displayed.
   * @returns {ReactElement} The rendered toolbar with custom buttons and layout.
   */
  const renderToolbar = (
    Toolbar: (props: ToolbarProps) => ReactElement,
    isDownloadable: boolean = true
  ): ReactElement => (
    <Toolbar>
      {(slots: ToolbarSlot) => {
        const {
          CurrentPageInput,
          Download,
          EnterFullScreen,
          GoToNextPage,
          GoToPreviousPage,
          NumberOfPages,
          Print,
          Zoom,
          ZoomIn,
          ZoomOut,
        } = slots;
        return (
          <ToolbarStyled>
            <div className="toolbar__btn zoomout">
              <ZoomOut />
            </div>
            <div className="toolbar__btn zoom">
              <Zoom />
            </div>
            <div className="toolbar__btn zoomin">
              <ZoomIn />
            </div>
            <div className="toolbar__btn goto-prev">
              <GoToPreviousPage />
            </div>
            <div className="toolbar__btn goto-input">
              <CurrentPageInput />
              <span className="current-page-input">/</span>
              <NumberOfPages />
            </div>
            <div className="toolbar__btn goto-next">
              <GoToNextPage />
            </div>
            <div className="toolbar__btn toolbar__btn--fullscreen">
              <EnterFullScreen />
            </div>
            {isDownloadable && (
              <>
                <div className="toolbar__btn download">
                  <Download />
                </div>
                <div className="toolbar__btn print">
                  <Print />
                </div>
              </>
            )}
            <div className="toolbar__btn space"></div>
          </ToolbarStyled>
        );
      }}
    </Toolbar>
  );

  // Create an instance of the default layout plugin with custom toolbar and sidebar settings.
  const defaultLayoutPluginInstance = defaultLayoutPlugin({
    sidebarTabs: (defaultTabs) => [
      defaultTabs[0], // Show only the Bookmarks tab in the sidebar.
    ],
    renderToolbar: (Toolbar) => renderToolbar(Toolbar, isDownloadable),
  });

  const handleDocumentLoad = (): void => {
    const { activateTab } = defaultLayoutPluginInstance;
    activateTab(0);
  };

  return (
    <Worker workerUrl="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js">
      <div
        style={{
          height: "100%",
          width: "100%",
          marginLeft: "auto",
          marginRight: "auto",
        }}
      >
        {fileUrl ? (
          <Viewer
            fileUrl={fileUrl}
            plugins={[defaultLayoutPluginInstance]}
            onDocumentLoad={handleDocumentLoad}
            localization={jp_JP as unknown as LocalizationMap}
            theme="dark"
            defaultScale={SpecialZoomLevel.PageWidth}
            transformGetDocumentParams={(options) => ({
              ...options,
              cMapUrl: cMapUrl,
              cMapPacked: true,
            })}
          />
        ) : (
          <div
            className="loading-pdf"
            style={{
              height: "100%",
              width: "100%",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Spinner />
          </div>
        )}
      </div>
    </Worker>
  );
};

// Styled component for the PDF container and toolbar
const Styled = styled(Presentation)``;

// Styled component for the toolbar layout
const ToolbarStyled = styled.div`
  display: flex;
  align-items: center;
  width: 100%;

  .toolbar__btn {
    padding: 0px 2px;
    display: flex;
    align-items: center;
    justify-content: center;

    button {
      outline: none;
      border-style: none !important;
    }
  }

  .zoomout {
    margin-left: 50px;
  }

  .goto-prev {
    margin-left: auto;
  }

  .goto-input {
    display: inline-flex;
    align-items: center;
    color: #fff !important;
  }

  .current-page-input {
    margin: 0 4px;
  }

  .toolbar__btn--fullscreen {
    margin-left: auto;
  }

  .download,
  .print {
    padding: 0px 2px;
  }

  .space {
    margin-right: 12px;
    width: 10px;
    height: 1px;
  }
`;

/**
 * Main PDFViewer component that renders the styled Presentation component.
 *
 * @param {PDFViewerProps} props - The properties for the PDF viewer component.
 * @returns {ReactElement} The rendered styled PDF viewer component.
 */
export const PDFViewer: React.FC<PDFViewerProps> = ({
  fileUrl,
  isDownloadable,
}: PDFViewerProps): ReactElement => {
  return <Styled fileUrl={fileUrl} isDownloadable={isDownloadable} />;
};
