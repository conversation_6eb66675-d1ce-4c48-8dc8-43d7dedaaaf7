"use client";
import { useState, useEffect, useRef } from "react";
import { Box, Button, Text, TextInput } from "@mantine/core";
import { propcolors } from "styles/colors";
import { notifications } from "@mantine/notifications";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import { ax } from "../../../../utils/axios";
import { ImagePreview } from "../../document/components/ImagePreview";
import { CustomBreadcrumb } from "../../../../components/breadcrumb";
import useSWR from "swr";
import { TiptapEditor } from "features/drive/document/components/TiptapEditor";

// Helper function to convert blocks to Tiptap JSON format
const convertBlocksToTiptapJSON = (blocks: any[]): any => {
  if (!blocks || blocks.length === 0) {
    return {
      type: 'doc',
      content: [
        {
          type: 'dBlock',
          content: [
            {
              type: 'paragraph',
              content: []
            }
          ]
        }
      ]
    };
  }

  const convertBlockToTiptapNode = (block: any): any => {
    const { type, content, props, children } = block;

    switch (type) {
      case 'heading':
        return {
          type: 'heading',
          attrs: { level: props?.level || 1 },
          content: convertContentToTiptapContent(content)
        };

      case 'paragraph':
        return {
          type: 'paragraph',
          content: convertContentToTiptapContent(content)
        };

      case 'bulleted_list':
        return {
          type: 'bulletList',
          content: children?.map((child: any) => ({
            type: 'listItem',
            content: [convertBlockToTiptapNode(child)]
          })) || []
        };

      case 'numbered_list':
        return {
          type: 'orderedList',
          content: children?.map((child: any) => ({
            type: 'listItem',
            content: [convertBlockToTiptapNode(child)]
          })) || []
        };

      case 'code':
        return {
          type: 'codeBlock',
          content: convertContentToTiptapContent(content)
        };

      case 'quote':
        return {
          type: 'blockquote',
          content: convertContentToTiptapContent(content)
        };

      case 'divider':
        return {
          type: 'horizontalRule'
        };

      case 'image':
        return {
          type: 'image',
          attrs: {
            src: props?.url || '',
            alt: props?.name || '',
            width: props?.previewWidth || null
          }
        };

      default:
        return {
          type: 'paragraph',
          content: convertContentToTiptapContent(content)
        };
    }
  };

  const convertContentToTiptapContent = (content: any[]): any[] => {
    if (!content) return [];

    return content.map((item: any) => {
      if (item.type === 'text') {
        const marks: any[] = [];
        const styles = item.styles || {};

        if (styles.bold) marks.push({ type: 'bold' });
        if (styles.italic) marks.push({ type: 'italic' });
        if (styles.strikethrough) marks.push({ type: 'strike' });
        if (styles.code) marks.push({ type: 'code' });
        if (styles.link) marks.push({ type: 'link', attrs: { href: styles.link } });

        return {
          type: 'text',
          text: item.text || '',
          ...(marks.length > 0 && { marks })
        };
      }
      return item;
    });
  };

  const tiptapContent = blocks.map(block => convertBlockToTiptapNode(block));

  return {
    type: 'doc',
    content: [
      {
        type: 'dBlock',
        content: tiptapContent
      }
    ]
  };
};

// Utility function to convert Tiptap JSON to block format (same as TiptapEditor)
const convertToBlocks = (tiptapJSON: any): any[] => {
  if (!tiptapJSON || !tiptapJSON.content) return [];

  const blocks: any[] = [];

  tiptapJSON.content.forEach((dBlock: any) => {
    if (dBlock.type === 'dBlock' && dBlock.content) {
      dBlock.content.forEach((node: any) => {
        const block = {
          id: `block-${Date.now()}-${Math.random()}`,
          type: getBlockType(node),
          props: getBlockProps(node),
          content: node.type !== 'image' ? getBlockContent(node) : [],
          children: getBlockChildren(node)
        };
        blocks.push(block);
      });
    }
  });

  return blocks;
};

const getBlockType = (node: any): string => {
  switch (node.type) {
    case 'heading': return 'heading';
    case 'paragraph': return 'paragraph';
    case 'bulletList': return 'bulleted_list';
    case 'orderedList': return 'numbered_list';
    case 'horizontalRule': return 'divider';
    case 'codeBlock': return 'code';
    case 'blockquote': return 'quote';
    case 'image': return 'image';
    case 'listItem': return 'list_item';
    default: return 'paragraph';
  }
};

const getBlockProps = (node: any): any => {
  const baseProps = {
    textColor: 'default',
    backgroundColor: 'default',
    textAlignment: 'left'
  };

  if (node.type === 'heading') {
    return { ...baseProps, level: node.attrs?.level || 1 };
  }

  if (node.type === 'image') {
    return {
      ...baseProps,
      textAlignment: 'center',
      name: node.attrs?.alt || 'image.jpg',
      url: node.attrs?.src || '',
      caption: node.attrs?.caption || '',
      showPreview: true,
      previewWidth: node.attrs?.width || 454
    };
  }

  return baseProps;
};

const getBlockContent = (node: any): any[] => {
  if (!node.content) return [];

  const content: any[] = [];

  const processContent = (nodes: any[]) => {
    nodes.forEach((child: any) => {
      if (child.type === 'text') {
        content.push({
          type: 'text',
          text: child.text || '',
          styles: getTextStyles(child.marks || [])
        });
      } else if (child.type === 'hardBreak') {
        content.push({
          type: 'text',
          text: '\n',
          styles: {}
        });
      } else if (child.content && child.type !== 'bulletList' && child.type !== 'orderedList') {
        processContent(child.content);
      }
    });
  };

  processContent(node.content);
  return content;
};

const getBlockChildren = (node: any): any[] => {
  if (node.type === 'bulletList' || node.type === 'orderedList') {
    return processListItems(node.content || []);
  }
  return [];
};

const processListItems = (items: any[]): any[] => {
  return items.map(item => {
    const listItemBlock = {
      id: `item-${Date.now()}-${Math.random()}`,
      type: 'listItem',
      props: {
        textColor: 'default',
        backgroundColor: 'default',
        textAlignment: 'left'
      },
      content: getBlockContent(item),
      children: [] as any[]
    };

    if (item.content) {
      for (const child of item.content) {
        if (child.type === 'bulletList' || child.type === 'orderedList') {
          listItemBlock.children = processListItems(child.content || []);
        }
      }
    }

    return listItemBlock;
  });
};

const getTextStyles = (marks: any[]): any => {
  const styles: any = {};

  marks.forEach((mark: any) => {
    switch (mark.type) {
      case 'bold': styles.bold = true; break;
      case 'italic': styles.italic = true; break;
      case 'strike': styles.strikethrough = true; break;
      case 'code': styles.code = true; break;
      case 'link': styles.link = mark.attrs?.href || ''; break;
    }
  });

  return styles;
};

type EditDocumentEditorProps = {
  itemInfo: DriveItemInfo;
  onClose: () => void;
  onSave: () => void;
  portal_id: string;
};

export const EditDocumentEditor: React.FC<EditDocumentEditorProps> = ({
  itemInfo,
  onClose,
  onSave,
  portal_id,
}) => {
  const [title, setTitle] = useState<string>(itemInfo.name || "");
  const [content, setContent] = useState<any>(null); // JSON content instead of HTML
  const [contentBlocks, setContentBlocks] = useState<any[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [fileError, setFileError] = useState<string | null>(null);
  const editorRef = useRef<any>(null);

  // Fetch thumbnail if available
  useEffect(() => {
    if (itemInfo.thumbnail_path) {
      // Store the thumbnail path directly
      // The ImagePreview component will handle formatting
      setThumbnailUrl(itemInfo.thumbnail_path);
    }
  }, [itemInfo]);

  // Parse content if it's JSON
  useEffect(() => {
    if (!itemInfo.content) {
      // No content, set default
      setContent(null);
      setContentBlocks([]);
      return;
    }

    try {
      // Try to parse as JSON first
      const parsedContent = JSON.parse(itemInfo.content);
      if (Array.isArray(parsedContent)) {
        setContentBlocks(parsedContent);
        // Convert JSON blocks to Tiptap JSON format
        const tiptapJSON = convertBlocksToTiptapJSON(parsedContent);
        setContent(tiptapJSON);
      } else {
        // JSON but not array, assume it's already Tiptap JSON
        setContent(parsedContent);
        setContentBlocks([]);
      }
    } catch (error) {
      // Not JSON, treat as legacy content - create basic structure
      const basicTiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'dBlock',
            content: [
              {
                type: 'paragraph',
                content: [
                  {
                    type: 'text',
                    text: itemInfo.content
                  }
                ]
              }
            ]
          }
        ]
      };
      setContent(basicTiptapJSON);
      setContentBlocks([]);
    }
  }, [itemInfo]);

  // Force update contentBlocks when content changes (fallback)
  useEffect(() => {
    if (content && (!contentBlocks || contentBlocks.length === 0)) {
    }
  }, [content, contentBlocks]);

  console.log('EditDocumentEditor state:', {
    content: content ? 'JSON object' : 'empty',
    contentType: content?.type || 'unknown',
    contentBlocksCount: contentBlocks?.length || 0,
    contentBlocksPreview: contentBlocks?.slice(0, 2) || [],
    title,
    itemInfoContent: itemInfo.content ? 'has content' : 'empty'
  });

  const handleFileChange = (file: File | null) => {
    if (!file) {
      setSelectedFile(null);
      setFileError(null);
      return;
    }

    const MAX_THUMBNAIL_SIZE = 3 * 1024 * 1024; // 3MB
    if (file.size > MAX_THUMBNAIL_SIZE) {
      setFileError("ファイルサイズは3MB以下にしてください");
      return;
    }

    setSelectedFile(file);
    setFileError(null);
  };

  const handleRemoveImage = () => {
    setSelectedFile(null);
    setFileError(null);
  };

  const handleThumbnailRemove = () => {
    setThumbnailUrl(null);
    setSelectedFile(null);
    setFileError(null);
  };

  const handleSaveThumbnail = async (file: File): Promise<void> => {
    const formData = new FormData();
    formData.append("thumbnail", file);

    await ax.put(`api/v1/drive/file/${itemInfo.id}/thumbnail`, formData);

    // Refresh thumbnail URL
    setThumbnailUrl(null);
    setSelectedFile(file);
  };

  const handleDeleteThumbnail = async (): Promise<void> => {
    await ax.delete(`api/v1/drive/file/${itemInfo.id}/thumbnail`);

    setThumbnailUrl(null);
    setSelectedFile(null);
  };

  // Wrapper for onJSONChange to add debugging
  const handleJSONChange = (blocks: any[]) => {
    console.log('TiptapEditor onJSONChange called with blocks:', blocks?.length || 0, blocks?.slice(0, 2));
    setContentBlocks(blocks);
  };

  // Wrapper for onChange to add debugging
  const handleContentChange = (json: any) => {
    console.log('TiptapEditor onChange called with JSON:', json);
    setContent(json);
  };

  // Handle editor ready
  const handleEditorReady = (editor: any) => {
    console.log('Editor is ready:', !!editor);
    editorRef.current = editor;
  };

  // Function to get current editor content as blocks
  const getCurrentEditorBlocks = () => {
    if (editorRef.current) {
      try {
        const editorJSON = editorRef.current.getJSON();
        // Use the same conversion logic as TiptapEditor
        const blocks = convertToBlocks(editorJSON);
        console.log('Got current editor blocks:', blocks?.length || 0);
        return blocks;
      } catch (error) {
        console.error('Error getting current editor blocks:', error);
      }
    }
    return null;
  };

  const handleSave = async () => {
    console.log('HandleSave - Current state:', {
      contentBlocks: contentBlocks?.length || 0,
      content: content ? 'has content' : 'empty',
      title: title || 'empty'
    });

    // Validation for content - required
    const isContentEmpty = !contentBlocks ||
      contentBlocks.length === 0 ||
      contentBlocks.every(block =>
        !block.content ||
        block.content.length === 0 ||
        block.content.every((item: any) => !item.text || item.text.trim() === '')
      );

    // Also check JSON content as fallback
    const isJSONContentEmpty = !content ||
      (content.type === 'doc' &&
       (!content.content ||
        content.content.length === 0 ||
        content.content.every((dBlock: any) =>
          !dBlock.content ||
          dBlock.content.length === 0 ||
          dBlock.content.every((node: any) =>
            node.type === 'paragraph' &&
            (!node.content || node.content.length === 0)
          )
        )
       )
      );

    console.log('Validation results:', {
      isContentEmpty,
      isJSONContentEmpty,
      shouldBlock: isContentEmpty && isJSONContentEmpty
    });

    if (isContentEmpty && isJSONContentEmpty) {
      return notifications.show({
        title: "エラー",
        message: "内容を入力してください",
        icon: <IconNotiFailed />,
      });
    }

    if (!selectedFile && !thumbnailUrl) {
      return notifications.show({
        title: "エラー",
        message: "サムネイルを選択してください",
        icon: <IconNotiFailed />,
      });
    }

    // Validation for thumbnail
    if (selectedFile && selectedFile.size >= 3 * 1024 * 1024) {
      return notifications.show({
        title: "エラー",
        message: "3MB以下のサムネイルを選択してください",
        icon: <IconNotiFailed />,
      });
    }

    setIsUploading(true);

    try {
      // Ensure we have the latest content blocks
      let finalContentBlocks = contentBlocks;

      // If contentBlocks is empty, try to get from current editor state
      if (!finalContentBlocks || finalContentBlocks.length === 0) {

        // Try to get current blocks from editor
        const currentBlocks = getCurrentEditorBlocks();
        if (currentBlocks && currentBlocks.length > 0) {
          finalContentBlocks = currentBlocks;
        } else if (content && content.type === 'doc') {
          // Fallback: convert current JSON content to blocks
          const blocks = convertToBlocks(content);
          if (blocks && blocks.length > 0) {
            finalContentBlocks = blocks;
            console.log('Created blocks from current JSON content:', finalContentBlocks.length);
          }
        }
      }

      // Prepare the content as JSON string
      const contentArray = Array.isArray(finalContentBlocks) ? finalContentBlocks : [];
      const contentJSON = JSON.stringify(contentArray);

      const requestPayload = {
        name: title.trim() || "名称未設定",
        content: contentJSON, // Send as JSON string
        type: "normal",
        portal_id: portal_id,
        is_document: true,
      };

      console.log('Saving with data:', {
        name: requestPayload.name,
        contentBlocksCount: contentArray.length,
        contentPreview: contentArray.slice(0, 2), // Show first 2 blocks for debug
        contentJSONLength: contentJSON.length,
        hasNewThumbnail: !!selectedFile,
        itemId: itemInfo.id
      });

      // Update document content and metadata
      await ax.put(`api/v1/drive/file/${itemInfo.id}`, requestPayload);

      // If there's a new thumbnail, upload it separately
      if (selectedFile) {
        const thumbnailFormData = new FormData();
        thumbnailFormData.append("thumbnail", selectedFile);

        await ax.put(`api/v1/drive/file/${itemInfo.id}/thumbnail`, thumbnailFormData);
      }

      notifications.show({
        title: "成功",
        message: "ファイルを保存しました",
        icon: <IconNotiSuccess />,
      });

      onSave();
    } catch (error: any) {
      let errorTitle = "エラー";
      let errorMessage = "ファイルの保存に失敗しました";

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      notifications.show({
        title: errorTitle,
        message: errorMessage,
        icon: <IconNotiFailed />,
      });
    } finally {
      setIsUploading(false);
    }
  };
  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch((_err) => {});

  const { data: portalList } = useSWR("api/v1/drive/portals", fetcher, {
    revalidateOnFocus: false,
  });

  const portal = portalList?.find(
    (item: DrivePortal) => item.id === Number(portal_id),
  );

  const BREADCRUMB_LIST = [
    {
      title: "ホーム",
      href: `/drive${portal_id && `?portal_id=${portal_id}`}`,
      id: "root-nav",
    },
    {
      title: portal?.name,
      href: `/drive${portal_id && `?portal_id=${portal_id}`}`,
      id: "portal-nav-portal",
    },
    {
      title: "名称未設定",
      id: "document",
    },
  ];

  return (
    <Box
      css={{
        "overflow-y": "scroll",
        " scrollbar-width": "none",
        "-ms-overflow-style": "none",
        "::-webkit-scrollbar": {
          "display": "none"
        },
        "header": {
          "display": "flex",
          "justify-content": "space-between",
          "padding": "10px 20px",
          "border-top": `solid 1px ${propcolors.gray[200]}`,
          "border-bottom": `solid 1px ${propcolors.gray[200]}`,
        },
        ".header-button-container": {
          "display": "flex",
          "gap": "15px",
        },
        ".main": {
          "display": "flex"
        }
      }}
    >
      <header>
        <CustomBreadcrumb title="ポータル" list={BREADCRUMB_LIST} />
        <Box className="header-button-container">
          <Button
            variant="default"
            disabled={isUploading}
          >
            キャンセル
          </Button>
          <Button
            onClick={handleSave}
            loading={isUploading}
            disabled={isUploading}
          >
            {isUploading ? "保存中..." : "保存する"}
          </Button>
        </Box>
      </header>

      <Box css={{width: "60%", margin: "0 auto", padding:"60px", height: "100vh"}}>
        <ImagePreview
          file={selectedFile || undefined}
          thumbnailPath={itemInfo.thumbnail_path || undefined}
          itemId={itemInfo.id}
          onFileChange={handleFileChange}
          onRemove={handleRemoveImage}
          onThumbnailRemove={handleThumbnailRemove}
          maxWidth={460}
          showChangeButton={true}
          showRemoveButton={true}
          enableEditMode={true}
          onSaveThumbnail={handleSaveThumbnail}
          onDeleteThumbnail={handleDeleteThumbnail}
        />
        {fileError && (
          <Text css={{ color: "red", marginTop: "8px", fontSize: "14px" }}>
            {fileError}
          </Text>
        )}
        <TextInput
          placeholder="タイトルを入力"
          variant="unstyled"
          className="mt-2"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          disabled={isUploading}
          styles={{
            input: {
              border: 'none',
              backgroundColor: 'transparent',
              boxShadow: 'none',
              fontSize: '16px',
              marginTop: "20px",
              borderStyle: "none !important",
            },
          }}
        />

        <TiptapEditor
          content={content}
          onChange={handleContentChange}
          onJSONChange={handleJSONChange}
          onEditorReady={handleEditorReady}
          placeholder="内容を入力してください..."
          disabled={isUploading}
        />
      </Box>
    </Box>
  );
};
