import styled from "@emotion/styled";
import Head from "next/head";
import { formatDate } from "constants/commons";
import { CommonListLayout } from "components/layouts/commonListLayout";
import { Button, Skeleton, Tooltip, Text } from "@mantine/core";
import { useCallback, useEffect, useState } from "react";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import useSWR from "swr";
import getFileSizeWithUnit from "utils/func/getFilesize";
import { DriveModifierModal } from "../explorer/components/modifyModal";
import Image from "next/image";
import { Skeleton as LoadingSkeleton } from "components/Skeleton";
import { useRouter } from "next/router";
import { notifications } from "@mantine/notifications";
import {
  usePortalListState,
  useSetPortalListState,
} from "utils/recoil/drive/portalListState";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { CustomBreadcrumb } from "components/breadcrumb";
import { PDFViewer } from "./components/pdfViever";
import { formatLogDate } from "utils/func/formatDateString";
import { DOWNLOAD_IFRAME_REMOVE_DELAY } from "../../../constants/commonSetting";
import { EditDocumentEditor } from "./components/EditDocumentEditor";

type logMode = "access" | "download";

type DriveExplorerProps = {};

type PresentationProps = {
  className?: string;
  isItemInfoFetching: boolean;
  itemInfo: DriveItemInfo | undefined;
  accessLog: DriveUserLog | undefined;
  isAccessLogFetching: boolean;
  downloadLog: DriveUserLog | undefined;
  isDownloadLogFetching: boolean;
  handleLogMode: (mode: logMode) => void;
  logMode: logMode;
  openModal: (mode: DriveModifierMode) => void;
  viewData: DriveViewData | undefined;
  isViewFetching: boolean;
  downloadFile: () => void;
  selectedPortalID: string;
  isCollab: boolean;
} & DriveExplorerProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  itemInfo,
  isItemInfoFetching,
  accessLog,
  downloadLog,
  viewData,
  isViewFetching,
  handleLogMode,
  logMode,
  openModal,
  downloadFile,
  selectedPortalID,
  isCollab,
}) => {
  console.log('itemIfo', itemInfo);
  const portalList = usePortalListState();
  if (!portalList) {
    return <LoadingSkeleton />;
  }

  const portal = portalList.find(
    (item) => item.id === Number(selectedPortalID)
  );

  const BREADCRUMB_LIST = [
    {
      title: "ホーム",
      href: `/drive${selectedPortalID && `?portal_id=${selectedPortalID}`}`,
      id: "root-nav",
    },
    {
      title: portal?.name,
      href: `/drive${selectedPortalID && `?portal_id=${selectedPortalID}`}`,
      id: "portal-nav-portal",
    },
  ];

  itemInfo?.breadcrumbs.slice(0, -1)?.forEach((item: DriveBreadcrumbItem) => {
    BREADCRUMB_LIST.push({
      title: item.name,
      href: isCollab
        ? `/drive/collab_files/${item.id}${selectedPortalID && `?portal_id=${selectedPortalID}`}`
        : `/drive/${item.id}${selectedPortalID && `?portal_id=${selectedPortalID}`}`,
      id: `portal-nav-${item.id}`,
    });
  });

  const isDownloadable = !itemInfo?.is_limit_download_all;

  return (
    <>
      <Head>
        <title>プレビュー | PartnerProp</title>
      </Head>
      {isItemInfoFetching ? (
        <>
          <LoadingSkeleton />
        </>
      ) : (
        <CommonListLayout className={className}>
          <section className="detail-wrapper">
            <CustomBreadcrumb
              title="ポータル"
              list={BREADCRUMB_LIST}
              isMultipleLongText={true}
              styleWrapper={{
                gridColumn: "1/3",
                height: "74px",
                padding: "22px 24px",
                borderBottom: `1px solid ${propcolors.gray[200]}`,
                borderTop: `1px solid ${propcolors.gray[200]}`,
              }}
            />
            {/* content */}
            <section className="detail-preview">
              {itemInfo && (
                <section className="detail-info-core">
                  <article className="detail-info-left">
                    <div className="left-box">
                      <h2 className="detail-info-core-title">
                        <TextFileName fileName={itemInfo.name} />
                      </h2>
                      <p className="detail-info-core-appendix">
                        {getFileSizeWithUnit(itemInfo.size)}
                      </p>
                    </div>
                    <p className="detail-info-core-date">
                      {formatDate(itemInfo?.created_at)}
                    </p>
                  </article>
                  <article className="detail-info-right">
                    <div className="navbar-buttons">
                      {!isCollab && (
                        <Button
                          onClick={() => openModal("share")}
                          className="navbar-buttons-share"
                          fullWidth
                        >
                          共有設定
                        </Button>
                      )}
                      {!itemInfo.is_limit_download_all && (
                        <Button
                          className="navbar-buttons-download"
                          fullWidth
                          onClick={downloadFile}
                        >
                          ダウンロード
                        </Button>
                      )}
                    </div>
                  </article>
                </section>
              )}
              <div className="detail-preview-content">
                {isViewFetching ? (
                  <div className="detail-preview-not-available">
                    プレビューを取得中...
                  </div>
                ) : (
                  itemInfo &&
                  viewData && (
                    <>
                      {["png", "jpg", "jpeg", "gif", "webp"].includes(
                        itemInfo.name
                          .substring(itemInfo.name.lastIndexOf(".") + 1)
                          .toLowerCase()
                      ) && (
                        <Image
                          className="detail-preview-window"
                          src={`${viewData.url}`}
                          layout="fill"
                          objectFit="contain"
                          alt=""
                        />
                      )}
                      {["pdf"].includes(
                        itemInfo.name
                          .substring(itemInfo.name.lastIndexOf(".") + 1)
                          .toLowerCase()
                      ) && (
                        <div className="detail-preview-pdf">
                          <PDFViewer
                            fileUrl={viewData.url}
                            isDownloadable={!itemInfo?.is_limit_download_all}
                          />
                        </div>
                      )}
                      {["mp4", "mp3", "flac", "wav"].includes(
                        itemInfo.name
                          .substring(itemInfo.name.lastIndexOf(".") + 1)
                          .toLowerCase()
                      ) && (
                        <video
                          className="detail-preview-window"
                          src={viewData.url}
                          controls
                          controlsList="nodownload"
                        />
                      )}
                      {![
                        "png",
                        "jpg",
                        "jpeg",
                        "gif",
                        "webp",
                        "pdf",
                        "mp4",
                        "mp3",
                        "flac",
                        "wav",
                      ].includes(
                        itemInfo.name
                          .substring(itemInfo.name.lastIndexOf(".") + 1)
                          .toLowerCase()
                      ) &&
                        itemInfo.name.split(".").pop()! && (
                          <div className="detail-preview-not-available">
                            プレビューが利用できません
                          </div>
                        )}
                    </>
                  )
                )}
              </div>
            </section>
            {/* info */}
            <section className="detail-info">
              {isItemInfoFetching ? (
                <>
                  <section className="detail-info-core">
                    <Skeleton height={16} />
                    <Skeleton height={14} mt={8} />
                    <Skeleton height={14} mt={8} />
                    <Skeleton height={16} mt={16} />
                    <Skeleton height={14} mt={8} />
                  </section>
                  <Skeleton height={"100%"} mt={16} />
                </>
              ) : (
                itemInfo && (
                  <>
                    <section className="detail-info-history">
                      <div className="segment-control">
                        <div
                          className={`${isDownloadable ? "" : "segment-control-limit-download"} segment-control-item segment-control-item-${logMode === "access" ? "active" : "disable"}`}
                          onClick={() => handleLogMode("access")}
                        >
                          閲覧済
                        </div>
                        {isDownloadable && (
                          <div
                            className={`segment-control-item segment-control-item-${logMode === "download" ? "active" : "disable"}`}
                            onClick={() => handleLogMode("download")}
                          >
                            ダウンロード済
                          </div>
                        )}
                      </div>
                      <section className="detail-info-logs">
                        {logMode === "access" ? (
                          accessLog ? (
                            Object.keys(accessLog).length > 0 ? (
                              Object.keys(accessLog).map(
                                (companyName, index) => {
                                  return (
                                    <article
                                      className="access-logs"
                                      key={index}
                                    >
                                      <p className="access-logs-head">
                                        {companyName}
                                      </p>
                                      {accessLog[companyName]
                                        .sort((a, b) =>
                                          a.updated_at > b.updated_at ? -1 : 1
                                        )
                                        .map((item, subIndex) => (
                                          <div
                                            className="download-logs-content"
                                            key={subIndex}
                                          >
                                            <div className="download-logs-content-avatar">
                                              {item?.partner_avatar_url ? (
                                                <Image
                                                  width={40}
                                                  height={40}
                                                  src={item.partner_avatar_url}
                                                  alt=""
                                                />
                                              ) : (
                                                <Image
                                                  width={18}
                                                  height={18}
                                                  src="/icons/user.svg"
                                                  alt="company-logo"
                                                />
                                              )}
                                            </div>
                                            <div className="download-logs-content-info">
                                              <p className="name">
                                                {item.partner_user_name}
                                              </p>
                                              <p className="email">
                                                {item.email}
                                              </p>
                                            </div>
                                            <span className="download-logs-content-date">
                                              {formatLogDate(item.updated_at)}
                                            </span>
                                          </div>
                                        ))}
                                    </article>
                                  );
                                }
                              )
                            ) : (
                              <p className="not-found">閲覧履歴はありません</p>
                            )
                          ) : (
                            <></>
                          )
                        ) : downloadLog ? (
                          Object.keys(downloadLog).length > 0 ? (
                            Object.keys(downloadLog).map(
                              (companyName, index) => (
                                <article className="download-logs" key={index}>
                                  <p className="download-logs-head">
                                    {companyName}
                                  </p>
                                  {downloadLog[companyName]
                                    .sort((a, b) =>
                                      a.updated_at > b.updated_at ? -1 : 1
                                    )
                                    .map(
                                      (item: DriveUser, subIndex: number) => (
                                        <div
                                          className="download-logs-content"
                                          key={subIndex}
                                        >
                                          <div className="download-logs-content-avatar">
                                            {item?.partner_avatar_url ? (
                                              <Image
                                                width={40}
                                                height={40}
                                                src={item.partner_avatar_url}
                                                alt=""
                                              />
                                            ) : (
                                              <Image
                                                width={18}
                                                height={18}
                                                src="/icons/user.svg"
                                                alt="company-logo"
                                              />
                                            )}
                                          </div>
                                          <div className="download-logs-content-info">
                                            <p className="name">
                                              {item.partner_user_name}
                                            </p>
                                            <p className="email">
                                              {item.email}
                                            </p>
                                          </div>
                                          <span className="download-logs-content-date">
                                            {formatLogDate(item.updated_at)}
                                          </span>
                                        </div>
                                      )
                                    )}
                                </article>
                              )
                            )
                          ) : (
                            <p className="not-found">
                              ダウンロード履歴はありません
                            </p>
                          )
                        ) : (
                          <></>
                        )}
                      </section>
                    </section>
                  </>
                )
              )}
            </section>
          </section>
        </CommonListLayout>
      )}
    </>
  );
};

const TextFileName = (props: { fileName: string }) => {
  const { fileName } = props;
  const MAX_FILE_NAME_DISPLAY = 50;

  if (fileName.length > MAX_FILE_NAME_DISPLAY) {
    return (
      <Tooltip
        label={fileName}
        maw={300}
        withArrow
        multiline
        transitionProps={{ duration: 200 }}
      >
        <Text>{fileName.slice(0, MAX_FILE_NAME_DISPLAY) + "..."}</Text>
      </Tooltip>
    );
  } else {
    return <Text>{fileName}</Text>;
  }
};

const Styled = styled(Presentation)`
  display: inherit;
  height: calc(100vh - 80px) !important;

  .not-found {
    min-height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .segment-control {
    height: 46px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    transition: all 0.3s;
    border: 1px solid ${propcolors.gray[200]};
    border-radius: 8px;
    &-limit-download {
      flex-basis: 100% !important;
    }
    &-item {
      display: flex;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;
      font-weight: 600;
      justify-content: center;
      align-items: center;
      height: 100%;
      flex-basis: 50%;
      &-active {
        color: white;
        background-color: #222222;
      }
    }
  }

  .navbar {
    &-buttons {
      display: flex;
      gap: 16px;
      &-share {
        border: 1px solid #e8eaed;
        background-color: white;
        color: #222222;
        height: 42px;
        border-radius: 8px;
      }
      &-download {
        background-color: #f93832;
        color: white;
        height: 42px;
        border-radius: 8px;
      }
    }
  }
  .detail {
    &-wrapper {
      display: grid;
      grid-template-columns: 1fr 320px;
      grid-template-rows: auto 1fr;
      height: 100%;
    }
    &-info {
      border-left: 1px solid ${propcolors.gray[200]};
      background-color: ${propcolors.white};
      padding: 16px;
      display: grid;
      height: calc(100vh - 140px);
      grid-template-rows: auto 1fr;
      gap: 1rem;
      &-core {
        display: flex;
        max-height: 77px;
        border-bottom: 1px solid ${propcolors.gray[200]};
        gap: 24px;
        width: 100%;
        padding: 16px 24px;
        &-title {
          font-size: 16px;
          font-weight: 600;
          color: #222222;
        }
        &-appendix {
          font-size: 14px;
          margin-top: 4px;
          font-weight: 400 !important;
          color: #222222 !important;
        }
        &-date {
          font-size: 14px;
          margin-top: 4px;
          font-weight: 300;
          color: #666666;
        }
      }
      &-left {
        display: flex;
        justify-content: space-between;
        width: 100%;
        align-items: center;
      }
      &-right {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 24px;
      }
      &-logs {
        height: calc(100vh - 140px);
        overflow-y: auto;
        .download-logs,
        .access-logs {
          &-head {
            font-size: 16px;
            cursor: default;
            font-weight: 600;
            &:first {
              margin-top: 14px;
            }
            margin-top: 24px;
          }
          &-content {
            width: 100%;
            cursor: default;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            &-avatar {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 40px;
              height: 40px;
              min-width: 40px;
              background-color: #f7f8f9;
              overflow: hidden;
              border-radius: 50%;
            }
            &-info {
              margin-left: 8px;
              flex-grow: 1;
              display: flex;
              justify-content: center;
              align-items: start;
              flex-direction: column;
            }
            .name {
              font-size: 14px;
              line-height: 14px;
              color: #222222;
            }
            .email {
              font-size: 12px;
              color: #666666;
            }
            &-date {
              max-width: 5ch;
              font-size: 12px;
              color: #666666;
              text-align: right;
            }
          }
        }
      }
      h2 {
        font-size: 1rem;
      }
      p,
      span {
        font-size: 0.875rem;
      }
    }
    &-preview {
      height: calc(100% - 74px);
      width: 100%;
      &-window {
        height: calc(100% - 74px);
        width: 100%;
        padding: 24px 16px 24px 16px;
        background-color: #f7f8f9;
      }
      &-content {
        position: relative !important;
        object-fit: scale-down !important;
        background-color: #f7f8f9;
        width: 100%;
        min-width: 650px;
        height: calc(100% - 74px) !important;
        min-height: 500px;
      }
      &-not-available {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
      }
      &-pdf {
        height: calc(100vh - 150px);
      }
    }
  }
`;

export const DriveDetail: React.FC<{ fileID: string }> = ({ fileID }) => {
  const [logMode, setLogMode] = useState<logMode>("access");
  const { query } = useRouter();
  const router = useRouter();
  const { portal_id } = query;
  const isCollab = router.pathname == "/drive/collab_files/detail/[file_id]";
  const setPortalList = useSetPortalListState();
  const [modalInnerProps, setModalInnerProps] =
    useState<ModalInnerProps | null>(null);
  const [showEditor, setShowEditor] = useState<boolean>(false);
  const detailFetcher = (file_id: string, path: string) =>
    ax
      .get(
        isCollab
          ? `api/v1/drive/collab_file/${file_id}${path}`
          : `api/v1/drive/file/${file_id}${path}`
      )
      .then((res) => res.data)
      .catch((err) => {
        switch (err.response.data.error) {
          case "Error: the file does not exist":
          case "Error: cannot access to the file":
            notifications.hide("drive-detail-not-found");
            notifications.show({
              id: "drive-detail-not-found",
              title: "ファイルが存在しませんでした",
              message:
                "該当ファイルが削除された、または存在しない可能性があります",
              icon: <IconNotiFailed />,
              autoClose: 5000,
            });
            router.push("/drive");
            break;

          default:
            break;
        }
        if (err.response.data.error === "Error: the file does not exist") {
          router.push("/drive");
        }
      });

  const { data: viewData, isValidating: isViewDataValidating } = useSWR(
    [fileID, "/view"],
    ([file_id, path]) => detailFetcher(file_id, path)
  );

  const {
    data: itemInfoData,
    isValidating: isItemInfoValidating,
    mutate: mutateItemInfo,
  } = useSWR([fileID, ""], ([file_id, path]) => detailFetcher(file_id, path));
  const {
    data: accessLogData,
    mutate: AccessLogMutate,
    isValidating: isAccessLogDataValidating,
  } = useSWR([fileID, "/logs/access"], ([file_id, path]) =>
    detailFetcher(file_id, path)
  );
  const {
    data: downloadLogData,
    mutate: DownloadLogMutate,
    isValidating: isDownloadLogDataValidating,
  } = useSWR([fileID, "/logs/download"], ([file_id, path]) =>
    detailFetcher(file_id, path)
  );

  const handleLogMode = useCallback((mode: logMode) => setLogMode(mode), []);

  useEffect(() => {
    if (logMode === "access") {
      AccessLogMutate();
    } else {
      DownloadLogMutate();
    }
    const getPortalList = async () => {
      const res = await ax.get("api/v1/drive/portals");
      setPortalList(res.data);
    };
    getPortalList();
  }, [logMode]);

  const downloadFile = () => {
    ax.get(
      isCollab
        ? `api/v1/drive/collab_file/${fileID}/download`
        : `api/v1/drive/file/${fileID}/download`
    )
      .then((res) => {
        const downloadUrl = res.data.url;
        // トリガー用非表示のiframeを作成して、srcにダウンロードURLを設定
        const iframe = document.createElement("iframe");
        iframe.style.display = "none";
        iframe.src = downloadUrl;
        document.body.appendChild(iframe);
        // トリガー用のiframeを削除
        setTimeout(() => {
          document.body.removeChild(iframe);
        }, DOWNLOAD_IFRAME_REMOVE_DELAY);
      })
      .catch((err) => {
        notifications.show({
          message: "ダウンロードに失敗しました",
          icon: <IconNotiFailed />,
          autoClose: 3000,
        });
      });
  };

  const openModal = useCallback((mode: DriveModifierMode) => {
    setModalInnerProps({
      mode,
      filename: itemInfoData?.name,
      fileID: itemInfoData?.id,
      opened: true,
    });
  }, []);

  // Check if content exists and show editor
  useEffect(() => {
    if (itemInfoData?.content && itemInfoData.content.trim() !== '') {
      setShowEditor(true);
    }
  }, [itemInfoData]);

  const handleCloseEditor = () => {
    setShowEditor(false);
  };

  const handleSaveEditor = () => {
    // Refresh item info after save
    mutateItemInfo();
    setShowEditor(false);
  };

  // Show EditDocumentEditor if content exists
  if (showEditor && itemInfoData) {
    return (
      <EditDocumentEditor
        itemInfo={itemInfoData}
        onClose={handleCloseEditor}
        onSave={handleSaveEditor}
        portal_id={portal_id as string}
      />
    );
  }

  return (
    <>
      <DriveModifierModal
        close={() => setModalInnerProps(null)}
        mode={modalInnerProps?.mode}
        fileID={itemInfoData ? itemInfoData.id : 0}
        filename={itemInfoData ? itemInfoData.name : ""}
        opened={modalInnerProps ? true : false}
        mutate={() => {
          mutateItemInfo();
        }}
        isLimitDownloadAll={itemInfoData?.is_limit_download_all}
      />
      <Styled
        viewData={viewData}
        isViewFetching={isViewDataValidating}
        itemInfo={itemInfoData}
        isItemInfoFetching={isItemInfoValidating}
        accessLog={accessLogData}
        isAccessLogFetching={isAccessLogDataValidating}
        downloadLog={downloadLogData}
        isDownloadLogFetching={isDownloadLogDataValidating}
        handleLogMode={handleLogMode}
        logMode={logMode}
        openModal={openModal}
        downloadFile={downloadFile}
        selectedPortalID={portal_id as string}
        isCollab={isCollab}
      />
    </>
  );
};
