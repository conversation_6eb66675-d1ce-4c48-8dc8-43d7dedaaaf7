import styled from "@emotion/styled";
import Head from "next/head";
import { BaseSyntheticEvent } from "react";
import { PropNormal } from "components/svgs/logo/prop";
import { propcolors } from "styles/colors";
import { useSearchParams } from "next/navigation";
import { ax } from "utils/axios";
import { useForm } from "react-hook-form";
import { useRouter } from "next/router";
import { notifications } from "@mantine/notifications";
import { getApiErrorMessage } from "utils/values/errorMessages";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";

type PresentationProps = {
  className?: string;
  submitFunc: (
    e?: BaseSyntheticEvent<object, any, any> | undefined
  ) => Promise<void>;
  errors: any;
  register: any;
  isSubmitting: boolean;
  watch: any;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  submitFunc,
  errors,
  register,
  isSubmitting,
  watch,
}) => {
  const password = watch("password", "");

  return (
    <>
      <Head>
        <title>パスワード変更 | PartnerProp</title>
      </Head>
      <main className={className}>
        <section id="login-wrapper">
          <div className="login-heading">
            <div className="proplogo">
              <PropNormal className="proplogo-icon" />
              <div className="proplogo-variant_group">
                <span className="proplogo-variant-light">for</span>
                <span className="proplogo-variant-bold">Vendor</span>
              </div>
            </div>
            <div className="login-message">
              <p className="login-heading-dialog">
                パスワードを設定してください
              </p>
              <p className="login-heading-warning">
                ※半角英数字, 大文字, 数字を含めてください
              </p>
            </div>
          </div>
          <form className="login-form" onSubmit={submitFunc}>
            <input
              type="email"
              placeholder="メールアドレス"
              {...register("email", {
                required: "メールアドレスは必須項目です",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "有効なメールアドレスを入力してください",
                },
              })}
            />
            {errors.email ? (
              <p className="error-message">{errors.email.message}</p>
            ) : (
              <p className="error-message"></p>
            )}
            <input
              type="password"
              placeholder="変更後のパスワード"
              {...register("password", {
                required: "パスワードは必須項目です",
                validate: (value: string) => {
                  if (value.length < 8) {
                    return "パスワードは最低8文字必要です";
                  }
                  if (value.length > 50) {
                    return "パスワードは50文字以内でなければなりません";
                  }
                  if (!/[a-z]/.test(value)) {
                    return "少なくとも1つの小文字が必要です";
                  }
                  if (!/[A-Z]/.test(value)) {
                    return "少なくとも1つの大文字が必要です";
                  }
                  if (!/\d/.test(value)) {
                    return "少なくとも1つの数字が必要です";
                  }
                  return true;
                },
              })}
            />
            {errors.password ? (
              <p className="error-message">{errors.password.message}</p>
            ) : (
              <p className="error-message"></p>
            )}
            <input
              type="password"
              placeholder="（確認）変更後のパスワード"
              {...register("password_confirmation", {
                required: "確認用のパスワードは必須項目です",
                validate: (value: string) =>
                  value === password || "パスワードが一致しません",
              })}
            />
            {errors.password_confirmation && (
              <p className="error-message">
                {errors.password_confirmation.message}
              </p>
            )}
            <button type="submit" className="type-proceed">
              {isSubmitting ? "変更しています..." : "ログイン"}
            </button>
          </form>
        </section>
      </main>
    </>
  );
};

const Styled = styled(Presentation)`
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;

  #login-wrapper {
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background-color: ${propcolors.white};
    box-shadow: 0px 0px 16px 0px rgba(221, 221, 221, 1);
    border-radius: 16px;
  }
  .proplogo {
    display: inline-flex;
    gap: 16px;
    align-items: center;
    &-icon {
      width: 200px;
    }
    &-variant-light {
      color: ${propcolors.red[700]};
      font-weight: 400;
      font-size: 20px;
      margin-right: 4px;
    }
    &-variant-bold {
      color: ${propcolors.red[700]};
      font-weight: 500;
      font-size: 20px;
    }
  }
  .error-message {
    color: ${propcolors.red[600]};
    font-size: 12px;
    text-align: left;
    width: 100%;
  }
  .login {
    &-message {
      display: flex;
      flex-direction: column;
      justify-content: center;
      text-align: center;
    }
    &-heading {
      display: flex;
      flex-flow: column;
      align-items: center;
      gap: 24px;
      &-dialog {
        font-size: 14px;
        color: ${propcolors.blackLight};
        margin-bottom: 2px;
        font-weight: 400;
      }
      &-warning {
        font-size: 12px;
        font-weight: 400;
        color: #e7211b;
        margin-bottom: 24px;
      }
    }
    &-form {
      display: flex;
      flex-flow: column;
      align-items: center;
      width: 100%;
      gap: 8px;
      input {
        width: 100%;
        padding: 12px;
        max-height: 41px;
        border-radius: 8px;
        background-color: ${propcolors.inputBackground};
        outline: none;
        border: 0px !important;
      }
      input::placeholder {
        color: ${propcolors.greyDefault};
        font-size: 14px;
      }
      &-actions {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
      }
      &-forgot {
        font-size: 14px;
      }
      button {
        width: 100%;
        margin-top: 16px;
        padding: 9.5px 16px;
        border-radius: 8px;
        font-weight: 400 !important;
        &.type-proceed {
          font-size: 14px;
          opacity: 1;
          font-weight: 600;
        }
      }
    }
  }
`;

export const ForgotPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm();
  const router = useRouter();
  const params = useSearchParams();
  const onSubmit = async (data: any) => {
    await ax.get("/sanctum/csrf-cookie").then(async (response) => {
      await ax
        .post("/api/v1/vendor_user/reset_password", {
          email: data.email,
          password: data.password,
          password_confirmation: data.password_confirmation,
          token: params.get("token"),
        })
        .then((res) => {
          router.push("/login");
        })
        .catch((err) => {
          notifications.show({
            title: "パスワードの更新に失敗しました。",
            message: getApiErrorMessage(err.response.data.message),
            icon: <IconNotiFailed />,
          });
        });
    });
  };
  const submitFunc = handleSubmit(onSubmit);
  return (
    <Styled
      submitFunc={submitFunc}
      errors={errors}
      register={register}
      isSubmitting={false}
      watch={watch}
    />
  );
};
