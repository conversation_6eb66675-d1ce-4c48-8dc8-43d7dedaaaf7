import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import utc from "dayjs/plugin/utc";
import useSWR, { mutate } from "swr";
import { ax } from "utils/axios";
import { fetcher } from "utils/axios/fetcher";
import type { FilterBody } from "../type/api";

dayjs.extend(utc);
dayjs.extend(customParseFormat);

export const useFilteredLeads = (
  condition: FilterBody,
  vendor_id: number | undefined,
) => {
  // created_at,update_atは "YYYY-MM-DD HH:mm" に丸める
  const formattedCondition: FilterBody = {
    ...condition,
    filters:
      condition.filters?.map((f) => {
        const isDateLike = dayjs(
          f.value,
          "YYYY-MM-DD HH:mm:ss",
          true,
        ).isValid();

        return {
          ...f,
          value: isDateLike
            ? dayjs(f.value).format("YYYY-MM-DD HH:mm")
            : f.value,
        };
      }) ?? [],
  };

  return useSWR(
    `/api/v1/leads/filter/${vendor_id}/${JSON.stringify(formattedCondition)}`,
    () => filteredLeadFetcher(formattedCondition),
  );
};

export const revalidateFilteredLead = () =>
  mutate((key) => String(key).startsWith("/api/v1/leads/filter"));

const filteredLeadFetcher = async (condition: FilterBody) => {
  // TODO: Partner を定義して index.d.ts からの依存をなくす
  //       いまは、/filter で取得できる Partner と 更新の body が同じ型として扱われていてよくない
  const response = await ax.post<{
    total_leads: number;
    total_pages: number;
    data: Lead[];
  }>("/api/v1/leads/filter", condition);
  if (response.status !== 200) {
    throw new Error("Failed to fetch data");
  }

  // TODO: 以下は暫定対処 PROP-3966 API側で閲覧不可のカラムを除外する対応が必要
  response.data.data.map((eachPartnerLead) => {
    eachPartnerLead.custom = eachPartnerLead.custom.filter(
      (eachCustom) => eachCustom.can_read,
    );
    return eachPartnerLead;
  });
  return response.data;
};

export const usePartnerUsersWithLeadId = (leadId: number) => {
  const { data, error } = useSWR<PartnerUser[]>(
    `/api/v1/leads/${leadId}/partner_users`,
    fetcher,
    {
      shouldRetryOnError: false,
    },
  );
  // NOTE: 未連携の パートナーの場合 403 のエラーが発生するため
  if (error) {
    return [];
  }
  return data;
};

export const useLeadCustomColumns = () => {
  return useSWR<CustomColumn[]>("/api/v1/lead_custom_column", fetcher);
};
