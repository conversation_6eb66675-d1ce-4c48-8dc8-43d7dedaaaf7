import { notifications } from "@mantine/notifications";
import { RiCloseCircleLine } from "@remixicon/react";
import { Head } from "components/data-table/head";
import { Variant } from "components/data-table/type";
import {
  getCustomColumnId,
  getLeadCustomColumnMeta,
} from "components/data-table/utils/custom";
import { LeadDataTableArg } from "features/leads/type/table";
import { customColumnContentsMap } from "features/leads/utils/custom";
import {
  ComponentProps,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { ax } from "utils/axios";
import * as Sentry from "@sentry/nextjs";
import { useVendorUsers } from "../vendor";
import {
  EditableDateCell,
  EditableDateTimeLocalCell,
  EditableFileCell,
  EditableLongTextCell,
  EditableNumberCell,
  EditablePartnerUserCell,
  EditableSelectCell,
  EditableTextCell,
} from "components/data-table/cell/editable";
import { revalidateFilteredLead, useLeadCustomColumns } from "../lead";

export const useCustomColumnsDef = (): LeadDataTableArg["columns"] => {
  const { data: vendorUsers } = useVendorUsers();
  const { data: customColumns } = useLeadCustomColumns();

  const updateCustomColumn = useCallback(
    async (
      leadId: number,
      customColumnId: number,
      value: string | number | null
    ) => {
      const { data: prev } = await ax.get<Partner>(`/api/v1/leads/${leadId}`);
      const updated = {
        ...prev,
        custom_column_contents: customColumnContentsMap(prev.custom, {
          customColumnId,
          value,
        }),
      };

      try {
        await ax.put(`/api/v1/leads/${leadId}`, updated);
      } catch (error) {
        notifications.show({
          title: "エラー",
          message: "更新に失敗しました",
          icon: <RiCloseCircleLine css={{ color: "#f93832" }} />,
        });
        Sentry.captureException(error);
        // 失敗した場合は再取得
        revalidateFilteredLead();
        return { ok: false };
      }
      return { ok: true };
    },
    []
  );
  return useMemo(
    () =>
      // NOTE: can_read が false のカラムは表示しない
      customColumns
        ?.filter((column) => column.can_read)
        .map((column) => {
          const variant = column.type_status;
          return {
            id: getCustomColumnId(column),
            header: (params) => (
              <Head header={params.header}> {column.column_label}</Head>
            ),
            accessorKey: "NOTE: cell で表示をつくるので accessorKey は使わない",
            // NOTE: 長文はフィルタリング不可
            enableColumnFilter: variant !== "LONG_TEXT",
            enableSorting: !["LONG_TEXT", "FILE", "STRING"].includes(variant),
            meta: {
              customize: getLeadCustomColumnMeta(
                variant as Variant,
                column,
                vendorUsers ?? []
              ),
            },
            cell: (params) => {
              const { customize } = params.column.columnDef.meta ?? {};
              const target = params.row.original.custom.find(
                (originalColumn) =>
                  originalColumn.column_name === column.column_name
              );
              const { lead_id } = params.row.original;
              if (!target) {
                return []; // 4402 カスタムカラムが正しくない場合は、スローせずに[]を返します。
              }
              switch (customize?.variant) {
                case "SELECT":
                  return (
                    <EditableSelectCellWithState
                      initialValue={String(target.select_id)}
                      params={params}
                      onChange={(value) =>
                        updateCustomColumn(lead_id, target.id, value)
                      }
                      editable={target.can_edit}
                    />
                  );
                case "VENDOR_USER":
                  return (
                    <EditableSelectCellWithState
                      withEmptyOption
                      initialValue={
                        target.vendor_user_id
                          ? String(target.vendor_user_id)
                          : ""
                      }
                      params={params}
                      onChange={(value) =>
                        updateCustomColumn(lead_id, target.id, value)
                      }
                      editable={target.can_edit}
                    />
                  );
                case "PARTNER_USER":
                  return (
                    <EditablePartnerUserCell
                      withEmptyOption
                      useSelectOptions={customize.useSelectOptions}
                      displayText={target.current ? String(target.current) : ""}
                      initialValue={
                        target.partner_user_id
                          ? String(target.partner_user_id)
                          : ""
                      }
                      partnerId={params.row.original.lead_id}
                      onChange={(value) =>
                        updateCustomColumn(lead_id, target.id, value)
                      }
                      editable={target.can_edit}
                    />
                  );

                case "FILE":
                  return (
                    <EditableFileCell
                      initialValue={
                        target.current ? String(target.current) : ""
                      }
                      onChange={async (file) => {
                        const path = `/api/v1/leads/${lead_id}/custom_file/${target.column_id}`;
                        if (!file) {
                          return;
                        }
                        const formData = new FormData();
                        formData.append("file", file);
                        await ax.post(path, formData);
                      }}
                      editable={target.can_edit}
                    />
                  );
                case "INTEGER":
                  return (
                    <EditableNumberCell
                      initialValue={
                        target.current ? Number(target.current) : null
                      }
                      onChange={(value) =>
                        updateCustomColumn(lead_id, target.id, value)
                      }
                      editable={target.can_edit}
                    />
                  );
                case "DATE":
                  return (
                    <EditableDateCell
                      initialValue={
                        target.current ? String(target.current) : ""
                      }
                      onChange={(value) =>
                        updateCustomColumn(lead_id, target.id, value)
                      }
                      editable={target.can_edit}
                    />
                  );
                case "LONG_TEXT":
                  return (
                    <EditableLongTextCell
                      initialValue={
                        target.current ? String(target.current) : ""
                      }
                      onChange={(value) =>
                        updateCustomColumn(lead_id, target.id, value)
                      }
                      editable={target.can_edit}
                    />
                  );
                case "DATETIME_LOCAL":
                  return (
                    <EditableDateTimeLocalCell
                      initialValue={
                        target.current ? String(target.current) : ""
                      }
                      onChange={(value) =>
                        updateCustomColumn(lead_id, target.id, value)
                      }
                      editable={target.can_edit}
                    />
                  );
                default:
                  return (
                    <EditableTextCell
                      initialValue={
                        target.current ? String(target.current) : ""
                      }
                      onChange={(value) =>
                        updateCustomColumn(lead_id, target.id, value)
                      }
                      editable={target.can_edit}
                    />
                  );
              }
            },
          };
        }) ?? [],
    [customColumns, vendorUsers]
  );
};

const EditableSelectCellWithState = <T,>(
  props: Omit<ComponentProps<typeof EditableSelectCell<T>>, "onChange"> & {
    onChange: (text: string | null) => Promise<{ ok: boolean }>;
  }
) => {
  // 値の更新に失敗した場合もとに戻すための state
  const [value, setValue] = useState(props.initialValue);
  // NOTE: initialValue が変更されたら state を更新する
  useEffect(() => {
    setValue(props.initialValue);
  }, [props.initialValue]);
  return (
    <EditableSelectCell
      {...props}
      initialValue={value}
      onChange={async (text) => {
        setValue(text);
        const { ok } = await props.onChange(text);
        if (!ok) {
          setValue(props.initialValue);
        }
      }}
    />
  );
};
