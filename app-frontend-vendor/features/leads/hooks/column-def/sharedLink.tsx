import { useMemo } from "react";
import { ax } from "utils/axios";
import LinkCopyButton from "../../components/LinkCopyButton";
import { LeadDataTableArg } from "../../type/table";
import { Head } from "components/data-table/head";
import { Cell } from "components/data-table/cell";
import { notifications } from "@mantine/notifications";
import * as Icon from "components/icons";

export const useSharedLinkColumnsDef = (): LeadDataTableArg["columns"] => {

  return useMemo(
    () => [
      {
        accessorKey: "shared_link",
        header: ({ header }) => <Head header={header}>案件URL</Head>,
        enableColumnFilter: false,
        enableSorting: false,
        size: 150,
        cell: (params) => {
          const leadId = String(params.row.original.lead_id);
          const handleCopyClick = async (leadId: string) => {
            const sharedUrl = await fetchSharedUrl(leadId);
            copyToClipboard(sharedUrl);
          };
          const fetchSharedUrl = async (leadId: string): Promise<string> => {
            const endpoint = `/api/v1/leads/${leadId}/permalink`;
            const response = await ax.get(endpoint);
            const sharedUrl = response.data;
            return sharedUrl;
          };
          const copyToClipboard = async (shared_url: string) => {
            if (shared_url !== null) {
              await navigator.clipboard.writeText(shared_url);
              notifications.show({
                title: "コピーしました",
                message: "案件URLをクリップボードにコピーしました",
                icon: <Icon.IconNotiSuccess />
              });
            }
          };
          return (
            <Cell>
              <div
                css={{
                  alignItems: "center",
                  display: "flex",
                  gap: "16px",
                  width: "100%",
                }}
              >
                <LinkCopyButton handleClick={() => handleCopyClick(leadId)} />
              </div>
            </Cell>
          );
        },
        meta: {
          customize: { variant: "STRING" },
        },
      },
    ],
    []
  );
};
