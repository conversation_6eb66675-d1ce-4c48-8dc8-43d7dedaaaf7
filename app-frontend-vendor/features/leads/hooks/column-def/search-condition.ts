import { ColumnFiltersState, SortingState } from "@tanstack/react-table";
import { getCustomColumnId } from "components/data-table/utils/custom";
import { Operator, Order } from "features/partners/type/api";
import { FilterValue } from "features/partners/type/filter";
import { useMemo } from "react";
import { useLeadCustomColumns } from "../lead";
import dayjs from "dayjs";

type PartnerTableFilterBody = {
  columns: {
    isMandatoryColumn: boolean;
    columnId: string;
  }[];
  filters: ColumnFiltersState;
  sorts: SortingState;
};

/**
 * SearchCondition を変換してcolumns, filters, sorts を返す
 * columns は accessorKey または customColumnName を持ち、ホワイトリストとして使う
 * filters, sorts はそのまま FilterBody に渡せる
 */
export const usePartnerTableFilterBody = (
  searchCondition: SearchCondition | undefined
): { data: PartnerTableFilterBody | null; error: Error | null } => {
  const { data: customColumns } = useLeadCustomColumns();
  return useMemo(() => {
    try {
      if (!searchCondition) {
        return { data: null, error: null };
      }
      const columns = searchCondition.columns.map((column) => {
        if (column.is_fixed_column) {
          return {
            isMandatoryColumn: true,
            columnId: toMandatoryColumnId(column.field),
          };
        }
        const target = customColumns?.find(
          (c) => c.column_name === column.field
        );
        if (!target) {
          throw new Error(
            "searchCondition.columns に存在しないカスタムカラムの columnName が指定されています"
          );
        }
        return {
          isMandatoryColumn: false,
          columnId: getCustomColumnId(target),
        };
      });

      const filters = searchCondition.filters.map((filter) => {
        const operatorsNeedingDateConversion = [
          "BEFORE_OR_EQUAL",
          "AFTER_OR_EQUAL",
          "GREATER_THAN_DATE",
          "LESS_THAN_DATE",
        ];
        const value = operatorsNeedingDateConversion.includes(filter.operator)
          ? dayjs(filter.value).format("YYYY-MM-DD HH:mm")
          : filter.value;

        if (filter.is_fixed_column) {
          const columnId = toMandatoryColumnId(filter.field);
          return {
            id: columnId,
            value: {
              value,
              operator: filter.operator,
            } as FilterValue,
          };
        } else {
          const target = customColumns?.find(
            (c) => c.column_name === filter.field
          );
          if (!target) {
            throw new Error(
              "searchCondition.filters に存在しないカスタムカラムの columnName が指定されています"
            );
          }
          return {
            id: getCustomColumnId(target),
            value: {
              operator: filter.operator as Operator,
              value,
            } as FilterValue,
          };
        }
      });

      const sorts = searchCondition.sorts.map((sort) => {
        if (sort.is_fixed_column) {
          const sortBy = toMandatoryColumnId(sort.sort_by);
          return {
            id: sortBy,
            desc: (sort.order_by as Order) === "DESC",
          };
        } else {
          const target = customColumns?.find(
            (c) => c.column_name === sort.sort_by
          );
          if (!target) {
            throw new Error(
              "searchCondition に存在しないカスタムカラムの columnName が指定されています"
            );
          }
          return {
            id: getCustomColumnId(target),
            desc: (sort.order_by as Order) === "DESC",
          };
        }
      });
      return { data: { columns, filters, sorts }, error: null };
    } catch (error) {
      return { data: null, error: error as Error };
    }
  }, [customColumns, searchCondition]);
};

const toMandatoryColumnId = (field: string) => {
  switch (field) {
    case "lead_status_name":
      return "lead_status";
    default:
      return field;
  }
};
