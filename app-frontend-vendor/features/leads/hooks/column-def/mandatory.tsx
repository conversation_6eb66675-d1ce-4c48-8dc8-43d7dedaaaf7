import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { usePrefectures, useLeadStatus } from "../master";
import { LeadDataTableArg } from "../../type/table";
import { Head } from "components/data-table/head";
import { Cell } from "components/data-table/cell";
import * as Icon from "components/icons";
import Image from "next/image";
import {
  ellipsisStyle,
  EllipsisText,
} from "components/ellipsis-text/EllipsisText";
import { revalidateFilteredLead, usePartnerUsersWithLeadId } from "../lead";
import { LeadStatusColor } from "constants/commons";
import LeadStatus from "constants/enums/leadStatus.enum";
import { notifications } from "@mantine/notifications";
import { RiCloseCircleLine } from "@remixicon/react";
import { ax } from "utils/axios";
import * as Sentry from "@sentry/nextjs";
import {
  EditableNumberCell,
  EditableSelectCell,
  EditableTextCell,
  KeepEditArea,
} from "components/data-table/cell/editable";
import Link from "next/link";
import { Select } from "@mantine/core";
import { ToggleEdit } from "components/data-table/toggle-edit";
import { useSetLeadProps } from "utils/recoil/lead/leadState";

const updateData = async ({
  leadId,
  columnId,
  value,
}: {
  leadId: string;
  columnId: string;
  value: string | number | null;
}) => {
  const { data: prev } = await ax.get<Partner>(`/api/v1/leads/${leadId}`);

  try {
    await ax.put(`/api/v1/leads/${leadId}`, {
      ...prev,
      [columnId]: value,
    });
  } catch (error) {
    notifications.show({
      title: "エラー",
      message: "更新に失敗しました",
      icon: <RiCloseCircleLine css={{ color: "#f93832" }} />,
    });
    Sentry.captureException(error);
    // 失敗した場合は再取得
    revalidateFilteredLead();
  }
};

export const useMandatoryColumnsDef = (
  handleClickDetail: (id: number) => void
): LeadDataTableArg["columns"] => {
  const { data: prefectures } = usePrefectures();
  const { data: leadStatus } = useLeadStatus();
  const setLeadProps = useSetLeadProps();

  return useMemo(
    () => [
      {
        accessorKey: "lead_name",
        header: ({ header }) => <Head header={header}>案件名</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        size: 320,
        cell: (params) => {
          const value = params.getValue() as string;
          return (
            <Cell>
              <div
                css={{
                  alignItems: "center",
                  display: "flex",
                  gap: "16px",
                  width: "100%",
                }}
              >
                <Link
                  css={{
                    color: "#0091ae",
                    ":hover": {
                      color: "#005f6b",
                      textDecoration: "underline",
                    },
                    ...ellipsisStyle,
                  }}
                  href={""}
                  onClick={() => handleClickDetail(params.row.original.lead_id)}
                >
                  {value}
                </Link>
              </div>
            </Cell>
          );
        },
        meta: {
          customize: { variant: "STRING" },
        },
      },
      {
        accessorKey: "partner_name",
        header: ({ header }) => <Head header={header}>共有先パートナー名</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        cell: (params) => {
          const value = params.getValue() as string;
          return (
            <Cell>
              <div
                css={{
                  alignItems: "center",
                  display: "flex",
                  gap: "16px",
                  width: "100%",
                }}
              >
                <div>
                  <Icon.Partner />
                </div>
                <EllipsisText value={value} />
              </div>
            </Cell>
          );
        },
        meta: {
          customize: { variant: "STRING" },
        },
      },
      {
        accessorKey: "partner_collaboration_id",
        header: ({ header }) => <Head header={header}>パートナー連携ID</Head>,
        meta: {
          customize: { variant: "STRING", disableEdit: true },
        },
        cell: (params) => (
          <Cell>
            <EllipsisText value={params.getValue() as string} />
          </Cell>
        ),
      },
      {
        id: "partner.user_name",
        accessorKey: "partner_user_id",
        header: ({ header }) => <Head header={header}>パートナー担当者</Head>,
        enableColumnFilter: true,
        meta: {
          customize: {
            variant: "PARTNER_USER",
            withEmptyOption: true,
            useSelectOptions: (id) => {
              const users = usePartnerUsersWithLeadId(id);
              return users?.map((pu) => ({
                label: pu.name + "-" + pu.email,
                value: String(pu.id),
              }));
            },
          },
        },
        cell: (params) => {
          const { partner_user_id, partner_user_name, lead_id } =
            params.row.original;
          const [value, setValue] = useState<string | null>(() =>
            partner_user_id ? String(partner_user_id) : ""
          );
          useEffect(() => {
            setValue(partner_user_id ? String(partner_user_id) : "");
          }, [partner_user_id]);
          const partnerUsers = usePartnerUsersWithLeadId(lead_id);
          const target = partnerUsers?.find((u) => String(u.id) === value);
          // Note: パートナーチームから退出したユーザーが紐づいていた場合、
          //       partnerUserに存在しないが partner_user_id, partner_user_name が存在する状態になる
          //       ユーザー選択できれば通常通り、できないならvalueに設定がある=初期値(退出ユーザー)利用とし、明示的に未設定になったら表示から消す
          const hasUserAssigned = Boolean(
            target || (value && partner_user_name)
          );
          const displayName = target?.name ?? (value ? partner_user_name : "");
          const logoUrl = target?.logo_url || "";

          if (
            params.column.columnDef.meta?.customize.variant !== "PARTNER_USER"
          ) {
            throw new Error(
              "PARTNER_USER is expected as variant, but got" +
                params.column.columnDef.meta?.customize.variant
            );
          }
          const selectOptions =
            params.column.columnDef.meta.customize.useSelectOptions(lead_id);
          return (
            <Cell>
              <ToggleEdit
                renderItem={(isEdit) => (
                  <KeepEditArea>
                    {isEdit ? (
                      <Select
                        data={[
                          { label: "(未選択)", value: "" },
                          ...(selectOptions ?? []),
                        ]}
                        value={value}
                        onChange={(input) => {
                          updateData({
                            leadId: lead_id.toString(),
                            columnId: "partner_user_id",
                            value: input,
                          });
                          setValue(input);
                        }}
                      />
                    ) : hasUserAssigned ? (
                      <div
                        css={{
                          alignItems: "center",
                          display: "flex",
                          gap: "16px",
                          width: "100%",
                        }}
                      >
                        <div>
                          {logoUrl ? (
                            <Image
                              src={logoUrl}
                              css={{ borderRadius: "40px" }}
                              objectFit="cover"
                              width={40}
                              height={40}
                              alt=""
                            />
                          ) : (
                            <Icon.VendorUser />
                          )}
                        </div>
                        <EllipsisText value={displayName} />
                      </div>
                    ) : (
                      <div />
                    )}
                  </KeepEditArea>
                )}
              />
            </Cell>
          );
        },
      },
      {
        accessorKey: "lead_status",
        header: ({ header }) => <Head header={header}> 案件ステータス</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: {
            disableEdit: true,
            variant: "SELECT",
            keys: { searchConditionKey: "lead_status_name" },
            selectOptions: Object.entries(leadStatus ?? {}).map(
              ([key, value]) => ({
                label: value.name,
                value: key,
              })
            ),
          },
        },
        cell: (params) => {
          const lead_status = params.getValue() as LeadStatus;
          const [value, setValue] = useState<LeadStatus>(() =>
            lead_status ? lead_status : LeadStatus.LEAD_PENDING
          );
          useEffect(() => {
            setValue(lead_status);
          }, [lead_status]);
          const { lead_id } = params.row.original;
          if (params.column.columnDef.meta?.customize.variant !== "SELECT") {
            throw new Error(
              "SELECT is expected as variant, but got" +
                params.column.columnDef.meta?.customize.variant
            );
          }
          const selectOptions =
            params.column.columnDef.meta?.customize.selectOptions;

          const target = leadStatus?.[lead_status];
          return (
            <Cell>
              <ToggleEdit
                renderItem={(isEdit) => (
                  <KeepEditArea>
                    {isEdit ? (
                      <Select
                        data={[...(selectOptions ?? [])]}
                        value={value}
                        onChange={(input) => {
                          updateData({
                            leadId: lead_id.toString(),
                            columnId: "lead_status",
                            value: input,
                          });
                          setValue(input as LeadStatus);
                        }}
                      />
                    ) : (
                      <div
                        className="leads-status-badge"
                        css={{
                          padding: "6px",
                          width: 84,
                          height: 26,
                          alignItems: "center",
                          justifyContent: "center",
                          display: "flex",
                          fontSize: 12,
                          lineHeight: "12px",
                          fontWeight: "600",
                          borderRadius: 4,
                          color: LeadStatusColor[value]?.textColor,
                          border: "1px solid #ffffff",
                          borderColor:
                            LeadStatusColor[value]?.borderColor ?? "#ffffff",
                          backgroundColor:
                            value === LeadStatus.LEAD_PENDING
                              ? "none"
                              : LeadStatusColor[value]?.borderColor,
                        }}
                      >
                        {leadStatus?.[value]?.name}
                      </div>
                    )}
                  </KeepEditArea>
                )}
              />
            </Cell>
          );
        },
      },
      {
        accessorKey: "url",
        header: ({ header }) => <Head header={header}>URL</Head>,
        enableColumnFilter: true,
        enableSorting: false,
        meta: {
          customize: { variant: "STRING" },
        },
        cell: (params) => {
          const initialValue = params.getValue() as string;
          const handleChange = useCallback(
            async (text: string | null) => {
              await updateData({
                leadId: params.row.original.lead_id.toString(),
                columnId: params.column.id,
                value: text,
              });
            },
            [params.column.id, params.row.original.lead_id]
          );

          return (
            <EditableTextCell
              initialValue={initialValue}
              onChange={handleChange}
            />
          );
        },
      },
      {
        accessorKey: "postal_code",
        header: ({ header }) => <Head header={header}>郵便番号</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: { variant: "STRING" },
        },
        cell: (params) => {
          const initialValue = params.getValue() as string;
          const handleChange = useCallback(
            async (text: string | null) => {
              await updateData({
                leadId: params.row.original.lead_id.toString(),
                columnId: params.column.id,
                value: text,
              });
            },
            [params.column.id, params.row.original.lead_id]
          );
          return (
            <EditableTextCell
              initialValue={initialValue}
              onChange={handleChange}
            />
          );
        },
      },
      {
        accessorKey: "prefecture_id",
        header: ({ header }) => <Head header={header}>都道府県</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: {
            variant: "SELECT",
            selectOptions:
              prefectures?.map(({ label, value }) => ({
                label,
                value: String(value),
              })) ?? [],
            withEmptyOption: true,
          },
        },
        cell: (params) => {
          const initialValue = params.getValue() as number | null;
          const [value, setValue] = useState(initialValue);
          useEffect(() => {
            setValue(initialValue);
          }, [initialValue]);
          const handleChange = useCallback(
            async (text: string | null) => {
              setValue(Number(text));
              await updateData({
                leadId: params.row.original.lead_id.toString(),
                columnId: params.column.id,
                value: text,
              });
            },
            [params.column.id, params.row.original.lead_id]
          );
          return (
            <EditableSelectCell
              initialValue={typeof value === "number" ? String(value) : ""}
              params={params}
              onChange={handleChange}
              withEmptyOption
            />
          );
        },
      },
      {
        accessorKey: "address",
        header: ({ header }) => <Head header={header}>住所</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: { variant: "STRING" },
        },
        cell: (params) => {
          const initialValue = params.getValue() as string;
          const handleChange = useCallback(
            async (text: string | null) => {
              await updateData({
                leadId: params.row.original.lead_id.toString(),
                columnId: params.column.id,
                value: text,
              });
            },
            [params.column.id, params.row.original.lead_id]
          );
          return (
            <EditableTextCell
              initialValue={initialValue}
              onChange={handleChange}
            />
          );
        },
      },
      {
        accessorKey: "number_of_employees",
        header: ({ header }) => <Head header={header}>従業員数</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: { variant: "INTEGER" },
        },
        cell: (params) => {
          const initialValue = params.getValue() as number | null;
          const handleChange = useCallback(
            async (num: number | null) => {
              await updateData({
                leadId: params.row.original.lead_id.toString(),
                columnId: params.column.id,
                value: num,
              });
            },
            [params.column.id, params.row.original.lead_id]
          );

          return (
            <EditableNumberCell
              initialValue={initialValue}
              onChange={handleChange}
            />
          );
        },
      },
      {
        accessorKey: "tel",
        header: ({ header }) => <Head header={header}>代表電話番号</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: { variant: "STRING" },
        },
        cell: (params) => {
          const initialValue = params.getValue() as string;
          const handleChange = useCallback(
            async (text: string | null) => {
              await updateData({
                leadId: params.row.original.lead_id.toString(),
                columnId: params.column.id,
                value: text,
              });
            },
            [params.column.id, params.row.original.lead_id]
          );
          return (
            <EditableTextCell
              initialValue={initialValue}
              onChange={handleChange}
            />
          );
        },
      },
      {
        accessorKey: "memo",
        header: ({ header }) => <Head header={header}>メモ</Head>,
        enableColumnFilter: true,
        enableSorting: false,
        meta: {
          customize: { variant: "STRING" },
        },
        cell: (params) => {
          const initialValue = params.getValue() as string;
          const handleChange = useCallback(
            async (text: string | null) => {
              await updateData({
                leadId: params.row.original.lead_id.toString(),
                columnId: params.column.id,
                value: text,
              });
            },
            [params.column.id, params.row.original.lead_id]
          );
          return (
            <EditableTextCell
              initialValue={initialValue}
              onChange={handleChange}
            />
          );
        },
      },
    ],
    [leadStatus, prefectures]
  );
};

export const useDateColumnsDef = (): LeadDataTableArg["columns"] => {
  return useMemo(
    () => [
      {
        accessorKey: "created_at",
        header: ({ header }) => <Head header={header}>作成日時</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: { variant: "DATETIME_LOCAL", disableEdit: true },
        },
        cell: (params) => {
          const value = params.getValue() as string;
          return (
            <Cell>
              <EllipsisText value={value} />
            </Cell>
          );
        },
      },
      {
        accessorKey: "updated_at",
        header: ({ header }) => <Head header={header}>最終更新日時</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: { variant: "DATETIME_LOCAL", disableEdit: true },
        },
        cell: (params) => {
          const value = params.getValue() as string;
          return (
            <Cell>
              <EllipsisText value={value} />
            </Cell>
          );
        },
      },
    ],
    []
  );
};
