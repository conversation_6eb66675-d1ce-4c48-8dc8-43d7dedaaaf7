import styled from "@emotion/styled";
import Head from "next/head";
import { propcolors } from "../../../styles/colors";
import { useLeadProps, useSetLeadProps } from "utils/recoil/lead/leadState";
import { LeadDetailLayout } from "./layout";
import { useGetDataMasters } from "utils/recoil/dataMasters";
import { useRouter } from "next/router";
import { ax } from "utils/axios";
import { useCallback, useEffect, useState } from "react";
import { SubTable } from "./components/subTable";
import { Center, Box } from "@mantine/core";

type PresentationProps = {
  className?: string;
  leadData: Lead | null;
  record?: SubtableRecord | null;
};

const Presentation: React.FC<PresentationProps> = ({ record, leadData }) => {
  return (
    <>
      <Head>
        <title>{leadData?.lead_id} | 案件 - PartnerProp</title>
      </Head>
      {record ? (
        <SubTable records={record} />
      ) : (
        <Center h={500}>
          <Box>データ取得中</Box>
        </Center>
      )}
    </>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-columns: 400px 1fr;
  .tab {
    &-root {
      height: 100%;
      display: grid;
      grid-template-rows: auto 1fr;
    }
    &-content {
      height: 100%;
    }
  }
  .lead {
    &-main {
      &-content {
        padding: 16px 16px 16px 0;
        height: 100%;
      }
    }
    &-sidebar {
      width: 320px;
      padding: 16px;
      display: grid;
      grid-template-rows: auto 1fr;
      grid-gap: 16px;
      &-content {
        width: 100%;
        font-size: 14px;
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 5px;
        background-color: ${propcolors.white};
        padding: 16px;
        &-products {
          &-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
          }
        }
      }
      &-id {
      }
      &-client {
        &_name {
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }
`;

export const LeadSubtable = () => {
  const leadData = useLeadProps();
  const setLeadData = useSetLeadProps();
  const dataMasters = useGetDataMasters();
  const [subtableData, setSubtableData] = useState<SubtableRecord | null>(null);
  const { query } = useRouter();

  const fetchLeadData = useCallback(
    (reset: boolean) => {
      if (reset) {
        setLeadData(null);
      }
      ax.get(`/api/v1/leads/${query.id}`).then((response) => {
        setLeadData(response.data);
      });
    },
    [query, setLeadData]
  );

  useEffect(() => {
    if (query.id && query.subtable_id) {
      if (leadData === null) {
        fetchLeadData(leadData !== null);
      }
      ax.get(
        `/api/v1/lead_sub_table/${query.subtable_id}/leads/${query.id}`
      ).then((res) => setSubtableData(res.data));
    }
  }, [query]);

  const sharePermalink = async () => {
    ax.get(`/api/v1/leads/${leadData?.lead_id}/permalink`).then((res) => {});
  };

  return (
    <LeadDetailLayout
      dataMasters={dataMasters}
      leadData={leadData}
      sharePermalink={sharePermalink}
      mobileLeadData={null}
    >
      <Styled record={subtableData} leadData={leadData} />
    </LeadDetailLayout>
  );
};
