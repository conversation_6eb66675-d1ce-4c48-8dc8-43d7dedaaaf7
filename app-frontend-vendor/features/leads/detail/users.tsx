import styled from "@emotion/styled";
import Head from "next/head";
import { propcolors } from "../../../styles/colors";
import {
  usePartnerProps,
} from "utils/recoil/partner/partnerState";

type PresentationProps = {
  className?: string;
  partnerData: Partner | null;
};

const Presentation: React.FC<PresentationProps> = ({ partnerData }) => {
  return (
    <>
      <Head>
        <title>
          {partnerData?.managed_partner_name} | パートナーユーザー - PartnerProp
        </title>
      </Head>
      {partnerData ? <></> : <>データ取得中</>}
    </>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-columns: 400px 1fr;
  .tab {
    &-root {
      height: 100%;
      display: grid;
      grid-template-rows: auto 1fr;
    }
    &-content {
      height: 100%;
    }
  }
  .lead {
    &-main {
      &-content {
        padding: 16px 16px 16px 0;
        height: 100%;
      }
    }
    &-sidebar {
      width: 400px;
      padding: 16px;
      display: grid;
      grid-template-rows: auto 1fr;
      grid-gap: 16px;
      &-content {
        width: 100%;
        font-size: 14px;
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 5px;
        background-color: ${propcolors.white};
        padding: 16px;
        &-products {
          &-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
          }
        }
      }
      &-id {
      }
      &-client {
        &_name {
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }
`;

export const PartnerUsers = () => {
  const partnerData = usePartnerProps();
  return <Styled partnerData={partnerData} />;
};
