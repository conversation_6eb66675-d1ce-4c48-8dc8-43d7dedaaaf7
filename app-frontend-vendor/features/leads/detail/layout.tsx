import styled from "@emotion/styled";
import { <PERSON><PERSON>, Text } from "@mantine/core";
import { modals } from "@mantine/modals";
import { notifications } from "@mantine/notifications";
import { CustomBreadcrumb } from "components/breadcrumb";
import { NavigationLink } from "components/link";
import { Skeleton } from "components/Skeleton";
import { GeneralTransition } from "components/transition";
import Image from "next/image";
import { useRouter } from "next/router";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconPartner from "public/icons/partner-icon.svg";
import IconUserPartner from "public/icons/user-icon.svg";
import { useCallback, useEffect, useState } from "react";
import { ax } from "utils/axios";
import { useIsMobileByUA } from "utils/hooks/useIsMobile";
import { useSetLeadList } from "utils/recoil/lead/leadListState";
import { useSessionUser } from "utils/recoil/sessionUserState";
import { getApiErrorMessage } from "utils/values/errorMessages";
import { propcolors } from "../../../styles/colors";
import BreadcrumbRender from "../components/breadcrumb/BreadcrumbRender";
import { EditButton } from "./components/generalInfo/components/partnerItem/components/EditButton";
import { SearchButton } from "./components/generalInfo/components/partnerItem/components/SearchButton";
import { LeadDetailMobileTabView } from "./components/leadDetailMobileTabView/leadDetailMobileTabView";

type PresentationProps = {
  className?: string;
  id?: string | string[] | undefined;
  leadData: Lead | null;
  dataMasters: DataMasters | null;
  children: React.ReactNode;
  deleteLead: () => Promise<void>;
  sharePermalink: () => Promise<void>;
  linkedPartnerUserList: User[] | null;
  filterLinkedPartnerUserList: (keyword: string) => User[] | null;
  subtableList: Subtable[] | null;
  mobileLeadData: Lead | null;
};

type LeadDetailProps = {
  id?: string | string[] | undefined;
  leadData: Lead | null;
  dataMasters: DataMasters | null;
  children: React.ReactNode;
  sharePermalink: () => Promise<void>;
  mobileLeadData: Lead | null;
};

const Presentation: React.FC<PresentationProps> = ({
  dataMasters,
  className,
  leadData,
  children,
  deleteLead,
  sharePermalink,
  linkedPartnerUserList,
  filterLinkedPartnerUserList,
  subtableList,
  mobileLeadData,
}) => {
  const sessionUser = useSessionUser();
  const userRole = sessionUser?.user_role;
  const isMobile = useIsMobileByUA();
  const [isEditing, setIsEditing] = useState(false);
  const toggleEditing = () => {
    setIsEditing(!isEditing);
  };
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const toggleEditingUsername = () => {
    setIsEditingUsername(!isEditingUsername);
  };

  return (
    <>
      {leadData && dataMasters ? (
        isMobile ? (
          <LeadDetailMobileTabView
            userRole={userRole ? userRole : 0}
            deleteDetail={deleteLead}
            sharePermalink={sharePermalink}
            leadData={mobileLeadData}
            linkedPartnerUserList={linkedPartnerUserList}
            filterLinkedPartnerUserList={filterLinkedPartnerUserList}
            dataMasters={dataMasters}
            leadDataProps={leadData}
            subtableList={subtableList}
          />
        ) : (
          <div className="lead-layout">
            <HeaderTitle>
              <header className="commonDetail-header">
                <CustomBreadcrumb
                  title="案件管理"
                  list={[
                    { title: "案件一覧", href: "/leads" },
                    { title: leadData.lead_name },
                  ]}
                />
              </header>
            </HeaderTitle>
            <main className={className}>
              <section className="main-content">
                <BreadcrumbRender leadStatus={leadData.lead_status} />
                <header>
                  <div className="leads-information">
                    <p className="leads-information-title_id">
                      <span className="leads-information-title_id_heading">
                        案件ID
                      </span>
                      {leadData.hex_record_id}
                    </p>
                    <h2 className="leads-information-title_name">
                      {leadData.lead_name}
                    </h2>
                  </div>

                  <div className="header-buttons">
                    <Button
                      variant="default"
                      onClick={sharePermalink}
                      className="share-permalink"
                    >
                      リンクを取得
                    </Button>
                    <Button
                      variant="default"
                      onClick={deleteLead}
                      className="del-partner"
                    >
                      案件の削除
                    </Button>
                  </div>
                </header>
                <nav className="leads-main-content-links">
                  <NavigationLink href={`/leads/${leadData.lead_id}`}>
                    <span>基本情報</span>
                  </NavigationLink>
                  {subtableList?.map((subtable) => (
                    <NavigationLink
                      key={subtable.sub_table_id}
                      href={`/leads/${leadData.lead_id}/subtable/${subtable.sub_table_id}`}
                    >
                      <span>{subtable.sub_table_name}</span>
                    </NavigationLink>
                  ))}
                </nav>
                {children}
              </section>

              <section className="sidebar">
                <section className="sidebar-information left">
                  <section className="sidebar-information-title_top">
                    パートナー
                  </section>
                  <section className="sidebar-information-title">
                    <p className="sidebar-information-title_status_heading">
                      共有先パートナー企業名
                    </p>
                    <div className="sidebar-information-title_status">
                      <p className="sidebar-information-title_status_name">
                        {leadData.partner_id ? (
                          <div className="infomation-partner">
                            {leadData?.partner_logo_url ? (
                              <Image
                                src={leadData.partner_logo_url}
                                style={{
                                  borderRadius: "40px",
                                  marginRight: "1rem",
                                }}
                                width={40}
                                height={40}
                                alt={leadData?.partner_name || ""}
                              />
                            ) : (
                              <IconPartner className="imgSpan" />
                            )}
                            <p className="right-name">
                              {leadData.partner_name}
                            </p>
                          </div>
                        ) : (
                          <span>未設定</span>
                        )}
                      </p>
                    </div>
                    <p className="sidebar-information-title_status_heading">
                      パートナー担当者
                    </p>

                    <div className="sidebar-information-title_status">
                      {!isEditingUsername &&
                        (leadData.partner_user_id ? (
                          <p className="sidebar-information-title_status_name">
                            <div className="infomation-partner">
                              {leadData?.partner_avatar_url ? (
                                <Image
                                  src={leadData.partner_avatar_url}
                                  style={{
                                    borderRadius: "40px",
                                    marginRight: "1rem",
                                  }}
                                  width={40}
                                  height={40}
                                  alt={leadData?.partner_user_name || ""}
                                />
                              ) : (
                                <IconUserPartner className="imgSpan" />
                              )}
                              <p className="right-name">
                                {leadData.partner_user_name}
                              </p>
                            </div>
                          </p>
                        ) : (
                          <p className="sidebar-information-title_status_name">
                            <div className="infomation-partner">
                              <IconUserPartner className="imgSpan" />
                              <p className="right-name">未設定</p>
                            </div>
                          </p>
                        ))}

                      <div
                        className={
                          isEditingUsername ? "right-icon-full" : "right-icon"
                        }
                      >
                        <SearchButton
                          title={
                            leadData.partner_user_name
                              ? leadData.partner_user_name
                              : "未設定"
                          }
                          current={
                            leadData.partner_user_id
                              ? leadData.partner_user_id
                              : -1
                          }
                          userList={linkedPartnerUserList}
                          DBlabel={"partner_user_id"}
                          filterList={filterLinkedPartnerUserList}
                          type={"USER"}
                          onBlur={toggleEditingUsername}
                          isEditing={isEditingUsername}
                          toggleEditing={toggleEditingUsername}
                        />
                      </div>
                    </div>
                  </section>
                  <div className="sidebar-line"></div>
                  <section className="sidebar-information-content no-backdrop">
                    <p className="sidebar-status-link_heading">
                      案件ステータス
                    </p>
                    <div className="sidebar-information-content_card_body">
                      <p className="sidebar-information-content_card_status">
                        {!isEditing &&
                          (leadData.lead_status ? (
                            <span>{leadData.lead_status_name}</span>
                          ) : (
                            <span>案件ステータスがありません</span>
                          ))}
                        <EditButton
                          title={"案件ステータス"}
                          type={"select"}
                          current={leadData.lead_status}
                          DBlabel={"lead_status"}
                          onBlur={toggleEditing}
                          isEditing={isEditing}
                          toggleEditing={toggleEditing}
                          value={
                            dataMasters.lead
                              ? Object.keys(dataMasters.lead)
                              : [
                                  "LEAD_PENDING",
                                  "LEAD_APPROACHING",
                                  "LEAD_APPOINTMENT",
                                  "LEAD_IN_PROGRESS",
                                  "LEAD_PIC_AGREEMENT",
                                  "LEAD_APPROVER_AGREEMENT",
                                  "LEAD_APPLIED",
                                  "LEAD_ON_HOLD",
                                  "LEAD_FAILURE",
                                  "LEAD_TERMINATION",
                                ]
                          }
                          placeholder={[
                            "未対応",
                            "アプローチ中",
                            "アポ設定",
                            "商談中",
                            "担当者合意",
                            "決裁者合意",
                            "申込済",
                            "保留",
                            "不成立",
                            "解約",
                          ]}
                        />
                      </p>
                    </div>
                  </section>
                </section>
              </section>
            </main>
          </div>
        )
      ) : (
        <Skeleton />
      )}
    </>
  );
};

const HeaderTitle = styled.div`
  .commonDetail-header {
    border-top: 1px solid ${propcolors.gray[200]};
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding: 16px 1.8rem;
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    -webkit-align-items: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
  .commonDetail-header svg {
    margin: 0 10px;
  }
  .vertical-line {
    display: inline-block;
    width: 1px;
    height: 20px;
    background-color: ${propcolors.gray[200]};
    margin: 0 24px;
    vertical-align: middle;
  }
  .commonList-header {
    font-size: 20px;
    font-weight: 400;
    color: ${propcolors.blackLight};
  }
  .commonList-header-small {
    font-weight: 400;
    font-size: 14px;
    color: ${propcolors.blackLight};
  }
`;

const Styled = styled(Presentation)`
  display: grid;
  grid-template-columns: 1fr 305px;
  padding: 0 1rem 0rem 0rem;
  gap: 0;

  header {
    display: flex;
    align-items: center;
    padding: 20px 1.8rem;
    gap: 10px;
  }

  .header-buttons {
    display: flex;
    gap: 20px;
    flex: 0 0 auto;
    margin-left: auto;
    button {
      border-radius: 8px;
      padding: 8px 11px;
      height: auto;
    }
  }

  .infomation-partner {
    display: flex;
  }

  .imgSpan {
    flex: 0 0 auto;
    margin-right: 1rem;
  }

  .right-name {
    line-height: 2.5rem;
    font-weight: 400;
    font-size: 14px;
    color: ${propcolors.blackLight};
  }

  .right-icon,
  .right-icon-full {
    margin-right: 0;
  }

  .leads-information {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    gap: 4px;

    &-title_id {
      font-size: 14px;
      font-weight: 400;
      color: ${propcolors.blackLightLabel};
    }

    &-title_id_heading {
      margin-bottom: 5px;
      margin-right: 8px;
    }

    &-title_name {
      font-size: 24px;
      font-weight: 400;
      color: ${propcolors.blackLight};
    }
  }

  .share-permalink {
    color: ${propcolors.blackLight};
    border: 1px solid ${propcolors.blackLightLabel};
    height: 36px;
    width: 132px;
    font-size: 14px;
    font-weight: 400;
    padding: 0;
    border-radius: 8px;
    font-family: "hiragino-kaku-gothic-pron", sans-serif;
  }
  .del-partner {
    color: ${propcolors.orangeDel};
    border: 1px solid ${propcolors.orangeDel};
    height: 36px;
    width: 132px;
    font-size: 14px;
    font-weight: 400;
    padding: 0;
    border-radius: 8px;
    font-family: "Inter", "system-ui";
  }

  .tab-root {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
  }

  .main-content {
    width: 100%;
    padding-top: 1rem;
    overflow-x: scroll;
  }

  .sidebar {
    width: 320px;
    display: grid;
    grid-template-rows: auto 1fr;
    border-left: 1px solid ${propcolors.gray[200]};
    height: 800px;
    overflow: auto;
    gap: 1rem;

    &-information,
    &-products {
      padding: 0;

      &-title_top,
      &_heading {
        padding: 0.7rem 25px;
        background-color: ${propcolors.greyBreadcrumb};
        color: ${propcolors.blackLightLabel};
        font-weight: 400;
        font-size: 14px;
      }

      &-title {
        padding: 25px;
        &_url_heading,
        &_status_heading {
          font-weight: 600;
          font-size: 12px;
          color: ${propcolors.blackLightLabel};
        }

        &_id {
          font-size: 20px;
          margin-bottom: 20px;
          font-weight: bold;

          &_heading {
            margin-right: 36px;
          }
        }

        &_name {
          font-weight: bold;
          margin-bottom: 1rem;
          font-size: 1.5rem;
        }

        &_url {
          font-weight: 300;
          font-size: 14px;
          color: ${propcolors.blackLight};
          text-decoration: underline;
          margin-top: 1rem;
          display: inline-block;
          width: min-content;
        }

        &_status {
          display: flex;
          align-items: center;

          &_name {
            margin-right: 10px;
            width: 100%;
            font-weight: 300;
            font-size: 14px;
            color: ${propcolors.blackLight};
          }

          &_heading:not(:first-child) {
            margin-top: 20px;
          }

          &_heading {
            margin-bottom: 16px;
            font-weight: 400;
            font-size: 12px;
            color: ${propcolors.blackLightLabel};
          }
        }
      }

      &-content {
        padding: 25px;

        &_card {
          &_heading {
            font-size: 12px;
            font-weight: bold;
          }

          &_body {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1rem;

            > button {
              width: 104px;
              font-size: 14px;
              font-weight: 600;
              height: 32px;
              padding: 0;
            }
          }

          &_status {
            display: flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            margin-top: -12px;

            span {
              width: 100%;
              color: ${propcolors.blackLight};
              font-weight: 400;
              font-size: 16px;
            }

            svg {
              margin-right: -0.5rem;
            }
          }

          &.vendor-user {
            grid-column: 1 / 3;
          }
        }
      }
    }

    &-line {
      border-top: 1px solid ${propcolors.gray[200]};
      margin: 0 25px;
    }

    &-status {
      gap: 64px;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid ${propcolors.gray[200]};

      .partner-delete {
        display: flex;
        margin-top: 20px;
        align-items: center;
        gap: 8px;
        padding: 0;
        border: 0;
        width: auto;

        > span {
          width: max-content;
          font-size: 20px;
          color: ${propcolors.partnerRed};
        }

        > svg {
          width: 20px;
          fill: ${propcolors.partnerRed};
        }
      }

      &-link {
        &_heading {
          margin-bottom: 20px;
          font-size: 12px;
          font-weight: 400;
          color: ${propcolors.blackLightLabel};
        }

        &_content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 1rem;

          button {
            width: 220px;
            height: 100%;
            font-size: 0.875rem;
          }
        }
      }

      &-contract {
        &_heading {
          margin-top: 1rem;
          margin-bottom: 0.5rem;
          font-size: 14px;
          font-weight: bold;
        }

        &_content {
          display: flex;
          font-size: 1rem;
          height: 33px;

          > button {
            border: 0px;
          }
        }

        .partner-main-content-links {
          border-bottom: 1px solid ${propcolors.gray[200]};
          a {
            display: inline-block;
            padding: 0 12px 12px 12px;
          }
        }
      }
    }
  }

  @media screen and (max-width: 512px) {
    padding: 0;
    height: fit-content;
    overflow: hidden;

    .sidebar {
      width: 100%;
    }

    .del-partner {
      grid-template-columns: 1fr;
      grid-template-rows: auto 1fr;
      overflow-y: scroll;
      height: 100%;
      padding: 0;
      margin: 0;
    }

    .main-content {
      width: 100%;
      padding-top: 1rem;
      overflow-x: scroll;
    }
  }

  .leads-main-content-links {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    border-bottom: 1px solid ${propcolors.gray[200]};
    margin-bottom: 18px;
    padding: 0 1.8rem;
    font-weight: 600;
    font-size: 14px;
    overflow-x: auto;
    white-space: nowrap;

    &::-webkit-scrollbar {
      height: 8px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 3px;
    }

    a {
      display: inline-block;
      text-align: center;
      color: ${propcolors.blackLight};
      white-space: nowrap;
      padding: 1rem 2rem;
      position: relative;

      span {
        font-weight: 400;
        font-size: 14px;
        white-space: nowrap;
      }
    }
  }
`;

const ModalTitle = styled.div`
  width: 100%;
  text-align: center;
  border-bottom: 1px solid ${propcolors.gray[200]};
  padding-bottom: 1rem;
  top: 1.3rem;
  position: absolute;
  margin: 0 -1rem;
  font-size: 16px;
  font-weight: 600;
  color: ${propcolors.blackLight};
`;

const ModalContent = styled.div`
  padding: 10px;
  .title-confirm {
    font-size: 18px;
    font-weight: 600;
    color: ${propcolors.blackLight};
    margin: 25px 0 20px 0;
  }
  .description {
    font-size: 12px;
    font-weight: 300;
    color: var(--Semantic-TEXT_BLACK, #23221e);
  }
`;

export const LeadDetailLayout: React.FC<LeadDetailProps> = ({
  children,
  dataMasters,
  leadData,
  sharePermalink,
  mobileLeadData,
}) => {
  const [linkedPartnerUserList, setLinkedPartnerUserList] = useState<
    User[] | null
  >(null);
  const [subtableList, setSubtableList] = useState<Subtable[] | null>(null);
  const setLeadList = useSetLeadList();
  const router = useRouter();
  const { id } = router.query;

  const filterLinkedPartnerUserList = (keyword: string) => {
    if (linkedPartnerUserList) {
      const currentFiltering = linkedPartnerUserList.filter((user) => {
        if (
          user.name.indexOf(keyword) !== -1 ||
          user.email.indexOf(keyword) !== -1
        ) {
          return true;
        } else {
          return false;
        }
      });

      return currentFiltering;
    } else return linkedPartnerUserList;
  };

  const fetchLinkedPartnerUser = useCallback(() => {
    if (leadData && leadData.lead_id > 0) {
      ax.get(`/api/v1/leads/${leadData.lead_id}/partner_users`).then((res) => {
        setLinkedPartnerUserList(res.data);
      });
    }
  }, [leadData]);

  const fetchSubtableList = useCallback(() => {
    ax.get(`api/v1/lead_sub_table`).then((response) => {
      setSubtableList(response.data);
    });
  }, []);

  useEffect(() => {
    if (subtableList === null) {
      fetchSubtableList();
    }
  }, [subtableList, fetchSubtableList]);

  useEffect(() => {
    fetchLinkedPartnerUser();
  }, [fetchLinkedPartnerUser]);

  const deleteLead = async () => {
    modals.openConfirmModal({
      title: <ModalTitle>案件の削除</ModalTitle>,
      size: "640px",
      closeButtonProps: { size: "24px" },
      children: (
        <ModalContent>
          <Text className="title-confirm">案件を削除しますか？</Text>
          <Text className="description">
            変更は取り消すことが出来ません。本当にこの案件を削除しますか？
          </Text>
        </ModalContent>
      ),
      labels: {
        confirm: "削除する",
        cancel: "キャンセル",
      },
      onConfirm: () => {
        ax.delete(`/api/v1/leads/${id}`)
          .then(() => {
            notifications.show({
              title: "案件が削除されました",
              message: "",
              icon: <IconNotiSuccess />,
            });
            setLeadList(null);
            router.push("/leads");
          })
          .catch((err) => {
            const errorMsg = err.response.data.error;
            if (errorMsg) {
              notifications.show({
                title: "案件削除に失敗しました",
                message: errorMsg,
                icon: <IconNotiFailed />,
              });
            } else {
              notifications.show({
                title: "案件削除に失敗しました",
                message: getApiErrorMessage(err.response.data.message),
                icon: <IconNotiFailed />,
              });
            }
          });
      },
      confirmProps: {
        sx: {
          width: "284px",
          height: "42px",
          right: "10px",
          fontSize: "14px",
          fontWeight: 600,
          marginBottom: "10px",
          borderRadius: "8px",
          backgroundColor: `${propcolors.black}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.black}`,
          },
        },
      },
      cancelProps: {
        variant: "outline",
        sx: {
          width: "284px",
          height: "42px",
          left: "25px",
          position: "absolute",
          fontSize: "14px",
          fontWeight: 600,
          marginBottom: "10px",
          borderRadius: "8px",
          borderColor: `${propcolors.greyDefault}`,
          backgroundColor: `${propcolors.greyDefault}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.greyDefault}`,
          },
        },
      },
    });
  };

  return (
    <Styled
      id={id}
      dataMasters={dataMasters}
      leadData={leadData}
      deleteLead={deleteLead}
      sharePermalink={sharePermalink}
      linkedPartnerUserList={linkedPartnerUserList}
      filterLinkedPartnerUserList={filterLinkedPartnerUserList}
      subtableList={subtableList}
      mobileLeadData={mobileLeadData}
    >
      <GeneralTransition>{children}</GeneralTransition>
    </Styled>
  );
};
