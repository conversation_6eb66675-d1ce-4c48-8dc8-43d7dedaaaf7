import styled from "@emotion/styled";
import { useGetDataMasters } from "utils/recoil/dataMasters";
import { PartnerItem } from "./components/partnerItem";

type GeneralInfoProps = {
  leadData: Lead;
};

type PresentationProps = {
  className?: string;
  prefectureList: string[] | undefined;
} & GeneralInfoProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  leadData,
  prefectureList,
}) => {
  return (
    <div className={className}>
      <PartnerItem
        type="text"
        current={leadData.lead_name}
        label={"案件名"}
        DBlabel={"lead_name"}
        value={leadData.lead_name}
      />
      <PartnerItem
        type={"text"}
        current={leadData.postal_code}
        label={"郵便番号"}
        DBlabel={"postal_code"}
        value={leadData.postal_code}
      />
      <PartnerItem
        type={"select"}
        current={leadData.prefecture_id}
        label={"都道府県"}
        DBlabel={"prefecture_id"}
        value={leadData.prefecture_id}
        placeholder={prefectureList}
      />
      <PartnerItem
        type={"text"}
        current={leadData.address}
        label={"住所"}
        DBlabel={"address"}
        value={leadData.address}
      />
      <PartnerItem
        type={"text"}
        current={leadData.tel}
        label={"代表電話番号"}
        DBlabel={"tel"}
        value={leadData.tel}
      />
      <PartnerItem
        type={"number"}
        current={leadData.number_of_employees}
        label={"従業員数"}
        DBlabel={"number_of_employees"}
        value={leadData.number_of_employees}
      />
      <PartnerItem
        type={"text"}
        current={leadData.url}
        label={"URL"}
        DBlabel={"url"}
        value={leadData.url}
      />
    </div>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  gap: 1rem;
  .generalinfo {
    &-heading {
      font-size: 16px;
      margin-bottom: 1rem;
    }
    &-products {
      &-list {
        display: grid;
        grid-gap: 16px;
        grid-template-columns: repeat(5, 1fr);
        grid-auto-rows: auto;
      }
    }
    &-profile {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-column-gap: 2rem;
      grid-auto-rows: auto;
      @media screen and (max-width: 512px) {
        grid-template-columns: 1fr;
      }
    }
  }
`;

export const MobileGeneralInfo: React.FC<GeneralInfoProps> = ({ leadData }) => {
  const dataMasters = useGetDataMasters();
  const flattenPrefectureList = dataMasters?.prefectures?.map((prefecture) => {
    return prefecture.label;
  });
  return <Styled leadData={leadData} prefectureList={flattenPrefectureList} />;
};
