import React, { useEffect, useState, useRef } from "react";
import { Cross2Icon } from "@radix-ui/react-icons";
import styled from "@emotion/styled";
import { ax } from "utils/axios";
import { useRouter } from "next/router";

import { useSetLeadProps } from "utils/recoil/lead/leadState";
import { DateInput, DateTimePicker } from "@mantine/dates";
import {
  FileInput,
  NumberInput,
  Select,
  SelectItem,
  TextInput,
  Textarea,
} from "@mantine/core";
import { useVendorUserList } from "utils/recoil/vendorUserListState";
import { usePartnerUserList } from "utils/recoil/partner/partnerUserListState";
import EditIcon from "public/icons/edit.svg";
import {
  CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT,
  CUSTOM_COLUMN_TYPE_NAME_STRING,
} from "constants/ja/common";
import {
  LONG_TEXT_MAX_LENGTH,
  STRING_MAX_LENGTH,
} from "constants/commonSetting";
import { propcolors } from "styles/colors";
import IconSelect from "public/icons/icon-arrow-down.svg";
import IconArrowDown from "public/icons/arrow-down.svg";
import IconArrowUp from "public/icons/arrow-up.svg";
import { formatDateString } from "utils/func/formatDateString";
import { useGetDataMasters } from "utils/recoil/dataMasters";
import { formatDatetimeLocal } from "features/partners/utils/formatHelper";
import useSWR from "swr";

const fetcher = (url: string) => ax.get(url).then((res) => res.data);

interface CustomContent {
  type_status: string;
  id: number;
  select_id?: number;
  vendor_user_id?: number;
  partner_user_id?: number;
  current?: string;
}

type EditButtonProps = {
  item: CustomColumn;
  isEditing: boolean;
  toggleEditing: () => void;
};

type PresentationalProps = {
  className?: string;
  setModValue: React.Dispatch<any>;
  modValue: string | number;
  setFileValue: React.Dispatch<any>;
  setDateValue: React.Dispatch<any>;
  dateValue: Date | null;
  setDateTimeValue: React.Dispatch<any>;
  dateTimeValue: Date | null;
  item: CustomColumn;
  onBlur: () => void;
  isEditing: boolean;
  toggleEditing: () => void;
  vendorUserList: VendorUser[] | null;
  partnerUserList: PartnerUser[] | null;
  setModValueNumber: React.Dispatch<any>;
  prefectures: SelectItem[];
  modValueNumber: string | number;
  error: string;
};

const Presentational: React.FC<PresentationalProps> = ({
  className,
  item,
  isEditing,
  setModValue,
  modValue,
  onBlur,
  toggleEditing,
  vendorUserList,
  partnerUserList,
  prefectures,
  setFileValue,
  setDateValue,
  dateValue,
  dateTimeValue,
  setDateTimeValue,
  modValueNumber,
  setModValueNumber,
  error,
}) => {
  const outsideRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const downloadClass = "download-file-down-btn";
      const deleteClass = "download-file-trash-btn";
      if (
        isEditing &&
        outsideRef.current &&
        !outsideRef.current.contains(event.target as Node) &&
        event.target instanceof HTMLElement &&
        !event.target.classList.contains(downloadClass) &&
        !event.target.classList.contains(deleteClass)
      ) {
        if (item.type_status === "FILE") {
          toggleEditing();
        } else if (item.type_status === "INTEGER") {
          onBlur();
        } else if (item.type_status === "DATETIME_LOCAL") {
          onBlur();
        }
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isEditing, item.type_status, toggleEditing, onBlur]);

  const handleBlur = () => {
    onBlur();
  };

  return (
    <div className={className} ref={outsideRef}>
      {!isEditing && <EditIcon onClick={toggleEditing} />}
      {isEditing && (
        <div>
          {item.type_status === "STRING" && (
            <>
              <TextInput
                className="width-max"
                onChange={(e) => setModValue(e.target.value)}
                defaultValue={item.current}
                onBlur={onBlur}
              />
              <div className="error-message">{error}</div>
            </>
          )}
          {item.type_status === "INTEGER" && (
            <NumberInput
              className="width-max"
              onChange={(e) => e && setModValueNumber(e)}
              placeholder="数値を入力してください。"
              type="number"
              value={Number(modValueNumber)}
              onBlur={handleBlur}
              rightSection={
                <div
                  style={{ display: "flex", flexDirection: "column", gap: 5 }}
                >
                  <IconArrowUp
                    style={{ cursor: "pointer" }}
                    onClick={() => {
                      setModValueNumber(Number(modValueNumber) + 1);
                    }}
                  />
                  <IconArrowDown
                    style={{ cursor: "pointer" }}
                    onClick={() => {
                      setModValueNumber(Number(modValueNumber) - 1);
                    }}
                  />
                </div>
              }
            />
          )}
          {item.type_status === "SELECT" && (
            <Select
              data={
                item.select_contents
                  ? item.select_contents.map((select_content, index) => {
                      return {
                        value: String(select_content.select_id),
                        label: `${select_content.select_value}`,
                      };
                    })
                  : []
              }
              defaultValue={String(item.select_id)}
              onChange={(e) => setModValue(Number(e))}
              rightSection={<IconSelect />}
              onBlur={onBlur}
              searchable
              autoFocus
            />
          )}
          {item.type_status === "DATE" && (
            <div>
              <DateInput
                value={dateValue}
                defaultValue={new Date()}
                valueFormat="YYYY年MM月DD日"
                locale="ja"
                monthLabelFormat="YYYY年MM月"
                yearLabelFormat="YYYY年"
                monthsListFormat="MM"
                yearsListFormat="YYYY"
                firstDayOfWeek={0}
                onChange={(e) => e && setDateValue(e)}
                onBlur={onBlur}
                autoFocus
                rightSection={<Cross2Icon onClick={() => setDateValue(null)} />}
              />
            </div>
          )}
          {item.type_status === "DATETIME_LOCAL" && (
            <div>
              <DateTimePicker
                value={dateTimeValue}
                locale="ja"
                valueFormat="YYYY年MM月DD日 HH:mm"
                onChange={(val) => setDateTimeValue(val)}
                firstDayOfWeek={0}
                rightSection={
                  <Cross2Icon
                    style={{ cursor: "pointer" }}
                    onClick={() => setDateTimeValue(null)}
                  />
                }
              />
            </div>
          )}
          {item.type_status === "VENDOR_USER" && (
            <Select
              data={
                vendorUserList
                  ? vendorUserList.map((user) => {
                      return {
                        value: String(user.id),
                        label: `${user.name}-${user.email}`,
                      };
                    })
                  : []
              }
              defaultValue={String(item.vendor_user_id)}
              onChange={(e) => setModValue(Number(e))}
              onBlur={() => {
                if (typeof modValue != "string") {
                  onBlur();
                } else {
                  toggleEditing();
                }
              }}
              rightSection={<IconSelect />}
              searchable
              autoFocus
              clearable
            />
          )}
          {item.type_status === "PARTNER_USER" && (
            <Select
              data={
                partnerUserList
                  ? partnerUserList.map((user) => {
                      return {
                        value: String(user.id),
                        label: `${user.name}-${user.email}`,
                      };
                    })
                  : []
              }
              defaultValue={String(item.partner_user_id)}
              onChange={(e) => setModValue(Number(e))}
              onBlur={() => {
                if (typeof modValue != "string") {
                  onBlur();
                } else {
                  toggleEditing();
                }
              }}
              rightSection={<IconSelect />}
              searchable
              autoFocus
              clearable
            />
          )}
          {item.type_status === "FILE" && (
            <>
              <FileInput
                onChange={(e) => setFileValue(e)}
                placeholder="ファイルを選択"
                description="最大500MBまで"
              />
              <div className="error-message">{error}</div>
            </>
          )}
          {item.type_status === "LONG_TEXT" && (
            <Textarea
              className="width-max"
              onChange={(e) => setModValue(e.target.value)}
              onBlur={onBlur}
              defaultValue={item.current}
              minRows={10}
              error={error}
            />
          )}
          {item.type_status === "PREFECTURE" && (
            <Select
              data={prefectures}
              placeholder="選択してください"
              rightSection={<IconSelect />}
              onBlur={onBlur}
              defaultValue={String(item.select_id)}
              onChange={(e) => setModValue(Number(e))}
              styles={{ rightSection: { pointerEvents: "none" } }}
            />
          )}
        </div>
      )}
    </div>
  );
};

const Styled = styled(Presentational)<{ isEditing: boolean }>`
  ${(props) =>
    !props.isEditing
      ? ``
      : `
  width: 100%;
  `}
  text-align: end;
  .del-file {
    margin-top: 1rem;
    color: ${propcolors.orangeDel};
    border: 1px solid ${propcolors.orangeDel};
  }
  .Button {
    border: 0;
  }
  .editor {
    &-content {
      width: 500px;
    }
    &-input {
      width: 300px;
    }
  }
  .width-max {
    width: 100%;
  }
  .error-message {
    -webkit-tap-highlight-color: transparent;
    color: inherit;
    font-size: inherit;
    line-height: 1.55;
    -webkit-text-decoration: none;
    text-decoration: none;
    word-break: break-word;
    color: ${propcolors.errorColor};
    font-size: calc(0.875rem - 0.125rem);
    line-height: 1.2;
    display: block;
    padding-top: 5px;
  }
  .mantine-TextInput-input {
    border-color: ${propcolors.gray[200]};
    background-color: white;
    color: ${propcolors.blackLight};
  }
  .mantine-TextInput-input:focus {
    background-color: ${propcolors.gray[150]};
  }
  .mantine-Select-item[data-selected] {
    background-color: ${propcolors.gray[150]};
    color: ${propcolors.blackLight};
  }
  .mantine-Textarea-input {
    border-color: ${propcolors.gray[200]};
    background-color: white;
    color: ${propcolors.blackLight};
    margin-top: 0;
    height: 4.8rem;
    width: 100%;
  }
  .mantine-Textarea-input:focus {
    background-color: ${propcolors.gray[150]};
  }
  .mantine-Input-input {
    border: 1px solid ${propcolors.gray[200]};
    background-color: white;
    border-radius: 8px;
  }
  .mantine-Input-input:focus {
    background-color: ${propcolors.gray[150]};
  }
  .mantine-FileInput-root {
    margin-top: 5px;
  }
  .mantine-FileInput-description {
    padding: 0.6rem;
    border-radius: 8px 8px 0 0;
    background-color: white;
    border-top: 1px solid ${propcolors.gray[200]};
    border-right: 1px solid ${propcolors.gray[200]};
    border-left: 1px solid ${propcolors.gray[200]};
    text-align: start;
  }
  .mantine-FileInput-input {
    border-radius: 0 0 8px 8px;
    background-color: ${propcolors.gray[150]};
    color: ${propcolors.blackLight};
    text-align: start;
  }
  .mantine-FileInput-placeholder {
    color: ${propcolors.blackLight};
  }
  .mantine-FileInput-wrapper {
    margin-top: 0;
  }
`;

export const CustomItemEdit: React.FC<EditButtonProps> = ({
  item,
  isEditing,
  toggleEditing,
}) => {
  const [modValue, setModValue] = useState<string | number>(item.current);
  const [fileValue, setFileValue] = useState<File | null>(null);
  const [dateValue, setDateValue] = useState<Date | null>(
    item.current ? new Date(formatDateString(item.current as string)) : null
  );
  const [dateTimeValue, setDateTimeValue] = useState<Date | null>(
    item.type_status === "DATETIME_LOCAL" && item.current
      ? new Date(formatDateString(item.current as string))
      : null
  );
  const [modValueNumber, setModValueNumber] = useState<string | number>(
    item.current || 0
  );
  const setLeadData = useSetLeadProps();
  const vendorUserList = useVendorUserList();
  const partnerUserList = usePartnerUserList();
  const router = useRouter();
  const { id } = router.query;
  const [error, setError] = useState("");
  const [prefectureInputList, setPrefectureInputList] = useState<SelectItem[]>(
    []
  );
  const dataMasters = useGetDataMasters();

  const { data: leadData, mutate: mutateLead } = useSWR(
    () => (router.isReady && id ? `/api/v1/leads/${id}` : null),
    fetcher,
    // onSuccess: setLead(data)がないとrecoilがずっと古いままになってしまい、putのrefetchがこけてしまう。
    // Fixme Recoil 周りをまるっと削って、leadData を直接使うようにする
    {
      onSuccess: (data) => {
        setLeadData(data);
      },
    }
  );

  const onSubmit = async () => {
    if (leadData && leadData.custom) {
      if (
        item.type_status === CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT &&
        modValue?.toString().length > LONG_TEXT_MAX_LENGTH
      ) {
        setError(
          LONG_TEXT_MAX_LENGTH.toLocaleString() + "文字以下で入力してください。"
        );
        return;
      }
      if (
        item.type_status === CUSTOM_COLUMN_TYPE_NAME_STRING &&
        modValue?.toString().length > STRING_MAX_LENGTH
      ) {
        setError(
          STRING_MAX_LENGTH.toLocaleString() + "文字以下で入力してください。"
        );
        return;
      }
      if (item.type_status === "FILE") {
        // file
        if (fileValue) {
          const maxFileSize = 500 * 1024 * 1024; // 500MB
          if (fileValue.size > maxFileSize) {
            setError("ファイルサイズは500MB以下で選択してください。");
            return;
          }
          const formData = new FormData();
          formData.append("file", fileValue);
          ax.post(`/api/v1/leads/${id}/custom_file/${item.id}`, formData)
            .then((res) => {
              mutateLead();
              toggleEditing();
              setError("");
            })
            .catch((err) => {});
        }
        return;
      }
      const custom_column_contents = leadData.custom.map(
        (custom_content: CustomContent) => {
          if (custom_content.type_status === "SELECT") {
            if (custom_content.id === item.id) {
              return {
                column_id: custom_content.id,
                select_id: modValue,
              };
            }
            return {
              column_id: custom_content.id,
              select_id: custom_content.select_id,
            };
          } else if (custom_content.type_status === "VENDOR_USER") {
            // vendor_user
            if (custom_content.id === item.id) {
              return {
                column_id: custom_content.id,
                vendor_user_id: modValue === 0 ? null : modValue,
              };
            }
            return {
              column_id: custom_content.id,
              vendor_user_id: custom_content.vendor_user_id,
            };
          } else if (custom_content.type_status === "PARTNER_USER") {
            // partner_user
            if (custom_content.id === item.id) {
              return {
                column_id: custom_content.id,
                partner_user_id: modValue === 0 ? null : modValue,
              };
            }
            return {
              column_id: custom_content.id,
              partner_user_id: custom_content.partner_user_id,
            };
          } else if (custom_content.type_status === "DATE") {
            // date
            if (custom_content.id === item.id) {
              return {
                column_id: custom_content.id,
                custom_column_content: dateValue
                  ? `${dateValue.getFullYear()}-${dateValue.getMonth() + 1}-${dateValue.getDate()}`
                  : null,
              };
            }
            return {
              column_id: custom_content.id,
              custom_column_content: custom_content.current,
            };
          } else if (custom_content.type_status === "DATETIME_LOCAL") {
            if (custom_content.id === item.id) {
              return {
                column_id: custom_content.id,
                custom_column_content: dateTimeValue
                  ? formatDatetimeLocal(dateTimeValue)
                  : null,
              };
            }
            return {
              column_id: custom_content.id,
              custom_column_content: custom_content.current,
            };
          } else if (custom_content.type_status === "INTEGER") {
            // integer
            if (custom_content.id === item.id) {
              return {
                column_id: custom_content.id,
                custom_column_content: modValueNumber ? modValueNumber : 0,
              };
            }
            return {
              column_id: custom_content.id,
              custom_column_content: custom_content.current,
            };
          } else {
            // string, integer, long_text
            if (custom_content.id === item.id) {
              return {
                column_id: custom_content.id,
                custom_column_content: modValue,
              };
            }
            return {
              column_id: custom_content.id,
              custom_column_content: custom_content.current,
            };
          }
        }
      );
      const body = {
        ...leadData,
        custom_column_contents,
      };
      ax.put(`/api/v1/leads/${id}`, body)
        .then((res) => {
          mutateLead();
          toggleEditing();
          setError("");
        })
        .catch((err) => {});
    }
  };
  useEffect(() => {
    if (fileValue) {
      onSubmit();
    }
  }, [fileValue]);

  useEffect(() => {
    if (dataMasters?.prefectures) {
      const selectItems: SelectItem[] = dataMasters.prefectures.map(
        (item) =>
          ({
            value: item.value,
            label: item.label,
          }) as unknown as SelectItem
      ); // Explicit type casting

      setPrefectureInputList(selectItems);
    }
  }, [dataMasters?.prefectures]);

  return (
    <Styled
      item={item}
      modValue={modValue}
      setModValue={setModValue}
      onBlur={() => {
        onSubmit();
      }}
      isEditing={isEditing}
      toggleEditing={toggleEditing}
      vendorUserList={vendorUserList}
      partnerUserList={partnerUserList}
      prefectures={prefectureInputList}
      setFileValue={setFileValue}
      dateValue={dateValue}
      setDateValue={setDateValue}
      dateTimeValue={dateTimeValue}
      setDateTimeValue={setDateTimeValue}
      modValueNumber={modValueNumber}
      setModValueNumber={setModValueNumber}
      error={error}
    />
  );
};
