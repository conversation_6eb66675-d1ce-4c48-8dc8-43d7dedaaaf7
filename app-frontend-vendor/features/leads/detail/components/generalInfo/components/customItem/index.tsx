import styled from "@emotion/styled";
import React, { useState } from "react";
import { propcolors } from "styles/colors";
import { CustomItemEdit } from "./components/EditButton";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import saveAs from "file-saver";
import { ActionIcon } from "@mantine/core";
import { useSetLeadProps } from "utils/recoil/lead/leadState";
import { notifications } from "@mantine/notifications";
import { getApiErrorMessage } from "utils/values/errorMessages";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconDownload from "public/icons/download.svg";
import IconTrash from "public/icons/trash.svg";

type PresentationProps = {
  className?: string;
  downloadFile: () => void;
  deleteFile: () => void;
  isEditing: boolean;
  toggleEditing: () => void;
} & CustomItemProps;

type CustomItemProps = {
  item: CustomColumn;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  item,
  downloadFile,
  deleteFile,
  isEditing,
  toggleEditing,
}) => {
  const formatNumberWithComma = (num: number) => {
    // 整数部分と小数部分を分離
    const parts = num.toString().split(".");
    // 整数部分に3桁ごとにカンマを挿入
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    // 整数部分と小数部分を結合して返却
    return parts.join(".");
  };

  const displayValue = () => {
    if (!item.current && item.type_status !== "SELECT") {
      return "未設定";
    }
    if (item.type_status === "INTEGER" && item.current) {
      return formatNumberWithComma(Number(item.current));
    } else {
      return item.current;
    }
  };

  return (
    <div className={`${className} ${!item.can_read ? "no-read" : ""}`}>
      <span className="info-label">
        {item.column_label ? item.column_label : ""}
      </span>
      {!item.can_read ? (
        <div className="no-read-message">このカラムは閲覧できません</div>
      ) : (
        <>
          {isEditing ? (
            <div className="file-controls">
              {item.type_status === "FILE" && (
                <>
                  <div className="download-file">
                    <span className="download-file-label">{item.current}</span>
                    {item.type_status === "FILE" && item.file_id !== null && (
                      <ActionIcon
                        onClick={() => downloadFile()}
                        className="download-file-down-btn"
                      >
                        <IconDownload />
                      </ActionIcon>
                    )}
                    {item.type_status === "FILE" && item.file_id !== null && (
                      <ActionIcon
                        onClick={() => deleteFile()}
                        className="download-file-trash-btn"
                      >
                        <IconTrash />
                      </ActionIcon>
                    )}
                  </div>
                </>
              )}
            </div>
          ) : (
            ""
          )}
          <div className="info-value-wrapper">
            {isEditing ? (
              <>
                {item.can_edit && (
                  <CustomItemEdit
                    item={item}
                    isEditing={isEditing}
                    toggleEditing={toggleEditing}
                  />
                )}
              </>
            ) : (
              <>
                {item.type_status === "LONG_TEXT" ? (
                  <span
                    className="info-value"
                    style={{
                      whiteSpace: "pre-wrap",
                      overflowWrap: "break-word",
                    }}
                  >
                    {displayValue()}
                  </span>
                ) : (
                  <span className="info-value">{displayValue()}</span>
                )}
                {item.can_edit && (
                  <CustomItemEdit
                    item={item}
                    isEditing={isEditing}
                    toggleEditing={toggleEditing}
                  />
                )}
              </>
            )}
          </div>
        </>
      )}
    </div>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid ${propcolors.gray[200]};
  font-size: 14px;
  .info {
    &-label {
      margin-bottom: 1rem;
      color: ${propcolors.blackLightLabel};
      font-size: 12px;
      font-weight: 400;
    }
    &-value {
      width: 100%;
      font-size: 14px;
      font-weight: 400;
      &.file {
        cursor: pointer;
        text-decoration: underline;
      }
      &-wrapper {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
  &.no-read {
    background-color: ${propcolors.gray[300]};
    color: ${propcolors.blackLightLabel};
    text-align: center;
  }
  
  .download-file {
    display: grid;
    grid-template-columns: 1fr auto auto;
    align-items: center;
    border: 1px solid ${propcolors.gray[200]};
    position: relative;
    padding-right: 3rem;
    border-radius: 8px;
    background-color: ${propcolors.greyBreadcrumb};
    &-label {
      display: inline-block;
      width: 100%;
      overflow-x: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 1rem;
      margin-top: 0rem;
      padding: 0.5rem;
      height: 35px;
      &_empty {
        color: ${propcolors.gray[400]};
        line-height: 1rem;
        margin-top: 0rem;
        padding: 0.5rem;
      }
    }
    &-down-btn {
      position: absolute;
      right: 2.5rem;
    }
    &-trash-btn {
      position: absolute;
      right: 0.5rem;
    }
  @media screen and (max-width: 1220px) {
    display: block;
  }
`;

export const CustomItem: React.FC<CustomItemProps> = ({ item }) => {
  const [isEditing, setEditing] = useState(false);
  const router = useRouter();
  const { id } = router.query;
  const setLeadData = useSetLeadProps();
  const [error, setError] = useState("");

  const fetchLeadData = async () => {
    ax.get(`/api/v1/leads/${id}`)
      .then((res) => {
        setLeadData(res.data);
      })
      .catch((err) => {});
  };

  const deleteFile = () => {
    if (item.file_id) {
      ax.delete(`/api/v1/leads/custom_file/${item.file_id}`)
        .then((res) => {
          notifications.show({
            title: "ファイルが削除されました",
            message: "",
            icon: <IconNotiSuccess />,
          });
          fetchLeadData();
          toggleEditing();
          setError("");
        })
        .catch((err) => {
          notifications.show({
            title: "ファイルの削除に失敗しました",
            message: getApiErrorMessage(err.response.data.message),
            icon: <IconNotiFailed />,
          });
        });
    }
  };

  const downloadFile = () => {
    ax.get(`/api/v1/leads/${id}/download/${item.id}`, { responseType: "blob" })
      .then((res) => {
        let name = "";
        if (item.current) {
          name = item.current as string;
        }
        let mineType = res.headers["content-type"];
        const blob = new Blob([res.data], { type: mineType });
        saveAs(blob, name);
      })
      .catch((err) => {});
  };
  const toggleEditing = () => {
    setEditing(!isEditing);
  };
  return (
    <Styled
      item={item}
      downloadFile={downloadFile}
      deleteFile={deleteFile}
      isEditing={isEditing}
      toggleEditing={toggleEditing}
    />
  );
};
