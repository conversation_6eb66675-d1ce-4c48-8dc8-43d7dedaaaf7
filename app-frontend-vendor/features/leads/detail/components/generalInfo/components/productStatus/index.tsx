import styled from "@emotion/styled";
import React, { memo, useEffect, useState } from "react";
import { propcolors } from "styles/colors";
import { useGetDataMasters } from "utils/recoil/dataMasters";
import { ax } from "utils/axios";
import {
  useLeadProducts,
  useSetLeadProducts,
} from "utils/recoil/lead/leadProductState";
import { Select } from "@mantine/core";
import { Text } from "@mantine/core";
import { useForm } from "@mantine/form";
import { useRouter } from "next/router";
import { modals } from "@mantine/modals";
import { notifications } from "@mantine/notifications";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconSelect from "public/icons/icon-arrow-down.svg";

type PresentationProps = {
  className?: string;
  handleValueChange: (next: string) => void;
  handleAvailability: (flag: string) => void;
  currentStatus?: string;
  status: { value: string; label: string }[];
  isFirst?: boolean | number;
} & ProductStatusProps;

type ProductStatusProps = {
  product: LeadProduct;
  isFirst: boolean;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  product,
  handleValueChange,
  handleAvailability,
  status,
  isFirst,
}) => {
  return (
    <div className={className}>
      <span className={`product-name ${isFirst ? "" : "with-border"}`}>
        {product.product_name}
      </span>
      <div className="product-modifier">
        <Select
          label="商談ステータス"
          data={status}
          defaultValue={product.negotiation_status}
          value={product.negotiation_status}
          onChange={handleValueChange}
          rightSection={<IconSelect />}
          styles={{ rightSection: { pointerEvents: "none" } }}
        />
        <Select
          label="承認ステータス"
          data={[
            {
              label: "未対応",
              value: "PENDING",
            },
            {
              label: "承認",
              value: "APPROVE",
            },
            {
              label: "否認",
              value: "DENY",
            },
          ]}
          onChange={handleAvailability}
          value={product.approval_status}
          defaultValue={product.approval_status}
          rightSection={<IconSelect />}
          styles={{ rightSection: { pointerEvents: "none" } }}
        />
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  display: flex;
  flex-flow: column;
  justify-content: space-between;
  padding: 10px 25px;
  label {
    font-weight: 400;
    font-size: 12px;
    color: ${propcolors.blackLightLabel};
    margin-bottom: 0.5rem;
  }
  .mantine-Select-input {
    font-weight: 400;
    font-size: 13px;
    color: ${propcolors.blackLight};
  }
  .mantine-Select-item[data-selected] {
    background-color: ${propcolors.gray[150]};
    color: ${propcolors.blackLight};
  }
  .mantine-Input-input {
    border: 1px solid ${propcolors.gray[200]};
    background-color: white;
    border-radius: 8px;
    padding-right: 1.5rem;
  }
  .mantine-Input-input:focus {
    background-color: ${propcolors.gray[150]};
  }
  .product {
    &status-selector {
      width: 100%;
      margin-top: 16px;
    }
    &-name {
      width: 100%;
      font-weight: 400;
      font-size: 14px;
      color: ${propcolors.blackLight};
      margin-top: 1rem;
      margin-bottom: 0.5rem;
      &.with-border {
        border-top: 1px solid ${propcolors.gray[200]};
        padding-top: 2rem;
        margin-top: 0;
      }
    }
    &-modifier {
      display: flex;
      gap: 0.5rem;
      margin-top: 0.5rem;
    }
  }
`;

const ModalTitle = styled.div`
  width: 100%;
  text-align: center;
  border-bottom: 1px solid ${propcolors.gray[200]};
  padding-bottom: 1rem;
  top: 1.3rem;
  position: absolute;
  margin: 0 -1rem;
  font-weight: 400;
  font-size: 16px;
  background-color: ${propcolors.white};
  color: ${propcolors.blackLight};
`;

const ModalContent = styled.div`
  padding: 10px;
  margin-bottom: 0px;
  .title-confirm {
    font-size: 18px;
    font-weight: 400;
    color: ${propcolors.blackLight};
    margin: 20px 0 32px 0;
  }
`;

const ProductStatusComponent: React.FC<ProductStatusProps> = ({
  product,
  isFirst,
}) => {
  const masters = useGetDataMasters();
  const leadProducts = useLeadProducts();
  const setLeadProducts = useSetLeadProducts();
  const router = useRouter();
  const { id } = router.query;
  const form = useForm<{
    negotiation_status: string;
    approval_status: string;
  }>({});
  const [statusSelect, setStatusSelect] = useState<
    { value: string; label: string }[]
  >([]);
  const handleValueChange = (next: string) => {
    if (!masters) return;
    const nextStatus = Object.keys(masters.negotiation).find(
      (value) => value === next
    );
    if (nextStatus === undefined) return;

    const reqBody = {
      negotiation_status: next,
      approval_status: product.approval_status,
    };
    ax.post(`/api/v1/leads/${id}/products/${product.product_id}`, reqBody)
      .then((res) => {
        notifications.show({
          title: "ステータス変更に成功しました。",
          message: `商品が「${masters.negotiation[nextStatus].name}」に変更されました。`,
          icon: <IconNotiSuccess />,
          autoClose: 3000,
        });
        if (leadProducts) {
          const newLeadProducts = [
            ...leadProducts.filter(
              (item) => item.product_name !== product.product_name
            ),
            { ...product, negotiation_status: next },
          ];
          setLeadProducts(
            newLeadProducts.sort((a, b) => a.product_id - b.product_id)
          );
        }
      })
      .catch((err) => {
        notifications.show({
          title: "ステータス変更に失敗しました。",
          message: `商品が正しく変更されませんでした。もう一度お試しください。`,
          icon: <IconNotiFailed />,
          autoClose: 5000,
        });
      });
  };

  const handleAvailability = (flag: string) => {
    const translator: { [key: string]: string } = {
      PENDING: "未対応",
      APPROVE: "承認",
      DENY: "否認",
    };
    if (!masters) return;
    modals.openConfirmModal({
      title: <ModalTitle>承認ステータスの変更</ModalTitle>,
      size: "640px",
      closeButtonProps: { size: "24px" },
      children: (
        <ModalContent>
          <Text className="title-confirm">本当にこの商品を承認しますか？</Text>
        </ModalContent>
      ),
      labels: {
        confirm: "承認",
        cancel: "キャンセル",
      },
      onConfirm: () => {
        const reqBody = {
          negotiation_status: product.negotiation_status,
          approval_status: flag,
        };
        ax.post(`/api/v1/leads/${id}/products/${product.product_id}`, reqBody)
          .then((res) => {
            notifications.show({
              title: "ステータス変更に成功しました。",
              message: `商品が「${translator[flag]}」に変更されました。`,
              icon: <IconNotiSuccess />,
              autoClose: 3000,
            });
            if (leadProducts) {
              const newLeadProducts = [
                ...leadProducts.filter(
                  (item) => item.product_name !== product.product_name
                ),
                { ...product, approval_status: flag },
              ];
              setLeadProducts(
                newLeadProducts.sort((a, b) => a.product_id - b.product_id)
              );
            }
          })
          .catch((err) => {
            notifications.show({
              title: "ステータス変更に失敗しました。",
              message: `商品が正しく変更されませんでした。もう一度お試しください。`,
              icon: <IconNotiFailed />,
              autoClose: 5000,
            });
          });
      },
      confirmProps: {
        sx: {
          width: "284px",
          height: "42px",
          right: "10px",
          fontSize: "14px",
          fontWeight: 400,
          marginBottom: "10px",
          borderRadius: "8px",
          backgroundColor: `${propcolors.black}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.black}`,
          },
        },
      },
      cancelProps: {
        variant: "outline",
        sx: {
          width: "284px",
          height: "42px",
          left: "25px",
          position: "absolute",
          fontSize: "14px",
          fontWeight: 400,
          marginBottom: "10px",
          borderRadius: "8px",
          borderColor: `${propcolors.greyDefault}`,
          backgroundColor: `${propcolors.greyDefault}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.greyDefault}`,
          },
        },
      },
    });
  };

  useEffect(() => {
    if (masters) {
      const data = Object.keys(masters.negotiation).map((value) => {
        return {
          value: value,
          label: masters.negotiation[value].name,
        };
      });
      setStatusSelect(data);
    }
  }, [masters]);
  return (
    masters && (
      <Styled
        product={product}
        handleValueChange={handleValueChange}
        handleAvailability={handleAvailability}
        status={statusSelect}
        isFirst={isFirst}
      />
    )
  );
};

export const LeadProductStatus = memo(ProductStatusComponent);
