import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import { ProductItem } from "features/partners/detail/components/generalInfo/components/productItem";

type ProductsListProps = {
  products: Product[];
};

type PresentationProps = {
  className?: string;
  products: Product[];
};

const Presentation: React.FC<PresentationProps> = ({ className, products }) => {
  return (
    <section className={className}>
      <section className="product-list-items">
        {products.map((item, index) => (
          <ProductItem key={index} product={item} />
        ))}
      </section>
    </section>
  );
};

const Styled = styled(Presentation)`
  .product {
    &-list {
      &-items {
        display: grid;
        gap: 8px;
      }
      &.active {
        margin-top: 12px;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid ${propcolors.gray[200]};
      }
      &.inactive {
      }
    }
  }
  .status {
    &-label {
      margin-bottom: 8px;
      display: block;
      font-weight: bold;
    }
  }
`;

export const ProductsList: React.FC<ProductsListProps> = ({ products }) => {
  return <Styled products={products} />;
};
