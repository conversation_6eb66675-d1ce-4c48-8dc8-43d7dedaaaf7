import styled from "@emotion/styled";
import { ActionIcon, Button, Group, NumberInput, Text } from "@mantine/core";
import { DateInput } from "@mantine/dates";
import { modals } from "@mantine/modals";
import type {
  ColDef,
  ICellRendererParams,
  SuppressKeyboardEventParams,
} from "ag-grid-community";
import { AGGrid } from "components/Grid";
import { propFileCellEditor } from "components/Grid/CellEditor/propFileCellEditor";
import { propLongTextEditor } from "components/Grid/CellEditor/propLongTextEditor";
import { propPartnerUserEditor } from "components/Grid/CellEditor/propPartnerUserEditor";
import { propSelectEditor } from "components/Grid/CellEditor/propSelectCellEditor";
import { propTextCellEditor } from "components/Grid/CellEditor/propTextCellEditor";
import { propVendorUserEditor } from "components/Grid/CellEditor/propVendorUserEditor";
import { Skeleton } from "components/Skeleton";
import {
  SUB_TABLE_DEFAULT_COLUMN_WIDTH,
  SUB_TABLE_LARGE_COLUMN_WIDTH,
} from "constants/commonSetting";
import {
  CUSTOM_COLUMN_TYPE_NAME_FILE,
  CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT,
  SUBTABLE_FILE_CHANGING,
} from "constants/ja/common";
import saveAs from "file-saver";
import { useRouter } from "next/router";
import IconDownload from "public/icons/download.svg";
import IconClose from "public/icons/icon_close_modal.svg";
import IconArrowDown from "public/icons/icon-grid-arrow-down.svg";
import IconArrowUp from "public/icons/icon-grid-arrow-up.svg";
import IconArrowNumberDown from "public/icons/icon-number-arrow-down.svg";
import IconArrowNumberUp from "public/icons/icon-number-arrow-up.svg";
import IconTrash from "public/icons/trash.svg";
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import { formatDateString } from "utils/func/formatDateString";
import {
  usePartnerUserList,
  useSetPartnerUserList,
} from "utils/recoil/partner/partnerUserListState";
import {
  useSetVendorUserList,
  useVendorUserList,
} from "utils/recoil/vendorUserListState";
import { ButtonHeaderGrid } from "../../../components/buttonHeaderGrid";

type PresentationProps = {
  className?: string;
  records: SubtableRecord;
};

type SubTableProps = {
  records: SubtableRecord;
};

const StyledDateInput = styled(DateInput)`
  margin-top: 1.7rem;
  .mantine-DateInput-input {
    border-color: ${propcolors.gray[200]};
    background-color: ${propcolors.gray[150]};
    color: ${propcolors.blackLight};
  }
`;

const StyledNumberInput = styled(NumberInput)`
  margin-top: 1.7rem;
  color: ${propcolors.inputBackground};
  .mantine-NumberInput-input {
    border-color: ${propcolors.gray[200]};
    background-color: ${propcolors.gray[150]};
    color: ${propcolors.blackLight};
  }
`;

const ModalTitle = styled.div`
  width: 100%;
  text-align: center;
  border-bottom: 1px solid ${propcolors.gray[200]};
  padding-bottom: 1rem;
  top: 1.3rem;
  position: absolute;
  margin: 0 -1rem;
  font-size: 16px;
  font-weight: 600;
  color: ${propcolors.blackLight};
`;

const ModalContent = styled.div`
  padding: 10px 10px 5px 10px;
  .title-confirm {
    font-size: 18px;
    font-weight: 600;
    color: ${propcolors.blackLight};
    margin: 10px 0 13px 0;
  }
  .description {
    font-size: 12px;
    font-weight: 300;
    color: var(--Semantic-TEXT_BLACK, #23221e);
  }
`;

const propDateEditor = memo(
  forwardRef(
    (props: { value: string; stopEditingCallback: () => void }, ref) => {
      const [dateValue, setDateValue] = useState<Date | null>(
        props.value ? new Date(formatDateString(props.value)) : new Date(),
      );
      const refInput = useRef<HTMLInputElement | null>(null);

      useImperativeHandle(ref, () => ({
        getValue: () =>
          dateValue
            ? `${dateValue.getFullYear()}-${String(dateValue.getMonth() + 1).padStart(2, "0")}-${String(dateValue.getDate()).padStart(2, "0")}`
            : "",
      }));

      useEffect(() => {
        if (refInput.current) {
          const editorBounds = refInput.current.getBoundingClientRect();
          const gridBounds = document
            .querySelector(".ag-root")
            ?.getBoundingClientRect();

          if (gridBounds) {
            const isOverlappingBottom = editorBounds.bottom > gridBounds.bottom;
            const isOverlappingTop = editorBounds.top < gridBounds.top;

            if (isOverlappingBottom) {
              refInput.current.style.top = `${-editorBounds.height}px`;
            } else if (isOverlappingTop) {
              refInput.current.style.top = `${editorBounds.height}px`;
            }
          }
        }
      }, []);

      return (
        <div ref={refInput}>
          <StyledDateInput
            value={dateValue}
            defaultValue={new Date()}
            valueFormat="YYYY年MM月DD日"
            locale="ja"
            monthLabelFormat="YYYY年M月"
            yearLabelFormat="YYYY年"
            monthsListFormat="M"
            yearsListFormat="YYYY"
            firstDayOfWeek={0}
            onChange={(e) => e && setDateValue(e)}
            rightSection={<IconClose onClick={() => setDateValue(null)} />}
            onBlur={() => {
              props.stopEditingCallback();
            }}
          />
        </div>
      );
    },
  ),
);

const propNumberEditor = memo(
  forwardRef(
    (props: { value: string; stopEditingCallback: () => void }, ref) => {
      const createInitialState = () => {
        return { value: props.value };
      };
      const refInput = useRef(null);
      const initialState = createInitialState();
      const [selectedValue, setSelectedValue] = useState<number | null>(
        Number(initialState.value),
      );

      const handleIncrement = () => {
        setSelectedValue(Number(selectedValue) + 1);
      };

      const handleDecrement = () => {
        setSelectedValue(
          Number(selectedValue) > 0 ? Number(selectedValue) - 1 : 0,
        );
      };

      useImperativeHandle(ref, () => ({
        getValue: () => selectedValue,
      }));
      return (
        <StyledNumberInput
          value={selectedValue ? selectedValue : 0}
          className="width-max"
          onChange={(e) =>
            setSelectedValue(typeof e === "number" ? Number(e) : null)
          }
          ref={refInput}
          onBlur={() => props.stopEditingCallback()}
          placeholder="数値を入力してください。"
          rightSection={
            <Group spacing={0} position="center">
              <ActionIcon onClick={handleIncrement}>
                <IconArrowNumberUp />
              </ActionIcon>
              <ActionIcon onClick={handleDecrement}>
                <IconArrowNumberDown />
              </ActionIcon>
            </Group>
          }
          type="number"
        />
      );
    },
  ),
);

const handleCellEditorType = (type: SubtableRecordType) => {
  switch (type) {
    case "SELECT":
      return propSelectEditor;
    case "DATE":
      return propDateEditor;
    case "INTEGER":
      return propNumberEditor;
    case "FILE":
      return propFileCellEditor;
    case "PARTNER_USER":
      return propPartnerUserEditor;
    case "VENDOR_USER":
      return propVendorUserEditor;
    case CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT:
      return propLongTextEditor;
    default:
      return propTextCellEditor;
  }
};

// サブテーブルのデータをAGGridのrowDataに渡せるよう変換
const mapRecordsToRowData = (records: SubtableRecordData[]) => {
  return records.map((record) =>
    record.values.reduce(
      (acc, cur) => ({
        ...acc,
        [cur.id.toString()]: cur.value,
      }),
      { record_id: record.record_id },
    ),
  );
};

const Presentation: React.FC<PresentationProps> = ({ className, records }) => {
  const [subtableData, setSubtableData] = useState<SubtableRecord>(records);
  const { query } = useRouter();
  const partnerUserList = usePartnerUserList();
  const setPartnerUserList = useSetPartnerUserList();

  const vendorUserList = useVendorUserList();
  const setVendorUserList = useSetVendorUserList();
  const router = useRouter();
  const { id } = router.query;
  const lead_id = Number(id);
  const [rowData, setRowData] = useState(mapRecordsToRowData(records.records));
  const gridApiRef = useRef<any>(null);
  const gridColumnApiRef = useRef<any>(null);

  // 入力モード（半角・全角(日本語)）の状態管理用
  const [isComposing, setIsComposing] = useState(false);
  // File変更時のローディング管理用
  const [isFileChanging, setIsFileChanging] = useState(false);

  useEffect(() => {
    if (vendorUserList === null) {
      ax.get(`/api/v1/vendor_users/`)
        .then((res) => {
          setVendorUserList(res.data);
        })
        .catch((err) => {});
    }
  }, [vendorUserList, setVendorUserList]);

  useEffect(() => {
    if (partnerUserList === null && lead_id > 0) {
      ax.get(`/api/v1/leads/${id}/partner_users`)
        .then((res) => {
          setPartnerUserList(res.data);
        })
        .catch((err) => {});
    }
  }, [partnerUserList, setPartnerUserList, id, lead_id]);

  const fetchAndUpdateRecords = useCallback(async (endpoint: string) => {
    try {
      const res = await ax.get(endpoint);
      setRowData(mapRecordsToRowData(res.data.records));
      setSubtableData(res.data);
    } catch (err) {
      console.error("Failed to fetch records:", err);
    }
  }, []);

  const handleAddRow = useCallback(async () => {
    try {
      await ax.post(
        `api/v1/lead_sub_table/${query.subtable_id}/leads/${query.id}/records`,
      );
      await fetchAndUpdateRecords(
        `/api/v1/lead_sub_table/${query.subtable_id}/leads/${query.id}`,
      );
      gridApiRef.current.api.refreshCells({ force: true });
      moveFocusToLatestRow();
    } catch (err) {}
  }, [query.id, query.subtable_id, fetchAndUpdateRecords]);

  const moveFocusToLatestRow = () => {
    if (gridApiRef.current) {
      // TODO SetTimeOutをやめる
      // AGGridの描画完了後にフォーカス移動したいため、100ミリ秒の遅延いれてます。
      // もっと良い方法にしたいです
      setTimeout(() => {
        const rowIndex = gridApiRef.current.getDisplayedRowCount() - 1; // 新しい行のインデックス
        const firstColKey = gridColumnApiRef.current
          .getAllDisplayedColumns()[0]
          .getColId(); // 最初の列のキー
        gridApiRef.current.setFocusedCell(rowIndex, firstColKey); // 最初のセルにフォーカスを設定
        gridApiRef.current.startEditingCell({
          // セル編集を開始
          rowIndex: rowIndex,
          colKey: firstColKey,
        });
      }, 100); // 100ミリ秒の遅延
    }
  };

  const downloadFile = (file_id: number, file_name: string) => {
    ax.get(`/api/v1/lead_sub_table/download/${file_id}`, {
      responseType: "blob",
    })
      .then((res) => {
        let name = "";
        if (file_name) {
          name = file_name as string;
        }
        const blob = new Blob([res.data], {
          type: res.headers["content-type"],
        });
        saveAs(blob, name);
      })
      .catch((err) => {});
  };

  const deleteFilled = (file_id: number) => {
    modals.openConfirmModal({
      title: <ModalTitle>ファイルの削除</ModalTitle>,
      size: "640px",
      closeButtonProps: { size: "24px" },
      children: (
        <ModalContent>
          <Text className="title-confirm">ファイルを削除しますか？</Text>
          <Text className="description">本当にファイルを削除しますか？</Text>
        </ModalContent>
      ),
      labels: {
        confirm: "削除する",
        cancel: "キャンセル",
      },
      onConfirm: () => {
        setIsFileChanging(true);
        ax.delete(`api/v1/lead_sub_table/value/${file_id}`)
          .then((res) => {
            fetchAndUpdateRecords(
              `/api/v1/lead_sub_table/${query.subtable_id}/leads/${query.id}`,
            );
          })
          .catch((err) => {})
          .finally(() => {
            setIsFileChanging(false);
          });
      },
      confirmProps: {
        sx: {
          width: "284px",
          height: "42px",
          right: "10px",
          fontSize: "14px",
          fontWeight: 400,
          marginBottom: "10px",
          borderRadius: "8px",
          backgroundColor: `${propcolors.black}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.black}`,
          },
        },
      },
      cancelProps: {
        variant: "outline",
        sx: {
          width: "284px",
          height: "42px",
          left: "25px",
          position: "absolute",
          fontSize: "14px",
          fontWeight: 400,
          marginBottom: "10px",
          borderRadius: "8px",
          borderColor: `${propcolors.greyDefault}`,
          backgroundColor: `${propcolors.greyDefault}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.greyDefault}`,
          },
        },
      },
    });
  };

  const formatDate = (date: string | number | Date) => {
    const dateObject =
      typeof date === "string"
        ? new Date(formatDateString(date))
        : new Date(date);
    const year = dateObject.getFullYear();
    const month = String(dateObject.getMonth() + 1).padStart(2, "0");
    const day = String(dateObject.getDate()).padStart(2, "0");
    return `${year}年${month}月${day}日`;
  };

  // セルの編集を完了させるコールバック関数
  const stopEditingCallback = () => {
    gridApiRef.current.stopEditing();
  };

  const colDefs: ColDef[] = records.columns
    .filter((column) => column.can_read)
    .map((column) => {
      return {
        field: column.column_id.toString(),
        headerName: column.column_label,
        minWidth:
          column.type_name === CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT ||
          column.type_name === CUSTOM_COLUMN_TYPE_NAME_FILE
            ? SUB_TABLE_LARGE_COLUMN_WIDTH
            : SUB_TABLE_DEFAULT_COLUMN_WIDTH,
        flex: 1,
        width:
          column.type_name === CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT ||
          column.type_name === CUSTOM_COLUMN_TYPE_NAME_FILE
            ? SUB_TABLE_LARGE_COLUMN_WIDTH
            : SUB_TABLE_DEFAULT_COLUMN_WIDTH,
        editable: column.can_edit,
        wrapText: column.type_name === CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT,
        autoHeight: column.type_name === CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT,
        cellStyle: {
          backgroundColor: column.can_edit
            ? propcolors.white
            : propcolors.gray[200],
        },
        cellEditorPopup: true,
        cellEditor: handleCellEditorType(column.type_name),
        cellRenderer: (props: ICellRendererParams) => {
          const cellValue = props.valueFormatted
            ? props.valueFormatted
            : props.value;
          const file_name = subtableData.records
            .find((data) => data.record_id === props.data.record_id)
            ?.values.find((value) => value.id === column.column_id)?.value;
          if (column.type_name === "FILE" && file_name) {
            const file_id = subtableData.records
              .find((data) => data.record_id === props.data.record_id)
              ?.values.find((value) => value.id === column.column_id)?.file_id;
            return (
              <button
                className="download-file"
                type="button"
                onClick={() => stopEditingCallback()}
              >
                <span className="download-file-label">{file_name}</span>
                <ActionIcon
                  onClick={() => downloadFile(file_id!, file_name)}
                  className="download-file-down-btn"
                >
                  <IconDownload />
                </ActionIcon>
                <ActionIcon
                  onClick={() => deleteFilled(file_id!)}
                  className="download-file-trash-btn"
                >
                  <IconTrash />
                </ActionIcon>
              </button>
            );
          } else if (column.type_name === "FILE") {
            return (
              <button
                className="download-file"
                type="button"
                onClick={() => stopEditingCallback()}
              >
                <span className="download-file-label_empty">未設定</span>
                <ActionIcon className="download-file-down-btn">
                  <IconDownload />
                </ActionIcon>
                <ActionIcon className="download-file-trash-btn">
                  <IconTrash />
                </ActionIcon>
              </button>
            );
          } else {
            if (!cellValue) {
              return <div className="cell-result">未設定</div>;
            }
            if (column.type_name === "DATE") {
              return (
                <div className="cell-result">
                  {formatDate(cellValue)}{" "}
                  <IconClose className="icon-close-date" />
                </div>
              );
            }
            return cellValue;
          }
        },
        cellEditorParams: {
          values:
            column.type_name === "SELECT"
              ? column.select_contents?.map((content) => ({
                  value: content.select_id.toString(),
                  label: content.select_value,
                }))
              : [],
          setIsComposing: setIsComposing,
          stopEditingCallback: stopEditingCallback,
        },
        suppressKeyboardEvent: (params) => {
          return suppressEnter(params, column.type_name);
        },
        valueFormatter: (params) => {
          if (column.type_name === "SELECT") {
            return (
              <div className="cell-result">
                {
                  column.select_contents?.find(
                    (content) =>
                      content.select_id.toString() === params.value ||
                      content.select_value === params.value,
                  )?.select_value
                }
              </div>
            );
          }
          if (column.type_name === "PARTNER_USER") {
            if (!params.value) {
              return <div className="cell-result">未設定</div>;
            }
            return (
              <div className="cell-result">
                {
                  partnerUserList?.find(
                    (user) =>
                      user.id.toString() === params.value ||
                      user.name === params.value,
                  )?.name
                }
              </div>
            );
          }
          if (column.type_name === "VENDOR_USER") {
            if (!params.value) {
              return <div className="cell-result">未設定</div>;
            }
            return (
              <div className="cell-result">
                {
                  vendorUserList?.find(
                    (user) =>
                      user.id.toString() === params.value ||
                      user.name === params.value,
                  )?.name
                }
              </div>
            );
          }
          if (column.type_name === "FILE") {
            // records内のレコードIDが一致するvaluesにカラムIDが一致するvalueを取得する処理
            const file_name = subtableData.records
              .find((data) => data.record_id === params.data.record_id)
              ?.values.find((value) => value.id === column.column_id)?.value;

            return file_name;
          }

          if (column.type_name === "STRING" || column.type_name === "INTEGER") {
            if (!params.value) {
              return <div className="cell-result">未設定</div>;
            }
            return <div className="cell-result">{params.value}</div>;
          }

          if (column.type_name === CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT) {
            if (!params.value) {
              return <div className="cell-result-area-empty">未設定</div>;
            }
            return (
              <div
                className="cell-result-area"
                style={{
                  whiteSpace: "pre-wrap",
                  display: "block",
                  lineHeight: 1,
                  paddingTop: 10,
                  paddingRight: 50,
                }}
              >
                {params.value}
              </div>
            );
          }
        },
        tooltipValueGetter: (params) => {
          if (column.type_name === "STRING") {
            return params.value;
          }
        },
      } as ColDef;
    });

  function suppressEnter(
    params: SuppressKeyboardEventParams,
    columnName: string,
  ) {
    var key = params.event.key;
    // 全角入力中の場合、Enterを使用不可にする()
    const suppress =
      columnName === CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT
        ? true
        : isComposing && key === "Enter";
    return suppress;
  }

  // 削除用のボタンを追加
  colDefs.push({
    headerName: "",
    headerComponentFramework: ButtonHeaderGrid,
    headerComponentParams: {
      handleAddRow: handleAddRow,
    },
    field: "actions",
    pinned: "right",
    cellStyle: { borderLeft: "none" },
    cellRendererFramework: (params: any) => {
      return (
        <div className="col-action">
          <Button
            className="btn-move-up"
            onClick={() => handleMoveRow(params, -1, params.data.record_id)}
            disabled={params.rowIndex === 0}
          >
            <IconArrowUp />
          </Button>
          <Button
            className="btn-move-down"
            onClick={() => handleMoveRow(params, 1, params.data.record_id)}
            disabled={params.rowIndex === rowData.length - 1}
          >
            <IconArrowDown />
          </Button>
          <Button
            className="del-row"
            onClick={() => handleDeleteRow(params.data.record_id)}
          >
            削除
          </Button>
        </div>
      );
    },
    width: 180,
    suppressSizeToFit: true,
    lockPosition: "right",
  });

  const handleCellValueChange = (event: any) => {
    const sub_table_column_id = event.colDef.field;
    const sub_table_record_id = subtableData.records[event.rowIndex].record_id;
    const type = subtableData.columns.find(
      (column) => column.column_id.toString() === sub_table_column_id,
    )?.type_name;
    const newValue = event.newValue;

    let data: any;
    if (type === "SELECT") {
      data = { select_id: newValue };
    } else if (type === "PARTNER_USER") {
      data = { partner_user_id: newValue === "" ? null : newValue };
    } else if (type === "VENDOR_USER") {
      data = { vendor_user_id: newValue === "" ? null : newValue };
    } else if (type === "FILE") {
      const formData = new FormData();
      formData.append("file", newValue);
      setIsFileChanging(true);
      ax.post(
        `/api/v1/lead_sub_table/value/${sub_table_column_id}/${sub_table_record_id}`,
        formData,
      )
        .then((res) => {
          fetchAndUpdateRecords(
            `/api/v1/lead_sub_table/${query.subtable_id}/leads/${query.id}`,
          );
        })
        .catch((err) => {})
        .finally(() => {
          setIsFileChanging(false);
        });
      return;
    } else {
      data = { value: newValue };
    }

    ax.post(
      `/api/v1/lead_sub_table/value/${sub_table_column_id}/${sub_table_record_id}`,
      data,
    )
      .then((res) => {})
      .catch((err) => {});
  };

  const handleDeleteRow = useCallback(
    async (recordId: string) => {
      modals.openConfirmModal({
        title: <ModalTitle>行の削除</ModalTitle>,
        size: "640px",
        closeButtonProps: { size: "24px" },
        children: (
          <ModalContent>
            <Text className="title-confirm">行を削除しますか？</Text>
            <Text className="description">
              行を削除後、この操作は元に戻せません。
            </Text>
          </ModalContent>
        ),
        labels: {
          confirm: "削除する",
          cancel: "キャンセル",
        },
        onConfirm: async () => {
          try {
            await ax.delete(`/api/v1/lead_sub_table/records/${recordId}`);
            fetchAndUpdateRecords(
              `/api/v1/lead_sub_table/${query.subtable_id}/leads/${query.id}`,
            );
          } catch (err) {}
        },
        confirmProps: {
          sx: {
            width: "284px",
            height: "42px",
            right: "10px",
            fontSize: "14px",
            fontWeight: 400,
            marginBottom: "10px",
            borderRadius: "8px",
            backgroundColor: `${propcolors.black}`,
            color: `${propcolors.white}`,
            "&:hover": {
              backgroundColor: `${propcolors.black}`,
            },
          },
        },
        cancelProps: {
          variant: "outline",
          sx: {
            width: "284px",
            height: "42px",
            left: "25px",
            position: "absolute",
            fontSize: "14px",
            fontWeight: 400,
            marginBottom: "10px",
            borderRadius: "8px",
            borderColor: `${propcolors.greyDefault}`,
            backgroundColor: `${propcolors.greyDefault}`,
            color: `${propcolors.white}`,
            "&:hover": {
              backgroundColor: `${propcolors.greyDefault}`,
            },
          },
        },
      });
    },
    [fetchAndUpdateRecords, query.id, query.subtable_id],
  );

  const handleMoveRow = (params: any, moveBy: number, recordId: string) => {
    const currentRowIndex = params.rowIndex;
    const toIndex = currentRowIndex + moveBy;

    if (toIndex < 0 || toIndex >= rowData.length) return;

    const direction = moveBy === 1 ? "down" : "up";

    // レコードの移動をAPIで実行
    moveRecord(direction, recordId)
      .then((response) => {
        fetchAndUpdateRecords(
          `/api/v1/lead_sub_table/${query.subtable_id}/leads/${query.id}`,
        );
      })
      .catch((err) => {});
  };

  // レコードの移動API呼び出し
  const moveRecord = (direction: "up" | "down", recordId: string) => {
    const endpoint = `/api/v1/lead_sub_table/records/${recordId}/move_${direction}`;
    return ax.put(endpoint);
  };

  return (
    <div className={className}>
      {isFileChanging && <Skeleton caption={SUBTABLE_FILE_CHANGING} />}
      <AGGrid
        ref={gridApiRef}
        className="grid-height-full"
        columnDefs={colDefs}
        rowData={rowData}
        onCellValueChanged={handleCellValueChange}
        onGridReady={(params) => {
          gridApiRef.current = params.api;
          gridColumnApiRef.current = params.columnApi;
        }}
        pagination={false}
      />
    </div>
  );
};

const Styled = styled(Presentation)`
  height: 100%;
  display: grid;
  grid-template-rows: auto 1fr;
  gap: 1rem;
  .header {
    display: flex;
    justify-content: flex-end;
    padding: 0 1.8rem;
    .btn-add-row {
      width: 80px;
      height: 32px;
      font-size: 14px;
      font-weight: 400;
      border-radius: 8px;
    }
  }
  .grid-height-full {
    height: calc(100vh - 415px);
    width: 100%;
    .ag-root-wrapper-body {
      min-height: auto;
    }
  }
  .ag-cell:hover .cell-result-area-empty {
    border: 1px solid ${propcolors.gray[200]};
    height: 45px;
    width: 360px;
    line-height: 45px;
    margin: 20px 0;
    border-radius: 8px;
    padding-left: 9px;
    margin-left: -10px;
    font-size: 14px;
    font-weight: 400;
    color: ${propcolors.blackLight};
  }
  .cell-result-area {
    width: 360px;
    height: 77px;
    line-height: 45px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .ag-cell:hover .cell-result-area {
    border: 1px solid ${propcolors.gray[200]};
    width: 360px;
    height: 77px;
    line-height: 45px;
    margin-top: 5px;
    border-radius: 8px;
    padding-left: 9px;
    margin-left: -10px;
    font-size: 14px;
    font-weight: 400;
    color: ${propcolors.blackLight};
  }
  .ag-cell:hover .cell-result {
    border: 1px solid ${propcolors.gray[200]};
    height: 45px;
    width: 200px;
    line-height: 45px;
    margin-top: 20px;
    border-radius: 8px;
    padding-left: 9px;
    margin-left: -10px;
    font-size: 14px;
    font-weight: 400;
    color: ${propcolors.blackLight};
  }
  .ag-row-hover .ag-cell,
  .ag-row-hover:before {
    background-color: white;
  }
  .icon-close-date {
    position: absolute;
    top: 30px;
    right: 20px;
    visibility: hidden;
  }
  .cell-result {
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .ag-cell:hover .cell-result .icon-close-date {
    visibility: visible;
  }
  .mantine-Select-dropdown {
    width: 200px !important;
    left: 10px !important;
  }
  .mantine-Select-wrapper .mantine-Input-input,
  .mantine-TextInput-wrapper .mantine-Input-input,
  .mantine-NumberInput-wrapper .mantine-Input-input,
  .mantine-DateInput-wrapper .mantine-Input-input {
    border: 1px solid ${propcolors.gray[200]};
    background-color: white;
    border-radius: 8px;
    height: 45px;
    width: 200px;
    margin-left: 8px;
  }
  .mantine-Input-input:focus {
    background-color: ${propcolors.gray[150]};
  }
  .mantine-NumberInput-rightSection {
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: auto;
  }
  .mantine-ActionIcon-root {
    min-width: 5px;
    min-height: 5px;
    width: 15px;
    height: 15px;
  }
  .ag-theme-alpine .mantine-DateInput-root,
  .ag-theme-alpine .mantine-NumberInput-root,
  .ag-theme-alpine .mantine-Select-root,
  .ag-theme-alpine .mantine-TextInput-root {
    margin-top: 20px;
  }
  .ag-cell-popup-editing .download-file {
    border: 1px solid ${propcolors.gray[200]};
    background-color: ${propcolors.greyBreadcrumb};
    &-down-btn {
      visibility: visible;
    }
    &-trash-btn {
      visibility: visible;
    }
  }
  .ag-cell:hover .download-file {
    border: 1px solid ${propcolors.gray[200]};
    background-color: white;
    width: 360px;
    &-down-btn {
      visibility: visible;
    }
    &-trash-btn {
      visibility: visible;
    }
  }
  .btn-move-down:disabled,
  .btn-move-down[data-disabled],
  .btn-move-up:disabled,
  .btn-move-up[data-disabled] {
    opacity: 0.2;
  }
  .download-file {
    display: grid;
    grid-template-columns: 1fr auto auto;
    align-items: center;
    position: relative;
    margin-top: 20px;
    padding-right: 3rem;
    border-radius: 0.25rem;
    height: 45px;
    width: 360px;
    &-label {
      display: inline-block;
      width: 100%;
      height: auto !important;
      overflow-x: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 1rem;
      margin-top: 0rem;
      padding: 0.5rem;
      &_empty {
        color: ${propcolors.blackLight};
        line-height: 1rem;
        margin-top: 0rem;
        padding: 0.5rem;
        height: auto !important;
      }
    }
    &-down-btn {
      position: absolute;
      right: 1.7rem;
      visibility: hidden;
    }
    &-trash-btn {
      position: absolute;
      right: 0.5rem;
      visibility: hidden;
    }
  }
  .btn-move-up:hover,
  .btn-move-down:hover,
  .del-row:hover {
    background-color: white;
  }
  .btn-move-down,
  .btn-move-up {
    border-radius: 4px;
    width: 32px;
    height: 32px;
    border: 1px solid ${propcolors.gray[400]};
    padding: 9px;
    background-color: white;
  }
  .col-action {
    width: 100%;
    margin-left: 22px;
    gap: 8px;
    display: inline-flex;
  }
  .del-row {
    color: ${propcolors.orangeDel};
    border: 1px solid ${propcolors.orangeDel};
    background: white;
    font-size: 14px;
    font-weight: 400;
    width: 52px;
    height: 32px;
    border-radius: 6px;
    padding: 0;
  }
  .ag-cell-value,
  .ag-group-value {
    overflow: visible;
    text-overflow: inherit;
  }
  .ag-cell.ag-cell-first-right-pinned:not(.ag-cell-range-left):not(
      .ag-cell-range-single-cell
    ) {
    border-left: none;
  }
  .mantine-TextInput-error {
    margin-left: 8px;
  }
`;

export const SubTable: React.FC<SubTableProps> = ({ records }) => {
  return <Styled records={records} />;
};
