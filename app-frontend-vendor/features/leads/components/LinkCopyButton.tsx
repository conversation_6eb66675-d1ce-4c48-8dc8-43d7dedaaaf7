import { ActionIcon } from "@mantine/core";
import { RiLink } from "@remixicon/react";
import React from "react";

type LinkCopyButtonProps = {
  handleClick: (event: React.MouseEvent<HTMLButtonElement | HTMLDivElement>) => void;
}

const LinkCopyButton: React.FC<LinkCopyButtonProps> = ({ handleClick }) => {
  return (
    <ActionIcon
      style={{ pointerEvents: 'auto' }}
      onClick={(event: React.MouseEvent<HTMLButtonElement | HTMLDivElement>) => {
        event.preventDefault();
        handleClick(event);
      }}
    >
      <RiLink/>
    </ActionIcon>
  )
}

export default LinkCopyButton;
