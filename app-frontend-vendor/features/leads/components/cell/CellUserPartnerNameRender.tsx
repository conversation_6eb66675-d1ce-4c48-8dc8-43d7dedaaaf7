import Image from "next/image";
import IconUserPartner from "public/icons/user-icon.svg";

interface PartnerUserProp {
  userName: string;
  thumbnail?: string | null;
}

interface CellUserPartnerNameRenderProp {
  value: PartnerUserProp;
}

const CellUserPartnerNameRender: React.FC<CellUserPartnerNameRenderProp> = (
  params,
) => {
  const { userName, thumbnail } = params.value;

  return (
    <div className="cell-user-partner-name-render">
      {userName && (
        <>
          <div className="cell-user-partner-name-render-image">
            {thumbnail ? (
              <Image src={thumbnail} style={{borderRadius: '40px'}} width={40} height={40} alt={userName} />
            ) : (
              <IconUserPartner className="imgSpan" />
            )}
          </div>
          <div className="cell-user-partner-name-render-text">{userName}</div>
        </>
      )}
    </div>
  );
};

export default CellUserPartnerNameRender;
