import styled from "@emotion/styled";
import { LeadStatusColor } from "constants/commons";
import LeadStatus from "constants/enums/leadStatus.enum";

interface IPresentationProp {
  status: LeadStatus;
  label: string;
}

interface ICellLeadStatusRender {
  value: IPresentationProp;
}

const Presentation: React.FC<IPresentationProp> = (props) => {
  const { status, label } = props;

  const statusColor = LeadStatusColor[status];

  return (
    <div
      className="leads-status-render"
      style={{
        display: "flex",
        // justifyContent: "center",
        alignItems: "center",
        height: "100%",
        width: "100%",
      }}
    >
      <div
        className="leads-status-badge"
        style={{
          padding: "6px",
          width: 84,
          height: 26,
          alignItems: "center",
          justifyContent: "center",
          display: "flex",
          fontSize: 12,
          lineHeight: "12px",
          fontWeight: "600",
          borderRadius: 4,
          color: statusColor?.textColor,
          border: "1px solid #ffffff",
          borderColor: statusColor?.borderColor ?? "#ffffff",
          backgroundColor:
            status === LeadStatus.LEAD_PENDING
              ? "none"
              : statusColor?.borderColor,
        }}
      >
        {label}
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  display: flex;
`;

const CellLeadStatusRender: React.FC<ICellLeadStatusRender> = (props) => {
  const { status, label } = props.value;

  return <Styled status={status} label={label} />;
};

export default CellLeadStatusRender;
