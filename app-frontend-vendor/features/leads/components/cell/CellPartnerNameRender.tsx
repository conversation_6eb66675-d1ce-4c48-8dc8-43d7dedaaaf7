import IconPartner from "public/icons/partner-icon.svg";
import Image from "next/image";

interface PartnerProp {
  userName: string;
  thumbnail?: string | null;
}

interface CellPartnerNameRenderProp {
  value: PartnerProp;
}

const CellPartnerNameRender: React.FC<CellPartnerNameRenderProp> = (params) => {
  const { userName, thumbnail } = params.value;
  return (
    <div className="cell-partner-name-render">
      {userName && (
        <>
          <div className="cell-partner-name-render-image">
            {thumbnail ? (
              <Image src={thumbnail} style={{borderRadius: '40px'}} width={40} height={40} alt={userName} />
            ) : (
              <IconPartner className="imgSpan" />
            )}
          </div>
          <div className="cell-partner-name-render-text">{userName}</div>
        </>
      )}
    </div>
  );
};

export default CellPartnerNameRender;
