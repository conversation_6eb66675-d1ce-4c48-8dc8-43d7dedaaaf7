import { SortingState, PaginationState } from "@tanstack/react-table";
import { CommonListLayout } from "components/layouts/commonListLayout";
import { MobileList } from "components/mobileList";
import { MobileListPager } from "components/mobileListPager";
import { NewLead } from "components/Modals/leads/newLead";
import { LeadsIcon } from "components/svgs/leads";
import { PER_PAGE_OF_LEAD_MOBILE } from "constants/commonSetting";
import { useFilteredLeads } from "features/leads/hooks/lead";
import { FilterBody } from "features/leads/type/api";
import { sortingMap } from "features/leads/utils/converter";
import { SearchListMobileModal } from "features/searchListMobileModal";
import Head from "next/head";
import { useEffect, useState } from "react";
import { useSessionUser } from "utils/recoil/sessionUserState";

const defaultSorts: SortingState = [
  {
    id: "updated_at",
    desc: true,
  },
];

const defaultPagination: PaginationState = {
  pageIndex: 0,
  pageSize: PER_PAGE_OF_LEAD_MOBILE,
};

export const MobileLeads = () => {
  const [pageNo, setPageNo] = useState<number>(0);
  const [conditions, setConditions] = useState<FilterBody>({
    page: defaultPagination.pageIndex,
    page_size: defaultPagination.pageSize,
    filters: [],
    sorts: sortingMap(defaultSorts),
  });
  const sessionUser = useSessionUser();
  const { data: filteredLead } = useFilteredLeads(conditions, sessionUser?.vendor_id);

  // ページネーションのページ番号を変更したときに再取得するための処理
  useEffect(() => {
    setConditions((prev) => ({
      ...prev,
      page: pageNo,
    }));
  }, [pageNo]);
  return (
    <CommonListLayout>
      <Head>
        <title>すべての案件 | PartnerProp</title>
      </Head>
      <header>
        <h2 className="commonList-header">
          <div className="commonList-header-icon">
            <LeadsIcon />
          </div>
          すべての案件
        </h2>
        <NewLead />
        <SearchListMobileModal isPartner={false} />
      </header>
      <div>
        <MobileList data={filteredLead?.data} />
        <MobileListPager
          setPageNo={setPageNo}
          pageNo={pageNo}
          total={filteredLead?.total_leads ?? 0}
        />
      </div>
    </CommonListLayout>
  );
};
