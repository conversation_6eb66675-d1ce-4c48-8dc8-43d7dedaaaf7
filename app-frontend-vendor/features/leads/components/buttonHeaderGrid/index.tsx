import styled from "@emotion/styled";
import React from "react";
import { Button } from "@mantine/core";
import { propcolors } from "styles/colors";

type ButtonHeaderGridProps = {
  className?: string;
  handleAddRow: () => void;
}

const Presentation: React.FC<ButtonHeaderGridProps> = ({ className, handleAddRow }) => {

  return (
    <div className={className}>
        <Button onClick={handleAddRow} className="btn-add-row">
            行の追加
        </Button>
    </div>
  );
};

const Styled = styled(Presentation)`
  width: 150px;
  text-align: right;
  position: absolute;
  right: 30px;
  .btn-add-row {
    border-radius: 8px;
    font-family: "Inter", "system-ui";
    font-size: 14px;
    font-weight: 400;
    color: white;
    background-color: ${propcolors.partnerRed}
  }
`;

export const ButtonHeaderGrid: React.FC<ButtonHeaderGridProps> = ({ handleAddRow }) => {
  return <Styled handleAddRow={handleAddRow}/>;
};
