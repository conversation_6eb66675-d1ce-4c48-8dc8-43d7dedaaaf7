import styled from "@emotion/styled";
import { useRouter } from "next/router";
import React from "react";
import { ax } from "utils/axios";
import { useLeadProps, useSetLeadProps } from "utils/recoil/lead/leadState";
import { propcolors } from "../../../../styles/colors";

const Breadcrumb = styled.div`
  max-width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  &::-webkit-scrollbar {
    height: 8px;
  }
  padding: 0 1.8rem 1rem 2rem;
  .breadcrumb {
    list-style: none;
    display: flex;
    width: 100%;
    padding: 0;
    margin: 0;
  }
  .breadcrumb li {
    flex: 1;
  }
  .breadcrumb li {
    position: relative;
    margin-right: 5px;
    text-align: center;
  }
  .breadcrumb li:last-child::before,
  .breadcrumb li:last-child::after {
    background: ${propcolors.greyBreadcrumb};
  }
  .breadcrumb li::before,
  .breadcrumb li::after {
    content: "";
    position: absolute;
    left: 0;
    height: 57%;
    width: 100%;
    background: white;
    border-left: 2px solid ${propcolors.greyBreadcrumb};
    border-right: 2px solid ${propcolors.greyBreadcrumb};
    z-index: -2;
  }
  .breadcrumb li:not(:first-of-type)::before,
  .breadcrumb li:not(:first-of-type)::after {
    background: ${propcolors.greyBreadcrumb};
  }
  .breadcrumb li::before {
    top: -2px;
    transform: skew(30deg);
    border-top: 2px solid ${propcolors.greyBreadcrumb};
  }
  .breadcrumb li::after {
    bottom: -2px;
    transform: skew(-30deg);
    border-bottom: 2px solid ${propcolors.greyBreadcrumb};
  }
  .breadcrumb li a {
    display: inline-block;
    position: relative;
    line-height: 2;
    padding: 0 20px;
    color: ${propcolors.greyDefault};
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    word-break: keep-all;
  }
  .breadcrumb li.actived::before,
  .breadcrumb li.actived::after {
    height: 57%;
    background-color: white;
  }
  .breadcrumb li.actived a,
  .breadcrumb li.actived a {
    color: ${propcolors.blackLight};
  }
  .breadcrumb li:first-of-type {
    background-color: white;
    border-left: 2px solid ${propcolors.greyBreadcrumb};
    left: -7px;
    box-sizing: content-box;
    color: ${propcolors.blackLight};
    margin-right: 4px;
    &::before,
    &::after {
    }
    &.actived {
      &::before,
      &::after {
        left: 2px;
        height: 50%;
      }
      &::before {
        top: -2px;
      }
    }
    &.active {
      left: -5px;
      border: 0px !important;
      &::before {
        left: 4px;
        height: 55% !important;
        top: -1px !important;
      }
      &::after {
        bottom: -1px !important;
        left: 4px;
        height: 55% !important;
      }
    }
  }
  .breadcrumb li:first-of-type a {
    color: ${propcolors.blackLight};
  }
  .breadcrumb li:first-of-type.active,
  .breadcrumb li:first-of-type.active {
    background-color: ${propcolors.black};
  }
  .breadcrumb li.active::before,
  .breadcrumb li.active::after {
    height: 57%;
    background-color: ${propcolors.black};
    border: 2px solid ${propcolors.black};
  }
  .breadcrumb li:first-of-type.active::before,
  .breadcrumb li:first-of-type.active::after {
    height: 55% !important;
    border: none !important;
  }
  .breadcrumb li:first-of-type.active::before {
    top: 0px;
  }
  .breadcrumb li:first-of-type.active::after {
    bottom: 0px;
  }
  .breadcrumb li:first-of-type.active::before,
  .breadcrumb li:first-of-type.active::after,
  .breadcrumb li:last-child.active::before,
  .breadcrumb li:last-child.active::after {
    height: 45%;
  }
  .breadcrumb li.active a {
    color: ${propcolors.white};
  }
  .breadcrumb li:first-of-type::before,
  .breadcrumb li:first-of-type::after {
    left: 5px;
  }
  .breadcrumb li:last-child {
    border-right: 10px solid ${propcolors.white};
    box-sizing: content-box;
    padding-right: 5px;
  }
  .pointer:hover {
    cursor: pointer;
  }

  @media screen and (max-width: 1220px) {
    .breadcrumb li::before {
      top: -1px;
    }
    .breadcrumb li:first-of-type.active::before {
      top: 1px;
    }
  }
`;

type BreadcrumbRenderProps = {
  leadStatus: string | number;
};

const BreadcrumbRender: React.FC<BreadcrumbRenderProps> = ({ leadStatus }) => {
  const leadData = useLeadProps();
  const setLeadData = useSetLeadProps();
  const router = useRouter();
  const { id } = router.query;

  const statusMap = {
    LEAD_PENDING: "未対応",
    LEAD_APPROACHING: "アプローチ中",
    LEAD_APPOINTMENT: "アポ設定",
    LEAD_IN_PROGRESS: "商談中",
    LEAD_PIC_AGREEMENT: "担当者合意",
    LEAD_APPROVER_AGREEMENT: "決裁者合意",
    LEAD_APPLIED: "申込済",
    LEAD_ON_HOLD: "保留",
    LEAD_FAILURE: "不成立",
    LEAD_TERMINATION: "解約",
  };

  const handleStatusChange = async (
    newStatusKey: String,
    newStatusValue: String
  ) => {
    const body = {
      ...leadData,
      lead_status: newStatusKey,
      lead_status_name: newStatusValue,
    };

    try {
      await ax.put(`/api/v1/leads/${id}`, body);
      fetchLeadProps();
    } catch (err) {}
  };

  const fetchLeadProps = async () => {
    const res = await ax.get(`/api/v1/leads/${id}`);
    setLeadData(res.data);
  };
  let activeFound = false;

  return (
    <Breadcrumb>
      <ul className="breadcrumb">
        {Object.entries(statusMap).map(([key, value], index, arr) => {
          if (leadStatus === key) {
            activeFound = true;
          }
          return (
            <li
              key={key}
              className={`pointer ${leadStatus === key ? "active" : ""} ${!activeFound && index !== arr.length - 1 ? "actived" : ""}`}
            >
              <a onClick={() => handleStatusChange(key, value)}>{value}</a>
            </li>
          );
        })}
      </ul>
    </Breadcrumb>
  );
};

export default BreadcrumbRender;
