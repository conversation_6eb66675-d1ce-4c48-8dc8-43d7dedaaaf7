import { <PERSON>a, StoryObj } from "@storybook/react";
import { Cell } from "components/data-table/cell";
import { Head } from "components/data-table/head";
import { useDataTable } from "components/data-table/hooks";
import { LeadsTable } from ".";

const meta = {
  title: "features/leads/table",
  component: LeadsTable,
} as Meta<typeof LeadsTable>;

export default meta;

const Template = () => {
  const table = useDataTable({
    columns: [
      {
        id: "lead_id",
        header: (props) => (
          <Head header={props.header}>
            とても長い名前のおかげで幅がどうなるかを確認してほしいです
          </Head>
        ),
        accessorKey: "lead_id",
        enableSorting: true,
        enableColumnFilter: true,
        size: 100,
        cell: (props) => <Cell>{props.getValue() as number}</Cell>,
      },
      {
        id: "name",
        header: (props) => (
          <Head header={props.header}>
            とても長い名前のおかげで幅がどうなるかを確認してほしいです
          </Head>
        ),
        accessorKey: "name",
        enableSorting: true,
        enableColumnFilter: true,
        size: 200,
        cell: (props) => <Cell>{props.getValue() as string}</Cell>,
      },
    ],
    data: [
      { lead_id: 1, name: "<PERSON>" },
      { lead_id: 2, name: "Bob" },
    ],
    pageCount: 1,
    initialTableState: { pagination: { pageIndex: 0, pageSize: 10 } },
  });
  return <LeadsTable table={table} isLoading={false} resetScrollKey={"key"} />;
};

export const Primary = {
  render: Template,
} as StoryObj<typeof meta>;
