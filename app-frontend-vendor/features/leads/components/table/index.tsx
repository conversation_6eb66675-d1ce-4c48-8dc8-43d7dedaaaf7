import { flexRender, type Table as TableType } from "@tanstack/react-table";
import LoadingSkeleton from "components/LoadingSkeleton";

export const LeadsTable = <T extends { lead_id: number }>({
  table,
  isLoading,
  resetScrollKey,
}: {
  isLoading: boolean;
  table: TableType<T>;
  resetScrollKey: string;
}) => {
  return (
    <div
      role="grid"
      css={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        minWidth: table.getCenterTotalSize(),
      }}
    >
      <div
        role="rowgroup"
        css={{
          zIndex: 1,
          backgroundColor: "#ffffff",
          border: "1px solid #e8eaed",
          position: "sticky",
          top: 0,
        }}
      >
        {table.getHeaderGroups().map((headerGroup) => (
          <div
            role="row"
            key={headerGroup.id}
            css={{
              display: "flex",
              height: "48px",
              boxShadow: "rgb(243, 243, 243) 0px 1px 3px 0px",
              borderBottom: "1px solid #e8eaed",
            }}
          >
            {headerGroup.headers.map((header) => {
              return (
                <div key={header.id} css={{ width: header.getSize() }}>
                  {header.isPlaceholder ? null : (
                    <>
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                    </>
                  )}
                </div>
              );
            })}
          </div>
        ))}
      </div>
      {isLoading && <LoadingSkeleton height={56} />}
      {!isLoading && table.getRowModel().rows.length === 0 && <EmptyState />}
      <div role="rowgroup" css={{ overflowY: "auto" }} key={resetScrollKey}>
        {table.getRowModel().rows.map((row) => {
          return (
            <div
              role="row"
              key={row.id}
              css={{
                backgroundColor: "#ffffff",
                borderBottom: "1px solid #dde2eb",
                display: "flex",
                height: "56px",
              }}
            >
              {row.getVisibleCells().map((cell) => {
                return (
                  <div
                    role="gridcell"
                    key={cell.id}
                    css={{ width: cell.column.getSize(), height: "56px" }}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    </div>
  );
};

const EmptyState = () => (
  <div
    css={{
      alignItems: "center",
      color: "#181d1f",
      display: "flex",
      flex: 1,
      minHeight: "500px",
      justifyContent: "center",
      position: "sticky",
      left: 0,
      // 88px はグローバルナビゲーションの幅
      width: "calc(100vw - 88px)",
    }}
  >
    データがありません
  </div>
);
