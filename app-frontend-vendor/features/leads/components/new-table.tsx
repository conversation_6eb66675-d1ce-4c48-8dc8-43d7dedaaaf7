import type {
  ColumnFilter,
  PaginationState,
  SortingState,
  Table,
  VisibilityState,
} from "@tanstack/react-table";
import { useDataTable } from "components/data-table/hooks";
import { PaginationControl } from "components/data-table/pagination-control";
import { DEFAULT_PAGINATION_PAGE_SIZE } from "constants/commonSetting";
import router from "next/router";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  useFilterSearchLeads,
  useSetFilterSearchLeads,
} from "utils/recoil/lead/leadFilterState";
import { usePrevPagePath } from "utils/recoil/navigation/navigationState";
import { useSessionUser } from "utils/recoil/sessionUserState";
import { useCheckBoxDeleteDef } from "../hooks/column-def/checkBoxDelete";
import { useCustomColumnsDef } from "../hooks/column-def/custom";
import {
  useDateColumnsDef,
  useMandatoryColumnsDef,
} from "../hooks/column-def/mandatory";
import { usePartnerTableFilterBody } from "../hooks/column-def/search-condition";
import { useSharedLinkColumnsDef } from "../hooks/column-def/sharedLink";
import { useFilteredLeads } from "../hooks/lead";
import type { Filter, FilterBody } from "../type/api";
import type { FilterValue } from "../type/filter";
import { filterMap, sortingMap } from "../utils/converter";
import { LeadsTable } from "./table";

const mergeFilters = (
  fixed: Filter[] | undefined,
  dynamic: Filter[] | undefined,
): Filter[] => {
  const map = new Map<string, Filter>();
  fixed?.forEach((f) => map.set(f.field, f));
  dynamic?.forEach((f) => map.set(f.field, f));
  return Array.from(map.values());
};

const defaultSorts: SortingState = [
  {
    id: "updated_at",
    desc: true,
  },
];

const defaultPagination: PaginationState = {
  pageIndex: 0,
  pageSize: DEFAULT_PAGINATION_PAGE_SIZE,
};

export const NewTable = ({
  searchCondition,
  searchPartnerName,
  sendDataCheckBoxDelete,
  setSearchPartnerNameDisplayName,
  flashDeleteDone,
}: {
  searchCondition: SearchCondition | undefined;
  searchPartnerName: string;
  sendDataCheckBoxDelete: (table: Table<Lead>) => void;
  setSearchPartnerNameDisplayName: Dispatch<SetStateAction<string>>;
  flashDeleteDone: boolean;
}) => {
  const [condition, setCondition] = useState<FilterBody>({
    page: defaultPagination.pageIndex,
    page_size: defaultPagination.pageSize,
    filters: [],
    sorts: sortingMap(defaultSorts),
  });

  const sessionUser = useSessionUser();
  const { data: filteredLead, isLoading } = useFilteredLeads(
    condition,
    sessionUser?.vendor_id,
  );

  const { data: filterBody } = usePartnerTableFilterBody(searchCondition);
  const handleClickCheckAll = () => {
    const isAllSelected = table.getIsAllRowsSelected();
    table.toggleAllRowsSelected(!isAllSelected);
  };

  const handleClickDetail = (id: number) => {
    handleDetailPage(id);
  };

  const mandatoryColDefs = useMandatoryColumnsDef(handleClickDetail);
  const dateColDefs = useDateColumnsDef();
  const customColDefs = useCustomColumnsDef();
  const sharedLinkColDefs = useSharedLinkColumnsDef();
  const useCheckBoxDeleteDefs = useCheckBoxDeleteDef(handleClickCheckAll);
  const [columnOrder, setColumnOrder] = useState<string[]>([]);

  const allColumns = useMemo(
    () => [
      ...useCheckBoxDeleteDefs,
      ...sharedLinkColDefs,
      ...mandatoryColDefs,
      ...customColDefs,
      ...dateColDefs,
    ],
    [
      customColDefs,
      dateColDefs,
      mandatoryColDefs,
      sharedLinkColDefs,
      useCheckBoxDeleteDefs,
    ],
  );

  const table = useDataTable({
    defaultColumn: { size: 200 }, // TODO: メモ化が必要かも
    columns: allColumns,
    data: filteredLead?.data ?? [],
    pageCount: filteredLead?.total_pages ?? 0,
    initialTableState: {
      pagination: defaultPagination,
      sorting: defaultSorts,
    },
    columnOrder: columnOrder,
  });

  useEffect(() => {
    table.resetRowSelection();
  }, [table]);

  // store pagination, search, and sorting values on the index page.
  const setFilterSearchLeads = useSetFilterSearchLeads();
  const handleDetailPage = (id: number) => {
    const filterJson = {
      pagination: table.getState().pagination,
      columnFilters: table.getState().columnFilters,
      sorting: table.getState().sorting,
    };
    // Recoilステートに保存
    setFilterSearchLeads(filterJson);
    router.push(`/leads/${id}?`);
  };

  const { columnFilters, pagination, sorting } = table.getState();
  // sendDataCheckBoxDeleteの安定した参照を保持するためにuseRefを使用
  const sendDataCheckBoxDeleteRef = useRef(sendDataCheckBoxDelete);

  // sendDataCheckBoxDeleteが変更されたときにrefを更新
  useEffect(() => {
    sendDataCheckBoxDeleteRef.current = sendDataCheckBoxDelete;
  }, [sendDataCheckBoxDelete]);

  // 安定したコールバック関数を作成
  const stableSendDataCheckBoxDelete = useCallback((table: Table<Lead>) => {
    sendDataCheckBoxDeleteRef.current(table);
  }, []);

  // flashDeleteDoneを監視し、必要に応じてテーブルをリセットするuseEffectを追加
  useEffect(() => {
    if (flashDeleteDone) {
      table.resetRowSelection(true);
    }
  }, [flashDeleteDone, table]);

  const [isFistLoadingBack, setIsFistFistLoadingBack] =
    useState<boolean>(false);
  const prevPage = usePrevPagePath();
  const filterSearchLeads = useFilterSearchLeads();

  // カラム可視性を設定する関数
  const setVisibleTable = useCallback(() => {
    if (!filterBody) {
      table.resetColumnVisibility();
      return;
    }

    const visibilityState: VisibilityState = table
      .getAllColumns()
      .map((column) => ({ id: column.id, visibility: true }))
      .reduce(
        (acc, { id }) => {
          acc[id] =
            id === "check_box_delete" || id === "shared_link"
              ? true
              : filterBody.columns.some((c) => c.columnId === id);
          return acc;
        },
        {} as { [key: string]: boolean },
      );
    table.setColumnVisibility(visibilityState);
  }, [filterBody, table]);

  // set the search filter value to the previous page.
  useEffect(() => {
    // Recoilステートからフィルター情報を取得
    const filterSearch = filterSearchLeads;
    setVisibleTable();
    if (filterSearch != null && prevPage === "/leads/[id]") {
      filterSearch.columnFilters.forEach((item: ColumnFilter) => {
        if (
          item.id === "lead_name" &&
          item.value &&
          typeof item.value === "object" &&
          "value" in item.value
        ) {
          const filterValue = item.value as FilterValue;
          if (filterValue.value) {
            setSearchPartnerNameDisplayName(filterValue.value);
          }
        }
      });
      table.setColumnFilters(filterSearch.columnFilters);
      table.setSorting(filterSearch.sorting);
      table.setPagination(filterSearch.pagination);

      setCondition((prev) => ({
        ...prev,
        filters: mergeFilters(
          filterMap(filterBody?.filters ?? []),
          filterMap(filterSearchLeads?.columnFilters ?? []),
        ),
        sorts: sortingMap(filterSearchLeads?.sorting ?? defaultSorts),
        page: filterSearchLeads?.pagination.pageIndex ?? prev.page,
        page_size: filterSearchLeads?.pagination.pageSize ?? prev.page_size,
      }));
    }
    setIsFistFistLoadingBack(true);
  }, [
    prevPage,
    filterSearchLeads,
    setVisibleTable,
    setSearchPartnerNameDisplayName,
    table,
    filterBody?.filters,
  ]);

  // 選択された行の変更を直接監視する - rowSelectionステートを使用
  const rowSelection = table.getState().rowSelection;
  useEffect(() => {
    if (!isFistLoadingBack || !rowSelection) {
      return;
    }
    stableSendDataCheckBoxDelete(table);
  }, [rowSelection, stableSendDataCheckBoxDelete, isFistLoadingBack, table]);

  // 前回のfilterBodyを追跡するためのrefを使用
  const prevFilterBodyRef = useRef(filterBody);
  useEffect(() => {
    // isFistLoadingBackがfalseの場合は何もしない
    if (!isFistLoadingBack) {
      return;
    }

    // filterBodyが実際に変更された場合のみリセットを実行
    // 初回レンダリングまたはRecoilステートからの復元時には実行しない
    if (
      prevFilterBodyRef.current !== filterBody &&
      prevFilterBodyRef.current !== null
    ) {
      // 固定フィルターの値が変更されたらページをリセット
      table.resetPageIndex();
      table.resetRowSelection(true);
      setVisibleTable();
    }
    setCondition((prev) => ({
      ...prev,
      filters: mergeFilters(
        filterMap(filterBody?.filters ?? []),
        filterMap(columnFilters),
      ),
    }));

    // refを更新
    prevFilterBodyRef.current = filterBody;
  }, [filterBody, isFistLoadingBack, table, setVisibleTable, columnFilters]);

  // 前回のfiltersとisFistLoadingBackを追跡するためのrefを使用
  const prevFiltersRef = useRef(filterBody?.filters);
  const prevIsFistLoadingBackFilterRef = useRef(isFistLoadingBack);

  useEffect(() => {
    // isFistLoadingBackがfalseの場合は何もしない
    if (!isFistLoadingBack) {
      prevIsFistLoadingBackFilterRef.current = false;
      return;
    }

    // 以下の場合にのみフィルターを適用する：
    // 1. filtersが実際に変更された場合
    // 2. isFistLoadingBackがfalseからtrueに変わった場合（詳細画面から戻った場合）ではない
    if (
      prevIsFistLoadingBackFilterRef.current &&
      prevFiltersRef.current !== filterBody?.filters
    ) {
      table.setColumnFilters(filterBody?.filters ?? []);
    }

    // refを更新
    prevFiltersRef.current = filterBody?.filters;
    prevIsFistLoadingBackFilterRef.current = true;
  }, [filterBody?.filters, isFistLoadingBack, table]);

  useEffect(() => {
    if (!isFistLoadingBack) {
      return;
    }
    setCondition((prev) => ({
      ...prev,
      filters: mergeFilters(
        filterMap(filterBody?.filters ?? []),
        filterMap(columnFilters),
      ),
    }));
  }, [isFistLoadingBack, filterBody?.filters, columnFilters]);

  // パートナー名検索
  // 前回のsearchPartnerNameとisFistLoadingBackを追跡するためのrefを使用
  const prevSearchPartnerNameRef = useRef(searchPartnerName);
  const prevIsFistLoadingBackSearchRef = useRef(isFistLoadingBack);

  useEffect(() => {
    // isFistLoadingBackがfalseの場合は何もしない
    if (!isFistLoadingBack) {
      prevIsFistLoadingBackSearchRef.current = false;
      return;
    }

    // 以下の場合にのみフィルターを適用する：
    // 1. searchPartnerNameが実際に変更された場合
    // 2. isFistLoadingBackがfalseからtrueに変わった場合（詳細画面から戻った場合）ではない
    if (
      prevIsFistLoadingBackSearchRef.current &&
      prevSearchPartnerNameRef.current !== searchPartnerName
    ) {
      const managedPartnerNameFilter: ColumnFilter = {
        id: "lead_name",
        value: searchPartnerName
          ? ({
              operator: "INCLUDES",
              value: searchPartnerName, // 空文字なら undefined にする
            } as FilterValue)
          : undefined,
      };
      // prev に lead_name がある場合は差し替える
      // ない場合はそのまま追加
      table.setColumnFilters((prev) => {
        const filters = [...prev];
        const index = filters.findIndex((f) => f.id === "lead_name");
        if (index === -1) {
          return [...filters, managedPartnerNameFilter];
        }
        filters[index] = managedPartnerNameFilter;
        return filters;
      });
    }

    // refを更新
    prevSearchPartnerNameRef.current = searchPartnerName;
    prevIsFistLoadingBackSearchRef.current = true;
  }, [isFistLoadingBack, searchPartnerName, table]);

  // ページネーション
  useEffect(() => {
    if (!isFistLoadingBack) {
      return;
    }
    setCondition((prev) => ({
      ...prev,
      page: pagination.pageIndex,
      page_size: pagination.pageSize,
    }));
    table.resetRowSelection();
  }, [isFistLoadingBack, pagination.pageIndex, pagination.pageSize, table]);

  // ソート
  // 前回のsortsとisFistLoadingBackを追跡するためのrefを使用
  const prevSortsRef = useRef(filterBody?.sorts);
  const prevIsFistLoadingBackSortsRef = useRef(isFistLoadingBack);

  useEffect(() => {
    // isFistLoadingBackがfalseの場合は何もしない
    if (!isFistLoadingBack) {
      prevIsFistLoadingBackSortsRef.current = false;
      return;
    }

    // 以下の場合にのみソートを適用する：
    // 1. sortsが実際に変更された場合
    // 2. isFistLoadingBackがfalseからtrueに変わった場合（詳細画面から戻った場合）ではない
    if (
      prevIsFistLoadingBackSortsRef.current &&
      prevSortsRef.current !== filterBody?.sorts
    ) {
      table.setSorting(filterBody?.sorts ?? defaultSorts);
    }

    // refを更新
    prevSortsRef.current = filterBody?.sorts;
    prevIsFistLoadingBackSortsRef.current = true;
  }, [filterBody?.sorts, isFistLoadingBack, table]);

  useEffect(() => {
    if (!isFistLoadingBack) {
      return;
    }
    setCondition((prev) => ({
      ...prev,
      sorts: sortingMap(sorting),
    }));
  }, [isFistLoadingBack, sorting]);

  // カラムの順序の決定
  useEffect(() => {
    if (filterBody?.columns && filterBody.columns.length > 0) {
      const columnIds = filterBody.columns.map((item) => item.columnId);
      columnIds.unshift("shared_link");
      columnIds.unshift("check_box_delete");
      setColumnOrder(columnIds);
    } else {
      setColumnOrder([]);
    }
  }, [filterBody?.columns]);

  return (
    <div css={{ height: "100%", display: "flex", flexDirection: "column" }}>
      <div css={{ flex: 1, minHeight: 0, overflowX: "auto" }}>
        <LeadsTable
          table={table}
          isLoading={isLoading}
          resetScrollKey={JSON.stringify({
            pagination,
            columnFilters,
            sorting,
          })}
        />
      </div>
      <PaginationControl
        table={table}
        rowCount={filteredLead?.total_leads ?? 0}
      />
    </div>
  );
};
