import { notifications } from "@mantine/notifications";
import { getApiErrorMessage } from "../../../utils/values/errorMessages";
import IconNotiFailed from "../../../public/icons/icon-noti-failed.svg";
import React from "react";

export const fetchDataErrorHandler = (e: any) => {
  notifications.show({
    title: "データの取得中にエラーが発生しました。",
    message: getApiErrorMessage(e.response.data.message),
    icon: <IconNotiFailed />,
  });
  throw e;
};

export const updateErrorHandler = (e: any) => {
  notifications.show({
    title: "データの更新中にエラーが発生しました。",
    message: getApiErrorMessage(e.response.data.message),
    icon: <IconNotiFailed />,
  });
  throw e;
};
