import dagre from "@dagrejs/dagre";
import "@xyflow/react/dist/style.css";
import { Node, Edge } from "@xyflow/react";

/**
 * カスタムDagreノードインターフェース
 */
export interface CustomDagreNode extends dagre.Node {
  label: string;
  width: number;
  height: number;
  type: "division" | "division_detail" | "user" | "connector";
  division_name?: string; // divisionノードにのみ存在
  noDivisionDetail?: boolean;
}

/**
 * 組織図のレイアウトオプション
 */
interface OrgChartOptions {
  rankdir?: "TB" | "BT" | "LR" | "RL"; // ランク方向
  nodesep?: number; // ノード間の間隔
  ranksep?: number; // ランク間の間隔
}

/**
 * 役職の順序付けマップ
 */
/** position 順序付け用マップ */
const positionOrderMap: Record<string, number> = {
  REPRESENTATIVE: 1,
  OFFICER: 2,
  DIVISION_MANAGER: 3,
  DEPARTMENT_MANAGER: 4,
  SECTION_MANAGER: 5,
  LEADER: 6,
  STAFF: 7,
  OTHER: 8,
};

/**
 * 役職に基づいて順序を取得
 * @param pos 役職名
 * @returns 順序番号
 */
function getPositionOrder(pos: string | null | undefined): number {
  return pos ? (positionOrderMap[pos] ?? 9999) : 9999;
}

/**
 * Dagreグラフの初期化
 * @param options レイアウトオプション
 * @returns 初期化されたDagreグラフ
 */
function initializeGraph(options: OrgChartOptions = {}): dagre.graphlib.Graph {
  const graph = new dagre.graphlib.Graph();
  graph.setGraph({
    rankdir: options.rankdir ?? "TB",
    nodesep: options.nodesep ?? 80,
    ranksep: options.ranksep ?? 80,
    marginx: 20,
    marginy: 20,
  });
  graph.setDefaultEdgeLabel(() => ({}));
  return graph;
}

/**
 * ユーザーを部門ごとにグループ化
 * @param users ユーザーの配列
 * @returns 部門ごとのユーザーのマップ
 */
function groupUsersByDivision(
  users: PartnerUser[]
): Record<string, PartnerUser[]> {
  const divisionMap: Record<string, PartnerUser[]> = {};
  users.forEach((user) => {
    const division = user.division?.trim() || "部署なし";
    if (!divisionMap[division]) {
      divisionMap[division] = [];
    }
    divisionMap[division].push(user);
  });
  return divisionMap;
}

/**
 * 部門ノードをグラフに追加
 * @param graph Dagreグラフ
 * @param divisionName 部門名
 */
function addDivisionNode(
  graph: dagre.graphlib.Graph,
  divisionName: string
): void {
  const divNodeId = `division-${divisionName}`;
  graph.setNode(divNodeId, {
    label: divisionName,
    width: 300,
    height: 40,
    type: "division",
  } as CustomDagreNode);
}

/**
 * ユーザーをdivision_detailごとにグループ化
 * @param users ユーザーの配列
 * @returns division_detailごとのユーザーのマップとdivision_detailなしのユーザー配列
 */
function groupUsersByDivisionDetail(users: PartnerUser[]): {
  detailMap: Record<string, PartnerUser[]>;
  usersWithoutDetail: PartnerUser[];
} {
  const detailMap: Record<string, PartnerUser[]> = {};
  const usersWithoutDetail: PartnerUser[] = [];

  users.forEach((user) => {
    const detail = user.division_detail?.trim();
    if (detail) {
      if (!detailMap[detail]) {
        detailMap[detail] = [];
      }
      detailMap[detail].push(user);
    } else {
      usersWithoutDetail.push(user);
    }
  });

  return { detailMap, usersWithoutDetail };
}

/**
 * division_detailノードをグラフに追加
 * @param graph Dagreグラフ
 * @param divisionName 部門名
 * @param detailName division_detail名
 * @returns 追加されたdivision_detailノードのID
 */
function addDivisionDetailNode(
  graph: dagre.graphlib.Graph,
  divisionName: string,
  detailName: string
): string {
  const detailNodeId = `detail-${divisionName}-${detailName}`;
  graph.setNode(detailNodeId, {
    label: detailName,
    width: 300,
    height: 35,
    type: "division_detail",
    noDivisionDetail: false,
  } as CustomDagreNode);
  // division -> division_detail のエッジを追加
  const divNodeId = `division-${divisionName}`;
  graph.setEdge(divNodeId, detailNodeId);
  return detailNodeId;
}

/**
 * ユーザーを肩書き順、name_kana順、name順でソートする関数
 * @param a 比較対象のユーザーA
 * @param b 比較対象のユーザーB
 * @returns ソート結果（負の数、0、正の数）
 */
function sortPartnerUsers(a: PartnerUser, b: PartnerUser): number {
  // 肩書き順でソート
  const posDiff = getPositionOrder(a.position) - getPositionOrder(b.position);
  if (posDiff !== 0) return posDiff;

  // name_kanaの存在確認
  const hasNameKanaA = !!a.name_kana?.trim();
  const hasNameKanaB = !!b.name_kana?.trim();

  if (hasNameKanaA && hasNameKanaB) {
    // 両方ともname_kanaが存在する場合はname_kanaでソート
    return a.name_kana!.trim().localeCompare(b.name_kana!.trim(), "ja");
  } else if (hasNameKanaA) {
    // Aのみname_kanaが存在する場合、Aを先に
    return -1;
  } else if (hasNameKanaB) {
    // Bのみname_kanaが存在する場合、Bを先に
    return 1;
  } else {
    // 両方ともname_kanaが存在しない場合はnameでソート
    return a.name.localeCompare(b.name, "ja");
  }
}

/**
 * ユーザーノードとエッジをグラフに追加
 * @param graph Dagreグラフ
 * @param parentNodeId 親ノードID（division_detailまたはconnector）
 * @param users ユーザーの配列
 */
function addUserNodesAndEdges(
  graph: dagre.graphlib.Graph,
  parentNodeId: string,
  users: PartnerUser[]
): void {
  // ソート関数を使用してユーザーをソート
  const sortedUsers = [...users].sort(sortPartnerUsers);

  let prevUserNodeId: string | null = null;
  sortedUsers.forEach((user, idx) => {
    const userNodeId =
      user.partner_user_id != null
        ? `user-pu-${user.partner_user_id}`
        : `user-id-${user.id}`;
    graph.setNode(userNodeId, {
      label: `${user.name}\n(${user.position || "N/A"})`,
      width: 300,
      height: 120,
      type: "user",
    } as CustomDagreNode);

    if (idx === 0) {
      // parentNode -> first user
      graph.setEdge(parentNodeId, userNodeId);
    } else if (prevUserNodeId) {
      // previous user -> current user
      graph.setEdge(prevUserNodeId, userNodeId);
    }

    prevUserNodeId = userNodeId;
  });
}

/**
 * division_detailがないユーザー用の仮想ノードをグラフに追加
 * @param graph Dagreグラフ
 * @param divisionName 部門名
 * @param users ユーザーの配列
 */
function addConnectorNodesAndUsers(
  graph: dagre.graphlib.Graph,
  divisionName: string,
  users: PartnerUser[]
): void {
  const connectorNodeId = `connector-${divisionName}`;
  graph.setNode(connectorNodeId, {
    label: "Connector",
    width: 300,
    height: 10,
    type: "connector",
  } as CustomDagreNode);
  // division -> connector のエッジを追加
  const divNodeId = `division-${divisionName}`;
  graph.setEdge(divNodeId, connectorNodeId);

  // ユーザーノードとエッジを追加
  addUserNodesAndEdges(graph, connectorNodeId, users);
}

/**
 * DagreグラフからReactFlow用のノードとエッジを生成
 * @param graph Dagreグラフ
 * @param users ユーザーの配列
 * @returns ReactFlow用のノードとエッジのオブジェクト
 */
function processGraphToReactFlow(
  graph: dagre.graphlib.Graph,
  users: PartnerUser[]
): { nodes: Node[]; edges: Edge[] } {
  const rfNodes: Node[] = [];
  const rfEdges: Edge[] = [];

  graph.nodes().forEach((nodeId) => {
    const node = graph.node(nodeId) as CustomDagreNode;

    // ConnectorNode は非表示に設定
    if (node.type === "connector") {
      rfNodes.push({
        id: nodeId,
        position: { x: node.x - node.width / 2, y: node.y - node.height / 2 },
        data: {
          label: node.label,
        },
        type: "connector",
      });
      return;
    }

    // 他のノード
    let nodeData: any = {};
    if (node.type === "user") {
      // ノードIDに基づいてユーザーを検索
      let user: PartnerUser | undefined;
      if (nodeId.startsWith("user-pu-")) {
        const userId = parseInt(nodeId.replace("user-pu-", ""), 10);
        user = users.find((u) => u.partner_user_id === userId);
      } else if (nodeId.startsWith("user-id-")) {
        const userId = parseInt(nodeId.replace("user-id-", ""), 10);
        user = users.find((u) => u.id === userId);
      }
      nodeData = { user: user || null };
    } else if (node.type === "division_detail") {
      nodeData = {
        label: node.label,
        noDivisionDetail: node.noDivisionDetail ?? false,
      };
    } else if (node.type === "division") {
      nodeData = {
        label: node.label,
        division_name: node.division_name ?? "",
      };
    }

    rfNodes.push({
      id: nodeId,
      position: { x: node.x - node.width / 2, y: node.y - node.height / 2 },
      data: nodeData,
      type: node.type,
    });
  });

  graph.edges().forEach((edgeObj, index) => {
    const edgeId = `edge-${index}`;
    rfEdges.push({
      id: edgeId,
      source: edgeObj.v,
      target: edgeObj.w,
      type: "step",
    });
  });

  return { nodes: rfNodes, edges: rfEdges };
}

/**
 * 組織図ノード・エッジ生成 + Dagreレイアウト
 * @param users PartnerUser配列
 * @param options Dagreの設定
 * @returns ReactFlow用のノードとエッジ
 */
export function createChartData(
  users: PartnerUser[],
  options: OrgChartOptions = {}
): { nodes: Node[]; edges: Edge[] } {
  // グラフの初期化
  const graph = initializeGraph(options);

  // ユーザーを部門ごとにグループ化
  const divisionMap = groupUsersByDivision(users);
  const sortedDivisions = Object.keys(divisionMap).sort();

  sortedDivisions.forEach((divisionName) => {
    // 部門ノードを追加
    addDivisionNode(graph, divisionName);

    // 部門内のユーザーを取得
    const usersInDivision = divisionMap[divisionName];

    // division_detailごとにユーザーをグループ化
    const { detailMap, usersWithoutDetail } =
      groupUsersByDivisionDetail(usersInDivision);

    // division_detailごとにノードとエッジを追加
    const sortedDetails = Object.keys(detailMap).sort();
    sortedDetails.forEach((detailName) => {
      const detailNodeId = addDivisionDetailNode(
        graph,
        divisionName,
        detailName
      );
      const usersInDetail = detailMap[detailName];
      addUserNodesAndEdges(graph, detailNodeId, usersInDetail);
    });

    // division_detailがないユーザーの場合、connectorノードを追加してユーザーノードを接続
    if (usersWithoutDetail.length > 0) {
      addConnectorNodesAndUsers(graph, divisionName, usersWithoutDetail);
    }
  });

  // Dagreレイアウトを適用
  dagre.layout(graph);

  // DagreグラフからReactFlow用のノードとエッジを生成
  const reactFlowData = processGraphToReactFlow(graph, users);

  return reactFlowData;
}
