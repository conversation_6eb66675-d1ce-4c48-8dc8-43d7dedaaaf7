import React from "react";
import { Handle, Position, NodeProps } from "@xyflow/react";
import { Node } from "@xyflow/react";

interface ConnectorNodeData extends Node<Record<string, unknown>, string> {
  data: {
    label: string;
  };
}

const ConnectorNode: React.FC<NodeProps<ConnectorNodeData>> = ({ data }) => {
  return (
    <div
      style={{
        width: "340px",
        height: "1px",
        backgroundColor: "transparent",
      }}
    >
      <Handle
        type="target"
        position={Position.Top}
        style={{
          background: "#B1B1B6",
          width: 1,
          height: 1,
          border: "none",
          visibility: "hidden",
        }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        style={{
          background: "#B1B1B6",
          width: 0,
          height: 0,
          border: "none",
          visibility: "hidden",
        }}
      />
    </div>
  );
};
export default ConnectorNode;
