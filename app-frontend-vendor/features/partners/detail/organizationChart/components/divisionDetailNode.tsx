// src/components/components/DivisionDetailNode.tsx
import React from "react";
import { Handle, Position, NodeProps } from "@xyflow/react";
import { Node } from "@xyflow/react";

interface DivisionDetailNodeData extends Node<Record<string, unknown>, string> {
  data: {
    label: string;
    noDivisionDetail?: boolean;
  };
}

const DivisionDetailNode: React.FC<NodeProps<DivisionDetailNodeData>> = ({
  data,
}) => {
  if (data.noDivisionDetail) {
    // 仮想ノードを非表示にし、スペースを確保
    return (
      <div
        style={{
          padding: "8px 16px",
          border: "2px solid #4682b4",
          borderRadius: "8px",
          textAlign: "center",
          width: "340px",
          // visibility: 'hidden', // 非表示にする場合はこれを利用
        }}
      >
        {data.label}
        <Handle
          type="source"
          position={Position.Bottom}
          style={{
            background: "#4682B4",
            visibility: "hidden",
          }}
        />
        <Handle
          type="target"
          position={Position.Top}
          style={{
            background: "#4682B4",
            visibility: "hidden",
          }}
        />
      </div>
    );
  }

  return (
    <div
      style={{
        padding: "8px 16px",
        border: "2px solid #4682b4",
        borderRadius: "8px",
        textAlign: "center",
        width: "340px",
        boxShadow: "2px 2px 5px rgba(0, 0, 0, 0.3)",
      }}
    >
      {data.label}
      <Handle
        type="source"
        position={Position.Bottom}
        style={{
          background: "#4682B4",
          visibility: "hidden",
        }}
      />
      <Handle
        type="target"
        position={Position.Top}
        style={{
          background: "#4682B4",
          visibility: "hidden",
        }}
      />
    </div>
  );
};

export default DivisionDetailNode;
