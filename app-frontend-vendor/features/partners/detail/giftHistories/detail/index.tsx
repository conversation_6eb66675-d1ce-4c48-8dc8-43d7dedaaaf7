import styled from "@emotion/styled";
import Head from "next/head";
import { propcolors } from "styles/colors";
import { GeneralInfo } from "./components/generalInfo";
import { usePartnerProps } from "utils/recoil/partner/partnerState";
import { GiftHistoryDetailLayout } from "./layout";
import { useRouter } from "next/router";
import useSWR from "swr";
import { fetcher } from "utils/axios/fetcher";
import { Skeleton } from "@mantine/core";

type PresentationProps = {
  className?: string;
  giftHistoryData: GiftHistory;
};

const Presentation: React.FC<PresentationProps> = ({ giftHistoryData }) => {
  return (
    <>
      <Head>
        <title>{giftHistoryData?.subject} | ギフト履歴詳細 - PartnerProp</title>
      </Head>
      <GeneralInfo giftHistoryData={giftHistoryData} />
    </>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-columns: 400px 1fr;
  .tab {
    &-root {
      height: 100%;
      display: grid;
      grid-template-rows: auto 1fr;
    }
    &-content {
      height: 100%;
    }
  }
  .lead {
    &-main {
      &-content {
        padding: 16px 16px 16px 0;
        height: 100%;
      }
    }
    &-sidebar {
      width: 320px;
      padding: 16px;
      display: grid;
      grid-template-rows: auto 1fr;
      grid-gap: 16px;
      &-content {
        width: 100%;
        font-size: 14px;
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 5px;
        background-color: ${propcolors.white};
        padding: 16px;
        &-products {
          &-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
          }
        }
      }
      &-id {
      }
      &-client {
        &_name {
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }
`;

const ModalTitle = styled.div`
  width: 100%;
  text-align: center;
  border-bottom: 1px solid ${propcolors.gray[200]};
  padding-bottom: 1rem;
  top: 1.3rem;
  position: absolute;
  margin: 0 -1rem;
  font-size: 16px;
  font-weight: 600;
  color: ${propcolors.blackLight};
`;

const ModalContent = styled.div`
  padding: 10px;
  .title-confirm {
    font-size: 18px;
    font-weight: 600;
    color: ${propcolors.blackLight};
    margin: 25px 0 20px 0;
  }
  .description {
    font-size: 12px;
    font-weight: 300;
    color: var(--Semantic-TEXT_BLACK, #23221e);
  }
`;

export const GiftHistoryDetail: React.FC<{
  giftHistoryId: number;
  managedPartnerId: number;
}> = ({ giftHistoryId, managedPartnerId }) => {
  const partnerData = usePartnerProps();
  const { push } = useRouter();

  const {
    data: giftHistoryList,
    error,
    isLoading,
  } = useSWR<GiftHistory[]>(
    managedPartnerId ? `/api/v1/partners/${managedPartnerId}/gifts` : null,
    fetcher,
    {
      revalidateOnFocus: false,
    }
  );

  if (error) {
    console.error(error);
  }

  if (isLoading || !giftHistoryList) {
    return <Skeleton />;
  }

  const giftHistory =
    giftHistoryList.find((gift) => gift.id === giftHistoryId) || null;

  return giftHistory ? (
    <GiftHistoryDetailLayout
      managedPartnerId={managedPartnerId}
      partnerData={partnerData}
      giftHistoryData={giftHistory}
    >
      <Styled giftHistoryData={giftHistory} />
    </GiftHistoryDetailLayout>
  ) : (
    <Skeleton />
  );
};
