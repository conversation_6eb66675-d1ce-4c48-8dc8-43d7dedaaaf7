import styled from "@emotion/styled";
import { Button } from "@mantine/core";
import type { ColDef } from "ag-grid-community";
import { AGGrid } from "components/Grid";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";
import { useGetDataMasters } from "utils/recoil/dataMasters";
import CellPartnerNameRender from "../../../components/cell/CellPartnerNameRender";

type ChildPartnersProps = {
  childPartners: ChildParterInfo[];
};

type PresentationProps = {
  className?: string;
  colDef: ColDef[];
} & ChildPartnersProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  childPartners,
  colDef,
}) => {
  return (
    <div className={className}>
      {childPartners && (
        <AGGrid
          className="grid-height-full"
          columnDefs={colDef}
          rowData={childPartners}
        />
      )}
    </div>
  );
};

const Styled = styled(Presentation)`
  height: calc(100% - 195px);
  display: flex;
  margin-top: -17px;
  .grid-height-full {
    height: 100%;
    width: 100%;
  }
  .cell-partner-name-render {
    display: flex;
    align-items: center;
    .cell-partner-name-render-image {
      display: flex;
      align-items: center;
    }
    .cell-partner-name-render-text {
      overflow: hidden;
      text-overflow: inherit;
      margin-left: 16px;
    }
  }
  .col-action {
    width: 100%;
    text-align: center;
  }
  .btn-detail {
    color: ${propcolors.blackLight};
    border: 1px solid ${propcolors.gray[400]};
    background: white;
    font-size: 14px;
    font-weight: 400;
    width: 52px;
    height: 32px;
    border-radius: 6px;
    padding: 0;
  }
  .ag-cell-value, .ag-group-value {
    overflow: visible;
    text-overflow: inherit;
  }
`;

export const RelatedPartnersTable: React.FC<ChildPartnersProps> = ({
  childPartners,
}) => {
  const dataMasters = useGetDataMasters();
  const prefectureList: Prefecture[] = dataMasters?.prefectures || [];
  const colDefs: ColDef[] = [
    {
      field: "managed_partner_hex_id",
      headerName: "レコードID",
      flex: 1,
      filter: "agTextColumnFilter",
    },
    {
      field: "managed_partner_name",
      headerName: "パートナー企業名",
      flex: 1,
      filter: "agTextColumnFilter",
      valueGetter: (parmas) => {
        return {
          partnerName: parmas.data?.managed_partner_name,
          thumbnail: null,
        };
      },
      cellRenderer: CellPartnerNameRender,
      cellClass: "cell-partner-name",
    },
    {
      field: "prefecture_id",
      headerName: "都道府県",
      flex: 1,
      valueGetter: (params) => {
        const prefectureId = params.data?.prefecture_id;
        const prefectureName = prefectureList?.find(
          (prefecture) => prefecture.value === prefectureId,
        )?.label;
        return prefectureName ? prefectureName : "";
      },
    },
    {
      field: "vendor_user_name",
      headerName: "ベンダー担当者",
      flex: 1,
      filter: "agTextColumnFilter",
    },
    {
      headerName: "",
      field: "actions",
      pinned: "right",
      cellStyle: { borderLeft: "none" },
      cellRendererFramework: (params: any) => {
        return (
          <div className="col-action">
            <Button
              onClick={() => transition(params.data.managed_partner_id)}
              className="btn-detail"
            >
              詳細
            </Button>
          </div>
        );
      },
      width: 100,
      suppressSizeToFit: true,
    },
  ];
  // 別パートナーへ遷移
  const { push } = useRouter();
  const transition = (id: string) => push(`/partners/${id}`);

  const filterdChildPartners = childPartners;
  return <Styled colDef={colDefs} childPartners={childPartners} />;
};
