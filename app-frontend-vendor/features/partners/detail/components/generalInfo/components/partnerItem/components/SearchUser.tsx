import React, { useState, useEffect } from "react";
import styled from "@emotion/styled";


import { usePartnerProps, useSetPartnerProps } from "utils/recoil/partner/partnerState";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";
import { notifications } from "@mantine/notifications";
import EditIcon from "public/icons/edit.svg";
import { getApiErrorMessage } from "utils/values/errorMessages";
import { Select } from "@mantine/core";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconSelect from "public/icons/icon-arrow-down.svg";

type SearchUserProps = {
  title: string | number;
  current: number;
  userList: VendorUser[] | null;
  onBlur: () => void;
  isEditing: boolean;
  toggleEditing: () => void;
};

type PresentationalProps = {
  className?: string;
  modValue: number;
  setModValue: React.Dispatch<React.SetStateAction<number>>;
  title: string | number;
  current: number;
  userList: VendorUser[] | null;
  onBlur: () => void;
  isEditing: boolean;
  toggleEditing: () => void;
  filter: (keyword: string) => void;
};

const Presentational: React.FC<PresentationalProps> = ({
  className,
  title,
  current,
  onBlur,
  userList,
  modValue,
  setModValue,
  isEditing,
  toggleEditing,
  filter,
}) => {
  const options = [
    { value: '-1', label: '担当者なし' },
    ...(userList ? userList.map((user, index) => ({
      value: String(index),
      label: `${user.name} ${user.email}`
    })) : [])
  ];
    
  return (
  <div className={className}>
    {!isEditing && (
    <EditIcon onClick={toggleEditing} className="button"/>
    )}
    {isEditing && (
      <>
        <Select
            className="width-max"
            placeholder="氏名もしくはメールアドレスで検索"
            onBlur={onBlur}
            onChange={(value) => setModValue(Number(value))}
            name="select"
            data={options}
            value={String(modValue)}
            rightSection={<IconSelect />}
            autoFocus
            searchable
          />
      </>
    )}
  </div>
)};

const Styled = styled(Presentational)`
  .Button {
    border: 0;
  }
  .button {
    cursor: pointer;
  }
  .editor {
    &-content {
      width: 500px;
    }
    &-input {
      width: 300px;
    }
  }
  .mantine-Select-item[data-selected] {
      background-color: ${propcolors.gray[150]};
      color: ${propcolors.blackLight};
  }
  .mantine-Input-input {
    border: 1px solid ${propcolors.gray[200]};
    background-color: white;
    border-radius: 8px;
  }
  .mantine-Input-input:focus {
    background-color: ${propcolors.gray[150]};
  }
`;

export const SearchUser: React.FC<SearchUserProps> = ({ 
    title, 
    current,
    userList, 
    onBlur,
    isEditing,
    toggleEditing,
  }) => {
  const [modValue, setModValue] = useState<number>(0);
  const [isModalOpen, toggleModal] = useState<boolean>(false);
  const [keywordValue, setKeyWordValue] = useState<string | number>();
  const [filteredUser, setFilteredUser] = useState<VendorUser[] | null>(userList);
  const partnerData = usePartnerProps();
  const setPartnerProps = useSetPartnerProps();
  const router = useRouter();
  const { id } = router.query;
  const onSubmit = async () => {
    if (partnerData) {
      // 担当者なし（modValue===-1以外の場合のみ、vendorUserIdを設定）
      let vendorUserId = null;

      if (modValue !== -1 && filteredUser && filteredUser.length > 0) {
        const selectedUser = filteredUser[modValue];
        if (selectedUser) {
          vendorUserId = selectedUser.id;
        }
      }

      const body = {
        ...partnerData,
        vendor_user_id: vendorUserId,
      };
  
      ax.put(`/api/v1/managed_partner/${id}`, body)
        .then((res) => {
          fetchPartnerProps();
          toggleEditing();
        })
        .catch((err) => {
          notifications.show({
            icon: <IconNotiFailed />,
            title: "ベンダー担当者の更新ができませんでした。",
            message: getApiErrorMessage(err.response.data.message),
            autoClose: 5000,
          });
        });
    }
  };
  const fetchPartnerProps = () => {
    if (partnerData) {
      ax.get(`/api/v1/managed_partner/${partnerData.managed_partner_id}`).then((response) => {
        setPartnerProps(response.data);
      });
    }
  };
  const filter = (keyword: string) => {
    if (userList) {
      const currentFiltering = userList.filter((user) => {
        if (user.name.indexOf(keyword) !== -1 || user.email.indexOf(keyword) !== -1) {
          return true;
        } else {
          return false;
        }
      });
      setFilteredUser(currentFiltering);
    }
  };

  useEffect(() => {
    if (userList && current) {
      const index = userList.findIndex(user => user.id === current);
      setModValue(index !== -1 ? index : -1);
    }
  }, [current, userList, isEditing]);

  useEffect(() => {
    // コンポーネントがマウントされた時に、userListをfilteredUserに設定
    setFilteredUser(userList);
  }, [userList]);

  // モーダル開閉時に値を初期化
  useEffect(() => {
    setModValue(-1)
  }, [isModalOpen])

  return (
    <Styled
      title={title}
      current={current}
      userList={filteredUser}
      modValue={modValue}
      setModValue={setModValue}
      onBlur={() => {
        onSubmit();
      }}
      toggleEditing={toggleEditing}
      isEditing={isEditing}
      filter={filter}
    />
  );
};
