import React, { useState, useEffect } from "react";
import styled from "@emotion/styled";


import { usePartnerProps, useSetPartnerProps } from "utils/recoil/partner/partnerState";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";
import { notifications } from "@mantine/notifications";
import EditIcon from "public/icons/edit.svg";
import { getApiErrorMessage } from "utils/values/errorMessages";
import { Select } from "@mantine/core";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconSelect from "public/icons/icon-arrow-down.svg";

type SearchPartnerCandidatesProps = {
  title: string | number;
  current: number;
  partnerList: ParentPartnerCandidate[] | null;
  onBlur: () => void;
  isEditing: boolean;
  toggleEditing: () => void;
};

type PresentationalProps = {
  className?: string;
  modValue: number;
  setModValue: React.Dispatch<React.SetStateAction<number>>;
  title: string | number;
  current: number;
  partnerList: ParentPartnerCandidate[] | null;
  onBlur: () => void;
  isEditing: boolean;
  toggleEditing: () => void;
  filter: (keyword: string) => void;
};

const Presentational: React.FC<PresentationalProps> = ({
  className,
  title,
  current,
  onBlur,
  partnerList,
  modValue,
  setModValue,
  isEditing,
  toggleEditing,
  filter,
}) => {
  const options = [
    { value: '-1', label: '関連パートナーなし' },
    ...(partnerList ? partnerList.map((user, index) => (
      {
      value: String(index),
      label: `${user.managed_partner_name}`
    })) : [])
  ];

  return (
  <div className={className}>
    {!isEditing && (
    <EditIcon onClick={toggleEditing} className="button"/>
    )}
    {isEditing && (
      <>
      <Select
            className="width-max"
            placeholder="会社名で検索"
            onBlur={onBlur}
            onChange={(value) => setModValue(Number(value))}
            name="select"
            data={options}
            value={String(modValue)}
            rightSection={<IconSelect />}
            autoFocus
            searchable
          />
        </>
    )}
  </div>
)};

const Styled = styled(Presentational)`
  .Button {
    border: 0;
  }
  .button {
    cursor: pointer;
  }
  .editor {
    &-content {
      width: 500px;
    }
    &-input {
      width: 300px;
    }
  }
  .mantine-Select-item[data-selected] {
      background-color: ${propcolors.gray[150]};
      color: ${propcolors.blackLight};
  }
  .mantine-Input-input {
    border: 1px solid ${propcolors.gray[200]};
    background-color: white;
    border-radius: 8px;
  }
  .mantine-Input-input:focus {
    background-color: ${propcolors.gray[150]};
  }
`;

export const SearchPartnerCandidates: React.FC<SearchPartnerCandidatesProps> = ({ 
  title, 
  current, 
  partnerList,
  onBlur,
  isEditing,
  toggleEditing,
 }) => {
  const [isModalOpen, toggleModal] = useState<boolean>(false);
  const [modValue, setModValue] = useState<number>(-1);
  const [keywordValue, setKeyWordValue] = useState<string | number>();
  const [filteredUser, setFilteredUser] = useState<ParentPartnerCandidate[] | null>(partnerList);
  const partnerData = usePartnerProps();
  const setPartnerProps = useSetPartnerProps();
  const router = useRouter();
  const { id } = router.query;

  const onSubmit = async () => {
    if (partnerData) {
      let parentId;
      let parentName;

      if (modValue !== -1 && filteredUser && filteredUser.length > 0) {
        const selectedUser = filteredUser[modValue];
        if (selectedUser) {
          parentId = selectedUser.managed_partner_id;
          parentName = selectedUser.managed_partner_name;
        }
      }

      const body = {
        ...partnerData,
        is_parent: 0,
        parent_partner_id: parentId,
        parent_partner_name: parentName,
      };
      ax.put(`/api/v1/managed_partner/${id}`, body)
        .then((res) => {
          fetchPartnerProps();
          toggleEditing();
        })
        .catch((err) => {
          notifications.show({
            icon: <IconNotiFailed />,
            title: "関連パートナーの更新ができませんでした。",
            message: getApiErrorMessage(err.response.data.message),
            autoClose: 5000,
          });
        });
    }
  };
  const fetchPartnerProps = () => {
    if (partnerData) {
      ax.get(`/api/v1/managed_partner/${partnerData.managed_partner_id}`).then((response) => {
        setPartnerProps(response.data);
      });
    }
  };
  const filter = (keyword: string) => {
    if (partnerList) {
      const currentFiltering = partnerList.filter((user) => {
        if (user.managed_partner_name.indexOf(keyword) !== -1) {
          return true;
        } else {
          return false;
        }
      });
      setFilteredUser(currentFiltering);
    }
  };

  useEffect(() => {
    if (partnerList && current) {
      const index = partnerList.findIndex(partner => partner.managed_partner_id === current);
      setModValue(index !== -1 ? index : -1);
    }
  }, [current, partnerList, isEditing]);


  useEffect(() => {
    if (!isEditing) {
      const index = filteredUser?.findIndex(user => user.managed_partner_id === current) ?? -1;
      setModValue(index);
    }
  }, [isEditing]);

  useEffect(() => {
    // コンポーネントがマウントされた時に、userListをfilteredUserに設定
    setFilteredUser(partnerList);
  }, [partnerList]);
  
  // モーダル開閉時に値を初期化
  useEffect(() => {
    setModValue(-1)
  }, [isModalOpen])

  return (
    <Styled
      title={title}
      current={current}
      partnerList={filteredUser}
      modValue={modValue}
      setModValue={setModValue}
      onBlur={() => {
        onSubmit();
      }}
      toggleEditing={toggleEditing}
      isEditing={isEditing}
      filter={filter}
    />
  );
};
