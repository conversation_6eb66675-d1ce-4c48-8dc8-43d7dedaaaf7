import styled from "@emotion/styled";
import React, { useState, useEffect } from "react";
import { propcolors } from "styles/colors";
import { EditButton } from "./components/EditButton";

type PresentationProps = {
  className?: string;
} & LeadItemProps;

type LeadItemProps = {
  label: string;
  current: string | number | null;
  value: string | string[] | number[] | number | null;
  placeholder?: string[];
  type: "text" | "select" | "number" | "textArea" | "label";
  DBlabel: string | number;
  custom?: boolean;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  label,
  current,
  placeholder,
  value,
  type,
  DBlabel,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [displayValue, setDisplayValue] = useState(current);

  const formatNumberWithComma = (num: number) => {
    // 整数部分と小数部分を分離
    const parts = num.toString().split(".");
    // 整数部分に3桁ごとにカンマを挿入
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    // 整数部分と小数部分を結合して返却
    return parts.join(".");
  };

  useEffect(() => {
    if (typeof current === "number" && type === "number") {
      setDisplayValue(formatNumberWithComma(current));
    } else if (placeholder && typeof current === "number") {
      setDisplayValue(placeholder[current]);
    } else {
      setDisplayValue(current ? current : "未設定");
    }
  }, [current, placeholder, type]);

  const toggleEditing = () => {
    setIsEditing(!isEditing);
  };

  return (
    <div className={className}>
      <span className="info-label">{label}</span>
      <div className="info-value-wrapper">
        {!isEditing && (
          <span className={`info-value ${type === "textArea" && "info-text"}`}>
            {displayValue}
          </span>
        )}
        {type !== "label" && (
          <EditButton
            type={type}
            title={label}
            current={current}
            placeholder={placeholder}
            value={value}
            DBlabel={DBlabel}
            onBlur={toggleEditing}
            isEditing={isEditing}
            toggleEditing={toggleEditing}
          />
        )}
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto 1fr;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid ${propcolors.gray[200]};
  font-size: 14px;
  .info {
    &-label {
      width: 150px;
      margin-bottom: 1rem;
      color: ${propcolors.blackLightLabel};
      font-size: 12px;
      font-weight: 400;
    }
    &-value {
      width: 100%;
      word-break: break-all;
      color: ${propcolors.blackLight};
      font-size: 14px;
      font-weight: 400;
      &-wrapper {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
    &-text {
      width: 100%;
      max-height: 300px;
      overflow-y: auto;
      white-space: pre-line;
    }
  }
  @media screen and (max-width: 1220px) {
    display: block;
  }
`;

export const PartnerItem: React.FC<LeadItemProps> = ({
  label,
  value,
  type,
  current,
  placeholder,
  DBlabel,
  custom,
}) => {
  return (
    <Styled
      DBlabel={DBlabel}
      type={type}
      current={current}
      placeholder={placeholder}
      label={label}
      value={value}
    />
  );
};
