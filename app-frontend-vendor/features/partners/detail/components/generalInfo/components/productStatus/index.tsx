import styled from "@emotion/styled";
import React, { memo, useEffect, useState } from "react";
import { propcolors } from "styles/colors";
import { useGetDataMasters } from "utils/recoil/dataMasters";
import { ax } from "utils/axios";
import {
  useLeadProducts,
  useSetLeadProducts,
} from "utils/recoil/lead/leadProductState";
import { Select, SelectItem } from "@mantine/core";
import { useRouter } from "next/router";

type PresentationProps = {
  className?: string;
  handleValueChange: (next: string) => void;
  handleAvailability: (flag: string) => void;
  currentStatus?: string;
  status: SelectItem[];
} & ProductStatusProps;

type ProductStatusProps = {
  product: LeadProduct;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  product,
  handleValueChange,
  handleAvailability,
  status,
}) => {
  return (
    <div className={className}>
      <span className="product-name">{product.product_name}</span>
      <div className="product-modifier">
        <Select
          label="商談状況"
          data={status}
          defaultValue={product.negotiation_status}
          value={product.negotiation_status}
          onChange={handleValueChange}
        />
        <Select
          label="販売承認"
          data={[
            {
              label: "未対応",
              value: "PENDING",
            },
            {
              label: "承認",
              value: "APPROVE",
            },
            {
              label: "否認",
              value: "DENY",
            },
          ]}
          onChange={handleAvailability}
          value={product.approval_status}
          defaultValue={product.approval_status}
        />
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  display: flex;
  flex-flow: column;
  justify-content: space-between;
  border: 1px solid ${propcolors.gray[200]};
  border-radius: 5px;
  padding: 8px;
  .product {
    &status-selector {
      width: 100%;
      margin-top: 16px;
    }
    &-name {
      width: 100%;
      font-weight: bold;
    }
    &-modifier {
      display: flex;
      gap: 0.5rem;
      margin-top: 0.5rem;
    }
  }
`;

const ProductStatusComponent: React.FC<ProductStatusProps> = ({ product }) => {
  const masters = useGetDataMasters();
  const leadProducts = useLeadProducts();
  const setLeadProducts = useSetLeadProducts();
  const { query } = useRouter();
  const [statusSelect, setStatusSelect] = useState<
    { value: string; label: string }[]
  >([]);
  const handleValueChange = (next: string) => {
    if (!masters) return;
    const nextStatus = Object.keys(masters.negotiation).find(
      (value) => value === next
    );
    if (!nextStatus) return;
    const check = confirm(
      `${product.product_name}のステータスを\n「${masters.negotiation[nextStatus].name}」に変更します。本当によろしいですか？`
    );
    if (check) {
      const reqBody = {
        negotiation_status: next,
        approval_status: product.approval_status,
      };
      ax.post(`/api/v1/leads/1/products/${product.product_id}`, reqBody)
        .then((res) => {
          alert("ステータス変更に成功しました。");
          if (leadProducts) {
            const newLeadProducts = [
              ...leadProducts.filter(
                (item) => item.product_name !== product.product_name
              ),
              { ...product, negotiation_status: next },
            ];
            setLeadProducts(
              newLeadProducts.sort((a, b) => a.product_id - b.product_id)
            );
          }
        })
        .then((err) => {});
    }
  };

  const handleAvailability = (flag: string) => {
    const translator: { [key: string]: string } = {
      PENDING: "未対応",
      APPROVE: "承認",
      DENY: "否認",
    };
    if (!masters) return;
    const check = confirm(`本当にこの商品を${translator[flag]}にしますか？`);
    if (check) {
      const reqBody = {
        negotiation_status: product.negotiation_status,
        approval_status: flag,
      };
      ax.post(
        `/api/v1/partner/${query.managed_partner_id}/products/${product.product_id}`,
        reqBody
      )
        .then((res) => {
          alert("ステータス変更に成功しました。");
          if (leadProducts) {
            const newLeadProducts = [
              ...leadProducts.filter(
                (item) => item.product_name !== product.product_name
              ),
              { ...product, approval_status: flag },
            ];
            setLeadProducts(
              newLeadProducts.sort((a, b) => a.product_id - b.product_id)
            );
          }
        })
        .then((err) => {});
    }
  };

  useEffect(() => {
    if (masters) {
      const data = Object.keys(masters.negotiation).map((value) => {
        return {
          value: value,
          label: masters.negotiation[value].name,
        };
      });
      setStatusSelect(data);
    }
  }, [masters]);
  return (
    masters && (
      <Styled
        product={product}
        handleValueChange={handleValueChange}
        handleAvailability={handleAvailability}
        status={statusSelect}
      />
    )
  );
};

export const ProductStatus = memo(ProductStatusComponent);
