import React, { useRef, useState, useEffect } from "react";
import { Cross2Icon } from "@radix-ui/react-icons";
import styled from "@emotion/styled";

import {
  usePartnerProps,
  useSetPartnerProps,
} from "utils/recoil/partner/partnerState";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";

import { DateInput, DateTimePicker } from "@mantine/dates";
import {
  FileInput,
  NumberInput,
  Select,
  Textarea,
  TextInput,
} from "@mantine/core";
import { useVendorUserList } from "utils/recoil/vendorUserListState";
import { usePartnerUserList } from "utils/recoil/partner/partnerUserListState";
import {
  CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT,
  CUSTOM_COLUMN_TYPE_NAME_STRING,
} from "constants/ja/common";
import {
  LONG_TEXT_MAX_LENGTH,
  STRING_MAX_LENGTH,
} from "constants/commonSetting";
import EditIcon from "public/icons/edit.svg";
import IconSelect from "public/icons/icon-arrow-down.svg";

import { formatDateString } from "utils/func/formatDateString";
import IconArrowNumberUp from "public/icons/icon-number-arrow-up.svg";
import IconArrowNumberDown from "public/icons/icon-number-arrow-down.svg";
import { formatDatetimeLocal } from "features/partners/utils/formatHelper";

type EditButtonProps = {
  item: CustomColumn;
  isEditing: boolean;
  toggleEditing: () => void;
};

type PresentationalProps = {
  className?: string;
  setModValue: React.Dispatch<any>;
  modValue: string | number;
  setFileValue: React.Dispatch<any>;
  setDateValue: React.Dispatch<any>;
  dateValue: Date | null;
  setDateTimeValue: React.Dispatch<any>;
  dateTimeValue: Date | null;
  item: CustomColumn;
  onBlur: () => void;
  isEditing: boolean;
  toggleEditing: () => void;
  vendorUserList: VendorUser[] | null;
  partnerUserList: PartnerUser[] | null;
  setModValueNumber: React.Dispatch<any>;
  modValueNumber: string | number;
  error: string;
};

const Presentational: React.FC<PresentationalProps> = ({
  className,
  item,
  isEditing,
  setModValue,
  modValue,
  onBlur,
  toggleEditing,
  vendorUserList,
  partnerUserList,
  setFileValue,
  setDateValue,
  dateValue,
  dateTimeValue,
  setDateTimeValue,
  modValueNumber,
  setModValueNumber,
  error,
}) => {
  const outsideRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const downloadClass = "download-file-down-btn";
      const deleteClass = "download-file-trash-btn";
      if (
        isEditing &&
        outsideRef.current &&
        !outsideRef.current.contains(event.target as Node) &&
        event.target instanceof HTMLElement &&
        !event.target.classList.contains(downloadClass) &&
        !event.target.classList.contains(deleteClass)
      ) {
        if (item.type_status === "FILE") {
          toggleEditing();
        } else if (item.type_status === "INTEGER") {
          onBlur();
        } else if (item.type_status === "DATETIME_LOCAL") {
          onBlur();
        }
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isEditing, item.type_status, toggleEditing, onBlur]);

  const handleBlur = () => {
    onBlur();
  };

  return (
    <div className={className} ref={outsideRef}>
      {!isEditing && <EditIcon onClick={toggleEditing} />}
      {isEditing && (
        <div>
          {item.type_status === "STRING" && (
            <>
              <TextInput
                className="width-max"
                onChange={(e) => setModValue(e.target.value)}
                defaultValue={item.current || ""}
                onBlur={onBlur}
              />
              <div className="error-message">{error}</div>
            </>
          )}
          {item.type_status === "INTEGER" && (
            <NumberInput
              className="width-max"
              onChange={(e) => e && setModValueNumber(e)}
              placeholder="数値を入力してください。"
              type="number"
              rightSection={
                <div
                  style={{ display: "flex", flexDirection: "column", gap: 10 }}
                >
                  <IconArrowNumberUp
                    style={{ cursor: "pointer" }}
                    onClick={() => {
                      setModValueNumber(Number(modValueNumber) + 1);
                    }}
                  />
                  <IconArrowNumberDown
                    style={{ cursor: "pointer" }}
                    onClick={() => {
                      setModValueNumber(Number(modValueNumber) - 1);
                    }}
                  />
                </div>
              }
              value={Number(modValueNumber)}
              onBlur={handleBlur}
            />
          )}
          {item.type_status === "SELECT" && (
            <Select
              data={
                item.select_contents
                  ? item.select_contents.map((item) => {
                    return {
                      value: String(item.select_id),
                      label: item.select_value,
                    };
                  })
                  : []
              }
              defaultValue={String(item.select_id)}
              onChange={(e) => setModValue(e)}
              onBlur={onBlur}
              rightSection={<IconSelect />}
              autoFocus
              searchable
            />
          )}
          {item.type_status === "DATE" && (
            <div>
              <DateInput
                value={dateValue}
                defaultValue={new Date()}
                valueFormat="YYYY年MM月DD日"
                locale="ja"
                monthLabelFormat="YYYY年MM月"
                yearLabelFormat="YYYY年"
                monthsListFormat="MM"
                yearsListFormat="YYYY"
                firstDayOfWeek={0}
                onChange={(e) => e && setDateValue(e)}
                autoFocus
                onBlur={onBlur}
                rightSection={<Cross2Icon onClick={() => setDateValue(null)} />}
              />
            </div>
          )}
          {item.type_status === "DATETIME_LOCAL" && (
            <div>
              <DateTimePicker
                value={dateTimeValue}
                locale="ja"
                valueFormat="YYYY年MM月DD日 HH:mm"
                onChange={(val) => setDateTimeValue(val)}
                firstDayOfWeek={0}
                rightSection={
                  <Cross2Icon
                    style={{ cursor: "pointer" }}
                    onClick={() => setDateTimeValue(null)}
                  />
                }
              />
            </div>
          )}
          {item.type_status === "VENDOR_USER" && (
            <Select
              data={
                vendorUserList
                  ? vendorUserList.map((user) => ({
                      value: String(user.id),
                      label: `${user.name}-${user.email}`,
                    }))
                  : []
              }
              defaultValue={
                item.vendor_user_id != null
                  ? String(item.vendor_user_id)
                  : undefined
              }
              onChange={(e) => setModValue(Number(e))}
              onBlur={() => {
                if (typeof modValue !== "string") {
                  onBlur();
                } else {
                  toggleEditing();
                }
              }}
              rightSection={<IconSelect />}
              autoFocus
              searchable
              clearable
            />
          )}
          {item.type_status === "PARTNER_USER" && (
            <Select
              data={
                partnerUserList
                  ? partnerUserList.map((user) => ({
                      value: String(user.id),
                      label: `${user.name}-${user.email}`,
                    }))
                  : []
              }
              defaultValue={
                item.partner_user_id != null
                  ? String(item.partner_user_id)
                  : undefined
              }
              onChange={(e) => setModValue(Number(e))}
              onBlur={() => {
                if (typeof modValue !== "string") {
                  onBlur();
                } else {
                  toggleEditing();
                }
              }}
              rightSection={<IconSelect />}
              autoFocus
              searchable
              clearable
            />
          )}
          {item.type_status === "FILE" && (
            <>
              <FileInput
                onChange={(e) => setFileValue(e)}
                placeholder="ファイルを選択"
                description="最大500MBまで"
              />
              <div className="error-message">{error}</div>
            </>
          )}
          {item.type_status === CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT && (
            <Textarea
              className="width-max"
              onChange={(e) => setModValue(e.target.value)}
              defaultValue={item.current || ""}
              minRows={10}
              error={error}
              onBlur={onBlur}
            />
          )}
        </div>
      )}
    </div>
  );
};

const Styled = styled(Presentational)<{ isEditing: boolean }>`
  ${(props) =>
    !props.isEditing
      ? ``
      : `
  width: 100%;
  `}
  text-align: end;
  .del-file {
    margin-top: 1rem;
    color: ${propcolors.orangeDel};
    border: 1px solid ${propcolors.orangeDel};
  }
  .Button {
    border: 0;
  }
  .editor {
    &-content {
      width: 500px;
    }
    &-input {
      width: 300px;
    }
  }
  .error-message {
    -webkit-tap-highlight-color: transparent;
    color: inherit;
    font-size: inherit;
    line-height: 1.55;
    -webkit-text-decoration: none;
    text-decoration: none;
    word-break: break-word;
    color: ${propcolors.errorColor};
    font-size: calc(0.875rem - 0.125rem);
    line-height: 1.2;
    display: block;
    padding-top: 5px;
  }
  .mantine-TextInput-input {
    border-color: ${propcolors.gray[200]};
    background-color: white;
    color: ${propcolors.blackLight};
    font-family: "Inter", "system-ui";
  }
  .mantine-TextInput-input:focus {
    background-color: ${propcolors.gray[150]};
  }
  .mantine-Select-item[data-selected] {
    background-color: ${propcolors.gray[150]};
    color: ${propcolors.blackLight};
  }
  .mantine-Textarea-input {
    border-color: ${propcolors.gray[200]};
    background-color: white;
    color: ${propcolors.blackLight};
    margin-top: 0;
    height: 4.8rem;
    width: 100%;
  }
  .mantine-Textarea-input:focus {
    background-color: ${propcolors.gray[150]};
  }
  .mantine-Input-input {
    border: 1px solid ${propcolors.gray[200]};
    background-color: white;
    border-radius: 8px;
  }
  .mantine-Input-input:focus {
    background-color: ${propcolors.gray[150]};
  }
  .mantine-FileInput-root {
    margin-top: 5px;
    font-family: "Inter", "system-ui";
  }
  .mantine-FileInput-description {
    padding: 0.6rem;
    border-radius: 8px 8px 0 0;
    background-color: white;
    border-top: 1px solid ${propcolors.gray[200]};
    border-right: 1px solid ${propcolors.gray[200]};
    border-left: 1px solid ${propcolors.gray[200]};
    font-family: "Inter", "system-ui";
    text-align: start;
  }
  .mantine-FileInput-input {
    border-radius: 0 0 8px 8px;
    background-color: ${propcolors.gray[150]};
    color: ${propcolors.blackLight};
    font-family: "Inter", "system-ui";
    text-align: start;
  }
  .mantine-FileInput-placeholder {
    color: ${propcolors.blackLight};
    font-family: "Inter", "system-ui";
  }
  .mantine-FileInput-wrapper {
    margin-top: 0;
    font-family: "Inter", "system-ui";
  }
`;

export const CustomItemEdit: React.FC<EditButtonProps> = ({
  item,
  isEditing,
  toggleEditing,
}) => {
  const [modValue, setModValue] = useState<string | number>(item.current ?? "");
  const [fileValue, setFileValue] = useState<File | null>(null);
  const [dateValue, setDateValue] = useState<Date | null>(
    item.type_status === "DATE" && item.current
      ? new Date(formatDateString(item.current as string))
      : null
  );
  // 追加: DATETIME_LOCAL 用
  const [dateTimeValue, setDateTimeValue] = useState<Date | null>(
    item.type_status === "DATETIME_LOCAL" && item.current
      ? new Date(formatDateString(item.current as string))
      : null
  );
  const [modValueNumber, setModValueNumber] = useState<string | number>(
    item.current || 0
  );
  const partnerData = usePartnerProps();
  const vendorUserList = useVendorUserList();
  const partnerUserList = usePartnerUserList();
  const setPartnerData = useSetPartnerProps();
  const router = useRouter();
  const { id } = router.query;
  const [error, setError] = useState("");

  const fetchPartnerProps = async () => {
    ax.get(`/api/v1/managed_partner/${id}`)
      .then((res) => {
        setPartnerData(res.data);
      })
      .catch(() => {});
  };

  const onSubmit = async () => {
    if (partnerData && partnerData.custom) {
      // 文字数チェック
      if (
        item.type_status === CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT &&
        modValue.toString().replace(/\r\n|\n|\r/g, "").length >
          LONG_TEXT_MAX_LENGTH
      ) {
        setError(
          LONG_TEXT_MAX_LENGTH.toLocaleString() + "文字以下で入力してください。"
        );
        return;
      }
      if (
        item.type_status === CUSTOM_COLUMN_TYPE_NAME_STRING &&
        modValue?.toString().length > STRING_MAX_LENGTH
      ) {
        setError(
          STRING_MAX_LENGTH.toLocaleString() + "文字以下で入力してください。"
        );
        return;
      }

      // FILE の場合は別途アップロード処理
      if (item.type_status === "FILE") {
        if (fileValue) {
          const maxFileSize = 500 * 1024 * 1024; // 500MB
          if (fileValue.size > maxFileSize) {
            setError("ファイルサイズは500MB以下で選択してください。");
            return;
          }
          const formData = new FormData();
          formData.append("file", fileValue);

          ax.post(
            `/api/v1/managed_partner/${id}/custom_file/${item.column_id}`,
            formData
          )
            .then(() => {
              fetchPartnerProps();
              toggleEditing();
              setError("");
            })
            .catch(() => {});
        }
        return;
      }

      // 他のタイプに関しては custom_column_contents を組み立てて一括更新
      const custom_column_contents = partnerData.custom.map(
        (custom_content: CustomColumn) => {
          if (custom_content.column_id !== item.column_id) {
            // 該当カラム以外は既存データをそのまま返す
            return {
              column_id: custom_content.column_id,
              custom_column_content: custom_content.current,
              select_id: custom_content.select_id,
              vendor_user_id: custom_content.vendor_user_id,
              partner_user_id: custom_content.partner_user_id,
            };
          }

          // 該当カラムのとき
          switch (item.type_status) {
            case "SELECT":
              return {
                column_id: custom_content.column_id,
                select_id: modValue, // select_idを更新
              };
            case "VENDOR_USER":
              return {
                column_id: custom_content.column_id,
                vendor_user_id: modValue === 0 ? null : modValue,
              };
            case "PARTNER_USER":
              return {
                column_id: custom_content.column_id,
                partner_user_id: modValue === 0 ? null : modValue,
              };
            case "DATE":
              return {
                column_id: custom_content.column_id,
                custom_column_content: dateValue
                  ? `${dateValue.getFullYear()}-${
                      dateValue.getMonth() + 1
                    }-${dateValue.getDate()}`
                  : null,
              };
            case "DATETIME_LOCAL":
              return {
                column_id: custom_content.column_id,
                custom_column_content:
                  dateTimeValue !== null
                    ? formatDatetimeLocal(dateTimeValue) // 「YYYY-MM-DDTHH:mm:ss」に変換
                    : null,
              };
            case "INTEGER":
              return {
                column_id: custom_content.column_id,
                custom_column_content: modValueNumber
                  ? Number(modValueNumber)
                  : 0,
              };
            default:
              // STRING or LONG_TEXT
              return {
                column_id: custom_content.column_id,
                custom_column_content: modValue,
              };
          }
        }
      );
      const body = {
        ...partnerData,
        custom_column_contents,
      };
      ax.put(`/api/v1/managed_partner/${id}`, body)
        .then((res) => {
          fetchPartnerProps();
          toggleEditing();
          setError("");
        })
        .catch((err) => {});
    }
  };

  useEffect(() => {
    if (fileValue) {
      onSubmit();
    }
  }, [fileValue]);

  return (
    <Styled
      item={item}
      setModValue={setModValue}
      modValue={modValue}
      onBlur={() => {
        onSubmit();
      }}
      isEditing={isEditing}
      toggleEditing={toggleEditing}
      vendorUserList={vendorUserList}
      partnerUserList={partnerUserList}
      setFileValue={setFileValue}
      dateValue={dateValue}
      setDateValue={setDateValue}
      dateTimeValue={dateTimeValue}
      setDateTimeValue={setDateTimeValue}
      modValueNumber={modValueNumber}
      setModValueNumber={setModValueNumber}
      error={error}
    />
  );
};
