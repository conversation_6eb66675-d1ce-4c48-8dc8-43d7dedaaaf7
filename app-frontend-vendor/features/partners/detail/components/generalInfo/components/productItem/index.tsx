import styled from "@emotion/styled";
import { Switch } from "@mantine/core";
import { modals } from "@mantine/modals";
import { useRouter } from "next/router";
import React, { memo } from "react";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import { useSetPartnerProductProps } from "utils/recoil/partner/partnerProductState";
import { useProductState } from "utils/recoil/product/productState";
import {
  usePartnerProps,
} from "utils/recoil/partner/partnerState";
import { notifications } from "@mantine/notifications";
import IconChecked from "public/icons/icon-checked.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";

type PresentationProps = {
  className?: string;
  toggleStatus: () => void;
  product: Product;
};

type PartnerProductProps = {
  product: Product;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  product,
  toggleStatus,
}) => {
  const iconChecked = (
    <IconChecked />
  );
  return (
    product && (
      <div
        className={`${className} ${product.sales_status === "ENABLED" ? `active` : `inactive`
          }`}
      >
        <span className="product-name">{product.product_name}</span>
        <Switch
          onClick={toggleStatus}
          color="dark.7"
          onLabel={iconChecked}
          checked={product.sales_status === "ENABLED" ? true : false}
        />
      </div>
    )
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-columns: 1fr auto;
  border-radius: 5px;
  padding: 8px;
  background-color: ${propcolors.background};
  border: 1px solid ${propcolors.gray[200]};
  margin-top: 0.5rem;
  .product {
    &-status {
      &-toggle {
        display: inline-block;
        padding: 0 0.5rem;
        border: none;
        color: ${propcolors.partnerRed};
      }
    }
    &-name {
      display: inline-block;
      width: 100%;
      font-size: 13px;
      margin-right: 16px;
      color: ${propcolors.switchDefault};
      padding-left: 8px;
    }
  }
`;

const Component: React.FC<PartnerProductProps> = ({ product }) => {
  const partnerData = usePartnerProps();
  const setPartnerProduct = useSetPartnerProductProps();
  const productStateList = useProductState();
  const { reload } = useRouter();

  const fetchPartnerProps = () => {
    if (partnerData) {
      ax.get(`/api/v1/partners/${partnerData.managed_partner_id}/products`)
        .then((response) => {
          setPartnerProduct(response.data);
        })
        .catch((err) => {});
    }
  };
  const toggleStatus = () => {
    const isAvailableForSales = productStateList?.find(
      (item) => item.product_id === product.product_id
    )?.product_status === "ENABLED";

    if (isAvailableForSales) {
      modals.openConfirmModal({
        title: `プロダクト「${product.product_name}」を${product.sales_status === "DISABLED" ? "有効" : "無効"
          }にします。よろしいですか？`,
        labels: {
          confirm: "はい",
          cancel: "いいえ",
        },
        onConfirm: () => {
          ax.post(
            `/api/v1/partners/${partnerData?.managed_partner_id}/products/${product.product_id}`
          ).then((res) => {
            fetchPartnerProps();
          });
        },
        confirmProps: {
          sx: {
            width: '47%',
            left: '0.3rem',
            fontSize: '0.88rem',
            backgroundColor: `${propcolors.black}`,
            color: `${propcolors.white}`,
            '&:hover': {
              backgroundColor: `${propcolors.black}`,
            },
          },
        },
        cancelProps: {
          variant: 'outline',
          sx: {
            width: '47%',
            left: '1rem',
            position: 'absolute',
            fontSize: '0.88rem',
            borderColor: `${propcolors.greyDefault}`,
            backgroundColor: `${propcolors.greyDefault}`,
            color: `${propcolors.white}`,
            '&:hover': {
              backgroundColor: `${propcolors.greyDefault}`,
            },
          },
        },
      });
    }
    else {
      notifications.show({
        title: "プロダクトが販売可能になっていません。",
        message: "プロダクトが無効です。プロダクト管理画面から当該プロダクトを有効に設定してください。",
        icon: <IconNotiFailed />,
      });
    }
  };
  return <Styled product={product} toggleStatus={toggleStatus} />;
};

export const ProductItem = memo(Component);
