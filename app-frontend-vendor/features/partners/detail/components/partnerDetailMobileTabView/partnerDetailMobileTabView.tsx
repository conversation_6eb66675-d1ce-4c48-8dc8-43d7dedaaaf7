import styled from "@emotion/styled";
import { Delete16Filled } from "@fluentui/react-icons";
import { ActionIcon, Select } from "@mantine/core";
import { Content, List, Root, Trigger } from "@radix-ui/react-tabs";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/router";
import IconPartner from "public/icons/icon-partner-big.svg";
import IconUserPartner from "public/icons/user-icon.svg";
import { useEffect, useMemo, useState } from "react";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import { getLinkStatusModifier } from "utils/constants/linkStatus";
import { EditButton } from "./../../components/generalInfo/components/partnerItem/components/EditButton";
import { Collaboration } from "../collaboration";
import { CustomItem } from "../generalInfo/components/customItem";
import { PartnerItem } from "../generalInfo/components/partnerItem";
import { SearchPartnerCandidates } from "../generalInfo/components/partnerItem/components/SearchPartnerCandidates";
import { SearchUser } from "../generalInfo/components/partnerItem/components/SearchUser";
import { SubtableMobileTabView } from "../subTable/subtableMobileTabView";

type SubTableItem = {
  value: string;
  label: string;
};

type PresentationProps = {
  deleteDetail: () => Promise<void>;
  partnerData: Partner;
  vendorUserList: VendorUser[] | null;
  parentCandidateList: ParentPartnerCandidate[] | null;
  dataMasters: DataMasters | null;
  subtableList: Subtable[] | null;
};

const Presentation: React.FC<PresentationProps> = ({
  deleteDetail,
  partnerData,
  vendorUserList,
  parentCandidateList,
  dataMasters,
  subtableList,
}) => {
  const flattenPrefectureList = dataMasters?.prefectures?.map((prefecture) => {
    return prefecture.label;
  });

  const [selectedTable, setSelectedTable] = useState<string>();
  const [subtableData, setSubtableData] = useState<SubtableRecord | null>(null);
  const { query } = useRouter();

  const convertedTableItems: SubTableItem[] = useMemo(
    () =>
      subtableList?.map((item) => {
        let tmpItem = {} as SubTableItem;
        tmpItem.value = item.sub_table_id.toString();
        tmpItem.label = item.sub_table_name;
        return tmpItem;
      }) || [],
    [subtableList],
  );

  const changeSubTableSelect = (value: string | null) => {
    if (value) {
      setSelectedTable(value);
    }
  };

  useEffect(() => {
    if (subtableList && convertedTableItems.length > 0) {
      setSelectedTable(subtableList[0].sub_table_id.toString());
    }
  }, [subtableList, convertedTableItems]);

  useEffect(() => {
    if (query.id && selectedTable) {
      setSubtableData(null);
      ax.get(
        `/api/v1/managed_partner_sub_table/${selectedTable}/managed_partners/${query.id}`,
      ).then((res) => {
        setSubtableData(res.data);
      });
    }
  }, [selectedTable, query.id]);

  const statusModifier = getLinkStatusModifier();

  const [isEditing, setIsEditing] = useState(false);
  const toggleEditing = () => {
    setIsEditing(!isEditing);
  };
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const toggleEditingUsername = () => {
    setIsEditingUsername(!isEditingUsername);
  };
  const [isEditingPartnerCandidate, setIsEditingPartnerCandidate] =
    useState(false);
  const toggleEditingPartnerCandidate = () => {
    setIsEditingPartnerCandidate(!isEditingPartnerCandidate);
  };

  return (
    <div>
      <section className="header-button-container">
        <Link className="sidebar-information-title_back" href="/partners">
          ← 一覧に戻る
        </Link>
        <ActionIcon variant="subtle" color="gray" onClick={deleteDetail}>
          <Delete16Filled />
          レコード削除
        </ActionIcon>
      </section>

      <Root className="TabsRoot" defaultValue="tab1">
        <List className="TabsList" aria-label="Manage your account">
          <Trigger className="TabsTrigger" value="tab1">
            契約情報
          </Trigger>
          <Trigger className="TabsTrigger" value="tab2">
            基本情報
          </Trigger>
          <Trigger className="TabsTrigger" value="tab3">
            追加情報
          </Trigger>
          {convertedTableItems && convertedTableItems.length > 0 && (
            <Trigger className="TabsTrigger" value="tab4">
              サブテーブル
            </Trigger>
          )}
        </List>
        <Content className="TabsContent tab-card" value="tab1">
          <section className="sidebar-information">
            <section className="sidebar-information-title">
              <div className="company-information">
                <IconPartner className="imgSpan" />
                <div className="conmapy-information-id-name">
                  <p className="company-information-title_id">
                    <span className="company-information-title_id_heading">
                      パートナーID
                    </span>
                    {partnerData.managed_partner_hex_id}
                  </p>
                  <p className="company-information-title_name">
                    {partnerData.managed_partner_name}
                  </p>
                </div>
              </div>
              {partnerData.url && (
                <p className="sidebar-information-url">
                  <span className="sidebar-information-url_heading">URL</span>
                  <Link
                    href={partnerData.url}
                    target="_blank"
                    className="sidebar-information-title_url"
                    style={{ width: "100%", wordBreak: "break-all" }}
                  >
                    {partnerData.url}
                  </Link>
                </p>
              )}
              <p className="sidebar-information-title_status_heading">
                ベンダー担当者
              </p>
              <div className="sidebar-information-title_status">
                {!isEditingUsername &&
                  (partnerData.vendor_user_id ? (
                    <p className="sidebar-information-title_status_name">
                      <div className="infomation-partner">
                        {partnerData?.vendor_avatar_url ? (
                          <Image
                            src={partnerData.vendor_avatar_url}
                            style={{
                              borderRadius: "40px",
                              marginRight: "1rem",
                            }}
                            width={40}
                            height={40}
                            alt={partnerData?.vendor_user_name || ""}
                          />
                        ) : (
                          <IconUserPartner className="imgSpan" />
                        )}
                        <p className="right-name">
                          {partnerData.vendor_user_name
                            ? partnerData.vendor_user_name
                            : partnerData.vendor_user_id}
                        </p>
                      </div>
                    </p>
                  ) : (
                    <p className="sidebar-information-title_status_name">
                      <div className="infomation-partner">
                        <IconUserPartner className="imgSpan" />
                        <p className="right-name">未設定</p>
                      </div>
                    </p>
                  ))}
                <div
                  className={
                    isEditingUsername ? "right-icon-full" : "right-icon"
                  }
                >
                  <SearchUser
                    title={
                      partnerData.vendor_user_name
                        ? partnerData.vendor_user_name
                        : "未設定"
                    }
                    current={
                      partnerData.vendor_user_id
                        ? partnerData.vendor_user_id
                        : -1
                    }
                    userList={vendorUserList}
                    onBlur={toggleEditingUsername}
                    isEditing={isEditingUsername}
                    toggleEditing={toggleEditingUsername}
                  />
                </div>
              </div>

              {partnerData.is_parent !== 1 && (
                <>
                  <p className="sidebar-information-title_status_heading">
                    関連パートナー
                  </p>
                  <div className="sidebar-information-title_status">
                    {!isEditingPartnerCandidate &&
                      (partnerData.parent_partner_id !== null ? (
                        <p className="sidebar-information-title_status_name">
                          <span>
                            {partnerData.parent_partner_name
                              ? partnerData.parent_partner_name
                              : ""}
                          </span>
                        </p>
                      ) : (
                        <p className="sidebar-information-title_status_name">
                          <span>未設定</span>
                        </p>
                      ))}
                    <div
                      className={
                        isEditingPartnerCandidate
                          ? "right-icon-full"
                          : "right-icon"
                      }
                    >
                      <SearchPartnerCandidates
                        title={
                          partnerData.parent_partner_name
                            ? partnerData.parent_partner_name
                            : "未設定"
                        }
                        current={
                          partnerData.parent_partner_id
                            ? partnerData.parent_partner_id
                            : -1
                        }
                        partnerList={parentCandidateList}
                        onBlur={toggleEditingPartnerCandidate}
                        isEditing={isEditingPartnerCandidate}
                        toggleEditing={toggleEditingPartnerCandidate}
                      />
                    </div>
                  </div>
                </>
              )}
            </section>
            <div className="sidebar-line"></div>
            <section className="sidebar-information-content no-backdrop">
              <p className="sidebar-information-content_card_heading">
                連携ステータス
              </p>
              <div className="sidebar-information-content_card_body">
                <p className="sidebar-information-content_card_message-top">
                  {partnerData.link_status ? (
                    <span className="message">
                      {statusModifier[partnerData.link_status].message}
                    </span>
                  ) : (
                    <span>N/A</span>
                  )}
                </p>
                <div className="sidebar-information-content_card_buttons">
                  <Collaboration partner={partnerData} />
                </div>
              </div>
              {partnerData.link_status === "LINK_REQUEST" && (
                <>
                  <p className="sidebar-information-content_card_heading">
                    連携依頼先
                  </p>
                  <div className="sidebar-information-content_card_body">
                    <p className="sidebar-information-content_card_status">
                      <span>{partnerData.request_sent_email}</span>
                    </p>
                  </div>
                </>
              )}
              <p className="sidebar-information-content_card_heading">
                契約ステータス
              </p>
              <div className="">
                <p className="sidebar-information-content_card_status">
                  {!isEditing &&
                    (partnerData.contract_status_name ? (
                      <span>{partnerData.contract_status_name}</span>
                    ) : (
                      <span>N/A</span>
                    ))}
                  <EditButton
                    title={`契約ステータス：${partnerData.contract_status_name}`}
                    type={"select"}
                    current={partnerData.contract_status}
                    DBlabel={"contract_status"}
                    onBlur={toggleEditing}
                    isEditing={isEditing}
                    toggleEditing={toggleEditing}
                    value={[
                      "CONTRACT_PENDING",
                      "CONTRACT_NEGOTIATION",
                      "CONTRACT_SIGNING",
                      "CONTRACT_ACTIVE",
                      "CONTRACT_CANCEL",
                    ]}
                    placeholder={[
                      "未対応",
                      "契約交渉中",
                      "契約締結中",
                      "契約済み",
                      "解約",
                    ]}
                  />
                </p>
              </div>
            </section>
          </section>
        </Content>
        {partnerData && (
          <>
            <Content className="TabsContent tab-card" value="tab2">
              <section className="generalinfo-profile">
                <PartnerItem
                  type={"text"}
                  current={partnerData.managed_partner_name}
                  label={"名前"}
                  DBlabel={"managed_partner_name"}
                  value={partnerData.managed_partner_name}
                />
                <PartnerItem
                  type={"text"}
                  current={partnerData.postal_code}
                  label={"郵便番号"}
                  DBlabel={"postal_code"}
                  value={partnerData.postal_code ? partnerData.postal_code : ""}
                />
                <PartnerItem
                  type={"select"}
                  current={partnerData.prefecture_id}
                  label={"都道府県"}
                  DBlabel={"prefecture_id"}
                  value={partnerData.prefecture_id}
                  placeholder={flattenPrefectureList}
                />
                <PartnerItem
                  type={"text"}
                  current={partnerData.address}
                  label={"住所"}
                  DBlabel={"address"}
                  value={partnerData.address ? partnerData.address : ""}
                />
                <PartnerItem
                  type={"number"}
                  current={partnerData.number_of_employees}
                  label={"従業員数"}
                  DBlabel={"number_of_employees"}
                  value={
                    partnerData.number_of_employees
                      ? partnerData.number_of_employees
                      : 0
                  }
                />
                <PartnerItem
                  type={"text"}
                  current={partnerData.tel}
                  label={"電話番号"}
                  DBlabel={"tel"}
                  value={partnerData.tel ? partnerData.tel : ""}
                />
                <PartnerItem
                  type={"text"}
                  current={partnerData.url}
                  label={"URL"}
                  DBlabel={"url"}
                  value={partnerData.url ? partnerData.url : ""}
                />
              </section>
            </Content>
            <Content className="TabsContent tab-card" value="tab3">
              <section className="generalinfo-profile">
                {partnerData.custom.map((item) => {
                  return <CustomItem key={item.column_id} item={item} />;
                })}
              </section>
              <section className="generalinfo-profile">
                <PartnerItem
                  type={"textArea"}
                  current={partnerData.memo}
                  label={"メモ"}
                  DBlabel={"memo"}
                  value={partnerData.memo ? partnerData.memo : ""}
                />
              </section>
            </Content>
          </>
        )}
        <Content
          className="TabsContent tab-card"
          value="tab4"
          style={{ overflow: "unset" }}
        >
          <section
            className="sidebar-information-title"
            style={{ maxHeight: "75vh" }}
          >
            <div>
              {convertedTableItems && (
                <Select
                  value={selectedTable}
                  defaultValue={selectedTable}
                  label="表示サブテーブル"
                  data={convertedTableItems}
                  onChange={(e) => changeSubTableSelect(e)}
                />
              )}
            </div>
            {subtableData && selectedTable ? (
              <SubtableMobileTabView
                records={subtableData}
                selectedSubTableId={selectedTable}
              />
            ) : (
              "データ取得中"
            )}
          </section>
        </Content>
      </Root>
      <style>{`
      .TabsRoot {
        display: flex;
        flex-direction: column;
        box-shadow: 0 2px 10px var(--black-a4);
        margin-bottom: 4rem;
      }
      .TabsList {
        flex-shrink: 0;
        display: flex;
        border-bottom: 1px solid var(--mauve-6);
      }
      .TabsTrigger {
        font-family: inherit;
        background-color: white;
        padding: 0 10px;
        height: 45px;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15px;
        line-height: 1;
        color: var(--mauve-11);
        user-select: none;
        font-size: 14px;
      }
      .TabsTrigger:first-child {
        border-top-left-radius: 6px;
      }
      .TabsTrigger:last-child {
        border-top-right-radius: 6px;
      }
      .TabsTrigger:hover {
        color: var(--violet-11);
      }
      .TabsTrigger[data-state='active'] {
        color: var(--violet-11);
        border-bottom: 4px solid #e0322d;
      }
      .company-information {
        flex: 1 1 auto;
        display: flex;
        flex-direction: row;
        margin-bottom: 16px;
      }
      .conmapy-information-id-name {
        margin-left: 24px;
        margin-bottom: 16px;
      }
      .company-information-title_id {
        font-size: 14px;
        font-weight: 400;
        color: ${propcolors.blackLightLabel};
      }
      .company-information-title_id_heading {
        font-size: 14px;
        margin-bottom: 5px;
        color: ${propcolors.blackLightLabel};
        margin-right: 8px;
      }
      .company-information-title_name {
        font-size: 18px;
        font-weight: 400;
        color: ${propcolors.blackLight};
      }
      .TabsContent {
        padding: 25px;
        margin-bottom: 5rem;
      }
      .TabsContent .tab-card {
        padding: 1rem;
        background-color: ${propcolors.white};
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 0.5rem;
        filter: drop-shadow(0px 0px 4px ${propcolors.gray[300]}80);
        overflow-x: hidden;
        max-width: 100%;
      }
      .header-button-container {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0.5rem 0.5rem 1rem;
        button {
          display: inline-block;
          width: auto;
          text-align: center;
        }
      }
      .TabsContent .sidebar {
        width: 100%;
        height: 800px;
        overflow: auto;
        display: grid;
        grid-template-rows: auto 1fr;
        @media screen and (max-width: 512px) {
          width: 100vw;
        }
      }
      .TabsContent .sidebar-line {
        border-top: 1px solid ${propcolors.gray[200]};
      }
      .TabsContent .sidebar-information {
        color: ${propcolors.blackLightLabel};
        padding: 0;
      }
      .TabsContent .sidebar-information-url_heading {
        font-weight: 400;
        font-size: 12px;
        color: ${propcolors.blackLightLabel};
      }
      .TabsContent .sidebar-information-title {
        display: grid;
        grid-template-rows: auto auto auto;
        padding-bottom: 25px;
      }
      .TabsContent .sidebar-information-title_id {
        font-size: 20px;
        margin-bottom: 20px;
        font-weight: bold;
      }
      .TabsContent .sidebar-information-title_name {
        font-size: 18px;
        font-weight: 400;
        color: ${propcolors.blackLight};
      }
      .TabsContent .sidebar-information-title_url {
        font-weight: 400;
        font-size: 14px;
        color: ${propcolors.blackLight};
        display: inline-block;
        width: min-content;
        text-decoration: underline;
        margin-top: 1rem;
      }
      .TabsContent .sidebar-information-title_status {
        display: flex;
        align-items: center;
      }
      .TabsContent .sidebar-information-title_status_heading {
        margin-top: 20px;
        margin-bottom: 16px;
        font-weight: 400;
        font-size: 12px;
        color: ${propcolors.blackLightLabel};
      }
      .TabsContent .sidebar-information-title_status_name {
        margin-right: 10px;
        width: 100%;
        font-weight: 400;
        font-size: 14px;
        color: ${propcolors.blackLight};
      }
      .TabsContent .sidebar-information-content {
        padding-top: 25px;
      }
      .TabsContent .sidebar-information-content_card_heading {
        font-size: 12px;
        font-weight: 400;
        margin-bottom: 16px;
        color: ${propcolors.blackLightLabel};
      }
      .TabsContent .sidebar-information-content_card_body {
        display: flex;
        justify-content: space-between;
        font-size: 1rem;
        font-weight: bold;
        margin-bottom: 25px;
      }
      .TabsContent .sidebar-information-content_card_message-top {
        margin-top: 6px;
        color: ${propcolors.blackLight};
        font-weight: 400;
        font-size: 16px;
      }
      .TabsContent .sidebar-information-content_card_status {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 100%;
      }
      .TabsContent .sidebar-information-content_card_status span {
        width: 100%;
        color: ${propcolors.blackLight};
        font-weight: 400;
        font-size: 16px;
      }
      .TabsContent .sidebar-products {
        width: 100%;
        font-size: 0.875rem;
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 5px;
        background-color: ${propcolors.white};
      }
      .TabsContent .sidebar-products_heading {
        padding: 0.7rem 25px 0.7rem 25px;
        background-color: ${propcolors.greyBreadcrumb};
        color: ${propcolors.blackLightLabel};
        border-top: 1px solid ${propcolors.gray[200]};
        font-weight: 400;
        font-size: 14px;
      }
      .TabsContent .sidebar-products-notfound {
        padding: 25px;
        font-size: 14px;
        font-weight: 400;
        color: ${propcolors.blackLight};
      }
      .infomation-partner {
        display: flex;
        align-items: center;
      }
      `}</style>
    </div>
  );
};

const Styled = styled(Presentation)``;

export const PartnerDetailMobileTabView: React.FC<PresentationProps> = ({
  deleteDetail,
  partnerData,
  vendorUserList,
  parentCandidateList,
  dataMasters,
  subtableList,
}) => {
  return (
    <Styled
      deleteDetail={deleteDetail}
      partnerData={partnerData}
      vendorUserList={vendorUserList}
      parentCandidateList={parentCandidateList}
      dataMasters={dataMasters}
      subtableList={subtableList}
    />
  );
};
