import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import { ProductItem } from "../generalInfo/components/productItem";

type ProductsListProps = {
  products: Product[];
};

type PresentationProps = {
  className?: string;
  active: Product[];
  inactive: Product[];
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  active,
  inactive,
}) => {
  return (
    <div className={className}>
      <div className="active-panel">
        <h3 className="active-title">販売可能ステータス　有効</h3>
        <div className="active-content">
          {active.map((item, index) => (
            <ProductItem key={index} product={item} />
          ))}
        </div>
      </div>
      <div className="inactive-panel">
        <h3 className="inactive-title">販売可能ステータス　無効</h3>
        <div className="inactive-content">
          {inactive.map((item, index) => (
            <ProductItem key={index} product={item} />
          ))}
        </div>
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  padding: 10px 25px;
  .mantine-Switch-root {
    margin-right: 8px;
  }
  .mantine-Switch-track {
    width: 36px;
    min-width: 36px;
  }
  .active {
    &-panel {
      padding-top: 1rem;
      padding-bottom: 25px;
    }
    &-title {
      color: ${propcolors.blackLight};
      padding-bottom: 1rem;
      font-size: 14px;
      font-weight: 400;
    }
  }
  .inactive {
    &-panel {
      padding-top: 25px;
      border-top: 1px solid ${propcolors.gray[200]};
    }
    &-title {
      color: ${propcolors.blackLight};
      padding-bottom: 1rem;
      font-size: 14px;
      font-weight: 400;
    }
    .mantine-Switch-track {
      background-color: white;
      border: 1px solid ${propcolors.gray[200]};
    }
    .mantine-Switch-thumb {
      background-color: ${propcolors.greyDefault};
    }
  }
`;

export const ProductsList: React.FC<ProductsListProps> = ({ products }) => {
  const activeProduct = products.filter(
    (product) => product.sales_status === "ENABLED"
  );
  const inactiveProduct = products.filter(
    (product) => product.sales_status === "DISABLED"
  );
  return <Styled active={activeProduct} inactive={inactiveProduct} />;
};
