import React from "react";
import styled from "@emotion/styled";
import { useDisclosure } from "@mantine/hooks";
import { Modal } from "@mantine/core";
import { propcolors } from "styles/colors";
import { SendRequestModal } from "./modalContent/sendRequestModal";
import { Button } from "@mantine/core";

type SendRequestProps = {
  partnerId: number;
  status: "LINK_CANCELLED" | "LINK_PENDING" | "LINK_REJECT" | "LINK_REQUEST";
  partner_collaboration_id: string;
};

type PresentationalProps = {
  className?: string;
  open: () => void;
  partnerId: number;
  buttonLabel: string;
};

const Presentational: React.FC<PresentationalProps> = ({
  className,
  open,
  partnerId,
  buttonLabel,
}) => (
  <Button fullWidth className={className} onClick={open}>
    {buttonLabel}
  </Button>
);

const Styled = styled(Presentational)`
  background-color: ${propcolors.blackLight};
  color: ${propcolors.white};
  width: 100%;
  font-size: 14px;
  line-height: 20px;
  :hover {
    background-color: ${propcolors.blackLight};
    color: ${propcolors.white};
  }
  .mantine-Modal-body {
    padding: 0;
  }
`;

const ModalTitle = styled.div`
  width: 100%;
  text-align: center;
  border-bottom: 1px solid ${propcolors.gray[200]};
  padding-bottom: 1rem;
  top: 1.3rem;
  position: absolute;
  font-weight: 400;
  font-size: 16px;
  background-color: ${propcolors.white};
`;

export const SendRequest: React.FC<SendRequestProps> = ({
  partnerId,
  status,
  partner_collaboration_id,
}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const buttonLabel = status === "LINK_REQUEST" ? "再連携依頼" : "連携依頼";
  return (
    <>
      <Styled partnerId={partnerId} open={open} buttonLabel={buttonLabel} />
      <Modal
        title={<ModalTitle>連携リクエストを送信</ModalTitle>}
        opened={opened}
        onClose={close}
        withCloseButton={false}
        size="640px"
        padding="0px"
        radius="8px"
      >
        <SendRequestModal
          partnerID={partnerId}
          partner_collaboration_id={partner_collaboration_id}
          close={close}
          status={status}
        />
      </Modal>
    </>
  );
};
