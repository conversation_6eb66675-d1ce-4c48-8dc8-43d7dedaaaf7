import React, { forwardRef } from "react";
import styled from "@emotion/styled";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import { Button, Checkbox, Textarea, TextInput, Tooltip } from "@mantine/core";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { getApiErrorMessage } from "utils/values/errorMessages";
import * as yup from "yup";
import { ValidationError } from "yup";
import { propcolors } from "styles/colors";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { RiInformationLine } from "@remixicon/react";

type SendRequestProps = {} & PresentationalProps;

type PresentationalProps = {
  partnerID: number;
  className?: string;
  close: () => void;
  status: "LINK_CANCELLED" | "LINK_PENDING" | "LINK_REJECT" | "LINK_REQUEST";
  partner_collaboration_id: string;
};

const emailValidationSchema = yup.object({
  email: yup
    .string()
    .email("無効なメールアドレスです")
    .required("メールアドレスは必須です"),
});

const Presentational: React.FC<PresentationalProps> = ({
  className,
  close,
  partnerID,
}) => {
  const form = useForm({
    initialValues: {
      email: "",
      message: "",
      is_double_check_skip: true,
    },
    validate: {
      email: (value) => {
        try {
          emailValidationSchema.validateSync({ email: value });
          return null;
        } catch (error) {
          if (error instanceof ValidationError) {
            return error.message;
          }
          return "不明なエラーが発生しました";
        }
      },
    },
  });
  const { reload } = useRouter();

  const onSubmit = form.onSubmit((values) => {
    ax.put(`/api/v1/managed_partners/${partnerID}/link_request`, {
      email: values.email,
      message: values.message,
      is_double_check: !values.is_double_check_skip,
    })
      .then((res) => {
        alert("操作が完了しました。");
        reload();
      })
      .catch((err) => {
        notifications.show({
          title: "連携依頼が送信できませんでした。",
          message:
            getApiErrorMessage(err.response.data.error) +
            " 入力されたメールアドレスをご確認の上、再度お試しください。問題が解消されない場合は、システム管理者にお問い合わせください。",
          icon: <IconNotiFailed />,
          autoClose: 5000,
        });
      });
  });
  return (
    <div className={className}>
      <form onSubmit={onSubmit}>
        <TextInput
          label={
            <span>
              パートナー担当者 メールアドレス
              <span style={{ color: "red" }}> ※</span>
            </span>
          }
          required={true}
          placeholder="<EMAIL>"
          name="email"
          {...form.getInputProps("email")}
          error={form.errors.email}
        />
        <Textarea
          label={
            <span
              style={{
                fontSize: "12px",
                fontWeight: 400,
                color: propcolors.blackLightLabel,
                margin: "28px 0px 5px 0px",
              }}
            >
              メッセージ
            </span>
          }
          style={{ marginTop: "15px", borderRadius: "8px" }}
          placeholder="XXのチームと連携したく、連携・承認のほどよろしくお願い致します。"
          minRows={4}
          name="message"
          {...form.getInputProps("message")}
        />
        <Checkbox
          label={
            <span style={{ display: "flex", alignItems: "center" }}>
              連携のダブルチェックをスキップする
              <Tooltip
                inline
                multiline
                width={480}
                label={
                  <>
                    <span>連携のダブルチェック：</span>
                    <br />
                    <span>
                      パートナー担当者が連携を許可した後、連携予定のチームを確認した上で連携するかどうかを決めることができます。
                    </span>
                  </>
                }
              >
                <Icon />
              </Tooltip>
            </span>
          }
          {...form.getInputProps("is_double_check_skip", { type: "checkbox" })}
          mt={15}
        />

        <div className="buttons">
          <Button className="buttons-cancel" onClick={close}>
            キャンセル
          </Button>
          <Button type="submit" className="buttons-submit">
            送信する
          </Button>
        </div>
      </form>
    </div>
  );
};

const Icon = forwardRef<HTMLDivElement>((props, ref) => (
  <span ref={ref} {...props} style={{ display: "inline-flex" }}>
    <RiInformationLine style={{ marginLeft: "5px" }} size={16} />
  </span>
));

const Styled = styled(Presentational)`
  padding: 24px;
  margin-top: 20px;
  .buttons {
    button {
      margin-top: 16px;
      width: 100%;
    }
  }
  .buttons {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 3px;
    &-cancel {
      padding: 0.8rem 1rem;
      width: 100%;
      height: auto;
      font-size: 14px;
      font-weight: 400;
      border-radius: 8px;
      color: ${propcolors.white};
      background-color: ${propcolors.greyDefault};
    }
    &-submit {
      padding: 0.8rem 1rem;
      width: 100%;
      height: auto;
      font-size: 14px;
      font-weight: 400;
      border-radius: 8px;
      background-color: ${propcolors.black};
    }
  }
  .mantine-TextInput-label {
    font-size: 12px;
    font-weight: 400;
    color: ${propcolors.blackLightLabel};
    margin: 28px 0px 5px 0px;
  }
  .mantine-TextInput-required {
    display: none;
  }
  .mantine-TextInput-input {
    height: 48px;
    border-radius: 8px;
  }
`;

export const SendRequestModal: React.FC<SendRequestProps> = ({
  partnerID,
  close,
  status,
  partner_collaboration_id,
}) => {
  return (
    <Styled
      close={close}
      partnerID={partnerID}
      partner_collaboration_id={partner_collaboration_id}
      status={status}
    />
  );
};
