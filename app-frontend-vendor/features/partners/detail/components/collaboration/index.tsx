import { useGetDataMasters } from "utils/recoil/dataMasters";
import { LinkStatus } from "./linkStatus";
import { SendRequest } from "./sendRequest";

type CollaborationProps = {
  partner: Partner;
};

export const Collaboration: React.FC<CollaborationProps> = ({ partner }) => {
  const masters = useGetDataMasters();
  const status = partner.link_status as
    | "LINK_CANCELLED"
    | "LINK_PENDING"
    | "LINK_REJECT"
    | "LINK_REQUEST";

  if (
    ["LINK_CANCELLED", "LINK_PENDING", "LINK_REJECT"].includes(
      partner.link_status
    )
  ) {
    return (
      <SendRequest
        partnerId={partner.managed_partner_id}
        status={status}
        partner_collaboration_id={partner.partner_collaboration_id}
      />
    );
  } else {
    if (partner.link_status === "LINK_REQUEST") {
      return (
        <>
          <SendRequest
            partnerId={partner.managed_partner_id}
            status={status}
            partner_collaboration_id={partner.partner_collaboration_id}
          />
          <LinkStatus
            status={partner.link_status}
            partnerId={partner.managed_partner_id}
          />
        </>
      );
    } else {
      return (
        <LinkStatus
          status={partner.link_status}
          partnerId={partner.managed_partner_id}
        />
      );
    }
  }
};
