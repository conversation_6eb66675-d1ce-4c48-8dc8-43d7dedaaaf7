import React from "react";
import styled from "@emotion/styled";
import { getLinkStatusModifier } from "utils/constants/linkStatus";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";
import { Button } from "@mantine/core";

type LinkStatusProps = {
  partnerId: number;
  status: string;
};

type PresentationProps = {
  className?: string;
  status: string;
  send: () => void;
};

const classPlaceholder: { [key: string]: string } = {
  LINK_PENDING: "linkstatus-request",
  LINK_CENCELLED: "linkstatus-cancelled",
  LINK_ACTIVE: "linkstatus-active",
  LINK_HOLD: "linkstatus-suspend",
  LINK_REJECT: "linkstatus-rejected",
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  status,
  send,
}) => {
  const statusModifier = getLinkStatusModifier();
  return (
    <Button
      fullWidth
      onClick={send}
      variant="subtle"
      className={`${className} linkstatus-toggle ${classPlaceholder[status]}`}
    >
      {statusModifier[status].button}
    </Button>
  );
};

const Styled = styled(Presentation)`
  font-size: 14px;
  &.linkstatus {
    &-request {
      background-color: ${propcolors.gray[100]};
      color: ${propcolors.black};
    }
    &-cancelled {
      background-color: ${propcolors.gray[100]};
      color: ${propcolors.blackLight};
    }
    &-active {
      background-color: ${propcolors.blackLight};
      color: ${propcolors.white};
    }
    &-suspend {
      background-color: ${propcolors.blackLight};
      color: ${propcolors.white};
    }
    &-rejected {
      background-color: ${propcolors.blackLight};
      color: ${propcolors.white};
    }
  }
`;

export const LinkStatus: React.FC<LinkStatusProps> = ({
  status,
  partnerId,
}) => {
  const { reload } = useRouter();
  const statusModifier = getLinkStatusModifier();
  const submit = async () => {
    const check = confirm(statusModifier[status].dialog);
    if (check) {
      ax.put(
        `/api/v1/managed_partners/${partnerId}${statusModifier[status].endpoint}`
      )
        .then((res) => {
          alert("操作が完了しました。");
          reload();
        })
        .catch((err) =>
          alert(`${err}: エラーが発生しました。もう一度お試しください。`)
        );
    }
  };
  return <Styled status={status} send={submit} />;
};
