import styled from "@emotion/styled";
import {
  ColDef,
  ICellRendererParams,
  SuppressKeyboardEventParams,
} from "ag-grid-community";
import { AGGrid } from "components/Grid";
import {
  useCallback,
  useEffect,
  useState,
  useRef,
  forwardRef,
  memo,
  useImperativeHandle,
} from "react";
import { propFileCellEditor } from "./../../../../../components/Grid/CellEditor/propFileCellEditor";
import { propPartnerUserEditor } from "./../../../../../components/Grid/CellEditor/propPartnerUserEditor";
import { propVendorUserEditor } from "./../../../../../components/Grid/CellEditor/propVendorUserEditor";
import { propTextCellEditor } from "components/Grid/CellEditor/propTextCellEditor";
import { ActionIcon, Button, NumberInput } from "@mantine/core";
import { Add16Filled, Delete16Filled } from "@fluentui/react-icons";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import {
  usePartnerUserList,
  useSetPartnerUserList,
} from "utils/recoil/partner/partnerUserListState";
import { Cross2Icon, DownloadIcon } from "@radix-ui/react-icons";
import saveAs from "file-saver";
import { propcolors } from "styles/colors";
import {
  useSetVendorUserList,
  useVendorUserList,
} from "utils/recoil/vendorUserListState";
import { DatePickerInput } from "@mantine/dates";
import { modals } from "@mantine/modals";
import { propSelectEditor } from "components/Grid/CellEditor/propSelectCellEditor";
import {
  CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT,
  SUBTABLE_FILE_CHANGING,
} from "constants/ja/common";
import { propLongTextEditor } from "components/Grid/CellEditor/propLongTextEditor";
import {
  ACCEPTABLE_DECIMAL_DIGIT_NUMBER,
  SUB_TABLE_LARGE_COLUMN_MOBILE_WIDTH,
  SUB_TABLE_MOBILE_DOUBLE_CLICK_DELAY,
} from "constants/commonSetting";
import { Skeleton } from "components/Skeleton";
import { formatDateString } from "utils/func/formatDateString";

type PresentationProps = {
  className?: string;
  records: SubtableRecord;
  selectedSubTableId: string;
};

type SubTableProps = {
  records: SubtableRecord;
  selectedSubTableId: string;
};

type FocusCell = {
  rowIndex: number;
  colId: string;
};

const propDateEditor = memo(
  forwardRef((props: { value: string }, ref) => {
    const [dateValue, setDateValue] = useState<Date | null>(
      props.value ? new Date(formatDateString(props.value)) : new Date()
    );
    const refInput = useRef<HTMLInputElement | null>(null);

    useImperativeHandle(ref, () => ({
      getValue: () =>
        dateValue
          ? `${dateValue.getFullYear()}-${
              dateValue.getMonth() + 1
            }-${dateValue.getDate()}`
          : "",
    }));

    useEffect(() => {
      if (refInput.current) {
        const editorBounds = refInput.current.getBoundingClientRect();
        const gridBounds = document
          .querySelector(".ag-root")
          ?.getBoundingClientRect();

        if (gridBounds) {
          const isOverlappingBottom = editorBounds.bottom > gridBounds.bottom;
          const isOverlappingTop = editorBounds.top < gridBounds.top;

          if (isOverlappingBottom) {
            refInput.current.style.top = `${-editorBounds.height}px`;
          } else if (isOverlappingTop) {
            refInput.current.style.top = `${editorBounds.height}px`;
          }
        }
      }
    }, []);

    return (
      <div ref={refInput}>
        <DatePickerInput
          dropdownType="modal"
          value={dateValue}
          defaultValue={new Date()}
          valueFormat="YYYY年M月D日"
          locale="ja"
          monthLabelFormat="YYYY年M月"
          yearLabelFormat="YYYY年"
          monthsListFormat="MM"
          yearsListFormat="YYYY"
          firstDayOfWeek={0}
          onChange={(e) => e && setDateValue(e)}
          rightSection={<Cross2Icon onClick={() => setDateValue(null)} />}
        />
      </div>
    );
  })
);

const propNumberEditor = memo(
  forwardRef((props: { value: string }, ref) => {
    const createInitialState = () => {
      return { value: props.value };
    };
    const refInput = useRef(null);
    const initialState = createInitialState();
    const [selectedValue, setSelectedValue] = useState<number | null>(
      Number(initialState.value)
    );

    useImperativeHandle(ref, () => ({
      getValue: () => selectedValue,
    }));
    return (
      <div ref={refInput}>
        <NumberInput
          value={selectedValue ? selectedValue : 0}
          className="width-max"
          onChange={(e) =>
            setSelectedValue(typeof e === "number" ? Number(e) : null)
          }
          placeholder="数値を入力してください。"
          type="number"
          hideControls
          precision={ACCEPTABLE_DECIMAL_DIGIT_NUMBER}
          formatter={(value) =>
            // 小数点以下の0について、末尾から連続する0を消す
            !Number.isNaN(parseFloat(value))
              ? `${value}`.replace(/\.0*$|(\.\d*?[1-9])0+$/, "$1")
              : value
          }
        />
      </div>
    );
  })
);

const handleCellEditorType = (type: SubtableRecordType) => {
  switch (type) {
    case "SELECT":
      return propSelectEditor;
    case "DATE":
      return propDateEditor;
    case "INTEGER":
      return propNumberEditor;
    case "FILE":
      return propFileCellEditor;
    case "PARTNER_USER":
      return propPartnerUserEditor;
    case "VENDOR_USER":
      return propVendorUserEditor;
    case CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT:
      return propLongTextEditor;
    default:
      return propTextCellEditor;
  }
};

// サブテーブルのデータをAGGridのrowDataに渡せるよう変換
const mapRecordsToRowData = (records: SubtableRecordData[]) => {
  return records.map((record) =>
    record.values.reduce(
      (acc, cur) => ({
        ...acc,
        [cur.id.toString()]: cur.value,
      }),
      { record_id: record.record_id }
    )
  );
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  records,
  selectedSubTableId,
}) => {
  const { query } = useRouter();
  const [subtableData, setSubtableData] = useState<SubtableRecord>(records);
  const partnerUserList = usePartnerUserList();
  const setPartnerUserList = useSetPartnerUserList();
  const vendorUserList = useVendorUserList();
  const setVendorUserList = useSetVendorUserList();
  const router = useRouter();
  const { id } = router.query;
  const [rowData, setRowData] = useState(mapRecordsToRowData(records.records));
  const gridApiRef = useRef<any>(null);
  const gridColumnApiRef = useRef<any>(null);
  // フォーカス状態管理用
  const [currentFocusCell, setCurrentFocusCell] = useState<FocusCell>();
  // 入力モード（半角・全角(日本語)）の状態管理用
  const [isComposing, setIsComposing] = useState(false);
  // File変更時のローディング管理用
  const [isFileChanging, setIsFileChanging] = useState(false);

  useEffect(() => {
    if (vendorUserList === null) {
      ax.get(`/api/v1/vendor_users/`)
        .then((res) => {
          setVendorUserList(res.data);
        })
        .catch((err) => {});
    }
  }, [vendorUserList, setVendorUserList]);

  useEffect(() => {
    if (partnerUserList === null) {
      ax.get(`/api/v1/managed_partner/${id}/partner_users`)
        .then((res) => {
          setPartnerUserList(res.data);
        })
        .catch((err) => {});
    }
  }, [partnerUserList, setPartnerUserList, id]);

  // 一定時間経過したらフォーカス管理から外す
  useEffect(() => {
    setTimeout(() => {
      setCurrentFocusCell(undefined);
    }, SUB_TABLE_MOBILE_DOUBLE_CLICK_DELAY);
  }, [currentFocusCell]);

  const fetchAndUpdateRecords = async (endpoint: string) => {
    try {
      const res = await ax.get(endpoint);
      setRowData(mapRecordsToRowData(res.data.records));
      setSubtableData(res.data);
    } catch (err) {}
  };

  const handleAddRow = useCallback(async () => {
    try {
      await ax.post(
        `api/v1/managed_partner_sub_table/${selectedSubTableId}/managed_partners/${query.id}/records`
      );
      fetchAndUpdateRecords(
        `/api/v1/managed_partner_sub_table/${selectedSubTableId}/managed_partners/${query.id}`
      );
    } catch (err) {}
  }, [selectedSubTableId]);

  const downloadFile = (file_id: number, file_name: string) => {
    ax.get(`/api/v1/managed_partner_sub_table/download/${file_id}`, {
      responseType: "blob",
    })
      .then((res) => {
        let name = "";
        if (file_name) {
          name = file_name as string;
        }
        const blob = new Blob([res.data], {
          type: res.headers["content-type"],
        });
        saveAs(blob, name);
      })
      .catch((err) => {});
  };

  const deleteFilled = (file_id: number) => {
    modals.openConfirmModal({
      title: "本当にファイルを削除しますか？",
      labels: {
        confirm: "削除",
        cancel: "キャンセル",
      },
      onConfirm: () => {
        setIsFileChanging(true);
        ax.delete(`api/v1/managed_partner_sub_table/value/${file_id}`)
          .then((res) => {
            fetchAndUpdateRecords(
              `/api/v1/managed_partner_sub_table/${selectedSubTableId}/managed_partners/${query.id}`
            );
          })
          .catch((err) => {})
          .finally(() => {
            setIsFileChanging(false);
          });
      },
    });
  };

  // セルの編集を完了させるコールバック関数
  const stopEditingCallback = () => {
    gridApiRef.current.stopEditing();
  };
  // セルのフォーカス時
  const onCellFocused = (event: any) => {
    // 既にフォーカス情報があり、現在のフォーカスと一致する場合
    if (
      currentFocusCell !== undefined &&
      currentFocusCell.rowIndex === event.rowIndex &&
      currentFocusCell.colId === event.column.colId
    ) {
      // セルを編集状態にする
      event.api.startEditingCell({
        rowIndex: event.rowIndex,
        colKey: event.column.getColId(),
      });
    } else {
      setCurrentFocusCell({
        rowIndex: event.rowIndex,
        colId: event.column.colId,
      });
    }
  };

  function suppressEnter(
    params: SuppressKeyboardEventParams,
    columnName: string
  ) {
    var key = params.event.key;
    // 全角入力中の場合、Enterを使用不可にする()
    const suppress =
      columnName === CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT
        ? true
        : isComposing && key === "Enter";
    return suppress;
  }

  const colDefs: ColDef[] = records.columns.map((column) => {
    return {
      field: column.column_id.toString(),
      headerName: column.column_label,
      width: SUB_TABLE_LARGE_COLUMN_MOBILE_WIDTH,
      autoHeight: column.type_name === CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT,
      editable: true,
      cellEditorPopup: true,
      cellEditor: handleCellEditorType(column.type_name),
      cellRenderer: (props: ICellRendererParams) => {
        const cellValue = props.valueFormatted
          ? props.valueFormatted
          : props.value;
        const file_name = subtableData.records
          .find((data) => data.record_id === props.data.record_id)
          ?.values.find((value) => value.id === column.column_id)?.value;
        if (column.type_name === "FILE" && file_name) {
          const file_id = subtableData.records
            .find((data) => data.record_id === props.data.record_id)
            ?.values.find((value) => value.id === column.column_id)?.file_id;
          return (
            <div className="download-file">
              <span className="download-file-label">{file_name}</span>
              <ActionIcon onClick={() => downloadFile(file_id!, file_name)}>
                <DownloadIcon />
              </ActionIcon>
              <ActionIcon onClick={() => deleteFilled(file_id!)}>
                <Delete16Filled />
              </ActionIcon>
            </div>
          );
        } else if (column.type_name === "FILE") {
          return (
            <div className="download-file">
              <span className="download-file-label_empty">
                ダブルクリックで登録...
              </span>
            </div>
          );
        } else {
          return cellValue;
        }
      },
      cellEditorParams: {
        values:
          column.type_name === "SELECT"
            ? column.select_contents?.map((content) => ({
                value: content.select_id.toString(),
                label: content.select_value,
              }))
            : [],
        setIsComposing: setIsComposing,
        stopEditingCallback: stopEditingCallback,
      },
      suppressKeyboardEvent: (params) => {
        return suppressEnter(params, column.type_name);
      },
      valueFormatter: (params) => {
        if (column.type_name === "SELECT") {
          return column.select_contents?.find(
            (content) => content.select_id.toString() === params.value
          )?.select_value;
        }
        if (column.type_name === "PARTNER_USER") {
          return partnerUserList?.find(
            (user) => user.id.toString() === params.value
          )?.name;
        }
        if (column.type_name === "VENDOR_USER") {
          return vendorUserList?.find(
            (user) => user.id.toString() === params.value
          )?.name;
        }
        if (column.type_name === "FILE") {
          // records内のレコードIDが一致するvaluesにカラムIDが一致するvalueを取得する処理
          const file_name = subtableData.records
            .find((data) => data.record_id === params.data.record_id)
            ?.values.find((value) => value.id === column.column_id)?.value;

          return file_name;
        }
        if (column.type_name === CUSTOM_COLUMN_TYPE_NAME_LONG_TEXT) {
          return (
            <span
              style={{
                whiteSpace: "pre-wrap",
                display: "block",
                lineHeight: 1,
                margin: 5,
              }}
            >
              {params.value}
            </span>
          );
        }
      },
    } as ColDef;
  });
  // 行移動用のボタンを追加
  colDefs.push({
    headerName: "",
    cellRendererFramework: (params: any) => (
      <div>
        <button
          onClick={() => handleMoveRow(params, -1, params.data.record_id)}
          disabled={params.rowIndex === 0}
        >
          ↑
        </button>
        <button
          onClick={() => handleMoveRow(params, 1, params.data.record_id)}
          disabled={params.rowIndex === rowData.length - 1}
        >
          ↓
        </button>
      </div>
    ),
    suppressMenu: true,
    width: 100,
  });

  // 削除用のボタンを追加
  colDefs.push({
    headerName: "",
    field: "actions",
    cellRendererFramework: (params: any) => {
      return (
        <Button onClick={() => handleDeleteRow(params.data.record_id)}>
          削除
        </Button>
      );
    },
    width: 100,
    suppressSizeToFit: true,
  });
  const touchHandler = (event: any) => {
    if (event.touches.length > 1) {
      handleCellValueChange(event);
    }
  };
  document.addEventListener("touchstart", touchHandler, {
    passive: false,
  });

  const handleCellValueChange = (event: any) => {
    const sub_table_column_id = event.colDef.field;
    const sub_table_record_id = subtableData.records[event.rowIndex].record_id;
    const type = subtableData.columns.find(
      (column) => column.column_id.toString() === sub_table_column_id
    )?.type_name;
    const newValue = event.newValue;

    let data;
    if (type === "SELECT") {
      data = { select_id: newValue };
    } else if (type === "PARTNER_USER") {
      data = { partner_user_id: newValue === "" ? null : newValue };
    } else if (type === "VENDOR_USER") {
      data = { vendor_user_id: newValue === "" ? null : newValue };
    } else if (type === "FILE") {
      const formData = new FormData();
      formData.append("file", newValue);
      setIsFileChanging(true);
      ax.post(
        `/api/v1/managed_partner_sub_table/value/${sub_table_column_id}/${sub_table_record_id}`,
        formData
      )
        .then((res) => {
          fetchAndUpdateRecords(
            `/api/v1/managed_partner_sub_table/${selectedSubTableId}/managed_partners/${query.id}`
          );
        })
        .catch((err) => {})
        .finally(() => {
          setIsFileChanging(false);
        });
      return;
    } else {
      data = { value: newValue };
    }

    ax.post(
      `/api/v1/managed_partner_sub_table/value/${sub_table_column_id}/${sub_table_record_id}`,
      data
    )
      .then((res) => {})
      .catch((err) => {});
  };

  const handleDeleteRow = useCallback(async (recordId: string) => {
    try {
      await ax.delete(`/api/v1/managed_partner_sub_table/records/${recordId}`);
      fetchAndUpdateRecords(
        `/api/v1/managed_partner_sub_table/${selectedSubTableId}/managed_partners/${query.id}`
      );
    } catch (err) {}
  }, []);

  const handleMoveRow = (params: any, moveBy: number, recordId: string) => {
    const currentRowIndex = params.rowIndex;
    const toIndex = currentRowIndex + moveBy;

    if (toIndex < 0 || toIndex >= rowData.length) return;

    const direction = moveBy === 1 ? "down" : "up";

    // レコードの移動をAPIで実行
    moveRecord(direction, recordId)
      .then((response) => {
        fetchAndUpdateRecords(
          `/api/v1/managed_partner_sub_table/${selectedSubTableId}/managed_partners/${query.id}`
        );
      })
      .catch((err) => {});
  };

  // レコードの移動API呼び出し
  const moveRecord = (direction: "up" | "down", recordId: string) => {
    const endpoint = `/api/v1/managed_partner_sub_table/records/${recordId}/move_${direction}`;
    return ax.put(endpoint);
  };

  // AgGridのwrapperにスタイルを追加してDatePickerのはみ出し表示を可能にする
  const changeAgGridStyleForMobile = () => {
    const agGrid = document.querySelector(".ag-root-wrapper");
    if (agGrid) {
      agGrid.setAttribute("style", "overflow: unset;");
    }
  };
  return (
    <div className={className}>
      <div className="header">
        <Button leftIcon={<Add16Filled />} onClick={handleAddRow}>
          行追加
        </Button>
      </div>
      {isFileChanging && <Skeleton caption={SUBTABLE_FILE_CHANGING} />}
      <AGGrid
        className=""
        columnDefs={colDefs}
        rowData={rowData}
        onCellValueChanged={handleCellValueChange}
        paginationPageSize={10}
        onCellFocused={onCellFocused}
        onGridReady={(params) => {
          gridApiRef.current = params.api;
          gridColumnApiRef.current = params.columnApi;
          changeAgGridStyleForMobile();
        }}
      />
    </div>
  );
};

const Styled = styled(Presentation)`
  height: 65vh;
  display: grid;
  grid-template-rows: auto 1fr;
  gap: 1rem;
  .header {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
  }
  .grid-height-full {
    height: 65vh;
    width: 100%;
    .ag-root-wrapper-body {
      min-height: 1000px;
    }
  }
  .download-file {
    display: grid;
    grid-template-columns: 1fr auto auto;
    align-items: center;
    &-label {
      display: inline-block;
      width: 100%;
      overflow-x: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &_empty {
        color: ${propcolors.gray[400]};
      }
    }
  }
`;

export const SubtableMobileTabView: React.FC<SubTableProps> = ({
  records,
  selectedSubTableId,
}) => {
  return <Styled records={records} selectedSubTableId={selectedSubTableId} />;
};
