import styled from "@emotion/styled";
import Head from "next/head";
import { propcolors } from "../../../styles/colors";
import {
    usePartnerProps,
    useSetPartnerProps,
} from "utils/recoil/partner/partnerState";
import { PartnerDetailLayout } from "./layout";
import { RelatedPartnersTable } from "./components/relatedPartnersTable";
import { ax } from "utils/axios";
import { useChildPartnersState } from "utils/recoil/partner/childPartnersListState";
import { useCallback, useEffect } from "react";
import { Skeleton } from "@mantine/core";

type RelatedPartnersProps = {
    childPartnersInfo: ChildParterInfo[] | null
}

type PresentationProps = {
    className?: string;
} & RelatedPartnersProps;

const Presentation: React.FC<PresentationProps> = ({ childPartnersInfo }) => {
    return (
        <>
            <Head>
                <title>
                    関連パートナー一覧 - PartnerProp
                </title>
            </Head>
            {childPartnersInfo ? <RelatedPartnersTable childPartners={childPartnersInfo} /> : <></>}
        </>
    );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-columns: 400px 1fr;
  .tab {
    &-root {
      height: 100%;
      display: grid;
      grid-template-rows: auto 1fr;
    }
    &-content {
      height: 100%;
    }
  }
  .lead {
    &-main {
      &-content {
        padding: 16px 16px 16px 0;
        height: 100%;
      }
    }
    &-sidebar {
      width: 400px;
      padding: 16px;
      display: grid;
      grid-template-rows: auto 1fr;
      grid-gap: 16px;
      &-content {
        width: 100%;
        font-size: 14px;
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 5px;
        background-color: ${propcolors.white};
        padding: 16px;
        &-products {
          &-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
          }
        }
      }
      &-id {
      }
      &-client {
        &_name {
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }
`;

export const RelatedPartners : React.FC<{ managed_partner_id: number }> = ({
  managed_partner_id,
}) => {
    const partnerProps = usePartnerProps();
    const setPartnerProps = useSetPartnerProps();
    const childPartnerList = useChildPartnersState();

    const fetchPartnerDetail = useCallback(
      (reset: boolean) => {
        if (reset) {
          setPartnerProps(null);
        }
        ax.get(`/api/v1/managed_partner/${managed_partner_id}`).then(
          (response) => {
            setPartnerProps(response.data);
          }
        );
      },
      [setPartnerProps, managed_partner_id]
    );
  
    useEffect(() => {
      if (!partnerProps) {
        fetchPartnerDetail(false);
      }
    }, [partnerProps]);

    return (
    <>
      {partnerProps ? (
        <PartnerDetailLayout
          managedPartnerId={managed_partner_id}
          partnerData={partnerProps}
        >
          <Styled childPartnersInfo={childPartnerList} />
        </PartnerDetailLayout>
      ) : (
        <Skeleton />
      )}
    </>
    );
};
