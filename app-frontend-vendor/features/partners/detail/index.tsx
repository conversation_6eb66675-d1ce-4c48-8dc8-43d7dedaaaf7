import styled from "@emotion/styled";
import Head from "next/head";
import { propcolors } from "../../../styles/colors";
import { GeneralInfo } from "./components/generalInfo";
import {
  usePartnerProps,
  useSetPartnerProps,
} from "utils/recoil/partner/partnerState";
import { PartnerDetailLayout } from "./layout";
import { useCallback, useEffect } from "react";
import { ax } from "utils/axios";
import { Skeleton } from "@mantine/core";

type PresentationProps = {
  className?: string;
  partnerData: Partner;
};

const Presentation: React.FC<PresentationProps> = ({ partnerData }) => {
  return (
    <>
      <Head>
        <title>
          {partnerData?.managed_partner_name} | パートナー詳細 - PartnerProp
        </title>
      </Head>
      <GeneralInfo partnerData={partnerData} />
    </>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-columns: 400px 1fr;
  .tab {
    &-root {
      height: 100%;
      display: grid;
      grid-template-rows: auto 1fr;
    }
    &-content {
      height: 100%;
    }
  }
  .lead {
    &-main {
      &-content {
        padding: 16px 16px 16px 0;
        height: 100%;
      }
    }
    &-sidebar {
      width: 320px;
      padding: 16px;
      display: grid;
      grid-template-rows: auto 1fr;
      grid-gap: 16px;
      &-content {
        width: 100%;
        font-size: 14px;
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 5px;
        background-color: ${propcolors.white};
        padding: 16px;
        &-products {
          &-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
          }
        }
      }
      &-id {
      }
      &-client {
        &_name {
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }
`;

export const PartnerDetail: React.FC<{ managed_partner_id: number }> = ({
  managed_partner_id,
}) => {
  const partnerProps = usePartnerProps();
  const setPartnerProps = useSetPartnerProps();

  const fetchPartnerDetail = useCallback(
    (reset: boolean) => {
      if (reset) {
        setPartnerProps(null);
      }
      ax.get(`/api/v1/managed_partner/${managed_partner_id}`).then(
        (response) => {
          setPartnerProps(response.data);
        }
      );
    },
    [setPartnerProps, managed_partner_id]
  );

  useEffect(() => {
    fetchPartnerDetail(false);
  }, []);

  return (
    <>
      {partnerProps ? (
        <PartnerDetailLayout
          managedPartnerId={managed_partner_id}
          partnerData={partnerProps}
        >
          <Styled partnerData={partnerProps} />
        </PartnerDetailLayout>
      ) : (
        <Skeleton />)}
    </>
  );
};
