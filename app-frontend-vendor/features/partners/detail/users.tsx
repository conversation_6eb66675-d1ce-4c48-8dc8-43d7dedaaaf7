import styled from "@emotion/styled";
import Head from "next/head";
import { propcolors } from "../../../styles/colors";
import {
  usePartnerProps,
  useSetPartnerProps,
} from "utils/recoil/partner/partnerState";
import { PartnerDetailLayout } from "./layout";
import { UserTable } from "./components/userTable";
import { ax } from "utils/axios";
import { useCallback, useEffect, useState } from "react";
import { Skeleton, SegmentedControl, Group } from "@mantine/core";
import { RiListCheck, RiOrganizationChart } from "@remixicon/react";
import { OrganizationChart } from "./organizationChart";

type ViewMode = "USERTABLE" | "ORGCHART";

type PresentationProps = {
  partnerData: Partner | null;
};

const Presentation: React.FC<PresentationProps> = ({ partnerData }) => {
  // 表示切り替え用State
  const [viewMode, setViewMode] = useState<ViewMode>("USERTABLE");
  const [isReactFlowReady, setIsReactFlowReady] = useState<boolean>(false);

  // 表と組織図との切り替え関数
  const handleChangeView = (newValue: string) => {
    // MantineのSegmentedControlは string | number で返るのでキャスト
    setViewMode(newValue as ViewMode);
  };

  // ReactFlow初期化の遅延処理
  useEffect(() => {
    if (viewMode === "ORGCHART") {
      // ReactFlowの初期化を遅延実行
      const timeoutId = setTimeout(() => {
        setIsReactFlowReady(true); // ReactFlowの初期化を開始
      }, 500); // 適切な遅延時間を調整（500msなど）

      return () => clearTimeout(timeoutId); // クリーンアップ
    } else {
      setIsReactFlowReady(false); // ReactFlow表示をリセット
    }
  }, [viewMode]);

  return (
    <>
      <Head>
        <title>
          {partnerData?.managed_partner_id} | パートナーユーザー - PartnerProp
        </title>
      </Head>

      <Group position="right" mb="md" ml="md" style={{ marginRight: "16px" }}>
        <SegmentedControl
          data={[
            {
              value: "USERTABLE",
              label: (
                <span style={{ display: "flex", alignItems: "center" }}>
                  <RiListCheck />
                </span>
              ),
            },
            {
              value: "ORGCHART",
              label: (
                <span style={{ display: "flex", alignItems: "center" }}>
                  <RiOrganizationChart />
                </span>
              ),
            },
          ]}
          value={viewMode}
          onChange={handleChangeView}
        />
      </Group>

      {partnerData ? (
        viewMode === "USERTABLE" ? (
          <UserTable />
        ) : isReactFlowReady ? (
          <OrganizationChart /> // ReactFlowの初期化後に表示
        ) : (
          <Skeleton /> // 初期化中のスケルトン表示
        )
      ) : (
        <>データ取得中</>
      )}
    </>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-columns: 400px 1fr;
  .tab {
    &-root {
      height: 100%;
      display: grid;
      grid-template-rows: auto 1fr;
    }
    &-content {
      height: 100%;
    }
  }
  .lead {
    &-main {
      &-content {
        padding: 16px 16px 16px 0;
        height: 100%;
      }
    }
    &-sidebar {
      width: 400px;
      padding: 16px;
      display: grid;
      grid-template-rows: auto 1fr;
      grid-gap: 16px;
      &-content {
        width: 100%;
        font-size: 14px;
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 5px;
        background-color: ${propcolors.white};
        padding: 16px;
        &-products {
          &-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
          }
        }
      }
      &-id {
      }
      &-client {
        &_name {
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }
`;

export const PartnerUsers: React.FC<{ managed_partner_id: number }> = ({
  managed_partner_id,
}) => {
  const partnerData = usePartnerProps();
  const setPartnerProps = useSetPartnerProps();

  const fetchPartnerDetail = useCallback(
    (reset: boolean) => {
      if (reset) {
        setPartnerProps(null);
      }
      ax.get(`/api/v1/managed_partner/${managed_partner_id}`).then(
        (response) => {
          setPartnerProps(response.data);
        }
      );
    },
    [setPartnerProps, managed_partner_id]
  );

  useEffect(() => {
    if (!partnerData) {
      fetchPartnerDetail(false);
    }
  }, [partnerData]);

  return (
    <>
      {partnerData ? (
        <PartnerDetailLayout
          managedPartnerId={managed_partner_id}
          partnerData={partnerData}
        >
          <Styled partnerData={partnerData} />
        </PartnerDetailLayout>
      ) : (
        <Skeleton />
      )}
    </>
  );
};
