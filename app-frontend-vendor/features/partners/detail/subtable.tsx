import styled from "@emotion/styled";
import Head from "next/head";
import { propcolors } from "../../../styles/colors";
import {
  usePartnerProps,
  useSetPartnerProps,
} from "utils/recoil/partner/partnerState";
import { PartnerDetailLayout } from "./layout";
import { ax } from "utils/axios";
import { useCallback, useEffect, useState } from "react";
import { SubTable } from "./components/subTable";
import { Center, Box, Skeleton } from "@mantine/core";

type PresentationProps = {
  className?: string;
  partnerData: Partner | null;
  record: SubtableRecord | null;
};

const Presentation: React.FC<PresentationProps> = ({ 
  record, 
  partnerData,
}) => {
  return (
    <>
      <Head>
        <title>
          {partnerData?.managed_partner_id} | パートナーユーザー - PartnerProp
        </title>
      </Head>
      {record ? <SubTable records={record} /> : 
        <Center h={500} >
          <Box>データ取得中</Box>
        </Center>
      }
    </>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-columns: 400px 1fr;
  .tab {
    &-root {
      height: 100%;
      display: grid;
      grid-template-rows: auto 1fr;
    }
    &-content {
      height: 100%;
    }
  }
  .lead {
    &-main {
      &-content {
        padding: 16px 16px 16px 0;
        height: 100%;
      }
    }
    &-sidebar {
      width: 400px;
      padding: 16px;
      display: grid;
      grid-template-rows: auto 1fr;
      grid-gap: 16px;
      &-content {
        width: 100%;
        font-size: 14px;
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 5px;
        background-color: ${propcolors.white};
        padding: 16px;
        &-products {
          &-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
          }
        }
      }
      &-id {
      }
      &-client {
        &_name {
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }
`;

export const PartnerSubtable : React.FC<{ managed_partner_id: number, subtable_id: number }> = ({
  managed_partner_id,
  subtable_id
}) => {
  const partnerData = usePartnerProps();
  const setPartnerData = useSetPartnerProps();
  const [subtableData, setSubtableData] = useState<SubtableRecord | null>(null);

  const fetchPartnerDetail = useCallback(
    (reset: boolean) => {
      if (reset) {
        setPartnerData(null);
      }
      ax.get(`/api/v1/managed_partner/${managed_partner_id}`).then((response) => {
        setPartnerData(response.data);
      });
    },
    [setPartnerData, managed_partner_id]
  );

  useEffect(() => {
    if (managed_partner_id && subtable_id) {
      if (partnerData === null) {
        fetchPartnerDetail(false);
      }
      ax.get(
        `/api/v1/managed_partner_sub_table/${subtable_id}/managed_partners/${managed_partner_id}`
      ).then((res) => setSubtableData(res.data));
    }
  }, [managed_partner_id, subtable_id]);

  return (
    <>
    {partnerData ? (
      <PartnerDetailLayout
      managedPartnerId={managed_partner_id}
      partnerData={partnerData}
    >
      <Styled record={subtableData} partnerData={partnerData} />
    </PartnerDetailLayout>
    ) : (
      <Skeleton />
    )}
    </>
  );
};

