import styled from "@emotion/styled";
import { But<PERSON>, Text } from "@mantine/core";
import { modals } from "@mantine/modals";
import { notifications } from "@mantine/notifications";
import { CustomBreadcrumb } from "components/breadcrumb";
import { NavigationLink } from "components/link";
import { Skeleton } from "components/Skeleton";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/router";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconPartner from "public/icons/icon-partner-big.svg";
import IconUserPartner from "public/icons/user-icon.svg";
import { useCallback, useEffect, useState } from "react";
import { ax } from "utils/axios";
import { getMasters } from "utils/axios/getMasters";
import { getLinkStatusModifier } from "utils/constants/linkStatus";
import { useIsMobileByUA } from "utils/hooks/useIsMobile";
import { useGetDataMasters, useSetDataMasters } from "utils/recoil/dataMasters";
import {
  useChildPartnersState,
  useSetChildPartnersState,
} from "utils/recoil/partner/childPartnersListState";
import { useSetPartnerList } from "utils/recoil/partner/partnerListState";
import { usePartnerProps } from "utils/recoil/partner/partnerState";
import { useSetPartnerUserList } from "utils/recoil/partner/partnerUserListState";
import {
  useSetVendorUserList,
  useVendorUserList,
} from "utils/recoil/vendorUserListState";
import { getApiErrorMessage } from "utils/values/errorMessages";
import { propcolors } from "../../../styles/colors";
import BreadcrumbRender from "../components/breadcrumb/BreadcrumbRender";
import { Collaboration } from "./components/collaboration";
import { EditButton } from "./components/generalInfo/components/partnerItem/components/EditButton";
import { SearchPartnerCandidates } from "./components/generalInfo/components/partnerItem/components/SearchPartnerCandidates";
import { SearchUser } from "./components/generalInfo/components/partnerItem/components/SearchUser";
import { MemberAddRequestButton } from "./components/MemberAddRequestButton";
import { PartnerDetailMobileTabView } from "./components/partnerDetailMobileTabView/partnerDetailMobileTabView";
import { VendorReviewConfirm } from "./components/vendorReviewConfirm";

type PresentationProps = {
  className?: string;
  partnerData: Partner | null;
  dataMasters: DataMasters | null;
  children: React.ReactNode;
  deleteDetail: () => Promise<void>;
  vendorUserList: VendorUser[] | null;
  subtableList: Subtable[] | null;
  childPartnerList: ChildParterInfo[] | null;
  parentCandidateList: ParentPartnerCandidate[] | null;
};

type partnerDetailProps = {
  managedPartnerId: number;
  partnerData: Partner | null;
  children: React.ReactNode;
};
const Presentation: React.FC<PresentationProps> = ({
  dataMasters,
  className,
  partnerData,
  children,
  vendorUserList,
  deleteDetail,
  subtableList,
  childPartnerList,
  parentCandidateList,
}) => {
  const isMobile = useIsMobileByUA();
  const statusModifier = getLinkStatusModifier();
  const [isEditing, setIsEditing] = useState(false);
  const toggleEditing = () => {
    setIsEditing(!isEditing);
  };
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const toggleEditingUsername = () => {
    setIsEditingUsername(!isEditingUsername);
  };
  const [isEditingPartnerCandidate, setIsEditingPartnerCandidate] =
    useState(false);
  const toggleEditingPartnerCandidate = () => {
    setIsEditingPartnerCandidate(!isEditingPartnerCandidate);
  };

  return (
    <>
      {partnerData && dataMasters ? (
        isMobile ? (
          <PartnerDetailMobileTabView
            deleteDetail={deleteDetail}
            partnerData={partnerData}
            vendorUserList={vendorUserList}
            parentCandidateList={parentCandidateList}
            dataMasters={dataMasters}
            subtableList={subtableList}
          />
        ) : (
          <div className="partner-layout">
            <HeaderTitle>
              <header className="commonDetail-header">
                <CustomBreadcrumb
                  title="パートナー"
                  list={[
                    { title: "パートナー一覧", href: "/partners" },
                    { title: partnerData.managed_partner_name },
                  ]}
                />
              </header>
            </HeaderTitle>
            <main className={className}>
              <section className="main-content">
                <BreadcrumbRender
                  contractStatus={partnerData.contract_status}
                />
                <header>
                  <IconPartner className="imgSpan" />
                  <div className="company-information">
                    <p className="company-information-title_id">
                      <span className="company-information-title_id_heading">
                        パートナーID
                      </span>
                      {partnerData.managed_partner_hex_id}
                    </p>
                    <h2 className="company-information-title_name">
                      {partnerData.managed_partner_name}
                    </h2>
                  </div>

                  <div className="header-buttons">
                    {partnerData.link_status === "LINK_ACTIVE" && (
                      <MemberAddRequestButton
                        managedPartnerId={partnerData.managed_partner_id}
                      />
                    )}
                    <Button
                      variant="default"
                      onClick={deleteDetail}
                      className="del-partner"
                    >
                      パートナーの削除
                    </Button>
                  </div>
                </header>

                <nav className="main-content-links">
                  {/* <p className="sidebar-information-title_id">
              <span className="sidebar-information-title_id_heading">
                レコードID
              </span>
              {partnerData.managed_partner_hex_id}
            </p>
            <p className="sidebar-information-title_name">
              {partnerData.managed_partner_name}
            </p> */}

                  <NavigationLink
                    href={`/partners/${partnerData.managed_partner_id}`}
                  >
                    <span>基本情報</span>
                  </NavigationLink>
                  {partnerData.link_status === "LINK_ACTIVE" ||
                  partnerData.link_status === "LINK_HOLD" ? (
                    <>
                      <NavigationLink
                        href={`/partners/${partnerData.managed_partner_id}/users`}
                      >
                        <span>ユーザー</span>
                      </NavigationLink>
                      <NavigationLink
                        href={`/partners/${partnerData.managed_partner_id}/giftHistories`}
                      >
                        <span>ギフト履歴</span>
                      </NavigationLink>
                    </>
                  ) : undefined}
                  {childPartnerList && childPartnerList.length > 0 && (
                    <NavigationLink
                      href={`/partners/${partnerData.managed_partner_id}/relatedPartners`}
                    >
                      <span>関連パートナー一覧</span>
                    </NavigationLink>
                  )}
                  {subtableList?.map((subtable) => (
                    <NavigationLink
                      key={subtable.sub_table_id}
                      href={`/partners/${partnerData.managed_partner_id}/subtable/${subtable.sub_table_id}`}
                    >
                      <span>{subtable.sub_table_name}</span>
                    </NavigationLink>
                  ))}
                </nav>
                {children}
              </section>
              <section className="sidebar">
                <section className="sidebar-information left">
                  <section className="sidebar-information-title_top">
                    パートナー
                  </section>
                  <section className="sidebar-information-title">
                    {partnerData.url && (
                      <p className="sidebar-information-url">
                        <span className="sidebar-information-url_heading">
                          URL
                        </span>
                        <Link
                          href={partnerData.url}
                          target="_blank"
                          className="sidebar-information-title_url"
                          style={{ width: "100%", wordBreak: "break-all" }}
                        >
                          {partnerData.url}
                        </Link>
                      </p>
                    )}
                    <p className="sidebar-information-title_status_heading">
                      ベンダー担当者
                    </p>
                    <div className="sidebar-information-title_status">
                      {!isEditingUsername &&
                        (partnerData.vendor_user_id ? (
                          <p className="sidebar-information-title_status_name">
                            <div className="infomation-partner">
                              {partnerData?.vendor_avatar_url ? (
                                <Image
                                  src={partnerData.vendor_avatar_url}
                                  style={{
                                    borderRadius: "40px",
                                    marginRight: "1rem",
                                  }}
                                  width={40}
                                  height={40}
                                  alt={partnerData?.vendor_user_name || ""}
                                />
                              ) : (
                                <IconUserPartner className="imgSpan" />
                              )}
                              <p className="right-name">
                                {partnerData.vendor_user_name
                                  ? partnerData.vendor_user_name
                                  : partnerData.vendor_user_id}
                              </p>
                            </div>
                          </p>
                        ) : (
                          <p className="sidebar-information-title_status_name">
                            <div className="infomation-partner">
                              <IconUserPartner className="imgSpan" />
                              <p className="right-name">未設定</p>
                            </div>
                          </p>
                        ))}

                      <div
                        className={
                          isEditingUsername ? "right-icon-full" : "right-icon"
                        }
                      >
                        <SearchUser
                          title={
                            partnerData.vendor_user_name
                              ? partnerData.vendor_user_name
                              : "未設定"
                          }
                          current={
                            partnerData.vendor_user_id
                              ? partnerData.vendor_user_id
                              : -1
                          }
                          userList={vendorUserList}
                          onBlur={toggleEditingUsername}
                          isEditing={isEditingUsername}
                          toggleEditing={toggleEditingUsername}
                        />
                      </div>
                    </div>

                    {partnerData.is_parent !== 1 ? (
                      <>
                        <p className="sidebar-information-title_status_heading">
                          関連パートナー
                        </p>
                        <div className="sidebar-information-title_status">
                          {!isEditingPartnerCandidate &&
                            (partnerData.parent_partner_id !== null ? (
                              <p className="sidebar-information-title_status_name">
                                <span>
                                  {partnerData.parent_partner_name
                                    ? partnerData.parent_partner_name
                                    : ""}
                                </span>
                              </p>
                            ) : (
                              <p className="sidebar-information-title_status_name">
                                <span>未設定</span>
                              </p>
                            ))}
                          <div
                            className={
                              isEditingPartnerCandidate
                                ? "right-icon-full"
                                : "right-icon"
                            }
                          >
                            <SearchPartnerCandidates
                              title={
                                partnerData.parent_partner_name
                                  ? partnerData.parent_partner_name
                                  : "未設定"
                              }
                              current={
                                partnerData.parent_partner_id
                                  ? partnerData.parent_partner_id
                                  : -1
                              }
                              partnerList={parentCandidateList}
                              onBlur={toggleEditingPartnerCandidate}
                              isEditing={isEditingPartnerCandidate}
                              toggleEditing={toggleEditingPartnerCandidate}
                            />
                          </div>
                        </div>
                      </>
                    ) : null}
                  </section>
                  <div className="sidebar-line"></div>
                  <section className="sidebar-information-content no-backdrop">
                    <p className="sidebar-information-content_card_heading">
                      連携ステータス
                    </p>

                    {partnerData.link_status === "LINK_VENDOR_REVIEW" ? (
                      <VendorReviewConfirm partnerData={partnerData} />
                    ) : (
                      <div className="sidebar-information-content_card_body">
                        <p
                          className="sidebar-information-content_card_message-top"
                          style={{ margin: "auto 0" }}
                        >
                          {partnerData.link_status ? (
                            <span className="message">
                              {statusModifier[partnerData.link_status].message}
                            </span>
                          ) : (
                            <span>N/A</span>
                          )}
                        </p>
                        <div className="sidebar-information-content_card_buttons">
                          <Collaboration partner={partnerData} />
                        </div>
                      </div>
                    )}

                    {partnerData.link_status === "LINK_REQUEST" && (
                      <>
                        <p className="sidebar-information-content_card_heading">
                          連携依頼先
                        </p>
                        <div className="sidebar-information-content_card_body">
                          <p className="sidebar-information-content_card_status">
                            <span>{partnerData.request_sent_email}</span>
                          </p>
                        </div>
                      </>
                    )}
                    <p className="sidebar-information-content_card_heading">
                      契約ステータス
                    </p>
                    <div className="">
                      <p className="sidebar-information-content_card_status">
                        {!isEditing &&
                          (partnerData.contract_status_name ? (
                            <span>{partnerData.contract_status_name}</span>
                          ) : (
                            <span>N/A</span>
                          ))}
                        <EditButton
                          title={`契約ステータス：${partnerData.contract_status_name}`}
                          type={"select"}
                          current={partnerData.contract_status}
                          DBlabel={"contract_status"}
                          onBlur={toggleEditing}
                          isEditing={isEditing}
                          toggleEditing={toggleEditing}
                          value={[
                            "CONTRACT_PENDING",
                            "CONTRACT_NEGOTIATION",
                            "CONTRACT_SIGNING",
                            "CONTRACT_ACTIVE",
                            "CONTRACT_CANCEL",
                          ]}
                          placeholder={[
                            "未対応",
                            "契約交渉中",
                            "契約締結中",
                            "契約済み",
                            "解約",
                          ]}
                        />
                      </p>
                    </div>
                  </section>
                </section>
              </section>
            </main>
          </div>
        )
      ) : (
        <Skeleton />
      )}
    </>
  );
};

const HeaderTitle = styled.div`
  .commonDetail-header {
    border-top: 1px solid ${propcolors.gray[200]};
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding: 16px 1.8rem;
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    -webkit-align-items: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
  .commonDetail-header svg {
    margin: 0 10px;
  }
  .vertical-line {
    display: inline-block;
    width: 1px;
    height: 24px;
    background-color: ${propcolors.gray[200]};
    margin: 0 24px;
    vertical-align: middle;
  }
`;

const Styled = styled(Presentation)`
  height: calc(100vh - 140px);
  display: grid;
  grid-template-columns: 1fr 305px;
  padding: 0 1rem 1rem 0rem;
  gap: 0;
  header {
    display: flex;
    align-items: center;
    padding: 20px 1.8rem;
  }
  .infomation-partner {
    display: flex;
  }
  .imgSpan {
    flex: 0 0 auto;
    margin-right: 1rem;
  }
  .right-name {
    line-height: 2.5rem;
    font-weight: 400;
    font-size: 14px;
    color: ${propcolors.blackLight};
  }
  .right-icon {
    margin-right: 0;
  }
  .right-icon-full {
    width: 100%;
    margin-right: 0;
  }
  .company-information {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
  }
  .company-information-title_id {
    font-size: 14px;
    font-weight: 400;
    color: ${propcolors.blackLightLabel};
  }
  .company-information-title_id_heading {
    font-size: 14px;
    margin-bottom: 5px;
    color: ${propcolors.blackLightLabel};
    margin-right: 8px;
  }
  .company-information-title_name {
    font-size: 24px;
    font-weight: 400;
    color: ${propcolors.blackLight};
  }

  .header-buttons {
    display: flex;
  }
  .del-partner {
    color: ${propcolors.orangeDel};
    border: 1px solid ${propcolors.orangeDel};
    height: 36px;
    width: 132px;
    font-size: 14px;
    font-weight: 400;
    padding: 0;
    border-radius: 8px;
    font-family: "Inter", "system-ui";
    @media screen and (max-width: 512px) {
      grid-template-columns: 1fr;
      grid-template-rows: auto 1fr;
      overflow-y: scroll;
      height: 100%;
      width: 100%;
      margin: 0;
      padding: 0;
    }
  }
  .tab {
    &-root {
      height: 100%;
      display: grid;
      grid-template-rows: auto 1fr;
    }
    &-content {
      height: 100%;
    }
  }
  .main-content {
    width: 100%;
    padding-top: 1rem;
    overflow-x: scroll;
  }
  .sidebar {
    width: 320px;
    height: calc(100vh - 160px);
    overflow: auto;
    display: grid;
    grid-template-rows: auto 1fr;
    border-left: 1px solid ${propcolors.gray[200]};
    @media screen and (max-width: 512px) {
      width: 100vw;
    }
    .prop-style {
      &_card {
        &.left {
          border-radius: 0.5rem !important;
          border-left: 0;
        }
      }
    }
    &-line {
      border-top: 1px solid ${propcolors.gray[200]};
      margin: 0 25px;
    }
    &-information {
      color: ${propcolors.blackLightLabel};
      padding: 0;
      &-url_heading {
        font-weight: 400;
        font-size: 12px;
        color: ${propcolors.blackLightLabel};
      }
      &-title {
        padding: 25px;
        display: grid;
        grid-template-rows: auto auto auto;
        &_id {
          font-size: 20px;
          margin-bottom: 20px;
          font-weight: bold;
          &_heading {
            margin-right: 36px;
          }
        }
        &_top {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.7rem 25px 0.7rem 25px;
          background-color: ${propcolors.greyBreadcrumb};
          color: ${propcolors.blackLightLabel};
          font-weight: 400;
          font-size: 14px;
          button {
            display: inline-block;
            width: auto;
            text-align: center;
          }
        }
        &_name {
          font-weight: bold;
          margin-bottom: 1rem;
          font-size: 1.5rem;
        }
        &_url {
          font-weight: 400;
          font-size: 14px;
          color: ${propcolors.blackLight};
          display: inline-block;
          width: min-content;
          text-decoration: underline;
          margin-top: 1rem;
        }
        &_status {
          display: flex;
          align-items: center;
          &_heading {
            margin-top: 20px;
            margin-bottom: 16px;
            font-weight: 400;
            font-size: 12px;
            color: ${propcolors.blackLightLabel};
          }
          &_name {
            margin-right: 10px;
            width: 100%;
            font-weight: 400;
            font-size: 14px;
            color: ${propcolors.blackLight};
          }
        }
      }
      &-content {
        padding: 25px;
        &_card {
          &_heading {
            font-size: 12px;
            font-weight: 400;
            margin-bottom: 16px;
            color: ${propcolors.blackLightLabel};
          }
          &_body {
            display: flex;
            justify-content: space-between;
            font-size: 1rem;
            font-weight: bold;
            margin-bottom: 25px;
            &:last-child {
              margin-bottom: 16px;
            }

            > button {
              width: 104px;
              font-size: 14px;
              font-weight: 400;
              height: 32px;
              padding: 0;
              border-radius: 6px;
              background-color: ${propcolors.blackLight};
            }
          }
          &_message-top {
            margin-top: 6px;
            color: ${propcolors.blackLight};
            font-weight: 400;
            font-size: 16px;
          }
          &_status {
            display: flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            span {
              width: 100%;
              color: ${propcolors.blackLight};
              font-weight: 400;
              font-size: 16px;
            }
          }
          &.vendor-user {
            grid-column: 1 / 3;
          }
        }
      }
    }
    &-products {
      &_heading {
        padding: 0.7rem 25px 0.7rem 25px;
        background-color: ${propcolors.greyBreadcrumb};
        color: ${propcolors.blackLightLabel};
        border-top: 1px solid ${propcolors.gray[200]};
        font-weight: 400;
        font-size: 14px;
      }
      &-notfound {
        padding: 25px;
        font-size: 14px;
        font-weight: 400;
        color: ${propcolors.blackLight};
      }
    }
    &-status {
      gap: 64px;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid ${propcolors.gray[200]};
      .partner-delete {
        display: flex;
        margin-top: 20px;
        align-items: center;
        gap: 8px;
        padding: 0;
        border: 0;
        width: auto;
        > span {
          width: max-content;
          font-size: 20px;
          color: ${propcolors.partnerRed};
        }
        > svg {
          width: 20px;
          fill: ${propcolors.partnerRed};
        }
      }
      &-link {
        &_heading {
          margin-bottom: 0.5rem;
          font-size: 14px;
          font-weight: 600;
        }
        &_content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 1rem;
          button {
            width: 220px;
            height: 100%;
            font-size: 0.875rem;
          }
        }
      }
      &-contract {
        &_heading {
          margin-top: 1rem;
          margin-bottom: 0.5rem;
          font-size: 14px;
          font-weight: bold;
        }
        &_content {
          display: flex;
          font-size: 1rem;
          height: 33px;
          > button {
            border: 0px;
          }
        }
        .partner {
          &-main {
            &-content {
              height: 100%;
              &-links {
                border-bottom: 1px solid ${propcolors.gray[200]};
                @media screen and (max-width: 512px) {
                  width: 100vw;
                  overflow-x: scroll;
                  display: flex;
                }
                a {
                  display: inline-block;
                  padding: 0 12px 12px 12px;
                  @media screen and (max-width: 512px) {
                    padding: 12px;
                    white-space: nowrap;
                  }
                }
              }
            }
          }
        }
      }
      &-content {
        width: 100%;
        font-size: 0.875rem;
        border: 1px solid ${propcolors.gray[200]};
        border-radius: 5px;
        background-color: ${propcolors.white};
        overflow: hidden;
        &-title {
          background: linear-gradient(
            to bottom,
            ${propcolors.gray[900]},
            ${propcolors.black}
          );
          color: ${propcolors.white};
          padding: 1rem;
        }
        &-products {
          &-notfound {
            height: 100%;
            display: flex;
            flex-flow: column;
            justify-content: center;
            align-items: center;
            > h3 {
              margin-bottom: 8px;
            }
          }
        }
      }
      &-id {
      }
      &-client {
        &_id {
          font-size: 0.875rem;
          margin-bottom: 0.5rem;
          &_heading {
            margin-right: 0.875rem;
          }
        }
        &_name {
          font-size: 1rem;
          font-weight: bold;
          margin-bottom: 0.5rem;
        }
        &_address,
        &_url {
          display: block;
          font-size: 0.875rem;
        }
        &_user {
          margin-top: 0.5rem;
          font-size: 1rem;
          &_heading {
            font-weight: bold;
            font-size: 0.875rem;
          }
          &_content {
            display: flex;
          }
        }
        &_memo {
          padding-top: 8px;
          margin-top: 8px;
          border-top: 2px dotted ${propcolors.gray[200]};
        }
      }
    }
  }
  .main-content-links {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    border-bottom: 1px solid ${propcolors.gray[200]};
    margin-bottom: 18px;
    padding: 0 1.8rem;
    font-weight: 600;
    font-size: 14px;
    overflow-x: auto;
    white-space: nowrap;

    &::-webkit-scrollbar {
      height: 8px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 3px;
    }
    a {
      display: inline-block;
      text-align: center;
      color: ${propcolors.blackLight};
      white-space: nowrap;
      padding: 1rem 2rem;
      position: relative;
      span {
        font-weight: 400;
        font-size: 14px;
        white-space: nowrap;
      }
    }
  }
`;

const ModalTitle = styled.div`
  width: 100%;
  text-align: center;
  border-bottom: 1px solid ${propcolors.gray[200]};
  padding-bottom: 1rem;
  top: 1.3rem;
  position: absolute;
  margin: 0 -1rem;
  font-size: 16px;
  font-weight: 600;
  color: ${propcolors.blackLight};
`;

const ModalContent = styled.div`
  padding: 10px;
  .title-confirm {
    font-size: 18px;
    font-weight: 600;
    color: ${propcolors.blackLight};
    margin: 25px 0 20px 0;
  }
  .description {
    font-size: 12px;
    font-weight: 300;
    color: var(--Semantic-TEXT_BLACK, #23221e);
  }
`;

export const PartnerDetailLayout: React.FC<partnerDetailProps> = ({
  children,
  managedPartnerId,
  partnerData,
}) => {
  const vendorUserList = useVendorUserList();
  const setVendorUserList = useSetVendorUserList();
  const [subtableList, setSubtableList] = useState<Subtable[] | null>(null);
  const childPartnerList = useChildPartnersState();
  const setChildPartnerList = useSetChildPartnersState();
  const setPartnerList = useSetPartnerList();
  const partnerProps = usePartnerProps();
  const { push } = useRouter();
  const [parentPartnerCandidateList, setParentPartnerCandidateList] = useState<
    ParentPartnerCandidate[] | null
  >(null);
  const setPartnerUserList = useSetPartnerUserList();

  const dataMasters = useGetDataMasters();
  const setDataMasters = useSetDataMasters();

  const fetchMasters = useCallback(() => {
    getMasters().then((res) => {
      setDataMasters({
        prefectures: res.prefectures,
        negotiation: res.negotiation,
        contract: res.contract,
        link: res.link,
        lead: res.lead,
        column_permissions: res.column_permissions,
      });
    });
  }, [setDataMasters]);

  useEffect(() => {
    if (dataMasters === null) {
      fetchMasters();
    }
  }, [dataMasters, fetchMasters]);

  const fetchVendorUserList = useCallback(() => {
    ax.get(`api/v1/vendor_users`).then((response) => {
      setVendorUserList(response.data);
    });
  }, [setVendorUserList]);

  const fetchSubtableList = useCallback(() => {
    ax.get(`api/v1/managed_partner_sub_table`).then((response) => {
      setSubtableList(response.data);
    });
  }, []);

  const fetchRelatedPartnersList = useCallback(() => {
    ax.get(`api/v1/managed_partner/${managedPartnerId}/relations`).then(
      (response) => {
        const relatedPartners = response.data.child_partners;
        // 自分自身が関連パートナーとして表出しないようフィルター処理する
        const filteredPartners = relatedPartners.filter(
          (partner: ChildParterInfo) =>
            partner.managed_partner_id !== managedPartnerId,
        );
        setChildPartnerList(filteredPartners);
      },
    );
  }, [managedPartnerId, setChildPartnerList]);

  const fetchParentPartnersCandidatesList = useCallback(() => {
    ax.get(
      `api/v1/managed_partner/${managedPartnerId}/parent_partner_candidates`,
    ).then((response) => {
      setParentPartnerCandidateList(response.data.parent_candidates);
    });
  }, [managedPartnerId]);

  useEffect(() => {
    if (subtableList === null) {
      fetchSubtableList();
    }
  }, [fetchSubtableList, subtableList]);

  useEffect(() => {
    if (partnerData?.is_parent === 1) {
      // 親パートナーの場合
      fetchRelatedPartnersList();
      setParentPartnerCandidateList(null);
    } else {
      setChildPartnerList(null);
      fetchParentPartnersCandidatesList();
    }
  }, [
    partnerData?.is_parent,
    fetchRelatedPartnersList,
    fetchParentPartnersCandidatesList,
    setChildPartnerList,
  ]);

  useEffect(() => {
    if (vendorUserList === null) {
      fetchVendorUserList();
    }
  }, [vendorUserList, fetchVendorUserList]);

  const fetchPartnerUserList = useCallback(() => {
    if (
      partnerProps?.link_status === "LINK_ACTIVE" ||
      partnerProps?.link_status === "LINK_VENDOR_REVIEW"
    ) {
      ax.get(`/api/v1/managed_partners/${managedPartnerId}/member`)
        .then((res) => {
          setPartnerUserList(res.data);
        })
        .catch(() => {
          setPartnerUserList(null);
        });
    } else {
      setPartnerUserList(null);
    }
  }, [managedPartnerId, setPartnerUserList, partnerProps?.link_status]);

  useEffect(() => {
    fetchPartnerUserList();
  }, [fetchPartnerUserList]);

  const deleteDetail = async () => {
    if (partnerProps?.link_status === "LINK_PENDING") {
      modals.openConfirmModal({
        title: <ModalTitle>パートナーの削除</ModalTitle>,
        size: "640px",
        closeButtonProps: { size: "24px" },
        children: (
          <ModalContent>
            <Text className="title-confirm">パートナーを削除しますか？</Text>
            <Text className="description">
              変更は取り消すことが出来ません。本当にこのパートナーを削除しますか？
            </Text>
          </ModalContent>
        ),
        labels: {
          confirm: "削除する",
          cancel: "キャンセル",
        },
        onConfirm: () => {
          ax.delete(`/api/v1/managed_partner/${managedPartnerId}`)
            .then(() => {
              notifications.show({
                title: "パートナーが削除されました",
                message: "",
                icon: <IconNotiSuccess />,
              });
              setPartnerList(null);
              push("/partners");
            })
            .catch((err) => {
              notifications.show({
                title: "パートナー削除に失敗しました",
                message: getApiErrorMessage(err.response.data.message),
                icon: <IconNotiFailed />,
              });
            });
        },
        confirmProps: {
          sx: {
            width: "284px",
            height: "42px",
            right: "10px",
            fontSize: "14px",
            fontWeight: 600,
            marginBottom: "10px",
            borderRadius: "8px",
            backgroundColor: `${propcolors.black}`,
            color: `${propcolors.white}`,
            "&:hover": {
              backgroundColor: `${propcolors.black}`,
            },
          },
        },
        cancelProps: {
          variant: "outline",
          sx: {
            width: "284px",
            height: "42px",
            left: "25px",
            position: "absolute",
            fontSize: "14px",
            fontWeight: 600,
            marginBottom: "10px",
            borderRadius: "8px",
            borderColor: `${propcolors.greyDefault}`,
            backgroundColor: `${propcolors.greyDefault}`,
            color: `${propcolors.white}`,
            "&:hover": {
              backgroundColor: `${propcolors.greyDefault}`,
            },
          },
        },
      });
    } else {
      notifications.show({
        title: "パートナーを削除できません",
        message: "削除可能なのは未連携のパートナーのみです。",
        icon: <IconNotiFailed />,
      });
    }
  };

  return (
    <Styled
      dataMasters={dataMasters}
      partnerData={partnerData}
      deleteDetail={deleteDetail}
      vendorUserList={vendorUserList}
      subtableList={subtableList}
      childPartnerList={childPartnerList}
      parentCandidateList={parentPartnerCandidateList}
    >
      {children}
    </Styled>
  );
};
