import Image from "next/image";
import IconUserVendor from "public/icons/user-icon.svg";

interface VendorUserNameProp {
  userName: string;
  thumbnail?: string | null;
}

interface CellVendorUserNameRenderProp {
  value: VendorUserNameProp;
}

const CellVendorUserNameRender: React.FC<CellVendorUserNameRenderProp> = (
  params,
) => {
  const { userName, thumbnail } = params.value;

  return (
    <div className="cell-vendor-user-name-render">
      {userName && (
        <>
          <div className="cell-vendor-user-name-render-image">
            {thumbnail ? (
              <Image src={thumbnail} style={{borderRadius: '40px'}} width={40} height={40} alt={userName} />
            ) : (
              <IconUserVendor className="imgSpan" />
            )}
          </div>
          <div className="cell-vendor-user-name-render-text">{userName}</div>
        </>
      )}
    </div>
  );
};

export default CellVendorUserNameRender;
