import { useMemo } from "react";
import { Cell } from "components/data-table/cell";
import { Checkbox } from "@mantine/core";
import { PartnerDataTableArg } from "../../type/table";
export const useCheckBoxDeleteDef = (
  handleClickCheckAll: () => void
): PartnerDataTableArg["columns"] => {
  return useMemo(
    () => [
      {
        accessorKey: "check_box_delete",

        header: (params) => {
          const isAllSelected = params.table.getIsAllRowsSelected();
          const isSomeSelected = params.table.getIsSomeRowsSelected();

          return (
            <div
              css={{
                color: "#666666",
                display: "flex",
                fontSize: "12px",
                fontWeight: "600",
                height: "100%",
                paddingLeft: "24px",
              }}
            >
              <div
                css={{
                  alignItems: "center",
                  display: "flex",
                  flex: 1,
                  justifyContent: "space-between",
                  minWidth: 0,
                  ":hover": {
                    "button.filter-button": {
                      opacity: 1,
                    },
                  },
                }}
              >
                <Checkbox
                  checked={isAllSelected}
                  indeterminate={isSomeSelected}
                  onChange={(event) => handleClickCheckAll()}
                  aria-label="Select all"
                />
              </div>
              <div
                {...{
                  onDoubleClick: () => params.header.column.resetSize(),
                  onMouseDown: params.header.getResizeHandler(),
                  onTouchStart: params.header.getResizeHandler(),
                  css: {
                    cursor: "col-resize",
                    position: "relative",
                    touchAction: "none",
                    userSelect: "none",
                    width: "8px",
                    "::after": {
                      backgroundColor: "#dde2eb",
                      content: '""',
                      height: "30%",
                      position: "absolute",
                      display: "block",
                      right: "calc(50% - 0.5px)",
                      top: "35%",
                      width: "1px",
                      zIndex: 1,
                    },
                  },
                }}
              />
            </div>
          );
        },
        aggregatedCell: () => null,
        size: 120,
        cell: (params) => {
          const clickItem = () => {
            params.row.toggleSelected(!params.row.getIsSelected());
          };
          return (
            <Cell>
              <div
                css={{
                  alignItems: "center",
                  display: "flex",
                  gap: "16px",
                  width: "100%",
                }}
              >
                <Checkbox
                  onChange={(checked) => clickItem()}
                  checked={params.row.getIsSelected()}
                />
              </div>
            </Cell>
          );
        },
      },
    ],
    [handleClickCheckAll]
  );
};
