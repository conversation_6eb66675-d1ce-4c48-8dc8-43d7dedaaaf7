import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { usePrefectures, useContractStatus, useLinkStatus } from "../master";
import { PartnerDataTableArg } from "../../type/table";
import { Head } from "components/data-table/head";
import { Cell } from "components/data-table/cell";
import * as Icon from "components/icons";
import Image from "next/image";
import { useVendorUsers } from "../vendor";
import {
  ellipsisStyle,
  EllipsisText,
} from "components/ellipsis-text/EllipsisText";
import { ToggleEdit } from "components/data-table/toggle-edit";
import Link from "next/link";
import { ax } from "utils/axios";
import { Select } from "@mantine/core";
import {
  KeepEditArea,
  EditableSelectCell,
  EditableTextCell,
  EditableNumberCell,
} from "components/data-table/cell/editable";
import { RiCloseCircleLine } from "@remixicon/react";
import { notifications } from "@mantine/notifications";
import { revalidateFilteredPartners } from "../partner";
import * as Sentry from "@sentry/nextjs";
import { useSetPartnerProps } from "utils/recoil/partner/partnerState";

const updateData = async ({
  partnerId,
  columnId,
  value,
}: {
  partnerId: string;
  columnId: string;
  value: string | number | null;
}) => {
  const { data: prev } = await ax.get<Partner>(
    `/api/v1/managed_partner/${partnerId}`
  );
  try {
    await ax.put(`/api/v1/managed_partner/${partnerId}`, {
      ...prev,
      [columnId]: value,
    });
  } catch (error) {
    notifications.show({
      title: "エラー",
      message: "更新に失敗しました",
      icon: <RiCloseCircleLine css={{ color: "#f93832" }} />,
    });
    Sentry.captureException(error);
    // 失敗した場合は再取得
    revalidateFilteredPartners();
  }
};

export const useMandatoryColumnsDef = (
  handleClickDetail: (id: number) => void
): PartnerDataTableArg["columns"] => {
  const { data: prefectures } = usePrefectures();
  const { data: contractStatus } = useContractStatus();
  const { data: linkStatus } = useLinkStatus();
  const { data: vendorUsers } = useVendorUsers();
  const setPartnerProps = useSetPartnerProps();
  const clickDetailItem = (partner: Partner) => {
    setPartnerProps(partner);
    handleClickDetail(partner.managed_partner_id);
  };
  return useMemo(
    () => [
      {
        accessorKey: "managed_partner_name",
        header: ({ header }) => <Head header={header}>パートナー名</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        size: 320,
        cell: (params) => {
          const value = params.getValue() as string;
          return (
            <Cell>
              <div
                css={{
                  alignItems: "center",
                  display: "flex",
                  gap: "16px",
                  width: "100%",
                }}
              >
                <div>
                  <Icon.Partner />
                </div>
                <Link
                  css={{
                    color: "#0091ae",
                    ":hover": {
                      color: "#005f6b",
                      textDecoration: "underline",
                    },
                    ...ellipsisStyle,
                  }}
                  href={""}
                  onClick={() => clickDetailItem(params.row.original)}

                >
                  {value}
                </Link>
              </div>
            </Cell>
          );
        },
        meta: {
          customize: { variant: "STRING" },
        },
      },
      {
        id: "vendor.user_name",
        accessorKey: "vendor_user_id",
        header: ({ header }) => <Head header={header}>ベンダー担当者</Head>,
        enableColumnFilter: true,
        meta: {
          customize: {
            variant: "VENDOR_USER",
            selectOptions: vendorUsers?.map((user) => ({
              label: user.name,
              value: String(user.id),
            })),
          },
        },
        cell: (params) => {
          const { vendor_avatar_url, vendor_user_id, managed_partner_id } =
            params.row.original;
          const [value, setValue] = useState<string | null>(
            vendor_user_id ? String(vendor_user_id) : ""
          );
          useEffect(() => {
            setValue(vendor_user_id ? String(vendor_user_id) : "");
          }, [vendor_user_id]);
          const target = vendorUsers?.find((u) => String(u.id) === value);

          if (
            params.column.columnDef.meta?.customize.variant !== "VENDOR_USER"
          ) {
            throw new Error(
              "VENDOR_USER is expected as variant, but got" +
                params.column.columnDef.meta?.customize.variant
            );
          }

          const { selectOptions } = params.column.columnDef.meta.customize;

          return (
            <Cell>
              <ToggleEdit
                renderItem={(isEdit) => (
                  <KeepEditArea>
                    {isEdit ? (
                      <Select
                        data={[
                          { label: "(未選択)", value: "" },
                          ...(selectOptions ?? []),
                        ]}
                        value={value}
                        onChange={(input) => {
                          updateData({
                            partnerId: managed_partner_id.toString(),
                            columnId: "vendor_user_id",
                            value: input,
                          });
                          setValue(input);
                        }}
                      />
                    ) : target ? (
                      <div
                        css={{
                          alignItems: "center",
                          display: "flex",
                          gap: "16px",
                          width: "100%",
                        }}
                      >
                        <div>
                          {target.logo_url ? (
                            <Image
                              src={target.logo_url}
                              css={{ borderRadius: "40px" }}
                              objectFit="cover"
                              width={40}
                              height={40}
                              alt=""
                            />
                          ) : (
                            <Icon.VendorUser />
                          )}
                        </div>
                        <EllipsisText value={target.name} />
                      </div>
                    ) : (
                      <div />
                    )}
                  </KeepEditArea>
                )}
              />
            </Cell>
          );
        },
      },
      {
        accessorKey: "link_status",
        header: ({ header }) => <Head header={header}> 連携ステータス</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: {
            variant: "SELECT",
            selectOptions: Object.entries(linkStatus ?? {}).map(
              ([key, value]) => ({
                label: value.name,
                value: key,
              })
            ),
          },
        },
        // NOTE: 連携ステータスは編集不可
        cell: (params) => {
          const value = params.getValue() as string;
          return (
            <Cell>
              <EllipsisText value={linkStatus?.[value]?.name ?? ""} />
            </Cell>
          );
        },
      },
      {
        accessorKey: "partner_collaboration_id",
        header: ({ header }) => <Head header={header}>パートナー連携ID</Head>,
        meta: {
          customize: { variant: "STRING" },
        },
        // NOTE: パートナー連携IDは編集不可
        cell: (params) => (
          <Cell>
            <EllipsisText value={params.getValue() as string} />
          </Cell>
        ),
      },
      {
        accessorKey: "contract_status",
        header: ({ header }) => <Head header={header}>契約ステータス</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: {
            variant: "SELECT",
            keys: { searchConditionKey: "contract_status_name" },
            selectOptions: Object.entries(contractStatus ?? {}).map(
              ([key, value]) => ({
                label: value.name,
                value: key,
              })
            ),
          },
        },
        cell: (params) => {
          const initialValue = params.getValue() as string;
          const [value, setValue] = useState(initialValue);
          useEffect(() => {
            setValue(initialValue);
          }, [initialValue]);

          const handleChange = useCallback(
            async (text: string | null) => {
              setValue(text ?? "");
              await updateData({
                partnerId: params.row.original.managed_partner_id.toString(),
                columnId: params.column.id,
                value: text,
              });
            },
            [params.column.id, params.row.original.managed_partner_id]
          );
          return (
            <EditableSelectCell
              initialValue={value}
              params={params}
              onChange={handleChange}
            />
          );
        },
      },
      {
        accessorKey: "url",
        header: ({ header }) => <Head header={header}>URL</Head>,
        enableColumnFilter: true,
        enableSorting: false,
        meta: {
          customize: { variant: "STRING" },
        },
        cell: (params) => {
          const initialValue = params.getValue() as string;
          const handleChange = useCallback(
            async (text: string | null) => {
              await updateData({
                partnerId: params.row.original.managed_partner_id.toString(),
                columnId: params.column.id,
                value: text,
              });
            },
            [params.column.id, params.row.original.managed_partner_id]
          );

          return (
            <EditableTextCell
              initialValue={initialValue}
              onChange={handleChange}
            />
          );
        },
      },
      {
        accessorKey: "postal_code",
        header: ({ header }) => <Head header={header}>郵便番号</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: { variant: "STRING" },
        },
        cell: (params) => {
          const initialValue = params.getValue() as string;
          const handleChange = useCallback(
            async (text: string | null) => {
              await updateData({
                partnerId: params.row.original.managed_partner_id.toString(),
                columnId: params.column.id,
                value: text,
              });
            },
            [params.column.id, params.row.original.managed_partner_id]
          );
          return (
            <EditableTextCell
              initialValue={initialValue}
              onChange={handleChange}
            />
          );
        },
      },
      {
        accessorKey: "prefecture_id",
        header: ({ header }) => <Head header={header}>都道府県</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: {
            variant: "SELECT",
            selectOptions:
              prefectures?.map(({ label, value }) => ({
                label,
                value: String(value),
              })) ?? [],
          },
        },
        cell: (params) => {
          const initialValue = params.getValue() as number | null;
          const [value, setValue] = useState(initialValue);
          const handleChange = useCallback(
            async (text: string | null) => {
              setValue(Number(text));
              await updateData({
                partnerId: params.row.original.managed_partner_id.toString(),
                columnId: params.column.id,
                value: text,
              });
            },
            [params.column.id, params.row.original.managed_partner_id]
          );
          return (
            <EditableSelectCell
              initialValue={typeof value === "number" ? String(value) : ""}
              params={params}
              onChange={handleChange}
              withEmptyOption
            />
          );
        },
      },
      {
        accessorKey: "address",
        header: ({ header }) => <Head header={header}>住所</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: { variant: "STRING" },
        },
        cell: (params) => {
          const initialValue = params.getValue() as string;
          const handleChange = useCallback(
            async (text: string | null) => {
              await updateData({
                partnerId: params.row.original.managed_partner_id.toString(),
                columnId: params.column.id,
                value: text,
              });
            },
            [params.column.id, params.row.original.managed_partner_id]
          );
          return (
            <EditableTextCell
              initialValue={initialValue}
              onChange={handleChange}
            />
          );
        },
      },
      {
        accessorKey: "number_of_employees",
        header: ({ header }) => <Head header={header}>従業員数</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: { variant: "INTEGER" },
        },
        cell: (params) => {
          const initialValue = params.getValue() as number | null;
          const handleChange = useCallback(
            async (num: number | null) => {
              await updateData({
                partnerId: params.row.original.managed_partner_id.toString(),
                columnId: params.column.id,
                value: num,
              });
            },
            [params.column.id, params.row.original.managed_partner_id]
          );

          return (
            <EditableNumberCell
              initialValue={initialValue}
              onChange={handleChange}
            />
          );
        },
      },
      {
        accessorKey: "tel",
        header: ({ header }) => <Head header={header}>代表電話番号</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: { variant: "STRING" },
        },
        cell: (params) => {
          const initialValue = params.getValue() as string;
          const handleChange = useCallback(
            async (text: string | null) => {
              await updateData({
                partnerId: params.row.original.managed_partner_id.toString(),
                columnId: params.column.id,
                value: text,
              });
            },
            [params.column.id, params.row.original.managed_partner_id]
          );
          return (
            <EditableTextCell
              initialValue={initialValue}
              onChange={handleChange}
            />
          );
        },
      },
    ],
    [contractStatus, linkStatus, prefectures, vendorUsers, handleClickDetail]
  );
};

export const useDateColumnsDef = (): PartnerDataTableArg["columns"] => {
  return useMemo(
    () => [
      {
        accessorKey: "created_at",
        header: ({ header }) => <Head header={header}>作成日時</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: { variant: "DATETIME_LOCAL", disableEdit: true },
        },
        cell: (params) => {
          const value = params.getValue() as string;
          return (
            <Cell>
              <EllipsisText value={value} />
            </Cell>
          );
        },
      },
      {
        accessorKey: "updated_at",
        header: ({ header }) => <Head header={header}>最終更新日時</Head>,
        enableColumnFilter: true,
        enableSorting: true,
        meta: {
          customize: { variant: "DATETIME_LOCAL", disableEdit: true },
        },
        cell: (params) => {
          const value = params.getValue() as string;
          return (
            <Cell>
              <EllipsisText value={value} />
            </Cell>
          );
        },
      },
    ],
    []
  );
};
