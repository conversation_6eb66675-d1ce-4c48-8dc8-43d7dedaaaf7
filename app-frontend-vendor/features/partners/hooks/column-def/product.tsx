import { notifications } from "@mantine/notifications";
import { Cell } from "components/data-table/cell";
import { EditableSelectCell } from "components/data-table/cell/editable";
import { Head } from "components/data-table/head";
import { getProductId } from "components/data-table/utils/product";
import { PartnerDataTableArg } from "features/partners/type/table";
import { useCallback, useEffect, useMemo, useState } from "react";
import { ax } from "utils/axios";
import { useProducts } from "../vendor";
import { RiCloseCircleLine } from "@remixicon/react";
import { AxiosError } from "axios";

export const useProductColumnsDef = (): PartnerDataTableArg["columns"] => {
  const { data: products } = useProducts();
  const updateData = useCallback(
    async (partnerId: number, productId: number) => {
      try {
        await ax.post(`/api/v1/partners/${partnerId}/products/${productId}`);
      } catch (err) {
        const { status } = err as AxiosError;
        if (status !== 200) {
          notifications.show({
            title: "プロダクトが販売可能になっていません。",
            message:
              "プロダクトが無効です。プロダクト管理画面から当該プロダクトを有効に設定してください。",
            icon: <RiCloseCircleLine css={{ color: "#f93832" }} />,
          });
          return { ok: false };
        }
      }
      return { ok: true };
    },
    []
  );
  const selectOptions = useMemo(
    () => [
      {
        label: "無効",
        value: "DISABLED",
      },
      {
        label: "有効",
        value: "ENABLED",
      },
    ],
    []
  );
  return useMemo((): PartnerDataTableArg["columns"] => {
    // undefined または空の object の場合は何も表示しない
    if (!products || Object.keys(products).length === 0) {
      return [];
    }
    return (
      (products as Product[]).map((product) => ({
        id: getProductId(product),
        accessorKey: "NOTE: cell で表示をつくるので accessorKey は使わない",
        accessorFn: (row) => {
          if (!Array.isArray(row.products)) {
            return "";
          }
          return row.products?.find(
            (src) => src.product_id === product.product_id
          )?.sales_status;
        },
        header: (params) => (
          <Head header={params.header}>{product.product_name}</Head>
        ),
        enableColumnFilter: true,
        enableSorting: false,
        meta: {
          customize: {
            variant: "SELECT",
            selectOptions: selectOptions,
          },
        },
        cell: (params) => {
          const original = params.row.original;
          // products が配列でない場合は何も表示しない
          const initialValue = !Array.isArray(original.products)
            ? ""
            : original.products?.find(
                (src) => src.product_id === product.product_id
              )?.sales_status;
          const [value, setValue] = useState(initialValue);

          // 初期値が変更されたときに state を更新する
          useEffect(() => {
            setValue(initialValue);
          }, [initialValue]);

          const linkActive = original.link_status === "LINK_ACTIVE";
          // 連携済みのときだけ編集可能にする
          return linkActive ? (
            <EditableSelectCell
              initialValue={value ?? ""}
              params={params}
              onChange={async (selectedValue) => {
                if (!selectedValue || selectedValue === value) return;

                setValue(selectedValue);
                const { ok } = await updateData(
                  original.managed_partner_id,
                  product.product_id
                );
                if (!ok) {
                  setValue("DISABLED");
                }
              }}
            />
          ) : (
            <Cell>
              {selectOptions.find((ops) => ops.value === value)?.label}
            </Cell>
          );
        },
      })) ?? []
    );
  }, [products, selectOptions, updateData]);
};
