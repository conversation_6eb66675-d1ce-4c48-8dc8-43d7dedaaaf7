import { notifications } from "@mantine/notifications";
import { RiCloseCircleLine } from "@remixicon/react";
import {
  EditableDateCell,
  EditableDatetimeCell,
  EditableFileCell,
  EditableLongTextCell,
  EditableNumberCell,
  EditablePartnerUserCell,
  EditableSelectCell,
  EditableTextCell,
} from "components/data-table/cell/editable";
import { Head } from "components/data-table/head";
import { Variant } from "components/data-table/type";
import {
  getCustomColumnId,
  getCustomColumnMeta,
} from "components/data-table/utils/custom";
import { PartnerDataTableArg } from "features/partners/type/table";
import { customColumnContentsMap } from "features/partners/utils/custom";
import {
  ComponentProps,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { ax } from "utils/axios";
import {
  revalidateFilteredPartners,
  useMangedPartnerCustomColumns,
} from "../partner";
import { useVendorUsers } from "../vendor";
import * as Sentry from "@sentry/nextjs";

export const useCustomColumnsDef = (): PartnerDataTableArg["columns"] => {
  const { data: vendorUsers } = useVendorUsers();
  const { data: customColumns } = useMangedPartnerCustomColumns();
  const updateCustomColumn = useCallback(
    async (
      partnerId: number,
      customColumnId: number,
      value: string | number | null
    ) => {
      const { data: prev } = await ax.get<Partner>(
        `/api/v1/managed_partner/${partnerId}`
      );
      const updated = {
        ...prev,
        custom_column_contents: customColumnContentsMap(prev.custom, {
          customColumnId,
          value,
        }),
      };

      try {
        await ax.put(`/api/v1/managed_partner/${partnerId}`, updated);
      } catch (error) {
        notifications.show({
          title: "エラー",
          message: "更新に失敗しました",
          icon: <RiCloseCircleLine css={{ color: "#f93832" }} />,
        });
        Sentry.captureException(error);
        // 失敗した場合は再取得
        revalidateFilteredPartners();
        return { ok: false };
      }
      return { ok: true };
    },
    []
  );
  return useMemo(
    () =>
      customColumns?.map((column) => {
        const variant = column.type_status;
        return {
          id: getCustomColumnId(column),
          header: (params) => (
            <Head header={params.header}> {column.column_label}</Head>
          ),
          accessorKey: "NOTE: cell で表示をつくるので accessorKey は使わない",
          // NOTE: 長文はフィルタリング不可
          enableColumnFilter: variant !== "LONG_TEXT",
          enableSorting: !["LONG_TEXT", "FILE", "STRING"].includes(variant),
          meta: {
            customize: getCustomColumnMeta(
              variant as Variant,
              column,
              vendorUsers ?? []
            ),
          },
          cell: (params) => {
            const { customize } = params.column.columnDef.meta ?? {};
            const target =
              params.row.original.managed_partner_custom_columns.find(
                (originalColumn) =>
                  originalColumn.column_name === column.column_name
              );
            const partnerId = params.row.original.managed_partner_id;
            if (!target) {
              return []; // 4402 カスタムカラムが正しくない場合は、スローせずに[]を返します。
            }
            switch (customize?.variant) {
              case "SELECT":
                return (
                  <EditableSelectCellWithState
                    initialValue={String(target.select_id)}
                    params={params}
                    onChange={(value) =>
                      updateCustomColumn(partnerId, target.column_id, value)
                    }
                  />
                );
              case "VENDOR_USER":
                return (
                  <EditableSelectCellWithState
                    withEmptyOption
                    initialValue={
                      target.vendor_user_id ? String(target.vendor_user_id) : ""
                    }
                    params={params}
                    onChange={(value) =>
                      updateCustomColumn(partnerId, target.column_id, value)
                    }
                  />
                );
              case "PARTNER_USER":
                return (
                  <EditablePartnerUserCell
                    withEmptyOption
                    useSelectOptions={customize.useSelectOptions}
                    displayText={target.current ? String(target.current) : ""}
                    initialValue={
                      target.partner_user_id
                        ? String(target.partner_user_id)
                        : ""
                    }
                    partnerId={params.row.original.managed_partner_id}
                    onChange={(value) =>
                      updateCustomColumn(partnerId, target.column_id, value)
                    }
                  />
                );

              case "FILE":
                return (
                  <EditableFileCell
                    initialValue={target.current ? String(target.current) : ""}
                    onChange={async (file) => {
                      const path = `/api/v1/managed_partner/${partnerId}/custom_file/${target.column_id}`;
                      if (!file) {
                        return;
                      }
                      const formData = new FormData();
                      formData.append("file", file);
                      await ax.post(path, formData);
                    }}
                  />
                );
              case "INTEGER":
                return (
                  <EditableNumberCell
                    initialValue={
                      target.current ? Number(target.current) : null
                    }
                    onChange={(value) =>
                      updateCustomColumn(partnerId, target.column_id, value)
                    }
                  />
                );
              case "DATE":
                return (
                  <EditableDateCell
                    initialValue={target.current ? String(target.current) : ""}
                    onChange={(value) =>
                      updateCustomColumn(partnerId, target.column_id, value)
                    }
                  />
                );
              case "LONG_TEXT":
                return (
                  <EditableLongTextCell
                    initialValue={target.current ? String(target.current) : ""}
                    onChange={(value) =>
                      updateCustomColumn(partnerId, target.column_id, value)
                    }
                  />
                );
              case "DATETIME_LOCAL":
                return (
                  <EditableDatetimeCell
                    initialValue={target.current ? String(target.current) : ""}
                    onChange={(value) =>
                      updateCustomColumn(partnerId, target.column_id, value)
                    }
                  />
                );
              default:
                return (
                  <EditableTextCell
                    initialValue={target.current ? String(target.current) : ""}
                    onChange={(value) =>
                      updateCustomColumn(partnerId, target.column_id, value)
                    }
                  />
                );
            }
          },
        };
      }) ?? [],
    [customColumns, updateCustomColumn, vendorUsers]
  );
};

const EditableSelectCellWithState = <T,>(
  props: Omit<ComponentProps<typeof EditableSelectCell<T>>, "onChange"> & {
    onChange: (text: string | null) => Promise<{ ok: boolean }>;
  }
) => {
  // 値の更新に失敗した場合もとに戻すための state
  const [value, setValue] = useState(props.initialValue);
  useEffect(() => {
    setValue(props.initialValue);
  }, [props.initialValue]);
  return (
    <EditableSelectCell
      {...props}
      initialValue={value}
      onChange={async (text) => {
        setValue(text);
        const { ok } = await props.onChange(text);
        if (!ok) {
          setValue(props.initialValue);
        }
      }}
    />
  );
};
