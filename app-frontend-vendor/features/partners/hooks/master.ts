import useSWR from "swr";
import { fetcher } from "utils/axios/fetcher";

export const usePrefectures = () => {
  return useSWR<Prefecture[]>("/api/v1/prefecture", fetcher, {
    revalidateOnFocus: false,
  });
};

export const useContractStatus = () => {
  return useSWR<ContractStatusList>("/api/v1/contract_status", fetcher, {
    revalidateOnFocus: false,
  });
};

export const useLinkStatus = () => {
  return useSWR<LinkStatusList>("/api/v1/link_status", fetcher, {
    revalidateOnFocus: false,
  });
};
