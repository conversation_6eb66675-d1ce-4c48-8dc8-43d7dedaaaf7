import styled from "@emotion/styled";
import {
  ArrowRep<PERSON>16Filled,
  Delete16<PERSON>illed,
  Edit16Filled,
} from "@fluentui/react-icons";
import { Button, Text } from "@mantine/core";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";
import { parseCookies } from "nookies";
import { useCallback, useEffect, useMemo, useState } from "react";
import { ax } from "utils/axios";
import { modals } from "@mantine/modals";
import IconUserVendor from "public/icons/user-icon.svg";
import IconUserThreadVendor from "public/icons/avatar-default.svg";
import IconEdit from "public/icons/icon-edit.svg";
import IconThead from "public/icons/thread.svg";
import IconDelete from "public/icons/trash.svg";
import IconAttachmentFile from "public/icons/attachment-file.svg";
import IconDownloadFile from "public/icons/download-file-chat.svg";
import IconArrowRight from "public/icons/icon-arrow-right.svg";
import Tooltip from "components/Tooltip";
import Image from "next/image";
import { notifications } from "@mantine/notifications";
import IconNotiFailed from "../../../../public/icons/icon-noti-failed.svg";
import {
  MIME_TYPE_FILE_CHAT_DISPLAY,
  REGEX_SPACE,
} from "constants/ruleValidation";
import { CustomPreviewFileChat } from "components/form/CustomPreviewFileChat";
import { useDisclosure } from "@mantine/hooks";
import { CustomPreviewFileChatModal } from "components/Modals/CustomPreviewFileChatModal";
import { useSetChatListName } from "../../../../utils/recoil/chatList/chatListNameState";
import { MessageBox } from "components/form/message-box";
import { useMentionEditor } from "components/form/hooks/use-mention-editor";
import type { Editor } from "@tiptap/react";

type ChatMessageBlockProps = {
  message: chatMessage;
  mentionables?: chatMentionableUser[] | null;
  mode: "channel" | "thread";
  mutate: () => void;
  content: string;
  messageIndex?: number;
  channelId?: string | number;
  totalReplies?: string | number;
  channel?: string;
  onRemove?: (
    parentId?: string | number,
    childId?: string | number | null
  ) => void;
  onEmit?: (chatPostId: number | string) => void;
};

type PresentationProps = {
  editor: Editor | null;
  message: chatMessage;
  mode: "channel" | "thread";
  className?: string;
  jumpThread: (id: number) => void;
  isVendor: boolean;
  userID: number;
  isReadOnly: boolean;
  toggleEditMode: () => void;
  resetValue: () => void;
  handleUpdateMessage: () => void;
  handleDeleteMessage: () => void;
  handleDeleteFile: (file_id: number) => void;
  channelId?: string | number;
  totalReplies?: string | number;
  handleEmit: (chatPostId: number | string) => void;
};

const MESSAGE_DELETED = "[このメッセージは削除されました]";
const Presentation: React.FC<PresentationProps> = ({
  className,
  editor,
  message,
  jumpThread,
  isVendor,
  userID,
  mode,
  isReadOnly,
  toggleEditMode,
  resetValue,
  handleUpdateMessage,
  handleDeleteMessage,
  handleDeleteFile,
  channelId,
  totalReplies,
  handleEmit,
}) => {
  const [opened, { open, close }] = useDisclosure(false);
  const [selectedFile, setSelectedFile] = useState<attached_files | null>(null);

  return (
    <div
      className={`${className} chat-content ${!isReadOnly && "chat-content-edit"}`}
      key={message.chat_post_id}
      style={
        message.unread === 1
          ? {
              backgroundColor: propcolors.backgroundChat,
            }
          : {}
      }
    >
      <div className="chat-heading">
        <section>
          <div className="chat-heading-sender">
            <div className="chat-item">
              <div className="avatar-user">
                {message.user.logo_url ? (
                  <Image
                    src={message.user.logo_url}
                    style={{ borderRadius: "40px" }}
                    width={40}
                    height={40}
                    alt=""
                  />
                ) : (
                  <IconUserVendor className="imgSpan" />
                )}
              </div>
              <div className="message-info">
                <div className="chat-info-top">
                  <div className="user-name">
                    <span className="user-name-content">
                      {message.user.name}
                    </span>
                    <span className="chat-heading-timestamp">
                      {message.timestamp.created_at}
                    </span>
                  </div>
                  <div className="chat-heading-buttons">
                    {message.deleted !== 1 && isReadOnly && (
                      <>
                        {userID ===
                          (isVendor
                            ? message.user.vendor_user_id
                            : message.user.partner_user_id) && (
                          <Tooltip text="削除する">
                            <IconDelete onClick={handleDeleteMessage}>
                              <Delete16Filled />
                            </IconDelete>
                          </Tooltip>
                        )}
                        {userID ===
                          (isVendor
                            ? message.user.vendor_user_id
                            : message.user.partner_user_id) && (
                          <Tooltip text="編集する">
                            <IconEdit onClick={toggleEditMode}>
                              <Edit16Filled />
                            </IconEdit>
                          </Tooltip>
                        )}
                        {mode === "channel" && !channelId && (
                          <Tooltip text="スレッド">
                            <IconThead
                              onClick={() => jumpThread(message.chat_post_id)}
                            >
                              <ArrowReply16Filled />
                            </IconThead>
                          </Tooltip>
                        )}
                      </>
                    )}
                  </div>
                </div>
                <div
                  className={`message-content ${!isReadOnly && "message-content-edit"}`}
                >
                  <MessageBox editor={editor} />
                  {!isReadOnly && (
                    <div className="chat-message-editor">
                      <Button
                        compact
                        variant={"subtle"}
                        className="btn-chat btn-cancel"
                        onClick={resetValue}
                      >
                        キャンセル
                      </Button>
                      <Button
                        compact
                        className="btn-chat btn-send"
                        onClick={handleUpdateMessage}
                      >
                        編集を保存
                      </Button>
                    </div>
                  )}
                </div>
                <FileListWrapper
                  style={{
                    display: "flex",
                    flexWrap: "wrap",
                    gap: "1rem",
                  }}
                >
                  {message.attached_files.map((file, index) => (
                    <FileItem
                      key={index}
                      style={{
                        height: MIME_TYPE_FILE_CHAT_DISPLAY.includes(
                          file.contentType
                        )
                          ? "fit-content"
                          : 34,
                      }}
                    >
                      <FileItemTitleWrapper>
                        <FileItemTitle className="chat-attachments-item">
                          <IconAttachmentFile className="attachments-icon" />
                          <span>{file.name}</span>
                        </FileItemTitle>
                        <FileItemAction>
                          <IconDownloadFile
                            onClick={() => window.open(file.url, "_blank")}
                          />
                          {!isReadOnly && (
                            <IconDelete
                              onClick={() => handleDeleteFile(file.id)}
                              style={{ marginLeft: "8px" }}
                            >
                              <Delete16Filled />
                            </IconDelete>
                          )}
                        </FileItemAction>
                      </FileItemTitleWrapper>
                      {MIME_TYPE_FILE_CHAT_DISPLAY.includes(
                        file.contentType
                      ) ? (
                        <CustomPreviewFileChat
                          file={file}
                          onClick={() => {
                            open();
                            setSelectedFile(file);
                          }}
                        />
                      ) : null}
                    </FileItem>
                  ))}
                </FileListWrapper>
                <CustomPreviewFileChatModal
                  opened={opened}
                  close={close}
                  file={selectedFile}
                />
                {Number(totalReplies) > 0 &&
                  mode !== "thread" &&
                  message.deleted !== 1 && (
                    <div
                      className="chat-threads"
                      style={
                        message.is_replies_unreads
                          ? {
                              backgroundColor: propcolors.backgroundChat,
                              padding: "12px",
                              borderRadius: "10px",
                            }
                          : {}
                      }
                      onClick={
                        channelId
                          ? () => handleEmit(message.chat_post_id)
                          : () => jumpThread(message.chat_post_id)
                      }
                    >
                      <div className="chat-threads-info">
                        <div className="avatars">
                          {message.thread_users?.length > 0 ? (
                            <div className="avatar-content">
                              {message.thread_users
                                ?.slice(0, 5)
                                .map((user, index) => (
                                  <div key={index} className="imgSpan">
                                    {user.logo_url ? (
                                      <Image
                                        src={user.logo_url}
                                        className="imgSpan-item"
                                        style={{ borderRadius: "40px" }}
                                        width={40}
                                        height={40}
                                        alt=""
                                      />
                                    ) : (
                                      <IconUserThreadVendor className="imgSpan-item" />
                                    )}
                                    {index == 4 &&
                                      message.thread_users.length > 5 && (
                                        <div className="number-over">
                                          +{message.thread_users.length - 5}
                                        </div>
                                      )}
                                  </div>
                                ))}
                            </div>
                          ) : (
                            <div className="avatar-content">
                              <div className="imgSpan">
                                <IconUserThreadVendor className="imgSpan-item" />
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="info-user">
                          {channelId ? (
                            <span
                              className={`total-thread ${message.thread_users?.length >= 5 && "ml-20p"}`}
                            >
                              {totalReplies} 件のその他の返信を表示
                            </span>
                          ) : (
                            <>
                              <span
                                className={`total-thread ${message.thread_users?.length >= 5 && "ml-20p"}`}
                              >
                                {totalReplies} 件の返信
                              </span>
                              スレッドを表示する
                            </>
                          )}
                        </div>
                      </div>
                      <IconArrowRight />
                    </div>
                  )}
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

const FileListWrapper = styled.div(() => ({
  width: "100%",
  overflow: "auto",
}));
const FileItem = styled.div({
  display: "flex",
  flexDirection: "column",
  gap: 16,
  border: `1px solid ${propcolors.border}`,
  borderRadius: "6px",
  padding: "9px 12px",
  width: "293px",
  // maxWidth: "293px",
  backgroundColor: "white",
});
const FileItemTitleWrapper = styled.div({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
});
const FileItemTitle = styled.div({
  display: "flex",
  alignItems: "center",
  gap: 5,
  fontSize: "14px",
  fontWeight: "600",
  color: propcolors.partnerRed,
});
const FileItemAction = styled.div({
  display: "flex",
  cursor: "pointer",
});

const Styled = styled(Presentation)`
  padding: 24px;
  background-color: ${propcolors.white};
  margin-bottom: 8px;
  border-radius: 10px;
  .mb-16p {
    margin-bottom: 16px;
  }
  .ml-20p {
    margin-left: 20px !important;
  }
  .number-over {
    color: ${propcolors.white};
    text-align: center;
    background-color: ${propcolors.numberOverChat};
    justify-content: center;
    align-items: center;
    font-weight: 600;
    display: flex;
    position: absolute;
    inset: 0;
    z-index: 9;
    border-radius: 40px;
    width: 20px;
    height: 20px;
    font-size: 11px;
  }
  &:hover {
    background-color: ${propcolors.backgroundChat};
    .mentions__mention {
      background-color: ${propcolors.backgroundChat} !important;
    }
    .chat-heading-buttons {
      display: flex;
      gap: 28px;
    }
  }
  .chat {
    &-heading {
      display: flex;
      justify-content: space-between;
      align-items: center;
      ${(props) => (props.channelId ? "margin-right: 15px" : "")};
      section {
        width: 100%;
      }
      .chat-item {
        display: inline-block;
        vertical-align: middle;
        line-height: normal;
        width: 100%;
        .chat-info-top {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .avatar-user {
          float: left;
          padding-top: 2px;
          margin-right: 16px;
          align-content: end;
        }
        .message-info {
          align-content: center;
          div {
            line-height: normal;
          }
          .user-name {
            color: ${propcolors.blackLight};
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 8px;
          }
          .chat-heading-timestamp {
            margin-left: 16px;
            color: ${propcolors.greyDefault};
            font-size: 12px;
            font-weight: 300;
          }
          .message-content {
            color: ${propcolors.blackLight};
            font-size: 14px;
            font-weight: 300;
            line-height: 21px;
            &-edit {
              .chat-message-editor {
                margin-top: 10px;
              }
              .btn-cancel {
                background-color: ${propcolors.backgroundCancel};
                color: ${propcolors.white};
                margin-right: 16px;
              }
              .btn-send,
              .btn-send:hover {
                background-color: ${propcolors.blackLight} !important;
                color: ${propcolors.white} !important;
              }
            }
          }
        }
      }
      &-sender {
        font-weight: bold;
        font-size: 0.875rem;
        ${(props) => (props.channelId ? " margin-left: 5px" : "")};
        &_icon {
          display: inline-block;
          width: 40px;
          height: 40px;
          border-radius: 20px;
          background-color: ${propcolors.partnerRed};
          color: ${propcolors.white};
          text-align: center;
          line-height: 40px;
          margin-right: 12px;
          font-size: 1rem;
          &.partner {
            background-color: ${propcolors.gray[800]};
          }
        }
      }
      &-timestamp {
        margin-top: 0.3rem;
        font-size: 0.8rem;
        color: ${propcolors.gray[500]};
      }
      &-buttons {
        display: flex;
        align-self: flex-start;
        display: none;
        svg {
          width: 16.67px;
          height: 16.67px;
          margin-right: 28px;
          cursor: pointer;
        }
        .tooltip svg {
          margin: 0px;
        }
      }
    }
    &-message {
      &-editor {
        display: flex;
        justify-content: flex-end;
        margin-top: 1rem;
      }
    }
    &-threads {
      width: 100%;
      display: grid;
      grid-template-columns: 1fr auto auto;
      align-items: center;
      margin-top: 1rem;
      cursor: pointer;
      font-size: 12px;
      font-weight: 300;
      border-top: 1px solid ${propcolors.border};
      color: ${propcolors.blackLightLabel};
      padding-top: 12px;
      &-info {
        display: flex;
        justify-content: start;
        align-items: center;
        line-height: 20px;
        .avatars {
          display: flex;
          align-items: center;
          max-width: 68px;
          height: 20px;
          .avatar-content {
            display: flex;
            height: 100%;
          }
          svg {
            height: 20px;
            width: 20px;
          }
        }

        .imgSpan {
          position: relative;
          top: 0;
          left: 0;
          &-item {
            border-radius: 10px;
            width: 20px;
            height: 20px;
          }
        }

        .imgSpan:nth-of-type(1) {
          z-index: 1;
          width: 15px;
        }

        .imgSpan:nth-of-type(2) {
          z-index: 2;
          left: 0px;
          width: 15px;
          position: relative;
        }

        .imgSpan:nth-of-type(3) {
          z-index: 3;
          left: 0px;
          width: 15px;
        }

        .imgSpan:nth-of-type(4) {
          z-index: 4;
          left: 0px;
          width: 15px;
        }

        .imgSpan:nth-of-type(5) {
          z-index: 5;
          left: 0px;
          width: 15px;
        }
        .total-thread {
          font-weight: 600;
          color: ${propcolors.partnerRed};
          margin: 0px 8px 0px 14px;
          line-height: 20px;
        }
      }
    }
    &-attachments {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1rem;
      margin-top: 1rem;
      &-item {
        border-radius: 6px;
        display: grid;
        grid-template-columns: 1fr auto auto;
        align-items: center;
        font-size: 14px;
        font-weight: 600;
        color: ${propcolors.partnerRed};
        background-color: white;
        .attachments-icon {
          margin-right: 8px;
          vertical-align: bottom;
        }
        svg {
          color: ${propcolors.partnerRed};
          cursor: pointer;
        }
        span {
          height: 15px;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: ${propcolors.partnerRed};
          line-height: 14px;
        }
      }
    }
  }
  @media screen and (max-width: 600px) {
    .chat-message__input {
      color: ${propcolors.blackLight} !important;
      top: -1px !important;
      left: 3.5px !important;
      :disabled {
        opacity: 1 !important;
      }
    }
    input:disabled,
    textarea:disabled {
      opacity: 1 !important;
    }
  }
`;

export const ChatMessageBlock: React.FC<ChatMessageBlockProps> = ({
  message,
  mentionables,
  mode,
  mutate,
  content,
  channelId,
  totalReplies,
  onEmit,
  onRemove,
  channel,
}) => {
  const [isReadOnly, setEditMode] = useState<boolean>(true);
  const mentions = useMemo(() => {
    return (
      mentionables?.map((item) => ({
        label: item.label,
        id: item.text,
      })) ?? []
    );
  }, [mentionables]);
  const { editor } = useMentionEditor({
    mentions,
    editable: !isReadOnly,
  });

  const { replace, query } = useRouter();
  const userID = JSON.parse(parseCookies()["user"]).user_id;
  const isVendor =
    typeof window !== "undefined" && window.location.hostname.includes("vendor")
      ? true
      : false;
  const jumpThread = (id: number) =>
    replace(
      `/chat/${query.id}?threads=${id}${
        query.title ? "&title=" + query.title : ""
      }`
    );

  const handleEmit = () => {
    if (onEmit) onEmit(message.chat_post_id);
  };

  useEffect(() => {
    editor?.commands.setMentionContent(
      message.deleted === 1 ? MESSAGE_DELETED : message.content
    );
  }, [editor, message.content, message.deleted]);

  const setChanelListName = useSetChatListName();

  useEffect(() => {
    setChanelListName(channel);
  }, []);

  const toggleEditMode = useCallback(() => {
    setEditMode((prev) => !prev);
  }, []);

  const resetValue = () => {
    editor?.commands.setMentionContent(message.content);
    setEditMode(true);
  };

  const handleUpdateMessage = () => {
    const content = editor?.getText({ blockSeparator: "\n" });
    if (!content?.replace(REGEX_SPACE, "").length) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "無効なテストタイトルです。",
        message: "適切な内容を入力してください。",
        autoClose: 5000,
      });
      return;
    }
    ax.put(`/api/v1/chat/messages/${message.chat_post_id}`, {
      content: content,
      parent_id: message.parent_id,
    });
    setEditMode(true);
  };

  const handleDeleteMessage = () => {
    modals.openConfirmModal({
      className: "modal-custom",
      title: "メッセージの削除",
      children: (
        <div className="modal-content">
          <div className="title-message">メッセージを削除しますか？</div>
          <div className="content-message">
            メッセージを削除すると、関連付けられている全てのファイルも同時に削除されます。この操作は元に戻せません。
          </div>
        </div>
      ),
      labels: {
        cancel: "キャンセル",
        confirm: "削除する",
      },
      onConfirm: () => {
        ax.delete(`/api/v1/chat/messages/${message.chat_post_id}`).then(
          (res) => {
            const isParent = !message.parent_id;
            const canDelete =
              (channel === "mention" &&
                res.data.is_deleted_from_mention_list) ||
              (channel === "reply" && res.data.is_deleted_from_reply_list);

            if (onRemove) {
              if (isParent) {
                onRemove(message.chat_post_id);
              } else if (message.parent_id) {
                if (canDelete) {
                  onRemove(message.parent_id);
                } else {
                  editor?.commands.setMentionContent(MESSAGE_DELETED);
                }
              }
            }
          }
        );
      },
    });
  };

  const ModalTitle = styled.div`
    width: 100%;
    text-align: center;
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding-bottom: 1rem;
    top: 1.3rem;
    position: absolute;
    margin: 0 -1rem;
    font-size: 16px;
    font-weight: 600;
    color: ${propcolors.blackLight};
  `;

  const ModalContent = styled.div`
    padding: 10px 10px 5px 10px;
    .title-confirm {
      font-size: 18px;
      font-weight: 600;
      color: ${propcolors.blackLight};
      margin: 10px 0 13px 0;
    }
    .description {
      font-size: 12px;
      font-weight: 300;
      color: var(--Semantic-TEXT_BLACK, #23221e);
    }
  `;

  const handleDeleteFile = (file_id: number) => {
    modals.openConfirmModal({
      title: <ModalTitle>ファイルの削除</ModalTitle>,
      size: "640px",
      closeButtonProps: { size: "24px" },
      children: (
        <ModalContent>
          <Text className="title-confirm">ファイルを削除しますか？</Text>
          <Text className="description">本当にファイルを削除しますか？</Text>
        </ModalContent>
      ),
      labels: {
        confirm: "削除する",
        cancel: "キャンセル",
      },
      onConfirm: () => {
        ax.delete(`/api/v1/chat/attached_files/${file_id}`)
          .then((res) => {
            if (mutate) {
              mutate();
            }
          })
          .catch((err) => {});
        setEditMode(true);
      },
      confirmProps: {
        sx: {
          width: "284px",
          height: "42px",
          right: "10px",
          fontSize: "14px",
          fontWeight: 400,
          marginBottom: "10px",
          borderRadius: "8px",
          backgroundColor: `${propcolors.black}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.black}`,
          },
        },
      },
      cancelProps: {
        variant: "outline",
        sx: {
          width: "284px",
          height: "42px",
          left: "25px",
          position: "absolute",
          fontSize: "14px",
          fontWeight: 400,
          marginBottom: "10px",
          borderRadius: "8px",
          borderColor: `${propcolors.greyDefault}`,
          backgroundColor: `${propcolors.greyDefault}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.greyDefault}`,
          },
        },
      },
    });
  };

  return (
    <Styled
      editor={editor}
      message={message}
      jumpThread={jumpThread}
      isVendor={isVendor}
      userID={userID}
      mode={mode}
      toggleEditMode={toggleEditMode}
      isReadOnly={isReadOnly}
      resetValue={resetValue}
      handleUpdateMessage={handleUpdateMessage}
      handleDeleteMessage={handleDeleteMessage}
      handleDeleteFile={handleDeleteFile}
      channelId={channelId}
      totalReplies={totalReplies}
      handleEmit={handleEmit}
    />
  );
};
