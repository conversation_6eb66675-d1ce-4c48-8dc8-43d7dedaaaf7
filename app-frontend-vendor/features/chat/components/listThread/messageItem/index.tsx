import useSWRImmutable from "swr/immutable";
import { ChatMessageBlock } from "../../message";
import { SendForm } from "../../sendForm";
import { useInView } from "react-intersection-observer";
import { ax } from "utils/axios";
import { useCallback, useEffect, useState } from "react";

type MessageItemProps = {
  index: number;
  message: chatMessage;
  className?: string;
  mutate: () => void;
  handleEmit: (chatPostId: number) => void;
  channel?: string;
  onRemove?: (
    parentId?: string | number,
    childId?: string | number | null
  ) => void;
  onClickReadButton: () => void;
  readMessage: (chatPostId: number, type: string | undefined) => void;
};

type ReadMessageResponse = {
  result: boolean;
  count_read: number;
};

export const MessageItem: React.FC<MessageItemProps> = ({
  index,
  message,
  className,
  mutate,
  handleEmit,
  channel,
  onRemove,
  onClickReadButton,
  readMessage,
}) => {
  const { ref, inView } = useInView();

  const markAsRead = useCallback(
    async (type: string | undefined = undefined) => {
      try {
        const response = await ax.post<ReadMessageResponse>(
          `api/v1/chat/message/${message.chat_post_id}/read`,
          { type }
        );

        if (response) {
          onClickReadButton();
          readMessage(message.chat_post_id, channel);
        }
      } catch (error) {
        console.error("Error marking message as read:", error);
      }
    },
    []
  );

  useEffect(() => {
    if (
      inView &&
      (message.unread === 1 ||
        message.replies?.some((item) => item.unread === 1))
    ) {
      markAsRead(channel);
    }
  }, [inView, message.total_replies, message.replies]);

  const handleShowMoreReplies = () => {
    handleEmit(message.chat_post_id);
    markAsRead();
  };

  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch((err) => console.error(err));

  const { data: mentionables, mutate: muteMention } = useSWRImmutable(
    message.channel_id
      ? `api/v1/chat/${message.channel_id}/mentionables`
      : null,
    fetcher
  );

  const [flgFile, setFlgFile] = useState<boolean>(false);

  const handleFile = (attachments: File[] | null) => {
    setFlgFile(false);
    if (attachments && attachments.length > 0) {
      setFlgFile(true);
    }
  };

  const countReadReplies =
    Number(message.total_replies) -
    Number(message.replies?.filter((reply) => reply.deleted === 0).length);

  return (
    <div ref={ref} className={className} key={message.chat_post_id}>
      <p className="channel-name"># {message.channel_name}</p>
      <div className="box-channel">
        <section className="messages-list" style={{ padding: 0 }}>
          <ChatMessageBlock
            key={`parent-${message.chat_post_id}-${index}`}
            message={message}
            content={message.content}
            mode={"channel"}
            mutate={mutate}
            totalReplies={countReadReplies}
            messageIndex={index}
            channelId={message.channel_id}
            onEmit={handleShowMoreReplies}
            onRemove={onRemove}
            channel={channel}
            mentionables={mentionables}
          />

          {message.replies &&
            message.replies.length > 0 &&
            message.replies.map((reply: chatMessage, ind: number) => (
              <ChatMessageBlock
                key={`child-${reply.chat_post_id}-${ind}`}
                message={reply}
                content={reply.content}
                mode={"channel"}
                mutate={mutate}
                channelId={message.channel_id}
                onRemove={onRemove}
                channel={channel}
                mentionables={mentionables}
              />
            ))}
        </section>
        <SendForm
          mode="channel"
          mentionables={mentionables}
          mutate={mutate}
          handleFile={handleFile}
          className="position: relative"
          channelId={message.channel_id}
          chatPostId={message.chat_post_id}
        />
      </div>
    </div>
  );
};
