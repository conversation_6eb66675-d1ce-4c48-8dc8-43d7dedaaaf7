import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import { useEffect, useRef, useState } from "react";
import { Skeleton } from "@mantine/core";
import { useInView } from "react-intersection-observer";
import { IconArrowDown, IconArrowUp } from "@tabler/icons-react";
import { Channel } from "pusher-js";
import { MessageItem } from "./messageItem";
import { cloneDeep } from "lodash";
import useSWR from "swr";
import { PER_PAGE_OF_MESSAGES } from "constants/commonSetting";
import { parseCookies } from "nookies";
import { useFocusChatPostId } from "utils/recoil/chatList/focusChatPost";

type ChatMessengerProps = {
  channel: string;
  badgeCount: number;
  channelPusher: Channel | null;
  onClickReadButton: () => void;
};

type PresentationProps = {
  className?: string;
  dataMessages?: chatMessagesListResponse | null;
  mutate: () => void;
  isLoading?: boolean;
  loadMore: (message: chatMessage) => void;
  badgeCount: number;
  channel?: string;
  channelPusher: Channel | null;
  params: { chat_post_id: number | null };
  onClickReadButton: () => void;
  reloadPage: () => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  dataMessages,
  mutate,
  isLoading,
  loadMore,
  badgeCount,
  channel,
  channelPusher,
  params,
  onClickReadButton,
  reloadPage,
}) => {
  const [messages, setMessages] = useState<chatMessagesListResponse | any>(
    null
  );
  const [scrollable, setScrollable] = useState<boolean>(true);
  const [paramsLoadMoreThread, setParamsLoadMoreThread] = useState<{
    chat_post_parent_id: number | null;
    chat_post_id: number | null;
    per_page: number;
  }>({
    chat_post_parent_id: null,
    chat_post_id: null,
    per_page: PER_PAGE_OF_MESSAGES,
  });
  const [unreadCount, setUnreadCount] = useState<number>(badgeCount);
  const [showReloadButton, setShowReloadButton] = useState<boolean>(false);
  const [isScrolling, setIsScrolling] = useState<boolean>(false);
  const [highlightedId, setHighlightedId] = useState<number | null>(null);

  const topRef = useRef<HTMLDivElement | null>(null);
  const bottomRef = useRef<HTMLDivElement | null>(null);
  const unreadRef = useRef<HTMLDivElement | null>(null);
  const focusChatPostId = useFocusChatPostId();
  const isFirstRender = params.chat_post_id === null;

  useEffect(() => {
    setParamsLoadMoreThread({
      chat_post_parent_id: null,
      chat_post_id: null,
      per_page: PER_PAGE_OF_MESSAGES,
    });
  }, [channel]);

  useEffect(() => {
    if (channelPusher) {
      if (channel == "reply") {
        channelPusher.bind(
          "message.receive-reply-chat",
          (data: {
            vendor_user_ids: number[];
            partner_user_ids: number[];
            threads: chatMessage;
            msg: chatMessage;
          }) => {
            const userCookies = JSON.parse(parseCookies(null, ["user"]).user);
            const userIds = Array.isArray(data.vendor_user_ids)
              ? data.vendor_user_ids
              : Object.values(data.vendor_user_ids);
            const newMsg = data.msg;
            if (userIds.includes(userCookies.user_id)) {
              setMessages((prevMessage: chatMessagesListResponse) => {
                if (prevMessage.data) {
                  let newState = cloneDeep(prevMessage);
                  const parentMessage = newState.data.find(
                    (message: chatMessage) =>
                      message.chat_post_id === newMsg.parent_id
                  );

                  if (parentMessage == null) {
                    setShowReloadButton(true);
                  } else if (parentMessage.replies) {
                    parentMessage.replies.push(newMsg);
                    parentMessage.total_replies =
                      Number(parentMessage.total_replies) + 1;
                    if (
                      data.msg?.parent_id === focusChatPostId ||
                      data.msg?.user?.vendor_user_id == userCookies.user_id
                    ) {
                      ax.post(
                        `api/v1/chat/message/${newMsg.chat_post_id}/read`
                      ).then(() => onClickReadButton());
                    } else {
                      setShowReloadButton(true);
                    }
                  }
                  return { ...prevMessage, data: newState.data };
                }
                return [];
              });
            }
          }
        );
      }

      if (channel == "mention") {
        channelPusher.bind(
          "message.receive-mention-chat",
          (data: {
            vendor_user_ids: number[];
            partner_user_ids: number[];
            threads: chatMessage;
            msg: chatMessage | null;
          }) => {
            const userCookies = JSON.parse(parseCookies(null, ["user"]).user);
            const userIds = Array.isArray(data.vendor_user_ids)
              ? data.vendor_user_ids
              : Object.values(data.vendor_user_ids);
            const newData = data.threads;
            const newMsg = data.msg;
            if (userIds.includes(userCookies.user_id)) {
              if (newMsg) {
                setMessages((prevMessage: chatMessagesListResponse) => {
                  if (prevMessage.data) {
                    let newState = cloneDeep(prevMessage);
                    const parentMessage = newState.data.find(
                      (message: chatMessage) =>
                        message.chat_post_id === newMsg.parent_id
                    );
                    if (parentMessage == null) {
                      setShowReloadButton(true);
                    } else if (parentMessage.replies) {
                      parentMessage.replies.push(newMsg);
                      parentMessage.total_replies =
                        Number(parentMessage.total_replies) + 1;
                      if (
                        data.msg?.parent_id === focusChatPostId ||
                        data.msg?.user?.vendor_user_id == userCookies.user_id
                      ) {
                        ax.post(
                          `api/v1/chat/message/${newMsg.chat_post_id}/read`
                        ).then(() => onClickReadButton());
                      } else {
                        setShowReloadButton(true);
                      }
                    }
                    return { ...prevMessage, data: newState.data };
                  }
                  return [];
                });
              } else {
                setShowReloadButton(true);
              }
            }
          }
        );
      }

      channelPusher.bind("message.updated", (data: chatMessage) => {
        if (data.parent_id != null) {
          setMessages((prevMessage: chatMessagesListResponse) => {
            if (prevMessage.data) {
              let newState = cloneDeep(prevMessage);
              const parentMessageIndex = newState.data.findIndex(
                (message: chatMessage) =>
                  message.chat_post_id === data.parent_id
              );

              if (parentMessageIndex > -1) {
                const parentMessage = newState.data[parentMessageIndex];
                if (parentMessage.replies) {
                  parentMessage.replies = parentMessage.replies.map(
                    (message: chatMessage) => {
                      let reply = cloneDeep(message);
                      if (message.chat_post_id === data.chat_post_id) {
                        reply.content = data.content;
                        reply.timestamp = data.timestamp;
                      }
                      return reply;
                    }
                  );
                }

                newState.data[parentMessageIndex] = parentMessage;
              }
              return { ...prevMessage, data: newState.data };
            }
            return [];
          });
        } else {
          setMessages((prevMessage: chatMessagesListResponse) => {
            if (prevMessage.data) {
              let newState = cloneDeep(prevMessage);
              const parentMessageIndex = newState.data.findIndex(
                (message: chatMessage) =>
                  message.chat_post_id === data.chat_post_id
              );

              if (parentMessageIndex > -1) {
                const parentMessage = newState.data[parentMessageIndex];
                parentMessage.content = data.content;
                parentMessage.timestamp = data.timestamp;
                newState.data[parentMessageIndex] = parentMessage;
              }
              return { ...prevMessage, data: newState.data };
            }
            return [];
          });
        }
      });

      channelPusher.bind("message.deleted", (data: chatMessage) => {
        if (data.parent_id != null) {
          setMessages((prevMessage: chatMessagesListResponse) => {
            if (prevMessage.data) {
              let newState = cloneDeep(prevMessage);
              const parentMessageIndex = newState.data.findIndex(
                (message: chatMessage) =>
                  message.chat_post_id === data.parent_id
              );

              if (parentMessageIndex > -1) {
                const parentMessage = newState.data[parentMessageIndex];
                if (parentMessage.replies) {
                  parentMessage.replies = parentMessage.replies.map(
                    (message: chatMessage) => {
                      let reply = cloneDeep(message);
                      if (message.chat_post_id === data.chat_post_id) {
                        reply.deleted = 1;
                        reply.attached_files = [];
                      }
                      return reply;
                    }
                  );
                  parentMessage.total_replies =
                    Number(parentMessage.total_replies) - 1;
                }

                if (parentMessage.total_replies === 0) {
                  newState.data.splice(parentMessageIndex, 1);
                } else {
                  newState.data[parentMessageIndex] = parentMessage;
                }
              }
              return { ...prevMessage, data: newState.data };
            }
            return [];
          });
        } else {
          setMessages((prevMessage: chatMessagesListResponse) => {
            if (prevMessage.data) {
              let newState = cloneDeep(prevMessage);
              const parentMessageIndex = newState.data.findIndex(
                (message: chatMessage) =>
                  message.chat_post_id === data.chat_post_id
              );

              if (parentMessageIndex > -1) {
                const parentMessage = newState.data[parentMessageIndex];
                parentMessage.deleted = 1;
                parentMessage.attached_files = [];
                newState.data[parentMessageIndex] = parentMessage;
              }
              return { ...prevMessage, data: newState.data };
            }
            return [];
          });
        }
      });

      return () => {
        if (channel == "reply") {
          channelPusher.unbind("message.receive-reply-chat");
        }
        if (channel == "mention") {
          channelPusher.unbind("message.receive-mention-chat");
        }
        channelPusher.unbind("message.updated");
        channelPusher.unbind("message.deleted");
      };
    }
  }, [channelPusher, channel, focusChatPostId]);

  useEffect(() => {
    setScrollable(false);
    if (dataMessages) {
      setMessages((prevMessage: chatMessagesListResponse) => ({
        data:
          prevMessage && !isFirstRender
            ? [
                ...(prevMessage?.data || []),
                ...dataMessages.data.map((message: chatMessage) => {
                  message.isFirstPage =
                    message.replies && message.replies[0]?.unread == 1;
                  return message;
                }),
              ]
            : dataMessages.data.map((message: chatMessage) => {
                message.isFirstPage =
                  message.replies && message.replies[0]?.unread == 1;
                return message;
              }),
        total_count: dataMessages.total_count,
      }));
    }
    setScrollable(true);
  }, [dataMessages]);
  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch((err) => console.error(err));

  const handleShowMoreMessage = (chatPostId: number) => {
    const parentMessage = messages.data.find(
      (message: chatMessage) => message.chat_post_id === chatPostId
    );
    if (parentMessage && parentMessage.replies.length > 0) {
      setParamsLoadMoreThread({
        chat_post_parent_id: chatPostId,
        chat_post_id: parentMessage.isFirstPage
          ? null
          : parentMessage.replies[0].chat_post_id,
        per_page: parentMessage.isFirstPage
          ? parentMessage.replies.length + PER_PAGE_OF_MESSAGES
          : PER_PAGE_OF_MESSAGES,
      });
      parentMessage.isFirstPage = false;
      setMessages((prevMessage: chatMessagesListResponse) => {
        if (prevMessage.data) {
          prevMessage.data.map((message: chatMessage) => {
            if (message.chat_post_id === parentMessage.chat_post_id)
              return parentMessage;
            return message;
          });
          return prevMessage;
        }
        return [];
      });
    }
  };

  const readMessage = async (chatPostId: number, type: string | undefined) => {
    setIsScrolling(true);

    await ax
      .post(`api/v1/chat/message/${chatPostId}/read`, { type })
      .then(() => setIsScrolling(false));
    setMessages((prevMessage: chatMessagesListResponse) => {
      if (prevMessage.data) {
        prevMessage.data.map((message: chatMessage) => {
          if (message.chat_post_id === chatPostId) {
            message.unread = 0;
            message.replies?.forEach((item) => {
              item.unread = 0;
            });
          }
          return message;
        });
        return prevMessage;
      }
      return [];
    });
    setShowReloadButton(false);
  };

  const urlWithParams = paramsLoadMoreThread.chat_post_parent_id
    ? paramsLoadMoreThread.chat_post_id
      ? `api/v1/chat/thread/${paramsLoadMoreThread.chat_post_parent_id}?chat_post_id=${paramsLoadMoreThread.chat_post_id}&per_page=${paramsLoadMoreThread.per_page}&is_mobile=0`
      : `api/v1/chat/thread/${paramsLoadMoreThread.chat_post_parent_id}?per_page=${paramsLoadMoreThread.per_page}&is_mobile=0`
    : null;

  const { data: threads } = useSWR(urlWithParams, fetcher);
  const { ref, inView } = useInView();

  useEffect(() => {
    if (inView && scrollable && dataMessages && dataMessages.data) {
      setTimeout(() => {
        loadMore(dataMessages.data[dataMessages.data.length - 1]);
      }, 300);
    }
  }, [inView]);

  useEffect(() => {
    if (threads?.data && paramsLoadMoreThread.chat_post_parent_id) {
      const filteredThreads = threads.data.filter(
        (thread: chatMessage) =>
          thread.parent_id === paramsLoadMoreThread.chat_post_parent_id
      );
      const newMessages = messages?.data?.map((msg: chatMessage) => {
        if (msg.chat_post_id === paramsLoadMoreThread.chat_post_parent_id) {
          return {
            ...msg,
            replies: paramsLoadMoreThread.chat_post_id
              ? [...(filteredThreads?.reverse() ?? []), ...(msg.replies ?? [])]
              : (filteredThreads?.reverse() ?? []),
          };
        }
        return msg;
      });
      setMessages({ ...messages, data: newMessages });
    }
  }, [threads]);

  const handleReloadPage = () => {
    setShowReloadButton(false);
    setUnreadCount(0);
    topRef.current?.scrollIntoView({ behavior: "smooth" });

    const checkIfScrollStopped = () => {
      const currentScroll = window.scrollY;
      setTimeout(() => {
        if (window.scrollY === currentScroll) {
          reloadPage();
        } else {
          requestAnimationFrame(checkIfScrollStopped);
        }
      }, 500);
    };

    requestAnimationFrame(checkIfScrollStopped);
  };

  const handleScrollToLatestUnread = () => {
    if (isScrolling) return;

    const latestUnreadId = findLatestUnread();
    if (unreadRef.current) {
      unreadRef.current.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });

      if (latestUnreadId) {
        setHighlightedId(latestUnreadId);
        setTimeout(() => {
          setHighlightedId(null);
        }, 500);
      }
    } else {
      bottomRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  };

  const findLatestUnread = () => {
    if (!messages?.data.length) {
      return null;
    }

    for (const item of messages.data) {
      if (item.unread === 1) {
        return item.chat_post_id;
      }

      if (item.replies?.length) {
        for (const reply of item.replies) {
          if (reply.unread === 1) {
            return item.chat_post_id;
          }
        }
      }
    }

    return null;
  };

  useEffect(() => {
    setUnreadCount(badgeCount);
  }, [badgeCount]);

  const handleRemove = (
    parentId?: number | string,
    childId: string | number | null = null
  ) => {
    const newMessages = (prevMessage: chatMessagesListResponse) => {
      if (!childId) {
        return prevMessage?.data.filter(
          (msg: chatMessage) => msg.chat_post_id !== parentId
        );
      }

      return prevMessage?.data.map((msg: chatMessage) => {
        if (msg.chat_post_id === parentId && childId) {
          return {
            ...msg,
            replies: msg?.replies
              ? msg.replies.filter((child) => child.chat_post_id !== childId)
              : [],
          };
        }
        return msg;
      });
    };

    setMessages((prevMessage: chatMessagesListResponse) => ({
      ...prevMessage,
      data: newMessages(prevMessage),
    }));
  };

  if (messages && messages?.data && messages?.data.length === 0)
    return (
      <div className={className}>
        <div className="messages-list nonactive no-data">
          表示するメッセージがありません。
        </div>
      </div>
    );

  if (
    (isFirstRender && isLoading) ||
    (!isFirstRender && isLoading && !messages)
  )
    return (
      <div className={`${className} box-chat`}>
        <section className="messages-list">
          <div
            className="messages-list nonactive"
            style={{
              display: "flex",
              marginRight: "20px",
              flexDirection: "column-reverse",
            }}
          >
            <Skeleton height={14} mb={"xl"} width="30%" />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} width="60%" />
            <Skeleton height={8} mb={"md"} width="20%" />
            <Skeleton height={40} circle mb={"xs"} />
            <Skeleton height={14} mb={"xl"} width="30%" />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} width="60%" />
            <Skeleton height={8} mb={"md"} width="20%" />
            <Skeleton height={40} circle mb={"xs"} />
          </div>
        </section>
      </div>
    );

  return (
    <div
      className={className}
      style={{ overflowY: "scroll", height: `calc(100vh - 248px)` }}
      key="box-threads"
    >
      <div ref={topRef} />
      {messages?.data &&
        messages?.data.map((item: chatMessage, index: number) => {
          const isHighlighted = highlightedId === item.chat_post_id;
          return (
            <div
              key={`${item.chat_post_id}-${index}`}
              ref={item.chat_post_id == findLatestUnread() ? unreadRef : null}
              style={{
                backgroundColor: isHighlighted ? "#FFEDEC" : "",
                transition: "background-color 0.5s ease",
              }}
            >
              <MessageItem
                index={index}
                message={item}
                className={className}
                mutate={mutate}
                handleEmit={handleShowMoreMessage}
                channel={channel}
                onRemove={handleRemove}
                onClickReadButton={onClickReadButton}
                readMessage={readMessage}
              />
            </div>
          );
        })}

      {unreadCount > 0 && (
        <div className={className}>
          <div className="scroll-bottom-wrap">
            <button
              className="scroll-bottom"
              onClick={
                showReloadButton ? handleReloadPage : handleScrollToLatestUnread
              }
            >
              {showReloadButton ? <IconArrowUp /> : <IconArrowDown />}
              <span>{unreadCount} 件の未読メッセージ</span>
            </button>
          </div>
        </div>
      )}

      <div ref={bottomRef} />

      {messages?.data &&
        messages.data.length > 0 &&
        messages.data.length < messages.total_count && (
          <div
            className={className}
            ref={ref}
            style={{ display: "flex", justifyContent: "center" }}
          >
            <div className="spinner" role="status"></div>
          </div>
        )}
    </div>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-rows: 1fr auto;
  margin: 16px;
  padding-top: 8px;

  .thread-view .sender-footer {
    padding-right: 0px;
  }

  .channel-name {
    font-size: 14px;
    color: ${propcolors.grayChannelName};
    font-weight: 400;
    padding: 8px 19px 8px;
  }

  .box-channel {
    background-color: ${propcolors.white};
    border: 1px solid ${propcolors.gray[200]};
    border-radius: 8px;

    .messages {
      &-list {
        display: flex;
        flex-direction: column;

        &.nonactive {
          margin-left: 1rem;
          padding-left: 16px;
        }
      }
      .no-data {
        padding: 15px;
      }

      &-item {
      }
    }
  }

  .chat-content-edit {
    background-color: ${propcolors.backgroundChat} !important;

    &-edit {
      .chat-attachments,
      .chat-threads {
        margin-top: 12px !important;
      }
    }
  }

  .spinner {
    height: 18px;
    width: 18px;
    display: inline-block;
    border-radius: 50%;
    border-width: 2px;
    border-style: solid;
    border-color: #f93932;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .scroll-bottom-wrap {
    display: flex;
    justify-content: center;
    align-content: center;
    position: fixed;
    bottom: 42px;
    left: 50%;
    transform: translateX(50%);
    z-index: 1000;

    .scroll-bottom {
      background: #f93932;
      color: #ffffff;
      border-radius: 30px;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;

      svg {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }
  }
`;

export const ListThreadWithChannel: React.FC<ChatMessengerProps> = ({
  channel,
  badgeCount,
  channelPusher,
  onClickReadButton,
}) => {
  const [params, setParams] = useState<{
    chat_post_id: number | null;
  }>({ chat_post_id: null });

  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch((err) => console.error(err));

  useEffect(() => {
    setParams({ chat_post_id: null });
    if (channel) mutateList();
  }, [channel]);

  const url =
    channel == "mention" ? "api/v1/chat/mentions" : "api/v1/chat/reply";

  const urlWithParams = params.chat_post_id
    ? `${url}?chat_post_id=${params.chat_post_id}`
    : url;

  const {
    data: dataMessages,
    mutate: mutateList,
    isValidating: isLoading,
  } = useSWR(urlWithParams, fetcher, {
    revalidateOnFocus: false,
  });

  const loadMore = (message: chatMessage) =>
    setParams({ chat_post_id: message.chat_post_id });

  const reloadPage = () => {
    setParams({ chat_post_id: null });
    mutateList();
  };

  return (
    <Styled
      channelPusher={channelPusher}
      dataMessages={dataMessages}
      mutate={mutateList}
      loadMore={loadMore}
      isLoading={isLoading}
      badgeCount={badgeCount}
      channel={channel}
      params={params}
      onClickReadButton={onClickReadButton}
      reloadPage={reloadPage}
    />
  );
};
