import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import { useEffect, useState } from "react";
import { Tabs } from "@mantine/core";
import { ax } from "utils/axios";
import useSWRImmutable from "swr/immutable";
import { useRouter } from "next/router";
import IconUserVendor from "../../../../public/icons/user-icon.svg";
import Image from "next/image";

type ChannelUserProps = {};

type PresentationProps = {
  className?: string;
  vendorUserList: VendorUser[] | null;
  partnerUserList: PartnerUser[] | null;
  isChannelMembersValidating: boolean;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  vendorUserList,
  partnerUserList,
  isChannelMembersValidating,
}) => {
  return (
    <div className={className}>
      <Tabs defaultValue={"vendorUser"}>
        <Tabs.List>
          <Tabs.Tab value="vendorUser">ベンダーユーザー</Tabs.Tab>
          <Tabs.Tab value="partnerUser">パートナーユーザー</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="vendorUser">
          {!isChannelMembersValidating ? (
            vendorUserList && vendorUserList.length > 0 ? (
              vendorUserList.map((user, index) => (
                <div className="userList" key={index}>
                  <div className="avatar-user">
                    {user.logo_url ? (
                      <Image
                        src={user.logo_url}
                        style={{ borderRadius: "40px" }}
                        width={40}
                        height={40}
                        alt=""
                      />
                    ) : (
                      <IconUserVendor className="imgSpan" />
                    )}
                  </div>
                  <div className="user-info">
                    <div className="user-name">{user.name}</div>
                    <div className="user-email">{user.email}</div>
                  </div>
                </div>
              ))
            ) : (
              <p className="name">
                このチャットにはユーザーが参加していません。
              </p>
            )
          ) : (
            <p className="name">ユーザー情報取得中です...</p>
          )}
        </Tabs.Panel>
        <Tabs.Panel value="partnerUser">
          {!isChannelMembersValidating ? (
            partnerUserList && partnerUserList.length > 0 ? (
              partnerUserList.map((user, index) => (
                <div className="userList" key={index}>
                  <div className="avatar-user">
                    {user.logo_url ? (
                      <Image
                        src={user.logo_url}
                        style={{ borderRadius: "40px" }}
                        width={40}
                        height={40}
                        alt=""
                      />
                    ) : (
                      <IconUserVendor className="imgSpan" />
                    )}
                  </div>
                  <div className="user-info">
                    <div className="user-name">{user.name}</div>
                    <div className="user-email">{user.email}</div>
                  </div>
                </div>
              ))
            ) : (
              <p className="name">
                このチャットにはユーザーが参加していません。
              </p>
            )
          ) : (
            <p className="name">ユーザー情報取得中です...</p>
          )}
        </Tabs.Panel>
      </Tabs>
    </div>
  );
};

const Styled = styled(Presentation)`
  .userList {
    display: inline-block;
    vertical-align: middle;
    line-height: normal;
    width: 100%;
    border-bottom: 1px solid ${propcolors.border};
    padding: 16px;
    .avatar-user {
      float: left;
      margin-right: 16px;
      margin-top: 1px;
      height: 42px;
      align-content: end;
    }
    .user-info {
      align-content: center;
      div {
        line-height: normal;
      }
      .user-name {
        color: ${propcolors.blackLight};
        font-size: 14px;
        font-weight: 400;
        line-height: 16.94px;
        margin-bottom: 8px;
      }
      .user-email {
        color: ${propcolors.blackLight};
        font-size: 14px;
        font-weight: 400;
        line-height: 16.94px;
      }
    }
  }
  .mantine-Tabs-tabsList {
    border: 0px;
    .mantine-Tabs-tab {
      border: 0px;
      width: 50%;
      padding: 16px 8px;
      background-color: ${propcolors.white};
      border: 1px solid ${propcolors.border};
      font-weight: 600;
      font-size: 14px;
      height: 46px;
      border-radius: 0px 8px 8px 0px;

      &:first-of-type {
        border-radius: 8px 0px 0px 8px;
      }

      &:hover {
        border-color: ${propcolors.border};
        background-color: ${propcolors.backgroundHover};
      }

      &[data-active] {
        background-color: ${propcolors.blackLight};
        border-color: ${propcolors.blackLight};
        color: ${propcolors.white};
      }
    }
  }
  .mantine-Tabs-panel {
    margin-top: 24px;
  }
`;

export const ChannelUser: React.FC<ChannelUserProps> = ({}) => {
  const [vendorUserList, setVendorUserList] = useState<VendorUser[] | null>(
    null
  );
  const [partnerUserList, setPartnerUserList] = useState<PartnerUser[] | null>(
    null
  );
  const { query } = useRouter();
  const { id } = query;
  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch((err) => {});
  const {
    data: channelMembers,
    mutate: mutateChannelMembers,
    isValidating: isChannelMembersValidating,
  } = useSWRImmutable(`api/v1/chat/${id}/members`, fetcher);

  useEffect(() => {
    if (channelMembers && channelMembers.length > 0) {
      setVendorUserList(
        channelMembers.filter(
          (channelMember: chatUser) => channelMember.vendor_user_id
        )
      );
      setPartnerUserList(
        channelMembers.filter(
          (channelMember: chatUser) => channelMember.partner_user_id
        )
      );
    }
  }, [channelMembers]);
  return (
    <Styled
      vendorUserList={vendorUserList}
      partnerUserList={partnerUserList}
      isChannelMembersValidating={isChannelMembersValidating}
    />
  );
};
