import styled from "@emotion/styled";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";
import useSWR from "swr";
import useSWRImmutable from "swr/immutable";
import { ax } from "utils/axios";
import { ChatMessageBlock } from "../message";
import React, { useEffect, useRef, useState } from "react";
import { SendForm } from "../sendForm";
import { ChatThreads } from "../threads";
import { Skeleton } from "@mantine/core";
import { useInView } from "react-intersection-observer";
import { Channel } from "pusher-js";
import { IconArrowUp } from "@tabler/icons-react";
import {
  useChatCountUnreadChannel,
  useSetChatCountUnreadChannel,
} from "utils/recoil/chatList/chatUnreadChannelState";
import { useSetChatPostIds } from "utils/recoil/chatList/chatPostIdsState";
import {
  useChannelUnreadAllMessage,
  useSetChannelUnreadAllMessage,
} from "utils/recoil/chatList/chatChannelUnreadAllMessageState";
import { parseCookies } from "nookies";

type ChatMessengerProps = {
  channelPusher: Channel | null;
  updateBadgeCount: () => void;
  sendDataPostRead: (postId: number[]) => void;
  updateUnread: () => void;
};

type PresentationProps = {
  className?: string;
  channelPusher: Channel | null;
  dataMessages: chatMessage[] | null;
  isValidatingMessage: boolean;
  mentionables: chatMentionableUser[] | null;
  mutate: () => void;
  totalMessages: number;
  loadMore: (message: chatMessage) => void;
  params: { chat_post_id: number | null };
  updateBadgeCount: () => void;
  updateUnread: () => void;
  loadMoreUnread: (message: chatMessage, chat_post_end_id: number) => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  channelPusher,
  dataMessages,
  isValidatingMessage,
  mentionables,
  mutate,
  totalMessages,
  loadMore,
  params,
  updateBadgeCount,
  updateUnread,
  loadMoreUnread,
}) => {
  const { query } = useRouter();
  const { id, threads } = query;
  const router = useRouter();
  const classThread = router.query["threads"] || null;
  const [flgFile, setFlgFile] = useState<boolean>(false);
  const [scrollable, setScrollable] = useState<boolean>(true);
  const [messages, setMessages] = useState<chatMessage[] | null>(null);
  const [newThreadMessage, setNewThreadMessage] = useState<chatMessage | null>(
    null
  );
  const isFirstRender = params.chat_post_id === null;
  const [isUnreadThreads, setIsUnreadThreads] = useState<{
    parentId: number;
    is_unread: boolean;
  }>({ parentId: 0, is_unread: false });
  const chatCountUnreadChannel = useChatCountUnreadChannel();
  const [visibleItems, setVisibleItems] = useState<number[]>([]);
  const setChatCountUnreadChannel = useSetChatCountUnreadChannel();

  useEffect(() => {
    return () =>
      setChatCountUnreadChannel({
        count: 0,
        lastPostIdUnread: null,
        listId: visibleItems,
      });
  }, []);

  useEffect(() => {
    if (messages) {
      setMessages((prev) => {
        if (!prev) return [];

        prev.map((item) => {
          if (item.chat_post_id == isUnreadThreads.parentId) {
            item.is_replies_unreads = isUnreadThreads.is_unread;
          }
          return item;
        });
        return prev;
      });
    }
  }, [isUnreadThreads]);
  const updateChatMessageBlock = (
    parentId: number | null,
    messageData: any
  ) => {
    if (parentId !== null && !isNaN(parentId)) {
      const userCookies = JSON.parse(parseCookies(null, ["user"]).user);
      const newData = {
        ...messageData,
        unread: messageData.user.vendor_user_id != userCookies.user_id,
      };
      setNewThreadMessage(newData);
      setMessages((prev) => {
        if (prev) {
          const parentMessage = prev.find(
            (message) => message.chat_post_id === parentId
          );
          if (parentMessage) {
            if (typeof parentMessage.total_replies === "number") {
              parentMessage.total_replies++;
            } else if (!parentMessage.total_replies) {
              parentMessage.total_replies = 1;
            }
            if (userCookies.user_id != messageData.user.vendor_user_id) {
              parentMessage.is_replies_unreads = newData.is_replies_unreads;
            }
          }
          return prev;
        } else {
          return [];
        }
      });
    } else {
      setMessages((prev) => [messageData, ...(prev ?? [])]);
    }
  };

  useEffect(() => {
    if (channelPusher) {
      channelPusher.bind("message.created", (data: chatMessage) => {
        if (data.channel_id === Number(id)) {
          updateUnread();
          if (data?.user?.vendor_user_id !== undefined) {
            const params: any = {
              vendor_user_id: data?.user?.vendor_user_id || "",
              partner_user_id: data?.user?.partner_user_id || "",
              parent_id: data?.parent_id || "",
            };

            const queryString = Object.keys(params)
              .map((key) => `${key}=${encodeURIComponent(params[key])}`)
              .join("&");

            ax.get(`/api/v1/get-avatar-chat?${queryString}`)
              .then((res) => {
                const userData = {
                  ...data.user!,
                  logo_url: res?.data?.avatar_url || null,
                  file_name: res?.data?.file_name || null,
                };
                const userCookies = JSON.parse(
                  parseCookies(null, ["user"]).user
                );
                if (userCookies.user_id === data.user.vendor_user_id) {
                  data.unread = 0;
                }
                const messageData = {
                  ...data!,
                  user: userData,
                };
                updateChatMessageBlock(data.parent_id, messageData);
              })
              .catch((error) => {
                if (error) {
                  const userData = {
                    ...data.user!,
                    logo_url: null,
                    file_name: "default-avatar",
                  };
                  const messageData = {
                    ...data!,
                    user: userData,
                  };
                  updateChatMessageBlock(data.parent_id, messageData);
                }
              });
          } else {
            if (data.parent_id !== null) {
              setNewThreadMessage(data);
            }
            setMessages((prev) => [data, ...(prev ?? [])]);
          }
        }
      });

      channelPusher.bind("message.updated", (data: chatMessage) => {
        if (data.channel_id === Number(id)) {
          if (data.parent_id === null) {
            setMessages((prev) => {
              if (prev) {
                return prev.map((message) => {
                  if (message.chat_post_id === data.chat_post_id) {
                    message.content = data.content;
                  }
                  return message;
                });
              } else {
                return [];
              }
            });
          } else {
            setNewThreadMessage({ ...data, isUpdated: true });
          }
        }
      });

      channelPusher.bind("message.deleted", (data: chatMessage) => {
        if (data.channel_id === Number(id)) {
          if (data.parent_id === null) {
            setMessages((prev) => {
              if (prev) {
                return prev.map((message) => {
                  if (message.chat_post_id === data.chat_post_id) {
                    message.deleted = 1;
                    message.attached_files = [];
                  }
                  return message;
                });
              } else {
                return [];
              }
            });
          } else {
            setNewThreadMessage({ ...data, isDeleted: true });
            setMessages((prev) => {
              if (prev) {
                const parentMessage = prev.find(
                  (message) => message.chat_post_id === data.parent_id
                );
                if (parentMessage) {
                  if (typeof parentMessage.total_replies === "number") {
                    parentMessage.total_replies--;
                  } else if (!parentMessage.total_replies) {
                    parentMessage.total_replies = 0;
                  }
                }
                return prev;
              } else {
                return [];
              }
            });
          }
        }
      });

      return () => {
        channelPusher.unbind("message.created");
        channelPusher.unbind("message.updated");
        channelPusher.unbind("message.deleted");
      };
    }
  }, [channelPusher, id, query]);

  const handleFile = (attachments: File[] | null) => {
    setFlgFile(false);
    if (attachments && attachments.length > 0) {
      setFlgFile(true);
    }
  };

  const { ref, inView } = useInView();
  useEffect(() => {
    if (inView && scrollable && dataMessages) {
      setTimeout(() => {
        if (dataMessages[dataMessages.length - 1]) {
          loadMore(dataMessages[dataMessages.length - 1]);
        }
      }, 300);
    }
  }, [inView]);
  const [isToUnread, setIsToUnread] = useState(false);
  useEffect(() => {
    setScrollable(false);
    if (dataMessages) {
      setMessages((prevMessage) =>
        prevMessage && !isFirstRender
          ? [...prevMessage, ...dataMessages]
          : dataMessages
      );
    }
    setScrollable(true);
    if (isToUnread) {
      gotoUnread();
    }
  }, [dataMessages]);

  useEffect(() => {
    if (inView && scrollable && messages) {
      setTimeout(() => {
        loadMore(messages[messages.length - 1]);
      }, 300);
    }
  }, [inView]);
  const itemsRef = useRef<(HTMLDivElement | null)[]>([]);
  const setChatPostIds = useSetChatPostIds();
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        setVisibleItems((prev) => {
          return entries
            .filter((entry) => entry.isIntersecting)
            .map((entry) => {
              const target = entry.target as HTMLElement;
              const childElement = target.querySelector(
                ".chat-content"
              ) as HTMLElement;
              if (childElement) {
                // unread mess
                setMessages((prev) => {
                  if (!prev) return [];
                  prev.map((item) => {
                    if (
                      item.chat_post_id == Number(target.dataset.key) &&
                      item.unread == 1
                    ) {
                      item.unread = 0;
                    }
                    return item;
                  });
                  return prev;
                });
              }
              return Number(target.dataset.key);
            });
        });
      },
      { threshold: 0.5 }
    );

    itemsRef.current.forEach((item) => {
      if (item && Number(item.dataset.unread) == 1) observer.observe(item);
    });

    return () => observer.disconnect();
  }, [messages]);
  const gotoUnread = () => {
    if (chatCountUnreadChannel.lastPostIdUnread != null) {
      let check = false;
      let key = 0;
      itemsRef.current.forEach((item, index) => {
        if (
          Number(item?.dataset.key) == chatCountUnreadChannel.lastPostIdUnread
        ) {
          check = true;
          key = index;
          return;
        }
      });
      if (check) {
        // @ts-ignore
        itemsRef.current[key].scrollIntoView({
          behavior: "smooth",
          block: "end",
        });
        setIsToUnread(false);
      } else {
        if (dataMessages) {
          setIsToUnread(true);
          loadMoreUnread(
            dataMessages[dataMessages.length - 1],
            chatCountUnreadChannel.lastPostIdUnread
          );
        }
      }
    }
  };
  useEffect(() => {
    setChatPostIds(visibleItems);
  }, [visibleItems]);

  const channelReadAllMessage = useChannelUnreadAllMessage();
  const setChannelUnreadAllMessage = useSetChannelUnreadAllMessage();

  useEffect(() => {
    if (channelReadAllMessage && id && channelReadAllMessage == Number(id)) {
      setMessages(
        (prevMessages) =>
          prevMessages &&
          prevMessages.map((msg) => {
            return channelReadAllMessage == Number(id)
              ? {
                  ...msg,
                  unread: 0,
                  is_replies_unreads: false,
                }
              : msg;
          })
      );
      setChatCountUnreadChannel({
        count: 0,
        lastPostIdUnread: null,
        listId: visibleItems,
      });
      setChannelUnreadAllMessage(null);
    }
  }, [channelReadAllMessage]);

  useEffect(() => {
    setTimeout(() => {
      setMessages(
        (prevMessages) =>
          prevMessages &&
          prevMessages.map((msg) => {
            return chatCountUnreadChannel.listId &&
              chatCountUnreadChannel.listId.includes(msg.chat_post_id) &&
              msg.unread !== 0
              ? {
                  ...msg,
                  unread: 0,
                }
              : msg;
          })
      );
    }, 1000);
  }, [chatCountUnreadChannel.count]);

  return (
    <div
      className={`${className} box-chat ${
        classThread ? "thread-show-message" : ""
      } ${flgFile ? "box-chat-send-file" : ""}`}
    >
      {threads && (
        <ChatThreads
          threads={threads ? threads.toString() : null}
          mentionables={mentionables}
          mode="thread"
          newThreadMessage={newThreadMessage}
          updateBadgeCount={updateBadgeCount}
          updateUnread={setIsUnreadThreads}
        />
      )}
      <section
        className="messages-list"
        style={{ paddingBottom: 0, paddingRight: 16 }}
      >
        {!threads &&
          chatCountUnreadChannel &&
          chatCountUnreadChannel.count > 0 && (
            <div className="scroll-top-wrap">
              <button
                className="scroll-bottom"
                onClick={() => {
                  gotoUnread();
                }}
              >
                <IconArrowUp />
                <span>{chatCountUnreadChannel.count} 件の未読メッセージ</span>
              </button>
            </div>
          )}
        {(!isFirstRender && isValidatingMessage && !messages) ||
        (isFirstRender && isValidatingMessage) ? (
          <div className="messages-list nonactive">
            <Skeleton height={14} mb={"xl"} width="30%" />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} width="60%" />
            <Skeleton height={8} mb={"md"} width="20%" />
            <Skeleton height={40} circle mb={"xs"} />
            <Skeleton height={14} mb={"xl"} width="30%" />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} width="60%" />
            <Skeleton height={8} mb={"md"} width="20%" />
            <Skeleton height={40} circle mb={"xs"} />
          </div>
        ) : (
          <>
            {messages && messages.length === 0 ? (
              <div className="messages-list nonactive">
                表示するメッセージがありません。
              </div>
            ) : messages ? (
              messages.map((msg: chatMessage, index) => {
                return (
                  <div
                    key={index}
                    data-unread={msg.unread}
                    ref={(el) => (itemsRef.current[index] = el)}
                    data-key={msg.chat_post_id}
                  >
                    <ChatMessageBlock
                      key={msg.chat_post_id}
                      message={msg}
                      content={msg.content}
                      mentionables={mentionables}
                      mode={"channel"}
                      mutate={mutate}
                      totalReplies={Number(msg.total_replies)}
                      messageIndex={index}
                    />
                  </div>
                );
              })
            ) : (
              <></>
            )}

            {messages &&
              messages.length > 0 &&
              messages.length < totalMessages && (
                <div
                  ref={ref}
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    padding: "8px 0",
                  }}
                >
                  <div className="spinner" role="status"></div>
                </div>
              )}
          </>
        )}
      </section>
      <SendForm
        mentionables={mentionables}
        mode="channel"
        mutate={mutate}
        handleFile={handleFile}
      />
    </div>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-rows: 1fr auto;
  margin: 8px 16px 0;
  overflow: hidden;
  height: calc(100vh - 406px);
  &.box-chat-send-file {
    height: calc(100vh - 475px);
  }
  .thread-view .sender-footer {
    padding-right: 0px;
  }
  .messages {
    &-list {
      display: flex;
      overflow-y: scroll;
      height: 100%;
      flex-direction: column-reverse;
      &.nonactive {
        margin-left: 1rem;
      }
    }
  }

  .chat-content-edit {
    background-color: ${propcolors.backgroundChat} !important;
    &-edit {
      .chat-attachments,
      .chat-threads {
        margin-top: 12px !important;
      }
    }
  }

  .spinner {
    height: 18px;
    width: 18px;
    display: inline-block;
    border-radius: 50%;
    border-width: 2px;
    border-style: solid;
    border-color: #f93932;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .scroll-top-wrap {
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-content: center;
    position: absolute;
    top: 30px;
    left: 0;
    width: 100%;
    z-index: 1000;

    .scroll-bottom {
      background: #f93932;
      color: #ffffff;
      border-radius: 30px;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;

      svg {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }
  }

  .scroll-bottom-wrap {
    display: flex;
    justify-content: center;
    align-content: center;
    position: absolute;
    top: 30px;
    left: 25%;
    transform: translateX(50%);
    z-index: 1000;

    .scroll-bottom {
      background: #f93932;
      color: #ffffff;
      border-radius: 30px;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;

      svg {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }
  }
`;

export const ChatMessenger: React.FC<ChatMessengerProps> = ({
  channelPusher,
  updateBadgeCount,
  updateUnread,
}) => {
  const [message, setMessage] = useState<chatMessage[] | null>(null);
  const [totalMessages, setTotalMessages] = useState<number>(0);
  const { query } = useRouter();
  const { id } = query;
  const [params, setParams] = useState<{
    chat_post_id: number | null;
    chat_post_end?: number | null;
  }>({
    chat_post_id: null,
    chat_post_end: null,
  });

  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch((err) => {});

  const urlWithParams = params.chat_post_id
    ? `api/v1/chat/${id}/messages?chat_post_id=${params.chat_post_id}&chat_post_end=${params.chat_post_end}&is_mobile=0`
    : `api/v1/chat/${id}/messages?is_mobile=0`;

  const {
    data: messages,
    mutate: mutateMessageList,
    isValidating: isValidatingMessage,
  } = useSWR(urlWithParams, fetcher);

  const { data: mentionables, mutate: mutateMentionables } = useSWRImmutable(
    `api/v1/chat/${id}/mentionables`,
    fetcher
  );

  useEffect(() => {
    setParams({ chat_post_id: null });
    if (id) {
      // ここ絶対リファクタした方がいい処理、もっと効率的に過去のid参照してください
      mutateMentionables();
      mutateMessageList();
    }
  }, [id]);

  useEffect(() => {
    if (messages && messages.data) {
      setMessage(messages.data);
      setTotalMessages(messages.total_count);
    }
  }, [messages]);

  const loadMore = (message: chatMessage) => {
    setParams({ chat_post_id: message.chat_post_id });
  };
  const loadMoreUnread = (message: chatMessage, chat_post_end_id: number) => {
    setParams({
      chat_post_id: message.chat_post_id,
      chat_post_end: chat_post_end_id,
    });
  };

  return (
    <Styled
      channelPusher={channelPusher}
      dataMessages={message}
      isValidatingMessage={isValidatingMessage}
      mentionables={mentionables}
      mutate={mutateMessageList}
      totalMessages={totalMessages}
      loadMore={loadMore}
      params={params}
      updateBadgeCount={updateBadgeCount}
      updateUnread={updateUnread}
      loadMoreUnread={loadMoreUnread}
    />
  );
};
