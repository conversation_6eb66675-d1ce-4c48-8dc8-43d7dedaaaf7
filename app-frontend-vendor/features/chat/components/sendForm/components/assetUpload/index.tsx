import styled from "@emotion/styled";
import { useCallback, useState } from "react";
import { Button, FileInput } from "@mantine/core";

type AssetUploadProps = {
  setAssetList: (asset: File) => void;
};

type PresentationProps = {
  className?: string;
  asset: File | null;
  handleAssetUpload: (file: File) => void;
  handleAssetRegister: () => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  asset,
  handleAssetUpload,
  handleAssetRegister,
}) => {
  return (
    <div className={className}>
      <FileInput
        value={asset}
        onChange={handleAssetUpload}
        label="ファイル"
        placeholder="クリックして参照..."
      />
      <Button disabled={!asset} fullWidth onClick={handleAssetRegister}>
        登録
      </Button>
    </div>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  gap: 1rem;
`;

export const AssetUpload: React.FC<AssetUploadProps> = ({ setAssetList }) => {
  const [asset, setAsset] = useState<File | null>(null);
  const handleAssetUpload = useCallback((file: File) => {
    setAsset(file);
  }, []);
  const handleAssetRegister = () => {
    if (asset) {
      setAssetList(asset);
    }
  };
  return (
    <Styled
      asset={asset}
      handleAssetUpload={handleAssetUpload}
      handleAssetRegister={handleAssetRegister}
    />
  );
};
