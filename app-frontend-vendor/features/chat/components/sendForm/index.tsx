import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import React, { useEffect, useMemo, useState } from "react";
import { ActionIcon, Button, Modal } from "@mantine/core";
import { Dismiss16Filled } from "@fluentui/react-icons";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import { useDisclosure } from "@mantine/hooks";
import { convertFileToBase64 } from "utils/func/base64Converter";
import { notifications } from "@mantine/notifications";
import { FileUpload } from "../../../../components/FileUpload";
import useSWRImmutable from "swr/immutable";
import { MessageBox } from "components/form/message-box";
import { useMentionEditor } from "components/form/hooks/use-mention-editor";
import type { Editor } from "@tiptap/react";
import { RiCloseCircleLine } from "@remixicon/react";

type sendFormProps = {
  mentionables: chatMentionableUser[] | null | undefined;
  mode: "channel" | "thread";
  mutate?: () => void;
  handleFile?: (file: File[] | null) => void;
  className?: string;
  channelId?: string | number;
  chatPostId?: number;
};

type PresentationProps = {
  className?: string;
  editor: Editor | null;
  attachments: any[] | null;
  isSending: boolean;
  handleSend: () => void;
  assetModalOpened: boolean;
  openAssetModal: () => void;
  closeAssetModal: () => void;
  handleAssetUpload: (file: File) => void;
  handleDeleteAttachment: (index: number) => void;
} & sendFormProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  editor,
  mode,
  attachments,
  mentionables,
  isSending,
  handleSend,
  assetModalOpened,
  openAssetModal,
  closeAssetModal,
  handleAssetUpload,
  handleDeleteAttachment,
  chatPostId,
}) => {
  const [fileSelected, setFileSelected] = useState<File | null>(null);
  const [msgErrorFile, setMsgErrorFile] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);

  useEffect(() => {
    setMsgErrorFile(null);
    setFileSelected(null);
    setFileName(null);
  }, [assetModalOpened]);

  const onFileChange = (file: File | null) => {
    if (file) {
      setFileName(file.name);
      setFileSelected(file);
    } else {
      setFileName(null);
      setFileSelected(null);
    }
  };

  const handleUpload = () => {
    setMsgErrorFile(null);
    if (fileSelected) {
      handleAssetUpload(fileSelected);
    }
  };
  const router = useRouter();
  const classThread = router.query["threads"] || null;
  return (
    <div
      className={`${className} 
    ${classThread ? "thread-show" : ""}
    ${mode === "thread" ? "thread-view" : ""}`}
    >
      <>
        <MessageBox editor={editor} />
        {attachments && attachments.length > 0 && (
          <div className="sender-attachments">
            {attachments.map((file, index) => (
              <div className="sender-attachments-file" key={index}>
                <span>{file.name}</span>
                <ActionIcon
                  disabled={isSending}
                  onClick={() => handleDeleteAttachment(index)}
                >
                  <Dismiss16Filled />
                </ActionIcon>
              </div>
            ))}
          </div>
        )}
        <div className="sender-footer">
          <div className="sender-buttons">
            <Button
              onClick={openAssetModal}
              className={`btn-chat ${isSending ? "btn-disabled" : ""}`}
              compact
              variant="subtle"
              disabled={isSending}
            >
              ファイルを添付する
            </Button>
          </div>
          <Button
            onClick={handleSend}
            className="btn-chat btn-send"
            loading={isSending}
            disabled={
              editor?.isEmpty && (!attachments || attachments?.length == 0)
            }
          >
            {!isSending && "送信する"}
          </Button>
        </div>
      </>
      <Modal
        className="modal-upload modal-upload-chat"
        opened={assetModalOpened}
        onClose={closeAssetModal}
        withCloseButton={false}
        size={640}
        maw={640}
        miw={320}
      >
        <div className="modal-wrap">
          <div className="modal-header">
            <p>ファイルを添付</p>
          </div>
          <div className="modal-content">
            <div className="modal-content-input">
              <FileUpload
                label="ファイル"
                fileName={fileName}
                msgErrorFile={msgErrorFile}
                onFileChange={onFileChange}
              />
            </div>
            <div className="modal-footer">
              <div className="modal-buttons">
                <Button
                  className="modal-buttons-cancel full-width"
                  type="button"
                  color="gray"
                  onClick={closeAssetModal}
                >
                  キャンセル
                </Button>
                <Button
                  className="modal-buttons-submit full-width"
                  type="button"
                  disabled={!fileSelected}
                  onClick={handleUpload}
                >
                  登録
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-rows: 1fr auto auto;
  align-items: center;
  filter: drop-shadow(0 0 4px ${propcolors.gray[300]}30);
  ${(props) =>
    props.className
      ? props.className
      : "" +
        "position: absolute;" +
        "bottom: -16px;" +
        "left: -16px;" +
        `border: 1px solid ${propcolors.gray[200]};` +
        "margin: 1rem;" +
        `background-color: ${propcolors.white};`};
  width: 100%;
  padding: 12px 16px;
  z-index: 5;
  .thread-show-message & {
    width: 50%;
  }
  .btn-send,
  .btn-send:hover {
    background-color: ${propcolors.blackLight} !important;
    color: ${propcolors.white} !important;
  }
  .btn-send:disabled {
    background-color: #e8eaed !important;
  }
  .btn-disabled {
    background-color: #e9ecef !important;
    color: #adb5bd !important;
  }
  .sender {
    &-inputForm {
      padding: 1rem;
      border-bottom: 1px solid ${propcolors.border};
      & .pulse {
        animation: pulse 0.85s infinite;
        @keyframes pulse {
          0% {
            opacity: 0;
          }
          50% {
            opacity: 1;
          }
          100% {
            opacity: 0;
          }
        }
      }
    }
    &-attachments {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      align-items: center;
      padding: 1rem;
      font-size: 0.875rem;
      &-file {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem;
        border-radius: 0.5rem;
        border: 1px solid ${propcolors.gray[200]};
        span {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    &-footer {
      display: grid;
      grid-template-columns: 1fr auto;
      align-items: center;
      font-size: 0.875rem;
      margin-top: 10px;
    }
    &-buttons {
    }
  }
  .asset-modal {
    display: grid;
    gap: 1rem;
  }
  @media screen and (max-width: 600px) {
    .sender-input__highlighter {
      left: -6px;
    }
  }
`;

export const SendForm: React.FC<sendFormProps> = ({
  mentionables,
  mode,
  mutate,
  handleFile,
  className,
  channelId,
  chatPostId,
}) => {
  const [assetModalOpened, { open: openAssetModal, close: closeAssetModal }] =
    useDisclosure(false);
  const [attachments, setAttachments] = useState<File[] | null>(null);
  const [isSending, setIsSending] = useState(false);
  const mentions = useMemo(
    () =>
      mentionables
        ? mentionables.map((user) => ({
            label: user.label,
            id: user.text,
          }))
        : [],
    [mentionables]
  );
  const { editor } = useMentionEditor({ mentions });
  const { query } = useRouter();

  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch((err) => console.error(err));

  const { data: mention } = useSWRImmutable(
    channelId ? `api/v1/chat/${channelId}/mentionables` : null,
    fetcher
  );

  const handleSend = async () => {
    const content = editor?.getText({ blockSeparator: "\n" });
    if (editor?.isEmpty || !content) {
      return;
    }
    setIsSending(true);
    // 3000文字を超える場合のバリデーション
    if (content.length > 3000) {
      notifications.show({
        title:
          "メッセージの送信に失敗しました。3000文字以上の文章を送ることはできません。",
        message: `（現在の文字数：${content.length}文字）`,
        icon: (
          <RiCloseCircleLine size={32} css={{ color: propcolors.partnerRed }} />
        ),
        autoClose: 5000,
      });
      setIsSending(false);
      return;
    }

    // メンションのバリデーションを実施。CustomMentionInputにて置換済みのメンションに対して。
    // 置換された後の実例：@[ベンダー太郎]%text:ベンダー太郎:uuid=v90%
    //   @[名前]%text:表示名:uuid=v{vendor_user_id}%
    //   @[名前]%text:表示名:uuid=p{partner_user_id}%
    //   @[名前]%text:表示名:partner%
    //   @[名前]%text:表示名:vendor%
    //
    // テキストボックスvalueから@xxx:text:xxx形式のメンションを抽出。
    // text:以下の文字列を比較に使用する。
    const mentionPattern = /@\[([^\]]+)\]%text:([^\%]+)%/g;
    let match: RegExpExecArray | null;
    const mentionsInTextBox: { display: string; textId: string }[] = [];
    while ((match = mentionPattern.exec(content)) !== null) {
      mentionsInTextBox.push({ display: match[1], textId: match[2] });
    }

    // 取得済みのmentionables（そのch内でメンション可能な対象）から、text項目だけのリストを作る
    const mentionableTexts = mentionables
      ? mentionables.map((m) => m.text)
      : [];

    // チャンネル外へのメンションがあるかチェック
    const invalidMentions = mentionsInTextBox.filter(
      (m) => !mentionableTexts.includes(m.textId)
    );

    // チャンネル外へのメンションがある場合、エラーメッセージを表示
    if (invalidMentions.length > 0) {
      notifications.show({
        icon: (
          <RiCloseCircleLine size={32} css={{ color: propcolors.partnerRed }} />
        ),
        title: "チャンネル外メンバーへのメンションがあります",
        message: (
          <>
            @{invalidMentions.map((m) => m.display).join(", ")}{" "}
            は指定できません。
            <br />
            メンション対象を修正してください。
          </>
        ),
        autoClose: 5000,
      });
      setIsSending(false);
      return;
    }

    const attachedFiles =
      attachments &&
      (await Promise.all(
        attachments.map(async (file) => {
          const new_base64_string = (await convertFileToBase64(file)) as string;
          return {
            name: file.name,
            base64_string: new_base64_string.slice(
              new_base64_string.indexOf(",") + 1
            ),
          };
        })
      ));
    await ax
      .post(`/api/v1/chat/${channelId ?? query.id}/messages`, {
        content: content,
        parent_id:
          Number(query.threads) && mode === "thread"
            ? Number(query.threads)
            : (chatPostId ?? null),
        attached_files: attachedFiles,
      })
      .then(() => {
        editor?.commands.setMentionContent("");
      })
      .finally(() => {
        setIsSending(false);
      });
    setAttachments(null);
  };
  useEffect(() => {
    if (handleFile !== undefined) {
      handleFile(attachments);
    }
  }, [attachments]);

  const handleAssetUpload = (file: File) => {
    setAttachments((prev) => (prev ? [...prev, file] : [file]));
    closeAssetModal();
  };
  const handleDeleteAttachment = (index: number) => {
    setAttachments((prev) => prev?.filter((_, i) => i !== index) ?? null);
  };
  return (
    <Styled
      editor={editor}
      attachments={attachments}
      mentionables={mention ?? mentionables}
      isSending={isSending}
      handleSend={handleSend}
      handleFile={handleFile}
      mode={mode}
      assetModalOpened={assetModalOpened}
      openAssetModal={openAssetModal}
      closeAssetModal={closeAssetModal}
      handleAssetUpload={handleAssetUpload}
      handleDeleteAttachment={handleDeleteAttachment}
      className={className}
    />
  );
};
