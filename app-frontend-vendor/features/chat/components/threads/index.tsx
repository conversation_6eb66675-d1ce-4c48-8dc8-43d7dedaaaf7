import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import React, { useEffect, useRef, useState } from "react";
import { ActionIcon, Skeleton } from "@mantine/core";
import { Dismiss16Filled } from "@fluentui/react-icons";
import { ax } from "utils/axios";
import { SendForm } from "../sendForm";
import { useRouter } from "next/router";
import { ChatMessageBlock } from "../message";
import { useInView } from "react-intersection-observer";
import useSWR from "swr";
import { IconArrowUp } from "@tabler/icons-react";

type ChatThreadProps = {
  mentionables?: chatMentionableUser[] | null;
  mode: "channel" | "thread";
  newThreadMessage?: chatMessage | null;
  threads: string | null;
  updateBadgeCount: () => void;
  updateUnread?: (data: { parentId: number; is_unread: boolean }) => void;
};

type PresentationProps = {
  className?: string;
  closeThread: () => void;
  dataMessages: chatMessage[] | null;
  isMessageValidating: boolean;
  mutateMessageList: () => void;
  loadMore: (message: chatMessage) => void;
  totalMessages: number;
  params: { chat_post_id: number | null };
  parentMessage?: chatMessage | null;
  countReplyUnread?: {
    count: number;
    lastPostIdUnread: number;
    requestReadId: number[];
  };
  listUnreadReppy: (listIds: number[]) => void;
  loadMoreUnread: (message: chatMessage, chat_post_end_id: number) => void;
} & ChatThreadProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  mentionables,
  closeThread,
  dataMessages,
  isMessageValidating,
  mutateMessageList,
  mode,
  parentMessage,
  loadMore,
  totalMessages,
  params,
  updateBadgeCount,
  countReplyUnread,
  listUnreadReppy,
  loadMoreUnread,
}) => {
  const [flgFile, setFlgFile] = useState<boolean>(false);
  const [scrollable, setScrollable] = useState<boolean>(true);
  const [messages, setMessages] = useState<chatMessage[] | null>(null);
  const isFirstRender = params.chat_post_id === null;
  const [isToUnread, setIsToUnread] = useState(false);

  const handleFile = (attachments: File[] | null) => {
    setFlgFile(false);
    if (attachments && attachments.length > 0) {
      setFlgFile(true);
    }
  };

  const { ref, inView } = useInView();

  useEffect(() => {
    if (inView && scrollable && messages) {
      setTimeout(() => {
        loadMore(messages[messages.length - 1]);
      }, 300);
    }
  }, [inView]);

  useEffect(() => {
    if (dataMessages) {
      setMessages((prevMessage) =>
        prevMessage && !isFirstRender
          ? [...prevMessage, ...dataMessages]
          : dataMessages
      );
    }
    setScrollable(true);
    updateBadgeCount();
  }, [dataMessages]);

  const scrollRef = useRef<HTMLDivElement>(null);

  // auto scroll to bottom when go to threads
  useEffect(() => {
    if (scrollRef.current && isFirstRender) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [isFirstRender]);
  const itemsRefThread = useRef<(HTMLDivElement | null)[]>([]);
  const [visibleItems, setVisibleItems] = useState<number[]>([]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        setVisibleItems((prev) => {
          return entries
            .filter((entry) => entry.isIntersecting)
            .map((entry) => {
              const target = entry.target as HTMLElement;
              setMessages((prev) => {
                if (!prev) return [];
                prev.map((item) => {
                  if (
                    item.chat_post_id == Number(target.dataset.key) &&
                    item.unread == 1
                  ) {
                    item.unread = 0;
                  }
                  return item;
                });
                return prev;
              });
              return Number(target.dataset.key);
            });
        });
      },
      { threshold: 0.5 }
    );

    itemsRefThread.current.forEach((item) => {
      if (item) observer.observe(item);
    });
    // if(isToUnread){
    //   gotoUnread();
    // }
    return () => observer.disconnect();
  }, [messages]);

  useEffect(() => {
    listUnreadReppy(visibleItems);
  }, [visibleItems]);
  const gotoUnread = () => {
    if (countReplyUnread?.lastPostIdUnread != 0) {
      let check = false;
      let key = 0;
      itemsRefThread.current.forEach((item, index) => {
        if (
          countReplyUnread &&
          Number(item?.dataset.key) == countReplyUnread.lastPostIdUnread
        ) {
          check = true;
          key = index;
          return;
        }
      });
      if (check) {
        setIsToUnread(false);
        setTimeout(() => {
          // @ts-ignore
          itemsRefThread.current[key].scrollIntoView({
            behavior: "smooth",
            block: "end",
          });
        }, 300);
      } else {
        if (dataMessages) {
          setIsToUnread(true);
          // @ts-ignore
          itemsRefThread.current[messages?.length - 1].scrollIntoView({
            behavior: "smooth",
            block: "end",
          });
          if (countReplyUnread) {
            loadMoreUnread(
              dataMessages[dataMessages.length - 1],
              countReplyUnread.lastPostIdUnread
            );
          }
        }
      }
    }
  };
  return (
    <div className={className}>
      <div className="thread-header">
        <p className="thread-header-title">スレッド</p>
        <ActionIcon onClick={closeThread}>
          <Dismiss16Filled />
        </ActionIcon>
      </div>
      <section
        className={`messages-list messages-list-thread ${
          flgFile ? "messages-list-thread-send-file" : ""
        }`}
        ref={scrollRef}
      >
        {countReplyUnread && countReplyUnread.count > 0 && (
          <div className="scroll-top-wrap">
            <button
              className="scroll-bottom"
              onClick={() => {
                gotoUnread();
              }}
            >
              <IconArrowUp />
              <span>{countReplyUnread.count} 件の未読メッセージ</span>
            </button>
          </div>
        )}
        {(!isFirstRender && isMessageValidating && !messages) ||
        (isFirstRender && isMessageValidating) ? (
          <div className="nonactive">
            <Skeleton height={14} mb={"xl"} width="30%" />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} width="60%" />
            <Skeleton height={8} mb={"md"} width="20%" />
            <Skeleton height={40} circle mb={"xs"} />
            <Skeleton height={14} mb={"xl"} width="30%" />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} />
            <Skeleton height={14} mb={"xs"} width="60%" />
            <Skeleton height={8} mb={"md"} width="20%" />
            <Skeleton height={40} circle mb={"xs"} />
          </div>
        ) : (
          <>
            {messages ? (
              messages.map((msg: chatMessage, index) => (
                <div
                  key={index}
                  ref={(el) => (itemsRefThread.current[index] = el)}
                  data-key={msg.chat_post_id}
                  data-index={index}
                >
                  <ChatMessageBlock
                    key={msg.chat_post_id}
                    message={msg}
                    content={msg.content}
                    mentionables={mentionables}
                    mode={mode}
                    mutate={mutateMessageList}
                    messageIndex={index}
                  />
                </div>
              ))
            ) : (
              <></>
            )}
            {messages &&
              messages.length > 0 &&
              messages.length < totalMessages && (
                <div
                  ref={ref}
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    paddingBottom: 8,
                  }}
                >
                  <div className="spinner" role="status"></div>
                </div>
              )}
            {messages && messages.length == totalMessages && parentMessage && (
              <div className="parent-message">
                <div
                  className={
                    messages.length > 0 ? "parent-message-content" : ""
                  }
                >
                  <ChatMessageBlock
                    key={parentMessage.chat_post_id}
                    message={parentMessage}
                    content={parentMessage.content}
                    mentionables={mentionables}
                    mode={mode}
                    mutate={mutateMessageList}
                  />
                </div>
                {messages.length > 0 && (
                  <div className="parent-message-text-total-replies">
                    {totalMessages} 件の返信
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </section>
      <SendForm
        mentionables={mentionables}
        mode="thread"
        mutate={mutateMessageList}
        handleFile={handleFile}
      />
    </div>
  );
};

const Styled = styled(Presentation)`
  position: absolute;
  top: 0;
  right: 0;
  background: white;
  width: 50%;
  height: 100%;
  border-left: 1px solid ${propcolors.border};
  z-index: 6;
  display: flex;
  flex-direction: column;
  box-shadow: 0px 4px 8px 0px #cbd6e299;
  .thread {
    &-view {
      width: calc(100% - 2px) !important;
    }
    &-header {
      border-bottom: 1px solid ${propcolors.border};
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      color: ${propcolors.blackLight};
      font-weight: 600;
      height: 56px;
      line-height: 24px;
    }
    &-parent-message {
      padding: 16px 16px 0;
      background-color: ${propcolors.greyBreadcrumb};
    }
  }
  .messages {
    &-list {
      background-color: ${propcolors.greyBreadcrumb};
      padding: 16px 16px 0;
      display: flex;
      overflow-y: scroll;
      height: calc(100vh - 365px);
      flex-direction: column-reverse;
      &-thread .message-info {
        min-height: 40px !important;
        .user-name-content {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 245px !important;
          display: block;
          float: left;
        }
      }
      .nonactive {
        margin-left: 1rem;
        display: flex;
        flex-direction: column-reverse;
      }
    }
  }

  .parent-message {
    position: relative;

    &-content {
      border-bottom: 1px solid ${propcolors.border};
      padding-bottom: 8px;
      margin-bottom: 16px;
    }

    &-text-total-replies {
      position: absolute;
      font-size: 12px;
      color: ${propcolors.blackLightLabel};
      background-color: ${propcolors.greyBreadcrumb};
      bottom: 8px;
      padding-right: 8px;
    }
  }
`;

export const ChatThreads: React.FC<ChatThreadProps> = ({
  mentionables,
  mode,
  newThreadMessage,
  threads,
  updateBadgeCount,
  updateUnread,
}) => {
  const [threadMessages, setThreadMessages] = useState<chatMessage[]>([]);
  const [parentMessage, setParentMessage] = useState<chatMessage | null>(null);
  const { replace, query } = useRouter();
  const [params, setParams] = useState<{
    chat_post_id: number | null;
    chat_post_end?: number | null;
  }>({
    chat_post_id: null,
    chat_post_end: null,
  });
  const [totalMessages, setTotalMessages] = useState<number>(0);

  const closeThread = () => {
    replace(`/chat/${query.id}${query.title && "?title=" + query.title}`);
  };
  const fetcher = (url: string) => ax.get(url).then((res) => res.data);

  const urlWithParams = params.chat_post_id
    ? `api/v1/chat/thread/${query.threads}?chat_post_id=${params.chat_post_id}&chat_post_end=${params.chat_post_end}&is_mobile=0`
    : `api/v1/chat/thread/${query.threads}?is_mobile=0`;

  const {
    data: messages,
    mutate: mutateMessageList,
    isValidating: isMessageValidating,
  } = useSWR(urlWithParams, fetcher);

  useEffect(() => {
    if (query.threads) {
      setParams({ chat_post_id: null });
      setParentMessage(null);
      mutateMessageList();
    }
  }, [query.threads]);
  const [countReplyUnread, setcountReplyUnread] = useState<{
    count: number;
    lastPostIdUnread: number;
    requestReadId: number[];
  }>({ count: 0, lastPostIdUnread: 0, requestReadId: [] });
  const [listIdInView, setListIdInView] = useState<number[] | null>(null);

  const readReply = (listIds: number[] = []) => {
    ax.post(`api/v1/chat/thread/read-reply`, {
      channelId: query.id,
      thread: query.threads,
      listReplyIds: listIds,
    }).then((response) => {
      setcountReplyUnread(response.data);
      if (updateUnread) {
        updateUnread({
          parentId: Number(query.threads),
          is_unread: response.data.count != 0,
        });
      }
    });
  };
  const handleListIdsUnreadReppy = (listReplyIds: number[]) => {
    setListIdInView(listReplyIds);
  };
  const [listIdChanged, setListIdChanged] = useState<number[]>([]);
  useEffect(() => {
    setListIdChanged([...listIdChanged, ...countReplyUnread.requestReadId]);
    setThreadMessages(
      threadMessages.map((msg) => {
        return Number(
          listIdChanged.includes(msg.chat_post_id) && msg.unread !== 0
        )
          ? {
              ...msg,
              unread: 0,
            }
          : msg;
      })
    );
  }, [countReplyUnread.count]);

  //todo listen listId view or event new message
  useEffect(() => {
    if (listIdInView != null) {
      readReply(listIdInView);

      setListIdChanged([...listIdChanged, ...listIdInView]);
    }
  }, [listIdInView]);

  useEffect(() => {
    if (messages && messages.data) {
      const parentMessageData = messages.data.find(
        (message: chatMessage) => message.parent_id === null
      );
      if (parentMessageData) {
        setParentMessage(parentMessageData);
      }
      setThreadMessages(
        messages.data.filter(
          (message: chatMessage) => message !== parentMessageData
        )
      );
      setTotalMessages(messages.total_count);
    }
    if (messages != undefined) {
      const threadsIds = messages.data.map((message: chatMessage) => {
        return message.chat_post_id;
      });
      ax.post(`/api/v1/chat/thread/check-reply-read`, {
        threadsIds: threadsIds,
        id: query.id,
      }).then((response) => {
        if (updateUnread) {
          updateUnread({
            parentId: Number(query.threads),
            is_unread: response.data,
          });
        }
      });
    }
  }, [messages]);

  useEffect(() => {
    if (newThreadMessage) {
      if (newThreadMessage.isDeleted) {
        setThreadMessages((prev) => {
          if (prev) {
            return prev.map((message) => {
              if (message.chat_post_id === newThreadMessage.chat_post_id) {
                message.deleted = 1;
                message.attached_files = [];
              }
              return message;
            });
          } else {
            return [];
          }
        });
      } else if (newThreadMessage.isUpdated) {
        setThreadMessages((prev) => {
          if (prev) {
            return prev.map((message) => {
              if (message.chat_post_id === newThreadMessage.chat_post_id) {
                message.content = newThreadMessage.content;
              }
              return message;
            });
          } else {
            return [];
          }
        });
      } else if (newThreadMessage.parent_id === Number(query.threads)) {
        setThreadMessages((prev) => [newThreadMessage, ...prev]);
        setTotalMessages((prev) => ++prev);
      }
    }
  }, [newThreadMessage]);

  const loadMore = (message: chatMessage) => {
    setParams({ chat_post_id: message.chat_post_id });
  };
  const loadMoreUnread = (message: chatMessage, chat_post_end_id: number) => {
    setParams({
      chat_post_id: message.chat_post_id,
      chat_post_end: chat_post_end_id,
    });
  };
  return (
    <Styled
      mentionables={mentionables}
      dataMessages={threadMessages}
      isMessageValidating={isMessageValidating}
      mutateMessageList={mutateMessageList}
      mode={mode}
      closeThread={closeThread}
      threads={threads}
      loadMore={loadMore}
      parentMessage={parentMessage}
      totalMessages={totalMessages}
      params={params}
      updateBadgeCount={updateBadgeCount}
      countReplyUnread={countReplyUnread}
      listUnreadReppy={handleListIdsUnreadReppy}
      loadMoreUnread={loadMoreUnread}
    />
  );
};
