import styled from "@emotion/styled";
import { Badge, Box, Popover, Tooltip } from "@mantine/core";
import { RiMoreFill } from "@remixicon/react";
import Link from "next/link";
import { Channel } from "pusher-js";
import { useEffect, useRef, useState } from "react";
import { useInView } from "react-intersection-observer";
import { propcolors } from "styles/colors";
import { useSetChannelUnreadAllMessage } from "../../../../utils/recoil/chatList/chatChannelUnreadAllMessageState";

type PresentationProps = {
  channel: chatChannel;
  currentChannelID: string | string[] | undefined;
  readAllMessages: (id: number) => void;
  idChannelHover: number | null;
  setIdChannelHover: (id: number | null) => void;
  updateState: (
    id: number,
    inView: boolean,
    badgeCount: number,
    hasBeenInView: boolean,
    ref: HTMLDivElement | null
  ) => void;
  channelPusher: Channel | null;
};

const Presentation: React.FC<PresentationProps> = ({
  channel,
  currentChannelID,
  readAllMessages,
  idChannelHover,
  setIdChannelHover,
  updateState,
  channelPusher,
}) => {
  const { ref, inView } = useInView({ threshold: 0.5 });
  const itemRef = useRef<HTMLDivElement>(null);
  const [hasBeenInView, setHasBeenInView] = useState(false);
  const [opened, setOpened] = useState(false);
  const setChannelUnreadAllMessage = useSetChannelUnreadAllMessage();

  useEffect(() => {
    if (channelPusher) {
      channelPusher.bind("channel.update_badge_count", (data: chatMessage) => {
        if (channel.channel_id === data.channel_id) {
          setHasBeenInView(false);
        }
      });
    }
  }, [channelPusher]);

  useEffect(() => {
    if (inView) {
      if (!hasBeenInView) {
        setHasBeenInView(true);
      }
      updateState(
        channel.channel_id,
        inView,
        channel.badge_count,
        true,
        itemRef.current
      );
    } else {
      updateState(
        channel.channel_id,
        inView,
        channel.badge_count,
        hasBeenInView,
        itemRef.current
      );
    }
  }, [inView, channel.badge_count, hasBeenInView]);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleMouseLeave = (event: {
    relatedTarget: any;
    currentTarget: { contains: (arg0: any) => any };
  }) => {
    if (
      event.relatedTarget &&
      !event.currentTarget.contains(event.relatedTarget)
    ) {
      hoverTimeoutRef.current = setTimeout(() => {
        setIdChannelHover(null);
        setOpened(false);
      }, 100);
    }
  };

  const handleMouseEnter = (channelId: number) => {
    if (hoverTimeoutRef.current) clearTimeout(hoverTimeoutRef.current);
    setTimeout(() => {
      setIdChannelHover(channelId);
    }, 100);
  };

  return (
    <div ref={ref}>
      <Tooltip
        key={channel.channel_id}
        multiline
        label={channel.name}
        zIndex={10}
        offset={10}
        datatype="html"
        position="right"
        transitionProps={{ duration: 200 }}
        styles={(theme) => ({
          tooltip: {
            fontSize: "10px",
            padding: "5px",
            width: "max-content",
          },
        })}
        withinPortal={false}
        openDelay={50}
        closeDelay={0}
        opened={idChannelHover === channel.channel_id}
      >
        <Box
          className={`channel-item-link ${
            currentChannelID === channel.channel_id.toString()
              ? "active"
              : "inactive"
          }`}
          ref={itemRef}
        >
          <Link href={`/chat/${channel.channel_id}?title=${channel.name}`}>
            <p className={`${channel?.unread ? "has-unread" : ""}`}>
              {channel.name}
            </p>
          </Link>
          <Popover
            width={200}
            position="bottom-start"
            shadow="md"
            opened={opened}
            onChange={setOpened}
            withinPortal={false}
            offset={-2}
          >
            <Popover.Target>
              <Box
                className="action-button"
                onMouseEnter={() => handleMouseEnter(channel.channel_id)}
                onMouseLeave={handleMouseLeave}
                onClick={() => setOpened((prev) => !prev)}
              >
                {channel.badge_count > 0 ? (
                  idChannelHover === channel.channel_id ? (
                    <RiMoreFill color="white"/>
                  ) : (
                    <Badge>{channel.badge_count}</Badge>
                  )
                ) : (
                  <RiMoreFill color="white"/>
                )}
              </Box>
            </Popover.Target>

            <Popover.Dropdown
              onMouseEnter={() => handleMouseEnter(channel.channel_id)}
              onMouseLeave={handleMouseLeave}
              onClick={() => {
                if (channel.badge_count > 0 || channel.unread) {
                  readAllMessages(channel.channel_id);
                }
                setIdChannelHover(null);
                setOpened(false);
                setChannelUnreadAllMessage(channel?.channel_id);
              }}
              style={{
                cursor: "pointer",
                backgroundColor: propcolors.inputBackground,
              }}
            >
              <p
                style={{
                  color: propcolors.blackColor,
                  fontWeight: 400,
                  fontSize: "14px",
                }}
              >
                チャンネルを既読にする
              </p>
            </Popover.Dropdown>
          </Popover>
        </Box>
      </Tooltip>
    </div>
  );
};

const Styled = styled(Presentation)``;

export const ChannelBlock: React.FC<PresentationProps> = ({
  channel,
  currentChannelID,
  readAllMessages,
  idChannelHover,
  setIdChannelHover,
  updateState,
  channelPusher,
}) => {
  return (
    <Styled
      channel={channel}
      currentChannelID={currentChannelID}
      readAllMessages={readAllMessages}
      idChannelHover={idChannelHover}
      setIdChannelHover={setIdChannelHover}
      updateState={updateState}
      channelPusher={channelPusher}
    />
  );
};
