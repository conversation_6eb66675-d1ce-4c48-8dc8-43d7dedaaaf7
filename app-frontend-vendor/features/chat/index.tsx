import styled from "@emotion/styled";
import { <PERSON>ge, Box, Button, TextInput } from "@mantine/core";
import Head from "next/head";
import Link from "next/link";
import { useRouter } from "next/router";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import { Chat<PERSON>essenger } from "./components/messenger";
import { useDisclosure } from "@mantine/hooks";
import { ChannelUser } from "./components/channelUser";
import React, { useEffect, useRef, useState } from "react";
import Pusher, { Channel } from "pusher-js";
import { parseCookies } from "nookies";
import { CustomBreadcrumb } from "components/breadcrumb";
import { CustomConfirmModal } from "components/Modals/CustomConfirmModal";
import { ListThreadWithChannel } from "./components/listThread";
import { useInView } from "react-intersection-observer";
import IconSearch from "../../public/icons/search-line.svg";
import { IconArrowDown } from "@tabler/icons-react";
import { ChannelBlock } from "./components/channel";
import { useChatPostIds } from "../../utils/recoil/chatList/chatPostIdsState";
import {
  useChatCountUnreadChannel,
  useSetChatCountUnreadChannel,
} from "../../utils/recoil/chatList/chatUnreadChannelState";
import { RiRefreshLine } from "@remixicon/react";
import {
  MAX_CHANNELS_DISPLAY,
  PER_PAGE_OF_CHANNELS,
} from "constants/commonSetting";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";

const pusher = new Pusher(process.env.NEXT_PUBLIC_PUSHER_APP_KEY!, {
  cluster: process.env.NEXT_PUBLIC_PUSHER_CLUSTER!,
  channelAuthorization: {
    endpoint: `${process.env.NEXT_PUBLIC_API_HOST}/api/v1/chat/authChannel?is_mobile=0`,
    headers: {
      "X-XSRF-TOKEN": `${parseCookies()["XSRF-TOKEN"]}`,
    },
    transport: "ajax",
    customHandler: (params, callback) => {
      ax.post("/api/v1/chat/authChannel?is_mobile=0", {
        channel_name: params.channelName,
        socket_id: params.socketId,
      }).then((res) => callback(null, res.data));
    },
  },
});

type DriveExplorerProps = {};
type Mention = {
  badge_count: number;
};
type Post = {
  badge_count: number;
};

type PresentationProps = {
  className?: string;
  mention?: Mention;
  post?: Post;
  channels: chatChannel[];
  channelPusher: Channel | null;
  isChannelLoading: boolean;
  currentChannelID: string | string[] | undefined;
  channelName: string | string[];
  userListModalOpened: boolean;
  openUserListModal: () => void;
  closeUserListModal: () => void;
  loadMore: (index: number, scrollToUnreadChannel?: boolean) => void;
  totalChannel: chatChannel[];
  totalChannelCount: number;
  searchChannel: (channelName: string) => void;
  refreshChannels: () => void;
  reloadChannels: () => void;
  isFilterMode: boolean;
  setIsFilterMode: React.Dispatch<React.SetStateAction<boolean>>;
  updateBadgeCount: () => void;
  readAllMessages: (id: number) => void;
  updateUnread: () => void;
  showLoadMoreButton: boolean;
} & DriveExplorerProps;

const Presentation: React.FC<PresentationProps> = ({
  className,
  mention,
  post,
  channels,
  channelPusher,
  isChannelLoading,
  currentChannelID,
  channelName,
  userListModalOpened,
  openUserListModal,
  closeUserListModal,
  loadMore,
  totalChannel,
  totalChannelCount,
  searchChannel,
  refreshChannels,
  reloadChannels,
  isFilterMode,
  setIsFilterMode,
  updateBadgeCount,
  readAllMessages,
  updateUnread,
  showLoadMoreButton,
}) => {
  const badgeCount =
    (currentChannelID === "mention" && mention?.badge_count) ||
    (currentChannelID === "reply" && post?.badge_count) ||
    0;
  const totalChannelDisplay =
    totalChannelCount > MAX_CHANNELS_DISPLAY
      ? MAX_CHANNELS_DISPLAY
      : totalChannelCount;

  const [idChannelHover, setIdChannelHover] = useState<number | null>(null);
  const [listChannelState, setListChannelState] = useState<
    Array<{
      id: number;
      inView: boolean;
      badgeCount: number;
      hasBeenInView: boolean;
      ref: HTMLDivElement | null;
    }>
  >([]);
  const [showButtonLoadMoreChannelState, setShowButtonLoadMoreChannelState] =
    useState<boolean>(false);
  const [showScroll, setShowScroll] = useState<boolean>(false);
  const [autoScroll, setAutoScroll] = useState<boolean>(false);
  const [isScrolling, setIsScrolling] = useState<boolean>(false);
  const { ref, inView } = useInView();

  useEffect(() => {
    if (inView && !isFilterMode) {
      setShowScroll(false);
      loadMore(channels.length);
    }
  }, [inView]);

  const filterChannel = (channelName: string) => {
    if (channelName) {
      setIsFilterMode(true);
      searchChannel(channelName);
    } else {
      setIsFilterMode(false);
      refreshChannels();
    }
  };

  function debounce(func: Function, delay: number) {
    let timer: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timer);
      timer = setTimeout(() => func(...args), delay);
    };
  }

  // Debounce API call for filtering channels
  const debouncedFilterChannel = debounce((channelName: string) => {
    filterChannel(channelName);
  }, 300);

  const [postIds, setPostIds] = useState<number[]>([]);
  const handleDataPostRead = (postIds: number[]) => {
    setPostIds(postIds);
  };
  const [unreadCount, setUnreadCount] = useState(0);
  const updateListChannelState = (
    id: number,
    inView: boolean,
    badgeCount: number,
    hasBeenInView: boolean,
    ref: HTMLDivElement | null
  ) => {
    setListChannelState((prev) => {
      const existingIndex = prev.findIndex((item) => item.id === id);
      const newItem = { id, inView, badgeCount, hasBeenInView, ref };

      if (existingIndex === -1) {
        return [...prev, newItem];
      } else {
        const newState = [...prev];
        newState[existingIndex] = newItem;
        return newState;
      }
    });
  };

  useEffect(() => {
    if (showScroll) {
      setShowScroll(false);
    }
  }, [channels]);

  useEffect(() => {
    if (listChannelState.length === 0) return;
    if (listChannelState.every((item) => !item.hasBeenInView)) return;

    const allBadgesSeen = listChannelState
      .filter((item) => item.badgeCount > 0)
      .every((item) => item.hasBeenInView);
    const hasHiddenWithBadges = listChannelState.some(
      (item) => !item.inView && item.badgeCount > 0 && !item.hasBeenInView
    );

    if (
      totalChannel.filter((channel) => channel.badge_count > 0).length !=
      listChannelState.filter((channel) => channel.badgeCount > 0).length
    ) {
      setShowButtonLoadMoreChannelState(true);
    } else if (allBadgesSeen) {
      setShowButtonLoadMoreChannelState(false);
    } else if (hasHiddenWithBadges) {
      if (autoScroll) {
        scrollToFirstHiddenItem();
        setAutoScroll(false);
      } else {
        setShowButtonLoadMoreChannelState(true);
      }
    }
  }, [listChannelState, autoScroll]);

  const scrollToFirstHiddenItem = () => {
    if (isScrolling) return;
    const firstHiddenItem = listChannelState.find(
      (item) => !item.inView && item.badgeCount > 0 && !item.hasBeenInView
    );

    if (!firstHiddenItem) {
      loadMore(channels.length, true);
      setShowScroll(true);
      setAutoScroll(true);
    } else if (firstHiddenItem.ref) {
      setIsScrolling(true);
      firstHiddenItem.ref.scrollIntoView({ behavior: "smooth", block: "end" });
    }
    setTimeout(() => {
      setIsScrolling(false);
    }, 300);
  };

  const handleReloadChannels = () => {
    setShowButtonLoadMoreChannelState(false);
    setListChannelState([]);
    reloadChannels();
  };

  return (
    <>
      <Head>
        <title>チャット | PartnerProp</title>
      </Head>
      <div className={className} style={{ overflow: "hidden" }}>
        <header
          style={{
            display: "flex",
            justifyContent: "space-between",
            borderBottom: `1px solid ${propcolors.border}`,
            borderTop: `1px solid ${propcolors.border}`,
          }}
        >
          <CustomBreadcrumb
            title="チャット"
            styleWrapper={{ padding: "22px 24px" }}
          />
          <button onClick={handleReloadChannels} className="button-reload">
            <RiRefreshLine
              style={{
                cursor: "pointer",
                width: 20,
                height: 20,
              }}
            />
          </button>
        </header>
        <PanelGroup direction="horizontal" className="content-chat">
          <Panel
            defaultSize={14}
            maxSize={60}
            minSize={12}
            className="content-wrap"
          >
            <section className="channel-list">
              <>
                <p className="channel-group-name">最近のメッセージ</p>
                <Box
                  className={`channel-item-link ${
                    currentChannelID === "mention" ? "active" : "inactive"
                  }`}
                >
                  <Link
                    href={`/chat/mention?title=メンション一覧`}
                    key={`mention`}
                    style={{ paddingRight: 24 }}
                  >
                    <p
                      className={`${mention && mention.badge_count > 0 ? "has-unread" : ""}`}
                    >
                      メンション一覧
                    </p>
                    {mention && mention.badge_count > 0 && (
                      <Badge>{mention.badge_count}</Badge>
                    )}
                  </Link>
                </Box>
                <Box
                  className={`channel-item-link ${
                    currentChannelID === "reply" ? "active" : "inactive"
                  }`}
                >
                  <Link
                    href={`/chat/reply?title=返信一覧`}
                    key={`reply`}
                    style={{ paddingRight: 24 }}
                  >
                    <p
                      className={`${post && post.badge_count > 0 ? "has-unread" : ""}`}
                    >
                      返信一覧
                    </p>
                    {post && post.badge_count > 0 && (
                      <Badge>{post.badge_count}</Badge>
                    )}
                  </Link>
                </Box>
                <p className="channel-group-name">全てのチャンネル</p>
                <TextInput
                  icon={<IconSearch style={{ color: "red" }} />}
                  placeholder="チャンネル名で検索..."
                  className="input-search-channel"
                  onChange={(event) =>
                    debouncedFilterChannel(event.currentTarget.value)
                  }
                />
                {channels &&
                  channels.map((channel: chatChannel, index) => (
                    <ChannelBlock
                      key={index}
                      channel={channel}
                      currentChannelID={currentChannelID}
                      readAllMessages={readAllMessages}
                      idChannelHover={idChannelHover}
                      setIdChannelHover={setIdChannelHover}
                      updateState={updateListChannelState}
                      channelPusher={channelPusher}
                    />
                  ))}

                {(showLoadMoreButton || showButtonLoadMoreChannelState) && (
                  <div className="scroll-channel-bottom-wrap">
                    <button
                      className="scroll-channel-bottom"
                      onClick={scrollToFirstHiddenItem}
                    >
                      <IconArrowDown />
                      <span>未読のチャンネル</span>
                    </button>
                  </div>
                )}
                {showScroll && (
                  <div className="spinner-wrap">
                    <div className="spinner" role="status"></div>
                  </div>
                )}

                {!isFilterMode &&
                  (isChannelLoading ||
                    (channels && channels.length < totalChannelDisplay)) && (
                    <div
                      className={className}
                      ref={ref}
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        marginBottom: 8,
                      }}
                    >
                      <div className="spinner" role="status"></div>
                    </div>
                  )}
                {channels.length > 0 &&
                  channels.length == totalChannelDisplay &&
                  totalChannelCount > MAX_CHANNELS_DISPLAY && (
                    <div
                      style={{
                        marginTop: 12,
                        marginBottom: 12,
                        textAlign: "center",
                        color: "##666666",
                        fontSize: 11,
                      }}
                    >
                      <p>これ以上チャンネルを読み込めません</p>
                      <p>リロードボタンから再度読み込んでください</p>
                    </div>
                  )}
              </>
            </section>
          </Panel>
          <PanelResizeHandle className="w-2 cursor-ew-resize" />
          {currentChannelID &&
            (currentChannelID === "mention" || currentChannelID === "reply" ? (
              <Panel defaultSize={86} className="messages">
                <section className="messages-heading">
                  <p># {channelName}</p>
                </section>
                <ListThreadWithChannel
                  channel={currentChannelID}
                  badgeCount={badgeCount}
                  channelPusher={channelPusher}
                  onClickReadButton={updateBadgeCount}
                />
              </Panel>
            ) : currentChannelID != "all" ? (
              <Panel defaultSize={86} className="messages">
                <section className="messages-heading">
                  <p># {channelName}</p>
                  <Button onClick={openUserListModal} className="btn-chat">
                    チャンネルユーザー
                  </Button>
                </section>
                <ChatMessenger
                  channelPusher={channelPusher}
                  updateBadgeCount={updateBadgeCount}
                  sendDataPostRead={handleDataPostRead}
                  updateUnread={updateUnread}
                />
              </Panel>
            ) : (
              <Panel defaultSize={86}></Panel>
            ))}
        </PanelGroup>
        <CustomConfirmModal
          title="チャンネルユーザー"
          opened={userListModalOpened}
          onClose={closeUserListModal}
          isDisplayFooter={false}
        >
          <div className="modal-wrap">
            <div className="modal-content">
              <div className="modal-content-input">
                <ChannelUser />
              </div>
            </div>
          </div>
        </CustomConfirmModal>
      </div>
    </>
  );
};

const Styled = styled(Presentation)`
  .button-reload {
    margin: 22px 24px;
    display: flex;
    align-items: center;
    border: none;
    border-style: none!important;
  }

  .btn-chat {
    padding: 0px 12px;
    border: 1px solid ${propcolors.border};
    background-color: ${propcolors.white};
    color: ${propcolors.blackLight};
    font-size: 14px;
    font-weight: 600;
    line-height: 14px;
    height: 32px;
    border-radius: 6px;
    min-width: 84px;

    &:hover {
      background-color: ${propcolors.backgroundHover};
    }

    span {
      margin: 0;
    }
  }
  .user-name-content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 950px !important;
    display: block;
    float: left;
  }
  .messages-list {
    padding-bottom: 16px;
  }
  .thread-show-message {
    .messages-list {
      width: 50%;
    }

    .user-name-content {
      max-width: 550px !important;
    }
  }

  .messages-list-thread {
    width: 100% !important;
    .thread-show {
      padding-right: 8px !important;
    }
    .sender-input__control {
      max-width: 300px !important;
    }
  }

  .messages-list-thread-send-file {
    height: calc(100vh - 435px) !important;
  }

  .input-search-channel {
    margin: 0 24px 10px 24px;
  }

  @media screen and (max-width: 1400px) {
    .user-name-content {
      max-width: 600px !important;
    }
    .thread-show-message .user-name-content {
      max-width: 250px !important;
    }
  }

  .content-chat {
    height: 100%;
    background-color: white;
    position: relative;
    overflow: visible;
    z-index: 99;

    .content-wrap {
      position: relative;
      overflow: visible !important;
    }

    .channel {
      &-list {
        border: 1px solid ${propcolors.gray[200]};
        border-top: 0px;
        margin-left: 1px;
        height: calc(100vh - 180px);
        overflow-y: auto;

        .spinner {
          height: 18px;
          width: 18px;
          display: inline-block;
          border-radius: 50%;
          border-width: 2px;
          border-style: solid;
          border-color: #f93932;
          border-top-color: transparent;
          animation: spin 1s linear infinite;
          margin-top: 10px;
        }

        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      }

      &-group-name {
        font-size: 14px;
        font-weight: 400;
        line-height: inherit;
        background-color: unset;
        color: #8992a0;
        padding: 16px 8px !important;
      }

      &-item {
        .custom-tooltip {
          background-color: white;
          color: black;
        }
        &-link {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 45px;
          font-size: 14px;
          font-weight: 300;

          > a {
            width: -webkit-fill-available;
            padding: 8px 0 8px 24px;
            display: flex;
            justify-content: space-between;
          }

          > .action-button {
            width: 72px;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            cursor: pointer;
          }

          .has-unread {
            color: ${propcolors.blackLight};
            font-weight: 700;
          }

          .mantine-Badge-root {
            padding: 2px 4px !important;
            background-color: ${propcolors.partnerRed};
            color: ${propcolors.white};
            min-width: 28px;
          }

          &.active {
            background-color: ${propcolors.blackLight};
            &:hover {
              background-color: ${propcolors.blackLight};
              font-weight: 600;
              color: ${propcolors.white};
            }

            p {
              font-weight: 600;
              color: ${propcolors.white};
            }
            box-shadow: 0px 4px 4px 0px #00000040;
            border-bottom: 0px;
          }

          &:hover {
            background-color: ${propcolors.blackLight};
            p {
              font-weight: 600;
              color: ${propcolors.white};
            }
            box-shadow: 0px 4px 4px 0px #00000040;
            border-bottom: 0px;
          }

          p {
            font-weight: 300;
            color: ${propcolors.blackLight};
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            @supports (-webkit-line-clamp: 1) {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: initial;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
            }

            .mantine-Badge-root {
              margin-left: 20px;
            }
          }
        }
      }
    }

    .messages {
      background-color: ${propcolors.greyBreadcrumb};
      position: relative;
      &-heading {
        position: sticky;
        p {
          padding-right: 20px;
        }
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        font-size: 16px;
        font-weight: 600;
        color: ${propcolors.blackLight};
        border-bottom: 1px solid ${propcolors.border};
        background-color: ${propcolors.white};
      }
    }

    .scroll-channel-bottom-wrap {
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-content: center;
      position: absolute;
      bottom: 36px;
      left: 0;
      width: 100%;
      z-index: 1000;

      .scroll-channel-bottom {
        background: #f93932;
        color: #ffffff;
        border-radius: 30px;
        font-size: 14px;
        display: flex;
        justify-content: center;
        align-items: center;

        svg {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
      }
    }

    .spinner-wrap {
      display: flex;
      justify-content: center;
      align-content: center;
      position: absolute;
      bottom: 10px;
      width: 100%;
    }
  }
}

  .edit-button {
    cursor: pointer;
  }
  .modal {
    &-wrap {
      background: ${propcolors.white};
      border-radius: 8px;
    }
    &-header {
      color: ${propcolors.blackLight};
      padding: 16px 40px;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
      border-bottom: 1px solid ${propcolors.border};
    }
    &-content {
      flex-direction: column;
      display: flex;
      gap: 16px;
      &-input input {
        margin-top: 8px;
        height: 48px;
        border-radius: 0.5rem;
      }
      label {
        margin-top: 16px;
        color: ${propcolors.blackLightLabel};
        font-weight: 600;
        font-size: 12px;
      }
      &-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 24px;
      }
      .modal-form {
        padding: 8px 24px 24px 24px;
      }
      .modal-footer {
        background-color: ${propcolors.greyBreadcrumb};
        padding: 24px 40px 24px 24px;
        .modal-buttons {
          display: flex;
          justify-content: space-between;
          gap: 1rem;
          margin: 0px;
          font-weight: 600;
          padding: 0px 8px 0px 24px;
          &-cancel,
          &-reset {
            padding: 0.8rem 1rem;
            width: 100%;
            height: 42px;
            font-size: 14px;
            color: ${propcolors.white};
            background-color: ${propcolors.greyDefault};
            border: 0.0625rem solid transparent;
            border-radius: 8px;
          }
          &-submit {
            padding: 0.8rem 1rem;
            width: 100%;
            height: 42px;
            font-size: 14px;
            background-color: ${propcolors.black};
            border-radius: 8px;
          }
        }
        .modal-button-delete {
          margin: 16px 0px;
        }
      }
    }
  }
`;

export const ChatWrapper: React.FC = () => {
  const { query } = useRouter();
  const { id } = query;
  const [
    userListModalOpened,
    { open: openUserListModal, close: closeUserListModal },
  ] = useDisclosure(false);
  const [currentChannels, setCurrentChannels] = useState<chatChannel[]>([]);
  const [totalChannel, setTotalChannel] = useState<chatChannel[]>([]);
  const [totalChannelCount, setTotalChannelCount] = useState<number>(0);
  const [mention, setMention] = useState<Mention>({ badge_count: 0 });
  const [reply, setReply] = useState<Post>({ badge_count: 0 });
  const [channelPusher, setChannelPusher] = useState<Channel | null>(null);
  const [isFilterMode, setIsFilterMode] = useState<boolean>(false);
  const [isChannelLoading, setIsChannelLoading] = useState<boolean>(true);
  const [showLoadMoreButton, setShowLoadMoreButton] = useState<boolean>(false);
  const isFilterModeRef = useRef(isFilterMode);
  const chatCountUnreadChannel = useChatCountUnreadChannel();
  useEffect(() => {
    if (totalChannel.length) {
      setTotalChannel((prev) => {
        prev.map((item) => {
          if (
            item.channel_id == Number(query.id) &&
            chatCountUnreadChannel.count == 0
          ) {
            item.unread = false;
          }
          if (
            item.channel_id == Number(query.id) &&
            chatCountUnreadChannel.count > 0
          ) {
            item.unread = true;
          }
          return item;
        });
        return prev;
      });
    }
  }, [chatCountUnreadChannel, totalChannel, query.id]);
  useEffect(() => {
    isFilterModeRef.current = isFilterMode;
  }, [isFilterMode]);

  useEffect(() => {
    getTotalChannel();

    const customPusher = pusher.subscribe("private-channels.chat-event");
    setChannelPusher(customPusher);
    customPusher.bind("channel.update_badge_count", (data: chatMessage) => {
      ax.get(
        `api/v1/chat/get_channel_by_id?channel_id=${data.channel_id}`
      ).then((res) => {
        setMention(res.data.mention);
        setReply(res.data.post);

        setTotalChannel((prev) => {
          if (!prev) return [];
          const selectedChannel = prev.find(
            (channel: chatChannel) => channel.channel_id === data.channel_id
          );
          if (selectedChannel) {
            setCurrentChannels((prevChannel) => {
              if (!prevChannel) return [];
              const selectedCurrentChannel = prevChannel.find(
                (channel: chatChannel) => channel.channel_id === data.channel_id
              );
              if (selectedCurrentChannel) {
                return prevChannel.map((channel) => {
                  if (
                    channel.channel_id === selectedCurrentChannel.channel_id
                  ) {
                    const userCookies = JSON.parse(
                      parseCookies(null, ["user"]).user
                    );

                    return {
                      ...channel,
                      badge_count: res.data.badge_count,
                      unread:
                        data.deleted == 1
                          ? false
                          : userCookies.user_id != data.user.vendor_user_id,
                    };
                  }
                  return channel;
                });
              } else {
                const userCookies = JSON.parse(
                  parseCookies(null, ["user"]).user
                );
                if (
                  data.vendor_user_ids &&
                  data.vendor_user_ids.includes(userCookies.user_id)
                ) {
                  setShowLoadMoreButton(true);
                }
              }
              return prevChannel;
            });
            return prev.map((channel) => {
              if (channel.channel_id === selectedChannel.channel_id) {
                return {
                  ...channel,
                  badge_count: res.data.badge_count,
                  unread: res.data.unread,
                };
              }
              return channel;
            });
          }
          return prev;
        });
      });
    });
    return () => {
      customPusher.unbind_all();
      customPusher.unsubscribe();
    };
  }, []);

  const getTotalChannel = () => {
    ax.get(`api/v1/chat/channels?limit=${MAX_CHANNELS_DISPLAY}`)
      .then((res) => {
        const channels = res.data.channels;
        setTotalChannel(channels);
        setTotalChannelCount(res.data.total_channel);
        setCurrentChannels(channels.slice(0, PER_PAGE_OF_CHANNELS));
        setIsChannelLoading(false);
        setMention(res.data.mention);
        setReply(res.data.post);
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const chatPostIds = useChatPostIds();
  const setChatCountUnreadChannel = useSetChatCountUnreadChannel();

  useEffect(() => {
    if (id) {
      if (!isNaN(Number(id))) {
        ax.put(`api/v1/chat/${id}/read`, { chatPostIds: chatPostIds }).then(
          (res) => {
            updateBadgeCount(Number(id));
            let dataRes = res.data.total_unread;
            dataRes.listId = chatPostIds;
            setChatCountUnreadChannel(dataRes);
          }
        );
      }
    }
  }, [id, chatPostIds, query.threads]);
  const updateUnreadChatPost = () => {
    if (id) {
      if (!isNaN(Number(id))) {
        ax.put(`api/v1/chat/${id}/read`, { chatPostIds: chatPostIds }).then(
          (res) => {
            let data = res.data.total_unread;
            data.listId = chatPostIds;
            setChatCountUnreadChannel(data);
          }
        );
      }
    }
  };
  const updateBadgeCount = async (channel_id?: number) => {
    if (channel_id) {
      const res = await ax.get(
        `api/v1/chat/get_channel_by_id?channel_id=${channel_id}`
      );
      if (res) {
        setTotalChannel((prev) => {
          if (!prev) return [];
          return prev.map((channel) => {
            if (channel.channel_id === channel_id) {
              return {
                ...channel,
                badge_count: res.data.badge_count,
                unread: res.data.unread,
              };
            }
            return channel;
          });
        });
        setCurrentChannels((prev) => {
          if (!prev) return [];
          return prev.map((channel) => {
            if (channel.channel_id === channel_id) {
              return {
                ...channel,
                badge_count: res.data.badge_count,
                unread: res.data.unread,
              };
            }
            return channel;
          });
        });
        setMention(res.data.mention);
        setReply(res.data.post);
      }
    } else {
      const response = await ax.get(`api/v1/chat/channels`);
      if (response) {
        setTotalChannel((prev) => {
          if (!prev) return [];
          return prev.map((channel) => {
            const updatedChannel = response.data.channels.find(
              (item: chatChannel) => item.channel_id === channel.channel_id
            );

            if (updatedChannel) {
              return {
                ...channel,
                badge_count: updatedChannel.badge_count ?? 0,
                unread: updatedChannel.unread ?? false,
              };
            }

            return channel;
          });
        });
        setCurrentChannels((prev) => {
          if (!prev) return [];

          return prev.map((channel) => {
            const updatedChannel = response.data.channels.find(
              (item: chatChannel) => item.channel_id === channel.channel_id
            );

            if (updatedChannel) {
              return {
                ...channel,
                badge_count: updatedChannel.badge_count ?? 0,
                unread: updatedChannel.unread ?? false,
              };
            }

            return channel;
          });
        });
        setMention(response.data.mention);
        setReply(response.data.post);
      }
    }
  };

  const loadMore = (index: number, scrollToUnreadChannel?: boolean) => {
    if (index > 0) {
      let nextItems = [];
      if (scrollToUnreadChannel) {
        const unreadIndex = totalChannel.findIndex(
          (channel, i) => i > index && channel.badge_count > 0
        );

        if (unreadIndex !== -1) {
          nextItems = totalChannel.slice(index, unreadIndex + 1);
        } else {
          nextItems = totalChannel.slice(index, index + PER_PAGE_OF_CHANNELS);
        }
      } else {
        nextItems = totalChannel.slice(index, index + PER_PAGE_OF_CHANNELS);
      }
      setShowLoadMoreButton(false);
      if (nextItems.length > 0) {
        setTimeout(() => {
          setCurrentChannels((prevChannels) => [...prevChannels, ...nextItems]);
        }, 200);
      }
    }
  };

  const searchChannel = async (channelName: string) => {
    const res = await ax.get(
      `api/v1/chat/channels?channel_name=${channelName}`
    );
    setCurrentChannels(res.data.channels);
    setTotalChannel(res.data.channels);
  };

  const refreshChannels = async () => {
    const res = await ax.get(`api/v1/chat/channels`);
    setCurrentChannels(res.data.channels);
    setTotalChannel(res.data.channels);
  };

  const reloadChannels = async () => {
    setShowLoadMoreButton(false);
    setDefaultState();
    getTotalChannel();
  };

  const setDefaultState = () => {
    setIsChannelLoading(true);
    setCurrentChannels([]);
    setTotalChannel([]);
    setTotalChannelCount(0);
  };

  const readAllMessages = async (id: number) => {
    if (id) {
      await ax.put(`api/v1/chat/${id}/read_all`);
      const response = await ax.get(
        `api/v1/chat/get_channel_by_id?channel_id=${id}`
      );
      if (response) {
        setTotalChannel((prev) => {
          if (!prev) return [];
          return prev.map((channel) =>
            channel.channel_id === id
              ? {
                  ...channel,
                  badge_count: response.data.badge_count,
                  unread: response.data.unread,
                }
              : channel
          );
        });
        setCurrentChannels((prev) => {
          if (!prev) return [];

          return prev.map((channel) =>
            channel.channel_id === id
              ? {
                  ...channel,
                  badge_count: response.data.badge_count,
                  unread: response.data.unread,
                }
              : channel
          );
        });
        setMention(response.data.mention);
        setReply(response.data.post);
      }
    }
  };

  return (
    <Styled
      mention={mention}
      post={reply}
      channels={currentChannels}
      channelPusher={channelPusher}
      isChannelLoading={isChannelLoading}
      currentChannelID={id}
      channelName={query.title ? query.title : ""}
      userListModalOpened={userListModalOpened}
      openUserListModal={openUserListModal}
      closeUserListModal={closeUserListModal}
      loadMore={loadMore}
      totalChannel={totalChannel}
      totalChannelCount={totalChannelCount}
      searchChannel={searchChannel}
      refreshChannels={refreshChannels}
      reloadChannels={reloadChannels}
      isFilterMode={isFilterMode}
      setIsFilterMode={setIsFilterMode}
      updateBadgeCount={updateBadgeCount}
      readAllMessages={readAllMessages}
      updateUnread={updateUnreadChatPost}
      showLoadMoreButton={showLoadMoreButton}
    />
  );
};
