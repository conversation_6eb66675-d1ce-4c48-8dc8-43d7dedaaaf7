import React, { useState } from "react";
import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import { ActionIcon, rem } from "@mantine/core";
import {
  useCustomColumn,
  useSetCustomColumn,
} from "utils/recoil/customColumn/customColumnState";
import { modals } from "@mantine/modals";
import { useSetModalState } from "utils/recoil/modalState";
import { useSetEditCustomColumn } from "utils/recoil/customColumn/editCustomColumnState";
import { useDrag, useDrop } from "react-dnd";
import { useSessionUser } from "utils/recoil/sessionUserState";
import { CUSTOM_COLUMN_TYPE_STR_VALUES } from "constants/ja/common";
import { RiMenuLine, RiDeleteBin6Line } from "@remixicon/react";
import IconEdit from "public/icons/edit.svg";

type CustomColumnsProps = {
  mode: "partner" | "lead";
  toggleModified: (bool: boolean) => void;
};

type PresentationalProps = {
  className?: string;
  customColumns: CustomColumn[] | null;
  deleteColumn: (name: string) => void;
  editColumn: (column: CustomColumn) => void;
  // editColumn: (name: string) => void;
  moveItem: (fromIndex: number, toIndex: number) => void;
};

const Presentation: React.FC<PresentationalProps> = ({
  className,
  customColumns,
  deleteColumn,
  editColumn,
  moveItem,
}) => {
  const sessionUser = useSessionUser();
  const userRole = sessionUser?.user_role;
  return (
    <div className={className}>
      {customColumns &&
        customColumns.map((column) => (
          <DraggableItem
            key={column.sort_order}
            index={column.sort_order}
            item={column}
            moveItem={moveItem}
          >
            <div className="columns-item" key={column.column_id}>
              <div className="columns-item-heading">
                <div className="columns-item-text">
                  <div className="columns-item-text__name">
                    {column.column_label}
                  </div>
                  <div className="columns-item-text__id">
                    {column.column_name}
                  </div>
                </div>
                <p className="columns-item-type">
                  {
                    CUSTOM_COLUMN_TYPE_STR_VALUES.find(
                      (type) => type.value === column.type_status
                    )?.label
                  }
                </p>
              </div>
              {userRole === 1 && (
                <div className="columns-item-buttons">
                  <ActionIcon onClick={() => editColumn(column)}>
                    <IconEdit />
                  </ActionIcon>
                  <ActionIcon
                    onClick={() => {
                      deleteColumn(column.column_name);
                    }}
                  >
                    <RiDeleteBin6Line
                      style={{
                        cursor: "pointer",
                        width: rem(24),
                        height: rem(24),
                        fill: "#8992A0",
                      }}
                    />
                  </ActionIcon>
                </div>
              )}
            </div>
          </DraggableItem>
        ))}
    </div>
  );
};

const Styled = styled(Presentation)`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: min-content;
  border-top: 1px solid ${propcolors.border};
  .new-column-button {
    display: flex;
    gap: 2rem;
    align-items: center;
    justify-content: center;
    svg {
      margin-right: 1rem;
    }
  }
  .columns {
    &-item {
      width: 100%;
      padding: 0.5rem 0.5rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &-heading {
        display: flex;
        align-items: center;
        gap: 24px;
        width: 100%;
      }
      &-text {
        // display: flex;
        // align-items: center;
        // gap: 0.5em;
        width: 100%;
        max-width: 252px;
        font-size: 14px;
        line-height: 21px;
        color: #222222;
        font-weight: 300px;
        &__name {
          margin-bottom: 8px;
        }
      }
      &-type {
        padding: 0 0.25rem;
        font-size: 14px;
        line-height: 21px;
        color: #222222;
        font-weight: 300px;
      }
      &-buttons {
        opacity: 0;
        display: flex;
        gap: 12px;
        button {
          width: 32px;
          height: 32px;
        }
      }

      &:hover {
        .columns-item-buttons {
          opacity: 1;
        }
      }
    }
  }
  #deleteColumn {
    background-color: red;
  }
`;

const ModalTitle = styled.div`
  width: 100%;
  text-align: center;
  border-bottom: 1px solid ${propcolors.gray[200]};
  padding-bottom: 1rem;
  top: 1.3rem;
  position: absolute;
  margin: 0 -1rem;
  font-weight: 600;
  font-size: 18px;
  line-height: 27px;
`;
const ModalBody = styled.div`
  margin: 1rem 0;
  font-weight: 600;
  color: #222222;
  font-size: 18px;
  line-height: 27px;
`;
const ModalBottom = styled.div`
  font-size: 12px;
`;
export const CustomColumns: React.FC<CustomColumnsProps> = ({
  toggleModified,
}) => {
  const setCustomColumns = useSetCustomColumn();
  const customColumns = useCustomColumn();
  const setModal = useSetModalState();
  const setEditCustomColumn = useSetEditCustomColumn();

  const editColumn = (column: CustomColumn) => {
    setEditCustomColumn(column);
    setModal("editCustomColumn");
  };

  const deleteColumn = (name: string) => {
    modals.openConfirmModal({
      title: <ModalTitle>カラムの削除</ModalTitle>,
      labels: {
        confirm: "削除する",
        cancel: "キャンセル",
      },
      size: "640px",
      children: (
        <div style={{ padding: "0.5rem" }}>
          <ModalBody>カラムを削除しますか？</ModalBody>
          <ModalBottom>
            入力フォームやフィルターなどで使われていた場合、それらからも削除され、設定内容が変更されます。
            <br />
            本当に削除しますか？
          </ModalBottom>
        </div>
      ),
      onConfirm: () => {
        setCustomColumns((prev) => {
          // カラムを削除
          const updatedColumns = prev.filter(
            (item) => item.column_name !== name
          );

          // sort_orderを再割り当て
          const reorderedColumns = updatedColumns.map((item, index) => {
            return {
              ...item,
              sort_order: index,
            };
          });
          return reorderedColumns;
        });
        toggleModified(true);
      },
      onCancel: modals.closeAll,
      confirmProps: {
        sx: {
          width: "284px",
          borderRadius: "8px",
          fontSize: "14px",
          lineHeight: "14px",
          paddingTop: "14px",
          paddingBottom: "14px",
          marginRight: "8px",
          marginBottom: "8px",
          backgroundColor: `${propcolors.black}`,
          color: `${propcolors.white}`,
          height: "auto",
          "&:hover": {
            backgroundColor: `${propcolors.black}`,
          },
        },
      },
      cancelProps: {
        // variant: "outline",
        sx: {
          borderRadius: "8px",
          width: "284px",
          left: "24px",
          paddingTop: "14px",
          paddingBottom: "14px",
          position: "absolute",
          marginBottom: "8px",
          height: "auto",
          lineHeight: "14px",
          fontSize: "14px",
          borderColor: `${propcolors.greyDefault}`,
          backgroundColor: `${propcolors.greyDefault}`,
          color: `${propcolors.white}`,
          "&:hover": {
            backgroundColor: `${propcolors.greyDefault}`,
          },
        },
      },
    });
  };

  const moveItem = (fromIndex: number, toIndex: number) => {
    const items = customColumns.map((item) => ({ ...item }));
    const [movedItem] = items.splice(fromIndex, 1);
    items.splice(toIndex, 0, movedItem);
    // 並び替えたあとのsort_orderを更新
    items.forEach((item, index) => {
      item.sort_order = index;
    });
    setCustomColumns(items);
    toggleModified(true);
  };

  return (
    <Styled
      customColumns={customColumns}
      deleteColumn={deleteColumn}
      editColumn={editColumn}
      moveItem={moveItem}
    />
  );
};

// カスタムカラムの並び替え用の型を定義
type DraggableItemProps = {
  item: CustomColumn;
  index: number;
  moveItem: (fromIndex: number, toIndex: number) => void;
  [key: string]: any;
};
const ItemType = {
  ITEM: "ITEM",
};

// ドラッグアイタムの型を定義
type DraggedItem = {
  type: string;
  index: number;
  item: CustomColumn;
};

// カスタムカラムの並び替え用
const DraggableItem: React.FC<DraggableItemProps> = ({
  item,
  index,
  moveItem,
  ...props
}) => {
  const ref = React.useRef(null);

  const [, drop] = useDrop<DraggedItem>({
    accept: ItemType.ITEM,
    hover: (draggedItem) => {
      if (draggedItem.index !== index) {
        moveItem(draggedItem.index, index);
        draggedItem.index = index;
      }
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: ItemType.ITEM,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [isHovered, setIsHovered] = useState<boolean>(false);

  drag(drop(ref));

  return (
    <div
      ref={ref}
      style={{
        opacity: isDragging ? 0.5 : 1,
        backgroundColor: isHovered ? "#F7F8F9" : "#ffffff",
        display: "flex",
        gap: "13px",
        alignItems: "center",
        padding: "24px",
        borderRight: `1px solid ${propcolors.border}`,
        borderBottom: `1px solid ${propcolors.border}`,
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      {...props}
    >
      <RiMenuLine
        style={{
          cursor: "grab",
          width: rem(24),
          height: rem(24),
          fill: "#8992A0",
        }}
      />
      {props.children}
    </div>
  );
};
