import styled from "@emotion/styled";
import Head from "next/head";
import { useEffect, useState } from "react";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import { notifications } from "@mantine/notifications";
import { Button, Radio } from "@mantine/core";
import { CommonListLayout } from "components/layouts/commonListLayout";
import useSWR from "swr";
import useSWRImmutable from "swr/immutable";
import { useDisclosure } from "@mantine/hooks";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { CustomBreadcrumb } from "components/breadcrumb";
import { ContactFormDetailPreview } from "./components/preview";
import { ContactFormDetailSidebar } from "./components/sidebar";
import { EditColumnModal } from "./components/modals/editColumnModal";
import { SideDrawer } from "./components/drawer/sideDrawer";
import { ShareModal } from "./components/shareModal";
import { NOTIFICATION_MSG } from "constants/ja/common";

type PresentationProps = {
  className?: string;
  contactFormDetail: ContactFormDetail | null;
  openModal: (mode: string | null) => void;
  setContactFormDetail: (contactFormDetail: ContactFormDetail | null) => void;
  setSelectedColumn: (column: ContactFormColumn | null) => void;
  handleUpdateContactFormDetail: () => void;
  modalMode: string | null;
  setNewColumnOrder: (newColumnOrder: number) => void;
  backToList: () => void;
  openShareModal: () => void;
  isModified: boolean;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  contactFormDetail,
  openModal,
  setContactFormDetail,
  setSelectedColumn,
  handleUpdateContactFormDetail,
  modalMode,
  setNewColumnOrder,
  backToList,
  openShareModal,
  isModified,
}) => {
  return (
    <>
      {contactFormDetail ? (
        <CommonListLayout className={className}>
          <Head>
            <title>問い合わせ | PartnerProp</title>
          </Head>
          <header>
            <CustomBreadcrumb
              title="問い合わせ"
              list={[{ title: "フォーム編集" }]}
            />
            <div className="header-buttons">
              <Button variant="default" onClick={backToList}>
                一覧へ戻る
              </Button>
              <Button variant="default" onClick={openShareModal}>
                公開範囲
              </Button>
              <Radio.Group
                className="publish_radio"
                onChange={(e) => {
                  setContactFormDetail({
                    ...contactFormDetail,
                    is_published: e === "published" ? true : false,
                  });
                }}
                value={
                  contactFormDetail.is_published ? "published" : "unpublished"
                }
              >
                <Radio
                  value="published"
                  style={{ marginRight: "1rem" }}
                  label="公開"
                />
                <Radio
                  checked
                  value="unpublished"
                  style={{ marginRight: "1rem" }}
                  label="非公開"
                />
              </Radio.Group>
              <Button
                onClick={handleUpdateContactFormDetail}
                disabled={!isModified}
              >
                保存する
              </Button>
            </div>
          </header>
          <main>
            <ContactFormDetailSidebar
              modalMode={modalMode}
              openModal={openModal}
            />
            <ContactFormDetailPreview
              contactFormDetail={contactFormDetail}
              setContactFormDetail={setContactFormDetail}
              setSelectedColumn={setSelectedColumn}
              openModal={openModal}
              setNewColumnOrder={setNewColumnOrder}
            />
          </main>
        </CommonListLayout>
      ) : (
        <>Loading...</>
      )}
    </>
  );
};

const Styled = styled(Presentation)`
  header {
    padding: 15px 24px;
    border-bottom: 1px solid ${propcolors.gray[200]};

    .publish_radio {
      display: flex;
      align-items: center;
      padding: 0 16px;
      border-radius: 8px;
      border: 1px solid ${propcolors.gray[200]};
    }
  }
  main {
    display: grid;
    grid-template-columns: 360px 1fr;
    overflow-y: scroll;
  }
`;
export const ContactFormDetailPage = () => {
  const router = useRouter();
  const { uuid } = router.query;
  const [modalMode, setModalMode] = useState<string | null>(null);
  const [newColumnOrder, setNewColumnOrder] = useState(0);
  const [isModified, setIsModified] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [newFormUuid, setNewFormUuid] = useState<string>("");
  const [selectedColumn, setSelectedColumn] =
    useState<ContactFormColumn | null>(null);
  const [contactFormDetail, setContactFormDetail] =
    useState<ContactFormDetail | null>(null);
  const [isOpenShare, { open, close }] = useDisclosure();
  const [sharedPartners, setSharedPartners] = useState<SharedPartner[] | null>(
    null
  );
  const [sharedPartnerUsers, setSharedPartnerUsers] = useState<
    SharedPartnerUser[] | null
  >(null);
  const [isSharedToAllPartners, setIsSharedToAllPartners] =
    useState<boolean>(false);
  const openModal = (mode: string | null) => {
    setModalMode(mode);
  };
  const backToList = () => {
    router.push("/contact/form");
  };
  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch(() => {});
  const { data: contactFormDetailData, mutate: mutateContactFormDetailData } =
    useSWRImmutable(
      uuid && uuid !== "new" ? `api/v1/contact/form/${uuid}` : null,
      fetcher
    );

  const { data: permissionsData, mutate: mutatePermissionsData } = useSWR(
    uuid && uuid !== "new" ? `api/v1/contact/form/${uuid}/permissions` : null,
    fetcher
  );

  useEffect(() => {
    if (contactFormDetail !== null) {
      if (isLoaded || uuid === "new") {
        setIsModified(true);
      } else {
        setIsLoaded(true);
      }
    }
  }, [contactFormDetail]);

  useEffect(() => {
    if (isModified === false && newFormUuid !== "") {
      // contact/form/newをhistoryに追加しないためreplaceを使用
      router.replace(`/contact/form/${newFormUuid}`);
    }
  }, [isModified, newFormUuid]);

  useEffect(() => {
    if (permissionsData) {
      setSharedPartners(permissionsData.permissions.shared_linked_partners);
      setSharedPartnerUsers(permissionsData.permissions.shared_partner_users);
    }

    return () => {
      setSharedPartners(null);
      setSharedPartnerUsers(null);
    };
  }, [permissionsData]);

  useEffect(() => {
    if (contactFormDetailData?.contact_form !== undefined) {
      setContactFormDetail(contactFormDetailData?.contact_form);
    }
  }, [contactFormDetailData]);

  // 離脱処理
  useEffect(() => {
    if (isModified) {
      router.events.on("routeChangeStart", handleRouteChange);
      window.addEventListener("beforeunload", beforeUnloadHandler);

      return () => {
        router.events.off("routeChangeStart", handleRouteChange);
        window.removeEventListener("beforeunload", beforeUnloadHandler);
      };
    }
  }, [isModified, router]);

  const beforeUnloadHandler = (event: BeforeUnloadEvent) => {
    event.preventDefault();
    event.returnValue = "";
  };

  const handleRouteChange = () => {
    const confirmationMessage =
      "このページを離れようとしています。編集中の内容が失われる可能性があります。";
    if (!window.confirm(confirmationMessage)) {
      throw "changeRouter aborted";
    }
  };

  useEffect(() => {
    if (router.query.uuid === "new") {
      setContactFormDetail({
        uuid: "new",
        title: "新規フォーム",
        description: "フォーム冒頭文",
        is_published: false,
        is_description_visible: true,
        is_vendor_notified: true,
        is_partner_inquirer_notified: false,
        form_columns: [
          {
            uuid: "new",
            label: "一行テキスト",
            select_columns: [],
            column_type: "STRING",
            is_required: true,
            sort_order: 0,
            description: "",
            logo: null,
            logo_path: "",
            is_new: true,
          },
        ],
        logo: null,
        logo_path: "",
        thumbnail: null,
        thumbnail_path: "",
        vendor_assignee_type: "NONE",
        vendor_notified_type: "ALL_VENDOR_USER",
        vendor_notified_users: [],
        priority: 1,
        created_at: "",
        updated_at: "",
      });
    }
  }, []);

  const convertFileToBase64 = (file: File) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  };

  const handleUpdateContactFormDetail = async () => {
    // 少なくとも１つの列があるかどうかを確認
    if (!contactFormDetail?.form_columns || contactFormDetail?.form_columns.length === 0) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "問い合わせフォームの更新に失敗しました",
        message: "少なくとも 1 つの項目を追加してください",
        autoClose: 5000,
      });
      return;
    }

    // 必須のカラムが少なくとも１つあるかどうかを確認
    const requiredColumns = contactFormDetail?.form_columns.filter(
      (column) => column.is_required
    );
    if (!requiredColumns || requiredColumns.length === 0) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "問い合わせフォームの更新に失敗しました",
        message: "少なくとも１つの項目を必須項目として設定してください",
        autoClose: 5000,
      });
      return;
    }
    const newFormColumns = await Promise.all(
      contactFormDetail?.form_columns.map(async (column) => {
        let columnLogoBase64 = null;
        if (column.logo) {
          columnLogoBase64 = await convertFileToBase64(column.logo);
        }
        if (column.is_new) {
          // 新規追加された項目はuuidを削除して送信する
          return {
            label: column.label,
            select_columns: column.select_columns,
            column_type: column.column_type,
            is_required: column.is_required,
            sort_order: column.sort_order,
            description: column.description,
            logo: columnLogoBase64,
            is_logo_updated: columnLogoBase64 ? true : false,
          };
        } else {
          return {
            ...column,
            logo: columnLogoBase64,
            is_logo_updated: columnLogoBase64 ? true : false,
          };
        }
      }) || []
    );

    let titleLogoBase64 = null;
    if (contactFormDetail?.logo) {
      titleLogoBase64 = await convertFileToBase64(contactFormDetail?.logo);
    }
    let thumbnailBase64 = null;
    if (contactFormDetail?.thumbnail) {
      thumbnailBase64 = await convertFileToBase64(contactFormDetail?.thumbnail);
    }

    const payload: { [key: string]: any } = {
      ...contactFormDetail,
      form_columns: newFormColumns,
      vendor_notified_users: contactFormDetail?.vendor_notified_users?.map(
        (user) => user.id
      ),
      logo: titleLogoBase64,
      thumbnail: thumbnailBase64,
      is_logo_updated: contactFormDetail?.is_logo_updated ?? false,
      is_thumbnail_updated: contactFormDetail?.is_thumbnail_updated ?? false,
    };

    if (uuid === "new") {
      ax.post(`api/v1/contact/form`, payload)
        .then((res) => {
          notifications.show({
            icon: <IconNotiSuccess />,
            message: "問い合わせフォームの作成に成功しました。",
            autoClose: 5000,
          });
          setIsModified(false);
          setNewFormUuid(res.data.contact_form.uuid);
        })
        .catch((err) => {
          handleError(err);
        });
    } else if (uuid) {
      ax.put(`api/v1/contact/form/${uuid}`, payload)
        .then(() => {
          notifications.show({
            icon: <IconNotiSuccess />,
            message: "問い合わせフォームの更新に成功しました。",
            autoClose: 5000,
          });
          setIsModified(false);
          setIsLoaded(false);
          mutateContactFormDetailData();
        })
        .catch((err) => {
          handleError(err);
        });
    }
  };

  const handleError = (err: any) => {
    // エラーメッセージを取得
    const errorMessage = err.response.data.message;
    const errors = err.response.data.errors;
    // エラーメッセージによって表示するメッセージを変更
    let message = "問い合わせフォームの更新に失敗しました";
    if (err.response.status === 422) {
      message = "入力内容に誤りがあります";
      if (
        errorMessage.includes("label field has a duplicate value") &&
        errorMessage.includes("select_columns") // 選択肢の重複
      ) {
        message = "選択肢名が重複しています";
      } else if (
        errorMessage.includes("label field has a duplicate value") &&
        errorMessage.includes("form_columns") // フォーム名の重複
      ) {
        message = "項目名が重複しています";
      } else if (
        errorMessage.includes(
          "The vendor notified users field is required when vendor notified type is MEMBER."
        )
      ) {
        message = "社内の通知するユーザーを選択してください。";
      } else if (
        errorMessage.includes(
          "The vendor assignee user id field is required when vendor assignee type is MEMBER."
        )
      ) {
        message = "初期担当者を選択してください";
      }
    }
    notifications.show({
      icon: <IconNotiFailed />,
      title: "問い合わせフォームの更新に失敗しました",
      message: message,
      autoClose: 5000,
    });
  };

  const addColumn = (newColumn: ContactFormColumn) => {
    if (newColumn.column_type === "LOGO") {
      if (
        contactFormDetail &&
        contactFormDetail.form_columns.some(
          (column) => column.column_type === "LOGO"
        )
      ) {
        notifications.show({
          icon: <IconNotiFailed />,
          message: "挿入・画像は1つしか追加できません。",
          autoClose: 5000,
        });
        return;
      }
    }
    // contactFormDetail.from_columnsに新しい項目を追加する
    if (contactFormDetail) {
      if (contactFormDetail.form_columns) {
        // 現在の日時を取得
        const currentDate = new Date();
        // 日時をString型に変換
        const uuid = currentDate.toISOString();
        setContactFormDetail({
          ...contactFormDetail,
          form_columns: contactFormDetail.form_columns
            .map((column) => {
              if (column.sort_order >= newColumnOrder) {
                return { ...column, sort_order: column.sort_order + 1 };
              }
              return column;
            })
            .concat({
              ...newColumn,
              uuid: uuid,
              sort_order: newColumnOrder,
            })
            .sort((a, b) => a.sort_order - b.sort_order),
        });
      }
    }
  };

  const postSharedAllPartners = () => {
    ax.post(`api/v1/contact/form/${uuid}/share/all_partners`)
      .then((res) => {
        setIsSharedToAllPartners(true);
        notifications.show({
          icon: <IconNotiSuccess />,
          title: NOTIFICATION_MSG.SHARE_ALL_TITLE,
          message: NOTIFICATION_MSG.SHARE_ALL_MESSAGE,
          autoClose: 5000,
        });
        mutatePermissionsData();
      })
      .catch((err) => {
        handleErrorShare(err, true);
      });
  };

  const postUnsharedAllPartners = () => {
    ax.post(`api/v1/contact/form/${uuid}/unshare/all_partners`)
      .then((res) => {
        setIsSharedToAllPartners(false);
        notifications.show({
          icon: <IconNotiSuccess />,
          message: NOTIFICATION_MSG.SHARE_SETTING_CHANGE,
          autoClose: 5000,
        });
        mutatePermissionsData();
      })
      .catch((err) => {
        handleErrorShare(err, false);
      });
  };

  const postSharedPartner = (partner: SharedPartnerUser) => {
    ax.post(`api/v1/contact/form/${uuid}/share/partners`, partner)
      .then((res) => {
        notifications.show({
          icon: <IconNotiSuccess />,
          message: NOTIFICATION_MSG.SHARE_SETTING_CHANGE,
          autoClose: 5000,
        });
        mutatePermissionsData();
      })
      .catch((err) => {
        handleErrorShare(err, partner.has_access);
      });
  };

  const postSharedPartnerUser = (partner_user: SharedPartnerUser) => {
    ax.post(`api/v1/contact/form/${uuid}/share/partner_users`, partner_user)
      .then((res) => {
        notifications.show({
          icon: <IconNotiSuccess />,
          message: NOTIFICATION_MSG.SHARE_SETTING_CHANGE,
          autoClose: 5000,
        });
        mutatePermissionsData();
      })
      .catch((err) => {
        handleErrorShare(err, partner_user.has_access);
      });
  };

  const handleErrorShare = (err: any, has_access: boolean) => {
    if (err.response.status === 404) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "共有に失敗しました。",
        message:
          "フォームが見つかりませんでした、新規作成の場合は先に保存してください",
        autoClose: 5000,
      });
    } else {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "共有の更新に失敗しました。",
        message: has_access
          ? "問い合わせフォームの共有に失敗しました"
          : "問い合わせフォームの共有解除に失敗しました",
        autoClose: 5000,
      });
    }
  };

  const handleChangeShareStatus = (
    value: SharedPartnerUser,
    fetchMode: "all" | "partner" | "partner_user"
  ) => {
    if (fetchMode === "all") {
      if (value.has_access) postSharedAllPartners();
      else postUnsharedAllPartners();
    } else if (fetchMode === "partner") {
      // partnerの場合
      postSharedPartner(value);
    } else if (fetchMode === "partner_user") {
      // partner_userの場合
      postSharedPartnerUser(value);
    }
  };

  const handleOpenShareModal = () => {
    if (uuid === 'new') {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "公開範囲を設定できません",
        message: "公開範囲を設定する前に、フォームの内容を保存してください",
        autoClose: 5000,
      });
      return;
    }

    open();
  }

  return (
    <>
      <EditColumnModal
        close={() => setSelectedColumn(null)}
        opened={selectedColumn !== null}
        column={selectedColumn}
        setContactFormDetail={setContactFormDetail}
      />
      <ShareModal
        opened={isOpenShare}
        close={close}
        isSharedToAllPartners={isSharedToAllPartners}
        setIsSharedToAllPartners={setIsSharedToAllPartners}
        sharingPartnerList={sharedPartners ?? []}
        sharingPartnerUserList={sharedPartnerUsers ?? []}
        onSubmit={handleChangeShareStatus}
        title={"問い合わせフォームの共有"}
        description={"保存しなくても共有設定は反映されます"}
      />
      <SideDrawer
        openDrawer={modalMode !== null}
        contactFormDetail={contactFormDetail}
        modalMode={modalMode}
        openModal={openModal}
        addColumn={addColumn}
        setContactFormDetail={setContactFormDetail}
      />
      <Styled
        contactFormDetail={contactFormDetail}
        openModal={openModal}
        setContactFormDetail={setContactFormDetail}
        setSelectedColumn={setSelectedColumn}
        handleUpdateContactFormDetail={handleUpdateContactFormDetail}
        modalMode={modalMode}
        setNewColumnOrder={setNewColumnOrder}
        backToList={backToList}
        openShareModal={handleOpenShareModal}
        isModified={isModified}
      />
    </>
  );
};
