import styled from "@emotion/styled";
import { Button, Stack } from "@mantine/core";
import React from "react";
import { propcolors } from "styles/colors";

type UserSelectItemProps = {
  item: UserSelectItemDataProps;
  handlerOnclickButton: (params: UserSelectItemDataProps) => void;
};

type UserSelectItemDataProps = {
  value: number;
  label: string;
  subLabel?: string;
  button_label: string;
  active: boolean;
  onClick?: (params: UserSelectItemDataProps) => void;
};

const Styled = styled.div`
  display: flex;
  min-height: 48px;
  padding: 8px 16px;
  gap: 16px;
  justify-content: space-between;
  align-items: center;

  :nth-child(odd) {
    background: ${propcolors.white};
  }

  .UserSelect-content-label {
    font-size: 14px;
  }

  .UserSelect-content-button {
    &.on {
      color: #ffffff;
      background-color: #222222;
    }
    &.off {
      color: #c13515;
      border-color: #c13515;
      background-color: #ffffff;
    }
  }
`;

export const SelectItemComponent: React.FC<UserSelectItemProps> = ({
  item,
  handlerOnclickButton,
}) => {
  return (
    <Styled>
      <Stack spacing={1}>
        <p style={{ fontSize: 14 }}>{item.label}</p>
        {item.subLabel && (
          <p style={{ fontSize: 12, color: "rgba(137, 146, 160, 1)" }}>
            {item.subLabel}
          </p>
        )}
      </Stack>
      <Button
        className={`UserSelect-content-button ${item.active ? "on" : "off"}`}
        onClick={() => handlerOnclickButton(item)}
      >
        {item.button_label}
      </Button>
    </Styled>
  );
};
