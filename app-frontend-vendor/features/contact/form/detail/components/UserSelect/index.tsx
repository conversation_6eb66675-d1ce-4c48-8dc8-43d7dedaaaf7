import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import React, { useEffect, useState } from "react";
import { Loader } from "@mantine/core";
import { SelectItemComponent } from "./UserSelectItem";
import { RiExpandUpDownLine } from "@remixicon/react";

export type FetchMode = "all" | "partner" | "partner_user";

type UserSelectDataProps = {
  value: number;
  label: string;
  group?: string;
  button_label: string;
  active: boolean;
  onClick?: (params: UserSelectDataProps) => void;
};

type UserSelectProps = {
  isShareAll: boolean;
  placeholder?: string;
  isInputDisabled?: boolean;
  isLoading?: boolean;
  onClick: (params: UserSelectDataProps) => void;
  data: UserSelectDataProps[] | null;
  defaultValue?: string;
  isFilter?: boolean;
};

const Styled = styled.div`
  width: 100%;

  .share {
    &-wrap {
      display: block;
      padding: 0;
      border: 1px solid ${propcolors.gray[200]};
    }
    &-show {
      border-radius: 0 0 6px 6px;
      border: 1px solid ${propcolors.gray[200]};
      border-top: none;
      overflow: hidden;
      max-height: 0;
      overflow-y: scroll;
      &-header {
        display: flex;
        gap: 8px;
        justify-content: space-between;
        cursor: default;
        align-items: center;
        padding: 8px 16px;
        &-text {
          font-size: 12px;
          white-space: nowrap;
          color: #868e96;
        }
        &-line {
          width: 100%;
          height: 1px;
          background: ${propcolors.gray[200]};
        }
      }
    }
  }

  .UserSelect {
    display: block;
    padding: 0;
    border-radius: 8px;
    border: 1px solid ${propcolors.gray[200]};
    .UserSelect-input-wrap {
      position: relative;
      width: 100%;

      .UserSelect-input {
        box-sizing: border-box;
        background: none;
        width: 100%;
        height: 48px;
        padding: 0 16px;
        ::placeholder {
          border: none;
          color: #8992a0;
        }
        :disabled {
          background: #f1f3f5;
          ::placeholder {
            color: #ced4da;
          }
        }
      }
      .UserSelect-input-icon {
        position: absolute;
        top: 14px;
        right: 8px;
        display: flex;
        flex-direction: column;
        color: ${propcolors.gray[500]};

        svg {
          height: 16px;
        }
      }
    }
    .UserSelect-content {
      overflow: hidden;
      max-height: 0;
      overflow-y: scroll;
      ::-webkit-scrollbar {
        display: none;
      }

      &.open {
        max-height: 200px !important;
        transition: max-height 0.2s ease-in-out;
      }

      &.close {
        transition: max-height 0.2s ease;
        transition-delay: max-height 0.15s;
      }
      .UserSelect-content-header {
        color: ${propcolors.gray[500]};
        padding: 8px 16px;
        display: grid;
        grid-template-columns: auto 1fr;
        align-items: center;
        gap: 8px;
        .text {
          font-size: 12px;
        }
        .line {
          width: 100%;
          height: 1px;
          background: ${propcolors.gray[200]};
        }
      }
    }
    .UserSelect-loading {
      width: 100%;
      height: 144px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
`;

export const UserSelect: React.FC<UserSelectProps> = ({
  isShareAll,
  placeholder,
  isInputDisabled,
  isLoading,
  onClick: onClickHandler,
  data,
  defaultValue,
  isFilter,
}) => {
  const [showToggle, setShowToggle] = useState<boolean>(false);
  const [filterData, setFilterData] = useState<UserSelectDataProps[] | null>(
    null
  );
  const [value, setValue] = useState<string>("");
  const duplicateTitle: string[] = [];

  useEffect(() => {
    if (data) {
      setFilterData(data);
    }

    return () => {
      setFilterData(null);
    };
  }, [data]);

  useEffect(() => {
    setValue(defaultValue || "");
  }, [defaultValue]);

  useEffect(() => {
    if (!isFilter) return;
    if (value !== "" && data) {
      const filter = data.filter((item) => {
        return item.label.toLowerCase().includes(value.toLowerCase());
      });
      setFilterData(filter);
    } else if (value === "") {
      setFilterData(data);
    }
  }, [value]);

  const onChangeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    setShowToggle(true);
    setValue(e.target.value);
  };

  const handlerOnclickButton = (params: UserSelectDataProps) => {
    if (params.onClick) {
      params.onClick(params);
    } else {
      onClickHandler(params);
    }
    if (!isFilter) {
      // Filter機能が有効でない場合は、選択した値を表示する
      setValue(params.label);
    }
  };

  const handlerOnclickInput = () => {
    setShowToggle(!showToggle);
  };
  return (
    <Styled>
      <div className="UserSelect">
        <div className="UserSelect-input-wrap">
          <input
            type="text"
            placeholder={placeholder}
            className={`UserSelect-input`}
            onChange={(e) => onChangeHandler(e)}
            value={value}
            defaultValue={defaultValue}
            onClick={() => handlerOnclickInput()}
            disabled={isShareAll}
          />
          <div className="UserSelect-input-icon">
            <RiExpandUpDownLine />
          </div>
        </div>
        {isLoading ? (
          <div className="UserSelect-loading">
            <Loader size="lg" />
          </div>
        ) : (
          <div
            className={`UserSelect-content ${showToggle && !isShareAll ? "open" : "close"}`}
          >
            {filterData &&
              filterData.length > 0 &&
              filterData.map((item) => (
                <>
                  {item.group && !duplicateTitle.includes(item.group) && (
                    <div className="UserSelect-content-header">
                      <p className="text">{item.group}</p>
                      <div className="line"></div>
                    </div>
                  )}
                  {item.group && duplicateTitle.push(item.group) && <></>}
                  <SelectItemComponent
                    item={item}
                    handlerOnclickButton={handlerOnclickButton}
                  />
                </>
              ))}
          </div>
        )}
      </div>
    </Styled>
  );
};
