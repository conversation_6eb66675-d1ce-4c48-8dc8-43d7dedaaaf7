import { Switch, <PERSON><PERSON>, rem, <PERSON><PERSON> } from "@mantine/core";
import { propcolors } from "styles/colors";
import IconCheck from "public/icons/check.svg";
import { useChange } from "utils/recoil/drive/changeDropDownHook";
import { useEffect, useState } from "react";
import { RiBuildingLine, RiUserLine } from "@remixicon/react";
import styled from "@emotion/styled";
import { FetchMode } from ".";

const StyledSection = styled.section`
  border-radius: 5px;

  .loading-share {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 12vh;
  }
  .permission-list {
    &-item {
      max-width: 100%;
      transition: 0.2s;
      border-bottom: 1px solid ${propcolors.gray[200]};
      display: flex;
      justify-content: flex-start;
      align-items: center;
      gap: 16px;
      padding: 16px;
      :hover {
        background-color: ${propcolors.greyBreadcrumb};
      }
      &-switch {
        input:checked + .mantine-Switch-track {
          background-color: ${propcolors.black};
          border-color: ${propcolors.black};
          .mantine-Switch-thumb {
            background-color: ${propcolors.white};
          }
        }
        .mantine-Switch-track {
          height: 20px;
          background-color: ${propcolors.white};
          border-color: #e8eaed;
          .mantine-Switch-thumb {
            background-color: ${propcolors.switchOff};
          }
        }
      }
      &-logo {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: ${propcolors.gray[600]};
        background: ${propcolors.inputBackground};
      }
      &-info {
        flex-grow: 1;
        display: flex;
        justify-content: flex-start;
        items: center;
        flex-direction: column;
        cursor: default;
        gap: 8px;
        &-name {
          font-size: 14px;
          color: #222222;
          font-weight: 400;
          cursor: normal;
          color: ${propcolors.blackLight};
        }
        &-owner {
          font-size: 14px;
          color: #222222;
          font-weight: 400;
          color: ${propcolors.blackLight};
        }
      }
      &-remove {
        display: flex;
        justify-content: center;
        cursor: pointer;
        align-items: center;

        .remove-button {
          color: #c13515;
          border-color: #c13515;
          background-color: #ffffff;
        }
      }
      &-switch {
        width: 36px;
        height: 20px;
        input:checked + label {
          background-color: #222222;
          border-color: #222222;
        }
      }
    }
  }
`;

export default function PermissionList({
  permissionList,
  fetchMode,
  onSubmit,
}: {
  permissionList: SharedPartner[] | undefined;
  onSubmit: (
    value: { id: number; name: string; has_access: boolean },
    fetchMode: FetchMode
  ) => void;
  fetchMode: FetchMode;
}) {
  const changePermission = useChange();
  const [permissionListState, setPermissionListState] = useState<
    SharedPartner[] | SharedPartnerUser[] | undefined
  >(undefined);

  useEffect(() => {
    setPermissionListState(permissionList);
  }, [changePermission, fetchMode, permissionList]);

  const checkIcon = <IconCheck color={propcolors.white} />;

  return (
    <StyledSection>
      {permissionListState === undefined ? (
        <div className="loading-share">
          <Loader size="lg" />
        </div>
      ) : permissionListState !== undefined &&
        permissionListState.length > 0 ? (
        permissionListState
          .sort((a, b) => a.id - b.id)
          .map((item) => (
            <div className={`permission-list-item`} key={item.id}>
              <div className="permission-list-item-logo">
                {fetchMode === "partner" ? <RiBuildingLine /> : <RiUserLine />}
              </div>
              <div className="permission-list-item-info">
                <div className="permission-list-item-info-name">
                  {item.name}
                </div>
                {fetchMode === "partner_user" && (
                  <div className="permission-list-item-info-owner">
                    {item ? item["partner_name" as keyof SharedPartner] : ""}
                  </div>
                )}
              </div>
              <div className="permission-list-item-remove">
                {item.has_access !== undefined ? (
                  <Switch
                    className="permission-list-item-switch"
                    onLabel={checkIcon}
                    color="dark"
                    sx={{
                      width: rem(34),
                    }}
                    size="sm"
                    checked={item.has_access}
                    onChange={(e) =>
                      onSubmit(
                        {
                          id: item.id,
                          name: item.name,
                          has_access: e.target.checked,
                        },
                        fetchMode
                      )
                    }
                  />
                ) : (
                  <Button
                    className="remove-button"
                    onClick={() =>
                      onSubmit(
                        { id: item.id, name: item.name, has_access: false },
                        fetchMode
                      )
                    }
                  >
                    削除
                  </Button>
                )}
              </div>
            </div>
          ))
      ) : (
        <div className="no-data">no data</div>
      )}
    </StyledSection>
  );
}
