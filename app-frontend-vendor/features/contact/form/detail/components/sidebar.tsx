import styled from "@emotion/styled";
import { RiSurveyLine, RiMailVolumeLine, RiUserLine } from "@remixicon/react";
import { propcolors } from "styles/colors";

type PresentationProps = {
  className?: string;
} & ContactFormDetailSidebarProps;

type ContactFormDetailSidebarProps = {
  modalMode: string | null;
  openModal: (mode: string | null) => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  openModal,
}) => {
  return (
    <div className={className}>
      <div className="menu" onClick={() => openModal("title")}>
        <RiSurveyLine />
        <p>タイトル・冒頭文</p>
      </div>
      <div className="menu" onClick={() => openModal("image")}>
        <RiSurveyLine />
        <p>画像設定</p>
      </div>
      <div className="menu" onClick={() => openModal("mail_notification")}>
        <RiMailVolumeLine />
        <p>メール通知</p>
      </div>
      <div className="menu" onClick={() => openModal("assignee")}>
        <RiUserLine />
        <p>初期担当者の設定</p>
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  position: relative;
  overflow: hidden;
  .menu {
    height: 45px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    gap: 16px;
    font-size: 14px;
    cursor: pointer;

    :hover {
      background-color: ${propcolors.gray[200]};
    }
    svg {
      width: 15px;
    }
  }
`;
export const ContactFormDetailSidebar: React.FC<
  ContactFormDetailSidebarProps
> = ({ modalMode, openModal }) => {
  return <Styled modalMode={modalMode} openModal={openModal} />;
};
