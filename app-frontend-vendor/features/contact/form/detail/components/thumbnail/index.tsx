import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import React from "react";
import { BackgroundImage, Button, Text, Box, Stack } from "@mantine/core";
import { Dropzone } from "@mantine/dropzone";
import { UseFormReturnType } from "@mantine/form";

type ThumbnailDropZoneComponentProps = {
    openRef: React.RefObject<() => void>;
    onDrop: (files: File[]) => void;
    form?: UseFormReturnType<any>;
    accept?: {};
    imagePath: string | null | undefined
};

const Styled = styled.div`
  .logo-input {
    border: 1px solid ${propcolors.gray[300]};
    border-radius: 8px;
    background-color: ${propcolors.white};
    background-size: contain;
  }
  .drop-zone {
    display: flex;
    height: 208px;
    border: none;
    background-color: transparent;
    margin-top: 8px;
    justify-content: center;
    font-family: "Inter", "system-ui";
    align-items: center;
    flex-direction: column;
    &-title {
      font-size: 14px;
      color: #666666;
    }
    &-buttons {
      display: flex;
      gap: 15px;
    }
    &-inner {
      display: flex;
      justify-content: center;
      gap: 16px;
      align-items: center;
      flex-direction: column;
    }
    .upload-file-button {
      border: 1px solid ${propcolors.gray[200]};
      min-height: 42px;
      padding: 0px 12px;
      font-size: 14px;
      font-weight: 500 !important;
      font-family: "Inter", "system-ui";
      border: 1px solid #e8eaed;
      border-radius: 6px;
    }
  }
`;

export const ThumbnailDropZoneComponent: React.FC<ThumbnailDropZoneComponentProps> = ({
    openRef,
    onDrop: onDropHandler,
    form,
    accept,
    imagePath
}) => {

    return (
      <Styled>
          <BackgroundImage className="logo-input" src={imagePath ? imagePath : '/images/contact/form.png'}>
              <Dropzone
                openRef={openRef}
                multiple={false}
                onDrop={onDropHandler}
                className="drop-zone"
                activateOnClick={false}
                {...form?.getInputProps("logo")}
                h={117}
                radius={8}
                styles={{inner: {pointerEvents: "all"}}}
                accept={accept}
              >

                  <Box className="drop-zone-inner">
                      <Stack align={"center"} mih={90}/>
                      <div className="drop-zone-buttons">
                          <Button
                            mih={32}
                            h={32}
                            maw={129}
                            w={129}
                            onClick={() => openRef.current && openRef.current()}
                            className="upload-file-button"
                            variant={"default"}
                          >
                              <Text truncate="end">
                                  置換
                              </Text>
                          </Button>
                          <Button
                            mih={32}
                            h={32}
                            maw={129}
                            w={129}
                            onClick={() => onDropHandler([])}
                            className="upload-file-button"
                            disabled={!imagePath}
                            variant={"default"}
                          >
                              <Text truncate="end">
                                  削除
                              </Text>
                          </Button>
                      </div>
                  </Box>
              </Dropzone>
          </BackgroundImage>
      </Styled>
    );
};
