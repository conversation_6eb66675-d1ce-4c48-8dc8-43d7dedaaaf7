import styled from "@emotion/styled";
import { useState } from "react";
import { propcolors } from "styles/colors";
import { useRouter } from "next/router";
import { ActionIcon, Image } from "@mantine/core";
import { FormColumnComponent } from "./formColumn";
import {
  RiPencilLine,
  RiArrowUpLine,
  RiArrowDownLine,
  RiDeleteBinLine,
  RiAddFill,
} from "@remixicon/react";

type PresentationProps = {
  className?: string;
  selectedColumnID: string;
  setSelectedColumnID: (uuid: string) => void;
  handleDownOrder: (uuid: string, current_order: number) => void;
  handleUpOrder: (uuid: string, current_order: number) => void;
  handleDeleteColumn: (uuid: string) => void;
  contactFormDetail: ContactFormDetail;
  setSelectedColumn: (column: ContactFormColumn | null) => void;
  handleAddColumn: (sort_order: number) => void;
};

type ContactFormDetailPreviewProps = {
  contactFormDetail: ContactFormDetail;
  setContactFormDetail: (contactFormDetail: ContactFormDetail) => void;
  setSelectedColumn: (column: ContactFormColumn | null) => void;
  openModal: (mode: string | null) => void;
  setNewColumnOrder: (newColumnOrder: number) => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  selectedColumnID,
  setSelectedColumnID,
  handleDownOrder,
  handleUpOrder,
  handleDeleteColumn,
  contactFormDetail,
  setSelectedColumn,
  handleAddColumn,
}) => {
  return (
    <div className={className}>
      <div className="preview_wrap">
        <div className="preview_content">
          <div className="header">
            {contactFormDetail.logo_path && (
              <Image
                height={70}
                src={contactFormDetail.logo_path}
                fit="contain"
              />
            )}
            <h2 className="title">{contactFormDetail.title}</h2>
          </div>
          <div className="preview_inner">
            {contactFormDetail.is_description_visible && (
              <p className="description">{contactFormDetail.description}</p>
            )}
            <div className="add_column" onClick={() => handleAddColumn(0)}>
              <RiAddFill />
              <p>新規項目を挿入</p>
            </div>
            {contactFormDetail.form_columns.map((column) => (
              <>
                <div
                  key={column.uuid}
                  className="form_column"
                  onClick={() => setSelectedColumnID(column.uuid)}
                >
                  {column.uuid === selectedColumnID && (
                    <div className="form_active">
                      <div className="action_buttons">
                        <ActionIcon
                          className="edit"
                          onClick={() => setSelectedColumn(column)}
                        >
                          <RiPencilLine />
                        </ActionIcon>
                        {column.sort_order !== 0 && (
                          <ActionIcon
                            className="up"
                            onClick={() =>
                              handleUpOrder(column.uuid, column.sort_order)
                            }
                          >
                            <RiArrowUpLine />
                          </ActionIcon>
                        )}
                        {column.sort_order !==
                          contactFormDetail.form_columns.length - 1 && (
                          <ActionIcon
                            className="down"
                            onClick={() =>
                              handleDownOrder(column.uuid, column.sort_order)
                            }
                          >
                            <RiArrowDownLine />
                          </ActionIcon>
                        )}
                        <ActionIcon
                          className="delete"
                          onClick={() => handleDeleteColumn(column.uuid)}
                        >
                          <RiDeleteBinLine />
                        </ActionIcon>
                      </div>
                    </div>
                  )}
                  <FormColumnComponent contactFormColumn={column} />
                </div>
                <div
                  className="add_column"
                  onClick={() => handleAddColumn(column.sort_order + 1)}
                >
                  <RiAddFill />
                  <p>新規項目を挿入</p>
                </div>
              </>
            ))}
            <div className="submit_button">送信</div>
          </div>
        </div>
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  background-image: url("/background-checked-pattern.svg");
  background-size: cover;
  min-width: 1112px;
  display: flex;
  justify-content: center;
  height: 100%;
  overflow-y: scroll;
  overflow-x: hidden;

  .preview_wrap {
    .preview_content {
      margin: 40px;
      width: 1032px;
      border-radius: 16px;
      background-color: ${propcolors.white};
      box-shadow: 0 0 24px 5px rgba(128, 128, 128, 1);
      display: flex;
      flex-direction: column;
      align-items: center;
      .header {
        width: 100%;
        padding: 16px 0;
        border-radius: 16px 16px 0 0;
        background-color: ${propcolors.gray[150]};

        .title {
          width: 100%;
          font-size: 24px;
          line-height: 36px;
          text-align: center;
          border-radius: 16px 16px 0;
        }
      }

      .preview_inner {
        padding: 40px 0;
        width: 632px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .description {
          font-size: 16px;
          line-height: 24px;
          font-weight: 600;
        }
        .form_column {
          width: 100%;
          box-sizing: border-box;
          position: relative;

          :before {
            content: "";
            display: block;
            width: 100%;
            height: 100%;
            position: absolute;
            border-radius: 4px;
            top: 0;
            left: 0;
            border: 2px solid ${propcolors.red[600]};
            opacity: 0;
            z-index: 1;
          }

          :hover:before {
            transition: 0.3s;
            opacity: 0.3;
          }

          .form_active {
            display: block;
            width: 100%;
            height: 100%;
            position: absolute;
            border-radius: 4px;
            top: 0;
            left: 0;
            border: 2px solid ${propcolors.red[600]};
            z-index: 1;

            .action_buttons {
              display: flex;
              justify-content: flex-end;
              gap: 8px;
              padding: 0 8px;
              background-color: ${propcolors.white};
              position: absolute;
              top: -15px;
              right: 32px;

              .edit {
                background-color: ${propcolors.gray[700]};
              }
              .up {
                background-color: ${propcolors.black};
              }
              .down {
                background-color: ${propcolors.black};
              }
              .delete {
                background-color: ${propcolors.red[600]};
              }

              svg {
                color: ${propcolors.gray[150]};
              }
            }
          }
        }
        .add_column {
          width: 100%;
          height: 40px;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          color: ${propcolors.gray[600]};
          opacity: 0;
          cursor: pointer;

          :hover {
            opacity: 1;
            transition: 0.3s;
          }

          :before {
            content: "";
            display: block;
            width: 100%;
            height: 100%;
            position: absolute;
            border-radius: 16px;
            top: 0;
            left: 0;
            border: 4px solid ${propcolors.blue[600]};
            opacity: 0;
            z-index: 1;
          }

          :hover:before {
            transition: 0.3s;
            opacity: 0.3;
          }
        }
        .submit_button {
          width: 240px;
          height: 48px;
          border-radius: 24px;
          color: ${propcolors.white};
          text-align: center;
          line-height: 48px;
          font-size: 14px;
          font-weight: 600;
          background-color: ${propcolors.black};
        }
      }
    }
  }
`;
export const ContactFormDetailPreview: React.FC<
  ContactFormDetailPreviewProps
> = ({
  contactFormDetail,
  setContactFormDetail,
  setSelectedColumn,
  openModal,
  setNewColumnOrder,
}) => {
  const router = useRouter();
  const [selectedColumnSortOrder, setSelectedColumnSortOrder] =
    useState<string>("");

  const handleDownOrder = (uuid: string, current_order: number) => {
    // contactFormDetail.from_columnsの順番を変更する
    if (contactFormDetail) {
      if (
        contactFormDetail.form_columns &&
        contactFormDetail.form_columns.length > 0
      ) {
        // 最後の項目の場合はreturn
        if (current_order === contactFormDetail.form_columns.length - 1) return;

        setContactFormDetail({
          ...contactFormDetail,
          form_columns: contactFormDetail.form_columns
            .map((column) => {
              if (column.uuid === uuid) {
                // クリックされた項目のsort_orderを変更
                return { ...column, sort_order: current_order + 1 };
              } else if (column.sort_order === current_order + 1) {
                // クリックされた項目のsort_orderの一つ下の項目のsort_orderを変更
                return { ...column, sort_order: current_order };
              }
              return column;
            })
            .sort((a, b) => a.sort_order - b.sort_order), // sort_orderでソート
        });
      }
    }
  };

  const handleUpOrder = (uuid: string, current_order: number) => {
    // contactFormDetail.from_columnsの順番を変更する
    if (contactFormDetail) {
      if (
        contactFormDetail.form_columns &&
        contactFormDetail.form_columns.length > 0
      ) {
        // 最初の項目の場合はreturn
        if (current_order === 0) return;

        setContactFormDetail({
          ...contactFormDetail,
          form_columns: contactFormDetail.form_columns
            .map((column) => {
              if (column.uuid === uuid) {
                // クリックされた項目のsort_orderを変更
                return { ...column, sort_order: current_order - 1 };
              } else if (column.sort_order === current_order - 1) {
                // クリックされた項目のsort_orderの一つ上の項目のsort_orderを変更
                return { ...column, sort_order: current_order };
              }
              return column;
            })
            .sort((a, b) => a.sort_order - b.sort_order), // sort_orderでソート
        });
      }
    }
  };

  const handleDeleteColumn = (uuid: string) => {
    if (contactFormDetail) {
      if (
        contactFormDetail.form_columns &&
        contactFormDetail.form_columns.length > 0
      ) {
        setContactFormDetail({
          ...contactFormDetail,
          form_columns: contactFormDetail.form_columns
            .filter((column) => column.uuid !== uuid) // クリックされた項目を削除
            .map((column, index) => {
              return { ...column, sort_order: index }; // sort_orderを振り直す
            }),
        });
      }
    }
  };

  const handleAddColumn = (sort_order: number) => {
    openModal("new_column");
    setNewColumnOrder(sort_order);
  };

  return (
    <Styled
      selectedColumnID={selectedColumnSortOrder}
      setSelectedColumnID={setSelectedColumnSortOrder}
      handleDownOrder={handleDownOrder}
      handleUpOrder={handleUpOrder}
      handleDeleteColumn={handleDeleteColumn}
      contactFormDetail={contactFormDetail}
      setSelectedColumn={setSelectedColumn}
      handleAddColumn={handleAddColumn}
    />
  );
};
