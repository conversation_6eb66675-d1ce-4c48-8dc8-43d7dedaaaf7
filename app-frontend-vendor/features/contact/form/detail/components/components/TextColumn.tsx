import styled from "@emotion/styled";
import { Textarea } from "@mantine/core";
import { propcolors } from "styles/colors";

type TextColumnProps = {
  label: string;
  is_required: boolean | undefined;
};
const Title = styled.p({
  fontSize: 12,
  fontWeight: 500,
  marginBottom: "1rem",
});
const IconRequired = styled.span(
  (props: { required: boolean | undefined }) => ({
    color: propcolors.red[700],
    display: props.required ? "inline" : "none",
  })
);

export const TextColumn: React.FC<TextColumnProps> = ({
  label,
  is_required,
}) => {
  return (
    <>
      <Title>
        {label}
        <IconRequired required={is_required}>*</IconRequired>
      </Title>
      <Textarea minRows={4} />
    </>
  );
};
