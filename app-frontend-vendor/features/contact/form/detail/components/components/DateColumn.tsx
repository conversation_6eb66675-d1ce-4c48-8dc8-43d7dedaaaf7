import styled from "@emotion/styled";
import { DateInput } from "@mantine/dates";
import { RiCalendar2Fill } from "@remixicon/react";
import { propcolors } from "styles/colors";

type DateColumnProps = {
  label: string;
  is_required: boolean | undefined;
};
const Title = styled.p({
  fontSize: 12,
  fontWeight: 500,
  marginBottom: "1rem",
});
const IconRequired = styled.span(
  (props: { required: boolean | undefined }) => ({
    color: propcolors.red[700],
    display: props.required ? "inline" : "none",
  })
);

export const DateColumn: React.FC<DateColumnProps> = ({
  label,
  is_required,
}) => {
  return (
    <>
      <Title>
        {label}
        <IconRequired required={is_required}>*</IconRequired>
      </Title>
      <DateInput
        valueFormat="YYYY/MM/DD"
        maw={150}
        icon={<RiCalendar2Fill />}
        placeholder="YYYY/MM/DD"
      />
    </>
  );
};
