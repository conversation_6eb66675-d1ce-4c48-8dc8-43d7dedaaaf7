// components/DateTimeLocalColumn.tsx
import React from "react";
import styled from "@emotion/styled";
import { DateTimePicker } from "@mantine/dates";
import { RiCalendar2Fill } from "@remixicon/react";
import { propcolors } from "styles/colors";

type DateTimeLocalColumnProps = {
  label: string;
  is_required?: boolean;
};

const Title = styled.p({
  fontSize: 12,
  fontWeight: 500,
  marginBottom: "1rem",
});

const IconRequired = styled.span<{ required?: boolean }>(({ required }) => ({
  color: propcolors.red[700],
  display: required ? "inline" : "none",
}));

export const DateTimeLocalColumn: React.FC<DateTimeLocalColumnProps> = ({
  label,
  is_required,
}) => {
  return (
    <>
      <Title>
        {label}
        <IconRequired required={is_required}>*</IconRequired>
      </Title>
      <DateTimePicker
        icon={<RiCalendar2Fill />}
        valueFormat="YYYY/MM/DD HH:mm"
        placeholder="YYYY/MM/DD HH:mm"
        maw={250}
      />
    </>
  );
};
