import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import { Image } from "@mantine/core";

type LogoColumnProps = {
  logo_path: string | null;
};

const ImagePlaceholder = styled.div`
  width: 100%;
  height: 300px;
  background-color: ${propcolors.gray[200]};
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const LogoColumn: React.FC<LogoColumnProps> = ({ logo_path }) => {
  return (
    <>
      {logo_path ? (
        <Image src={logo_path} />
      ) : (
        <ImagePlaceholder>画像を選択してください</ImagePlaceholder>
      )}
    </>
  );
};
