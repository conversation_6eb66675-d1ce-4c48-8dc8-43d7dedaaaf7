import styled from "@emotion/styled";
import { TextInput } from "@mantine/core";
import { propcolors } from "styles/colors";

type StringColumnProps = {
  label: string;
  is_required: boolean | undefined;
};
const Title = styled.p({
  fontSize: 12,
  fontWeight: 500,
  marginBottom: "1rem",
});
const IconRequired = styled.span(
  (props: { required: boolean | undefined }) => ({
    color: propcolors.red[700],
    display: props.required ? "inline" : "none",
  })
);

export const StringColumn: React.FC<StringColumnProps> = ({
  label,
  is_required,
}) => {
  return (
    <>
      <Title>
        {label}
        <IconRequired required={is_required}>*</IconRequired>
      </Title>
      <TextInput />
    </>
  );
};
