import styled from "@emotion/styled";
import { Radio } from "@mantine/core";
import { propcolors } from "styles/colors";

type RadioColumnProps = {
  label: string;
  is_required: boolean | undefined;
  selectColumns: ContactFormSelectColumn[] | null;
};
const Title = styled.p({
  fontSize: 12,
  fontWeight: 500,
  marginBottom: "1rem",
});
const IconRequired = styled.span(
  (props: { required: boolean | undefined }) => ({
    color: propcolors.red[700],
    display: props.required ? "inline" : "none",
  })
);

const CheckboxInput = styled.div`
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 1rem;
`;

export const RadioColumn: React.FC<RadioColumnProps> = ({
  label,
  is_required,
  selectColumns,
}) => {
  return (
    <>
      <Title>
        {label}
        <IconRequired required={is_required}>*</IconRequired>
      </Title>
      <CheckboxInput>
        {selectColumns?.map((selectColumn) => (
          <Radio label={selectColumn.label} key={selectColumn.uuid} />
        ))}
      </CheckboxInput>
    </>
  );
};
