import styled from "@emotion/styled";
import { useRef } from "react";
import { propcolors } from "styles/colors";
import { DropZoneComponent } from "../DropZoneComponent";

type FileColumnProps = {
  label: string;
  is_required: boolean | undefined;
};
const Title = styled.p({
  fontSize: 12,
  fontWeight: 500,
  marginBottom: "1rem",
});
const IconRequired = styled.span(
  (props: { required: boolean | undefined }) => ({
    color: propcolors.red[700],
    display: props.required ? "inline" : "none",
  })
);

const FileColumnInput = styled.div`
  .drop-zone {
    display: flex;
    height: 117px;
    border-radius: 8px;
    border: 0.12px dashed #e8eaed;
    background-color: #f7f8f9;
    margin-top: 8px;
    justify-content: center;
    font-family: "Inter", "system-ui";
    align-items: center;
    flex-direction: column;
    &-inner {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    &-title {
      font-size: 14px;
      color: #666666;
    }
  }

  .upload-file-button {
    border: 1px solid ${propcolors.gray[200]};
    min-height: 42px;
    background-color: white;
    color: #222222;
    font-size: 14px;
    padding: 0 12px;
    font-weight: 500 !important;
    font-family: "Inter", "system-ui";
    border: 1px solid #e8eaed;
    border-radius: 6px;
  }
`;

export const FileColumn: React.FC<FileColumnProps> = ({
  label,
  is_required,
}) => {
  const openRef = useRef<() => void>(() => {});
  return (
    <>
      <Title>
        {label}
        <IconRequired required={is_required}>*</IconRequired>
      </Title>
      <FileColumnInput>
        <DropZoneComponent openRef={openRef} onDrop={() => {}} />
      </FileColumnInput>
    </>
  );
};
