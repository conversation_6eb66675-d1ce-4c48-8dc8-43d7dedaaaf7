import styled from "@emotion/styled";
import { <PERSON><PERSON>, Modal, TextInput, Switch, Textarea } from "@mantine/core";
import { UseFormReturnType, useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { useEffect, useRef } from "react";
import { propcolors } from "styles/colors";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { FileWithPath } from "@mantine/dropzone";
import { DropZoneComponent } from "../DropZoneComponent";
import { RiAddLine } from "@remixicon/react";
import { SelectColumns } from "./components/selectColumns";

type PresentationProps = {
  className?: string;
  form: UseFormReturnType<EditColumnFormProps>;
  handleSubmit: () => void;
} & EditContentModalProp;

type EditContentModalProp = {
  close: () => void;
  opened: boolean;
  column: ContactFormColumn | null;
  setContactFormDetail: React.Dispatch<
    React.SetStateAction<ContactFormDetail | null>
  >;
};

type EditColumnFormProps = {
  label: string;
  select_columns: ContactFormSelectColumn[] | null;
  description: string | null; // column_type === "DESCRIPTION" の場合のみ使用
  logo: FileWithPath | null; // column_type === "LOGO" の場合のみ使用
  is_required?: boolean;
  sort_order: number;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  close,
  opened,
  column,
  form,
  handleSubmit,
}) => {
  const ModalTitle = styled.div`
    width: 100%;
    text-align: center;
    border-bottom: 1px solid ${propcolors.gray[200]};
    padding-bottom: 1rem;
    top: 1.3rem;
    position: absolute;
    margin: 0 -1rem;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  `;
  const openRef = useRef<() => void>(() => {});
  return (
    <Modal
      title={<ModalTitle>項目を編集</ModalTitle>}
      opened={opened}
      withCloseButton={false}
      onClose={close}
      size="640px"
      className={className}
    >
      <form>
        {column &&
          column.column_type !== "DESCRIPTION" &&
          column.column_type !== "LOGO" && (
            <div className="label">
              <TextInput
                sx={{ marginBottom: "16px" }}
                description="日本語・英数字・記号等が使用できます"
                label={
                  <span>
                    項目タイトル
                    <span style={{ color: "red" }}> ※ </span>
                  </span>
                }
                rightSectionWidth={80}
                rightSection={
                  <div>{`${
                    form.values.label ? form.values.label.length : 0
                  }/50`}</div>
                }
                {...form.getInputProps("label")}
              />
            </div>
          )}
        {column && column.column_type === "DESCRIPTION" && (
          <div className="description">
            <Textarea
              label="補足文章"
              description="補足文章を入力してください"
              minRows={6}
              {...form.getInputProps("description")}
            />
          </div>
        )}
        {column && column.column_type === "LOGO" && (
          <div className="file-input">
            <DropZoneComponent
              openRef={openRef}
              onDrop={(files) => form.setFieldValue("logo", files[0])}
              form={form}
              accept={["image/png", "image/jpeg", "image/jpg"]}
            />
          </div>
        )}
        {column &&
          (column.column_type === "CHECKBOX" ||
            column.column_type === "RADIO") && (
            <div className="checkbox-input">
              <SelectColumns
                selectColumns={form.values.select_columns}
                handleChangeOrder={(newColumns) => {
                  form.setValues({ select_columns: newColumns });
                }}
              />
              <div
                className="add-column"
                onClick={() => {
                  if (form.values.select_columns?.length === 20) {
                    notifications.show({
                      icon: <IconNotiFailed />,
                      title: "項目の追加上限に達しました",
                      message: "項目の追加上限は20個です。",
                      color: "red",
                      autoClose: 5000,
                    });
                    return;
                  }
                  const maxSortOrder = form.values.select_columns?.length ?? 0;
                  // 現在時刻をKeyに設定
                  const newUuid = new Date().getTime().toString();
                  form.setValues({
                    select_columns: [
                      ...(form.values.select_columns ?? []),
                      {
                        uuid: newUuid,
                        label: "新規選択肢",
                        sort_order: maxSortOrder,
                      },
                    ],
                  });
                }}
              >
                <RiAddLine />
                <p>新規追加</p>
              </div>
            </div>
          )}
        {column &&
          column.column_type !== "DESCRIPTION" &&
          column.column_type !== "LOGO" && (
            <div className="is_required_switch">
              <p className="label">必須有無</p>
              <Switch
                label="必須項目にする"
                color="dark"
                size="md"
                checked={form.values.is_required}
                {...form.getInputProps("is_required")}
              />
            </div>
          )}
        <div className="modal-buttons">
          <Button className="modal-buttons-cancel" onClick={close}>
            キャンセル
          </Button>
          <Button
            color={"dark"}
            size={"md"}
            className="modal-buttons-submit full-width"
            onClick={handleSubmit}
          >
            追加する
          </Button>
        </div>
      </form>
    </Modal>
  );
};

const Styled = styled(Presentation)`
  .mantine-Modal-header {
    height: 56px;
  }
  .mantine-Modal-content {
    border-radius: 8px;
  }
  .mantine-TextInput-description {
    color: #666666;
  }
  .mantine-Select-required,
  .mantine-TextInput-required {
    display: none;
  }
  .buttons {
    display: flex;
    gap: 0.5rem;
  }
  .mantine-InputWrapper-root {
    margin: 0;
  }
  .new-button {
    svg {
      margin-right: 1rem;
    }
  }
  .mantine-Modal-body {
    padding: 0px;
  }
  form {
    height: 100%;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    padding: 24px;
    gap: 24px;
    label,
    .label {
      font-weight: 600;
      font-size: 12px;
      color: #666666;
    }
    .label {
      margin-bottom: 8px;
    }
    input {
      padding: 13.5px 70px 13.5px 16px;
      height: auto;
      font-size: 14px;
      line-height: 21px;
      border-radius: 8px;
    }
    .is_required_switch {
      width: 170px;
      cursor: pointer;
    }
    .checkbox-input {
      width: 400px;
      border-radius: 8px;
      border: 1px solid ${propcolors.gray[200]};
      .add-column {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: ${propcolors.gray[150]};
        padding: 16px;
        cursor: pointer;
      }
    }
  }
  .modal-buttons {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    margin-top: 0;
    &-cancel {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-weight: 600;
      font-size: 0.88rem;
      color: ${propcolors.white};
      background-color: ${propcolors.greyDefault};
      border-radius: 8px;
    }
    &-submit {
      padding: 13px 14px;
      width: 100%;
      height: auto;
      font-size: 0.88rem;
      background-color: ${propcolors.black};
      border-radius: 8px;
    }
  }
`;

export const EditColumnModal: React.FC<EditContentModalProp> = ({
  close,
  opened,
  column,
  setContactFormDetail,
}) => {
  const form = useForm<EditColumnFormProps>({
    validateInputOnChange: true,
    initialValues: {
      label: "",
      is_required: false,
      sort_order: 0,
      description: null,
      logo: null,
      select_columns: null,
    },
    validate: {
      label: (value, values) => {
        if (
          column?.column_type !== "DESCRIPTION" &&
          column?.column_type !== "LOGO"
        ) {
          if (value === "") {
            return "項目タイトルを入力してください";
          }
        }
      },
    },
  });

  useEffect(() => {
    if (column) {
      form.setValues({
        label: column.label,
        is_required: column.is_required,
        sort_order: column.sort_order,
        description: column.description,
        select_columns: column.select_columns,
      });
    }
  }, [column]);

  const handleSubmit = () => {
    if (form.isValid() === false) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "入力内容に誤りがあります",
        message: "入力内容を確認してください。",
        color: "red",
        autoClose: 5000,
      });
      form.validate();
      return;
    }
    if (column?.column_type === "LOGO" && !form.values.logo) {
      notifications.show({
        icon: <IconNotiFailed />,
        title: "入力内容に誤りがあります",
        message: "ファイルを選択してください。",
        color: "red",
        autoClose: 5000,
      });
      return;
    }
    form.values.logo && console.log(URL.createObjectURL(form.values.logo));
    setContactFormDetail((prev) => {
      if (column && prev) {
        return {
          ...prev,
          form_columns: prev.form_columns.map(
            (current_column: ContactFormColumn) => {
              if (current_column.uuid === column.uuid) {
                return {
                  ...current_column,
                  label: form.values.label,
                  is_required: form.values.is_required,
                  sort_order: form.values.sort_order,
                  description: form.values.description,
                  select_columns: form.values.select_columns,
                  logo: form.values.logo,
                  logo_path: form.values.logo
                    ? URL.createObjectURL(form.values.logo)
                    : null,
                };
              }
              return current_column;
            }
          ),
        };
      }
      return prev;
    });
    handleClose();
  };

  const handleClose = () => {
    // 初期化処理
    form.reset();

    close();
  };

  return (
    <Styled
      close={handleClose}
      opened={opened}
      column={column}
      form={form}
      handleSubmit={handleSubmit}
      setContactFormDetail={setContactFormDetail}
    />
  );
};
