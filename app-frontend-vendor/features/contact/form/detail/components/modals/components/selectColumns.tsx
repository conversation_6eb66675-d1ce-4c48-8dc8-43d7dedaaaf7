import styled from "@emotion/styled";
import { ActionIcon, rem, TextInput } from "@mantine/core";
import { RiMenuLine, RiDeleteBinLine } from "@remixicon/react";
import React from "react";
import { useDrop, useDrag } from "react-dnd";
import { propcolors } from "styles/colors";

type SelectColumnsProps = {
  selectColumns: ContactFormSelectColumn[] | null;
  handleChangeOrder: (selectColumns: ContactFormSelectColumn[]) => void;
};

const Styled = styled.div`
  .web_question_table-handle-data {
    cursor: grab;
  }
  .web_question_table-handle-data_inner {
    display: flex;
  }
`;

export const SelectColumns: React.FC<SelectColumnsProps> = ({
  selectColumns,
  handleChangeOrder,
}) => {
  const moveItem = (fromIndex: number, toIndex: number) => {
    if (selectColumns) {
      const items = selectColumns.map((item) => ({ ...item }));
      const [movedItem] = items.splice(fromIndex, 1);
      items.splice(toIndex, 0, movedItem);
      // 並び替えたあとのsort_orderを更新
      items.forEach((item, index) => {
        item.sort_order = index;
      });
      handleChangeOrder(items);
    }
  };
  return (
    <Styled>
      {selectColumns &&
        selectColumns.map((column) => (
          <DraggableItem
            key={column.uuid}
            item={column}
            index={column.sort_order}
            moveItem={moveItem}
          >
            <TextInput
              value={column.label}
              onChange={(e) => {
                const newColumns = selectColumns.map((item) => {
                  if (item.uuid === column.uuid) {
                    return { ...item, label: e.currentTarget.value };
                  }
                  return item;
                });
                handleChangeOrder(newColumns);
              }}
            />
            <ActionIcon
              onClick={() => {
                const newColumns = selectColumns.filter(
                  (item) => item.label !== column.label
                );
                handleChangeOrder(newColumns);
              }}
            >
              <RiDeleteBinLine />
            </ActionIcon>
          </DraggableItem>
        ))}
    </Styled>
  );
};
// カスタムカラムの並び替え用の型を定義
type DraggableItemProps = {
  item: ContactFormSelectColumn;
  index: number;
  moveItem: (fromIndex: number, toIndex: number) => void;
  [key: string]: any;
};
// ドラッグアイタムの型を定義
type DraggedItem = {
  type: string;
  index: number;
  item: WebTestQuestion;
};

const ItemType = {
  ITEM: "ITEM",
};

const DraggableItem: React.FC<DraggableItemProps> = ({
  item,
  index,
  moveItem,
  onSelectQuestion,
  onNotSelectQuestion,
  ...props
}) => {
  const ref = React.useRef<HTMLTableDataCellElement | null>(null);

  const [, drop] = useDrop<DraggedItem>({
    accept: ItemType.ITEM,
    hover(draggedItem, monitor) {
      // ref.currentが存在しない場合は何もしない
      if (!ref.current) {
        return;
      }
      const draggedItemIndex = item.sort_order;
      const targetItemIndex = draggedItem.index;

      // 自分自身との入れ替えは行わない
      if (draggedItemIndex === targetItemIndex) {
        return;
      }

      // 現在の要素の位置とサイズに関する情報を取得する
      const targetElementRect = ref.current?.getBoundingClientRect();

      // 現在の要素の縦の中心位置Yを計算する
      const targetElementMiddleY =
        (targetElementRect.bottom - targetElementRect.top) / 2;

      // マウスの現在の位置を取得する
      const mousePosition = monitor.getClientOffset();

      // マウスの現在の位置から、要素の上端までの距離を計算する
      const mousePositionFromTargetElementTop = mousePosition
        ? mousePosition.y - targetElementRect.top
        : 0;

      if (
        // indexを比較して下にドラッグする場合と判定
        draggedItemIndex < targetItemIndex &&
        // 入れ替え対象のTopからマウスまでの位置が入れ替え対象の中心より上にある場合入れ替えない
        mousePositionFromTargetElementTop > targetElementMiddleY
      ) {
        return;
      }

      // 上にドラッグする場合
      if (
        draggedItemIndex > targetItemIndex &&
        mousePositionFromTargetElementTop < targetElementMiddleY
      ) {
        return;
      }
      // 実際にアクションを実行する時間
      moveItem(draggedItem.index, index);
      draggedItem.index = index;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: ItemType.ITEM,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(drop(ref));

  return (
    <div
      ref={ref}
      style={{
        width: "100%",
        opacity: isDragging ? 0.5 : 1,
        display: "flex",
        gap: "16px",
        alignItems: "center",
        padding: "24px",
        borderBottom: `1px solid ${propcolors.border}`,
      }}
    >
      <RiMenuLine
        style={{
          cursor: "grab",
          width: rem(16),
          height: rem(16),
          marginLeft: rem(14),
        }}
      />
      {props.children}
    </div>
  );
};
