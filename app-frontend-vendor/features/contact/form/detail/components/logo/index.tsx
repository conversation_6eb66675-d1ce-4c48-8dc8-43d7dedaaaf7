import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import React from "react";
import { BackgroundImage, Button, Text, Box, Stack } from "@mantine/core";
import { Dropzone } from "@mantine/dropzone";
import { UseFormReturnType } from "@mantine/form";
import { RiUploadCloudFill } from "@remixicon/react";

type LogoDropZoneComponentProps = {
  openRef: React.RefObject<() => void>;
  onDrop: (files: File[]) => void;
  form?: UseFormReturnType<any>;
  accept?: {};
  imagePath: string | null | undefined
};

const Styled = styled.div`
  .logo-input {
    border: 1px solid ${propcolors.gray[300]};
    border-radius: 8px;
    background-color: ${propcolors.gray[200]};
    background-size: contain;
  }
  .drop-zone {
    display: flex;
    height: 208px;
    border: none;
    background-color: transparent;
    margin-top: 8px;
    justify-content: center;
    font-family: "Inter", "system-ui";
    align-items: center;
    flex-direction: column;
    &-title {
      font-size: 14px;
      color: #666666;
    }
    &-buttons {
      display: flex;
      gap: 15px;
    }
    &-inner {
      display: flex;
      justify-content: center;
      gap: 16px;
      align-items: center;
      flex-direction: column;
    }
    .upload-file-button {
      border: 1px solid ${propcolors.gray[200]};
      min-height: 42px;
      padding: 0px 12px;
      font-size: 14px;
      font-weight: 500 !important;
      font-family: "Inter", "system-ui";
      border: 1px solid #e8eaed;
      border-radius: 6px;
    }
  }
`;

export const LogoDropZoneComponent: React.FC<LogoDropZoneComponentProps> = ({
  openRef,
  onDrop: onDropHandler,
  form,
  accept,
  imagePath
}) => {
  return (
    <Styled>
      <BackgroundImage className="logo-input" src={imagePath?? ''}>
      <Dropzone
        openRef={openRef}
        multiple={false}
        onDrop={onDropHandler}
        className="drop-zone"
        activateOnClick={false}
        {...form?.getInputProps("logo")}
        h={117}
        radius={8}
        styles={{ inner: { pointerEvents: "all" } }}
        accept={accept}
      >

        <Box className="drop-zone-inner">
          <Stack align={"center"} mih={90}>
          {!imagePath &&
            <>
              <RiUploadCloudFill size={40} color={propcolors.gray[500]}/>
              <div className="drop-zone-title">ファイルをドラッグもしくは</div>
            </>
          }
          </Stack>
          <div className="drop-zone-buttons">
            <Button
              mih={32}
              h={32}
              maw={129}
              w={129}
              onClick={() => openRef.current && openRef.current()}
              className="upload-file-button"
              variant="default"
            >
              <Text truncate="end">
                {form?.values.logo ? form.values.logo.name : "ファイルを選択"}
              </Text>
            </Button>
            <Button
              mih={32}
              h={32}
              maw={129}
              w={129}
              onClick={() => onDropHandler([])}
              className="upload-file-button"
              variant="default"
            >
              <Text truncate="end">
                削除
              </Text>
            </Button>
          </div>
        </Box>
      </Dropzone>
      </BackgroundImage>
    </Styled>
  );
};
