import { CheckboxColumn } from "./components/CheckboxColumn";
import { DateColumn } from "./components/DateColumn";
import { DateTimeLocalColumn } from "./components/DateTimeLocalColumn";
import { DescriptionColumn } from "./components/DescriptionColumn";
import { FileColumn } from "./components/FileColumn";
import { LogoColumn } from "./components/LogoColumn";
import { RadioColumn } from "./components/RadioColumn";
import { StringColumn } from "./components/StringColumn";
import { TextColumn } from "./components/TextColumn";

type FormColumnProps = {
  contactFormColumn: ContactFormColumn;
};

export const FormColumnComponent: React.FC<FormColumnProps> = ({
  contactFormColumn,
}) => {
  return (
    <>
      {contactFormColumn.column_type === "STRING" && (
        <StringColumn
          label={contactFormColumn.label}
          is_required={contactFormColumn.is_required}
        />
      )}
      {contactFormColumn.column_type === "TEXT" && (
        <TextColumn
          label={contactFormColumn.label}
          is_required={contactFormColumn.is_required}
        />
      )}
      {contactFormColumn.column_type === "DATE" && (
        <DateColumn
          label={contactFormColumn.label}
          is_required={contactFormColumn.is_required}
        />
      )}
      {contactFormColumn.column_type === "DATETIME_LOCAL" && (
        <DateTimeLocalColumn
          label={contactFormColumn.label}
          is_required={contactFormColumn.is_required}
        />
      )}
      {contactFormColumn.column_type === "FILE" && (
        <FileColumn
          label={contactFormColumn.label}
          is_required={contactFormColumn.is_required}
        />
      )}
      {contactFormColumn.column_type === "DESCRIPTION" && (
        <DescriptionColumn description={contactFormColumn.description ?? ""} />
      )}
      {contactFormColumn.column_type === "LOGO" && (
        <LogoColumn logo_path={contactFormColumn.logo_path ?? ""} />
      )}
      {contactFormColumn.column_type === "CHECKBOX" && (
        <CheckboxColumn
          label={contactFormColumn.label}
          is_required={contactFormColumn.is_required}
          selectColumns={contactFormColumn.select_columns}
        />
      )}
      {contactFormColumn.column_type === "RADIO" && (
        <RadioColumn
          label={contactFormColumn.label}
          is_required={contactFormColumn.is_required}
          selectColumns={contactFormColumn.select_columns}
        />
      )}
    </>
  );
};
