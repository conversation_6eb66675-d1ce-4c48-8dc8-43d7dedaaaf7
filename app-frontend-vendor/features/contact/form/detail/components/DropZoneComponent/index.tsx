import styled from "@emotion/styled";
import { propcolors } from "styles/colors";
import React from "react";
import { Button, Text } from "@mantine/core";
import { Dropzone } from "@mantine/dropzone";
import { UseFormReturnType } from "@mantine/form";

type DropZoneComponentProps = {
  openRef: React.RefObject<() => void>;
  onDrop: (files: File[]) => void;
  form?: UseFormReturnType<any>;
  accept?: {};
};

const Styled = styled.div`
  .drop-zone {
    display: flex;
    height: 117px;
    border-radius: 8px;
    border: 0.12px dashed #e8eaed;
    background-color: #f7f8f9;
    margin-top: 8px;
    justify-content: center;
    font-family: "Inter", "system-ui";
    align-items: center;
    flex-direction: column;
    &-title {
      font-size: 14px;
      color: #666666;
    }
    &-inner {
      display: flex;
      justify-content: center;
      gap: 16px;
      align-items: center;
      flex-direction: column;
    }
    .upload-file-button {
      border: 1px solid ${propcolors.gray[200]};
      min-height: 42px;
      background-color: white;
      color: #222222;
      padding: 0px 12px;
      font-size: 14px;
      font-weight: 500 !important;
      font-family: "Inter", "system-ui";
      border: 1px solid #e8eaed;
      border-radius: 6px;
    }
  }
`;

export const DropZoneComponent: React.FC<DropZoneComponentProps> = ({
  openRef,
  onDrop: onDropHandler,
  form,
  accept,
}) => {
  return (
    <Styled>
      <Dropzone
        openRef={openRef}
        multiple={false}
        onDrop={(files) => onDropHandler(files)}
        className="drop-zone"
        activateOnClick={false}
        {...form?.getInputProps("logo")}
        h={117}
        bg="#F7F8F9"
        radius={8}
        styles={{ inner: { pointerEvents: "all" } }}
        accept={accept}
      >
        <div className="drop-zone-inner">
          <div className="drop-zone-title">ここにドラッグ&ドロップまたは</div>
          <Button
            mih={32}
            h={32}
            maw={129}
            w={129}
            onClick={() => openRef.current && openRef.current()}
            className="upload-file-button"
          >
            <Text truncate="end">
              {form?.values.logo ? form.values.logo.name : "ファイルを選択"}
            </Text>
          </Button>
        </div>
      </Dropzone>
    </Styled>
  );
};
