import styled from "@emotion/styled";
import { RiCloseLine } from "@remixicon/react";
import { propcolors } from "styles/colors";
import { AssigneeSetting } from "./components/assigneeSetting";
import { MailNotification } from "./components/mailNotification";
import { NewColumn } from "./components/newColumn";
import { TitleSettings } from "./components/titleSettings";
import { ImageSettings } from "./components/imageSetting";

type PresentationProps = {
  className?: string;
  close: () => void;
} & SideDrawerProps;

type SideDrawerProps = {
  openDrawer: boolean;
  contactFormDetail: ContactFormDetail | null;
  modalMode: string | null;
  openModal: (mode: string | null) => void;
  addColumn: (newColumn: ContactFormColumn) => void;
  setContactFormDetail: (contactFormDetail: ContactFormDetail | null) => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  openDrawer,
  modalMode,
  close,
  addColumn,
  contactFormDetail,
  setContactFormDetail,
}) => {
  return (
    <div className={`${className} ${openDrawer && "open"}`}>
      <div className="header">
        <h3>
          {modalMode === "new_column" && "新規項目追加"}
          {modalMode === "title" && "タイトル・冒頭文"}
          {modalMode === "image" && "画像設定"}
          {modalMode === "mail_notification" && "メール通知"}
          {modalMode === "assignee" && "初期担当者設定"}
        </h3>
        <RiCloseLine onClick={() => close()} />
      </div>
      <div className="contents">
        {modalMode === "new_column" && (
          <NewColumn addColumn={addColumn} close={close} />
        )}
        {modalMode === "title" && (
          <TitleSettings
            contactFormDetail={contactFormDetail}
            setContactFormDetail={setContactFormDetail}
          />
        )}
        {modalMode === "image" && (
            <ImageSettings
                setContactFormDetail={setContactFormDetail}
                contactFormDetail={contactFormDetail}
            />
        )}
        {modalMode === "mail_notification" && (
          <MailNotification
            contactFormDetail={contactFormDetail}
            setContactFormDetail={setContactFormDetail}
          />
        )}
        {modalMode === "assignee" && (
          <AssigneeSetting
            contactFormDetail={contactFormDetail}
            setContactFormDetail={setContactFormDetail}
          />
        )}
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  position: absolute;
  transition: 0.2s;
  margin-top: 73px;
  width: 360px;
  height: calc(100% - 80px - 73px);
  overflow-y: auto;
  background: ${propcolors.gray[150]};
  transform: translateX(-100%);
  z-index: 1;

  ::-webkit-scrollbar {
    display: none;
  }

  &.open {
    transform: translateX(0);
  }

  .header {
    padding: 12px 24px;
    color: ${propcolors.white};
    font-size: 14px;
    font-weight: 400;
    background: ${propcolors.black};
    display: flex;
    align-items: center;
    justify-content: space-between;

    svg {
      cursor: pointer;
    }
  }
`;
export const SideDrawer: React.FC<SideDrawerProps> = ({
  openDrawer,
  contactFormDetail,
  modalMode,
  openModal,
  addColumn,
  setContactFormDetail,
}) => {
  return (
    <Styled
      openDrawer={openDrawer}
      contactFormDetail={contactFormDetail}
      modalMode={modalMode}
      openModal={openModal}
      close={() => openModal(null)}
      addColumn={addColumn}
      setContactFormDetail={setContactFormDetail}
    />
  );
};
