import styled from "@emotion/styled";
import { Select } from "@mantine/core";
import { ContactFormAssigneeTypeLabel } from "constants/commons";
import { useEffect } from "react";
import { propcolors } from "styles/colors";
import useSWR from "swr";
import { ax } from "utils/axios";
import {
  useCompanyUser,
  useSetCompanyUser,
} from "utils/recoil/company/companyUserState";
import { UserSelect } from "../../UserSelect";

type PresentationProps = {
  className?: string;
  companyUser: ManagedUser[] | null;
  assignee_types: string[] | undefined;
} & AssigneeSettingProps;

type AssigneeSettingProps = {
  setContactFormDetail: (contactFormDetail: ContactFormDetail | null) => void;
  contactFormDetail: ContactFormDetail | null;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  setContactFormDetail,
  contactFormDetail,
  companyUser,
  assignee_types,
}) => {
  return (
    <div className={className}>
      <div className="select-input">
        <Select
          label="初期担当者を選択"
          placeholder="初期担当者を選択"
          searchable
          data={
            assignee_types
              ? assignee_types.map((type) => ({
                  value: type,
                  label: ContactFormAssigneeTypeLabel[type] || type,
                }))
              : []
          }
          value={contactFormDetail?.vendor_assignee_type}
          onChange={(e) =>
            setContactFormDetail(
              contactFormDetail
                ? {
                    ...contactFormDetail,
                    vendor_assignee_type:
                      (e as ContactFromVendorAssigneeType) ??
                      contactFormDetail.vendor_assignee_type,
                    // タイプがMEMBER以外の場合はユーザーIDをnullにする
                    vendor_assignee_user_id:
                      contactFormDetail.vendor_assignee_type !== "MEMBER"
                        ? null
                        : contactFormDetail.vendor_assignee_user_id,
                  }
                : null
            )
          }
        />
      </div>
      {contactFormDetail?.vendor_assignee_type === "MEMBER" && (
        <div className="user-select">
          <p className="label">社内ユーザーを選択</p>
          <UserSelect
            isLoading={companyUser === undefined}
            isInputDisabled={false}
            defaultValue={contactFormDetail?.vendor_assignee_user_name ?? undefined}
            onClick={(params) => {
              setContactFormDetail(
                contactFormDetail
                  ? {
                    ...contactFormDetail,
                    vendor_assignee_user_id: params.value,
                    vendor_assignee_user_name: params.label,
                    vendor_assignee_type: "MEMBER",
                  }
                  : null
              );
            } }
            data={companyUser
              ? companyUser.map((user) => ({
                value: user.id,
                label: user.name,
                button_label: "追加",
                active: true,
              }))
              : null} 
              isShareAll={false}
              />
        </div>
      )}
    </div>
  );
};

const Styled = styled(Presentation)`
  padding: 16px;
  font-size: 12px;
  display: grid;
  gap: 16px;
  label {
    font-size: 12px;
    font-weight: 600;
  }
  label,
  .label {
    font-weight: 600;
    font-size: 12px;
    color: #666666;
  }
  .label {
    margin-bottom: 8px;
  }
  input {
    font-size: 14px;
  }
  textarea {
    font-size: 14px;
    padding-right: 44px;
  }
  .notified_users {
    ul {
      display: grid;
      border-radius: 8px;
      border: 1px solid ${propcolors.gray[200]};
      li {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 16px;
        gap: 8px;
        background: ${propcolors.white};
        font-size: 14px;

        &:nth-child(odd) {
          background: ${propcolors.gray[150]};
        }

        .delete-button {
          width: 52px;
          min-width: 52px;
          padding: 0;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 400 !important;
          border 1px solid transparent;
          color: #C13515;
          border-color: #C13515;
          background-color: #ffffff;
      }
      }
    }
  }
`;
export const AssigneeSetting: React.FC<AssigneeSettingProps> = ({
  setContactFormDetail,
  contactFormDetail,
}) => {
  const companyUser = useCompanyUser();
  const setCompanyUser = useSetCompanyUser();
  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch(() => {});
  const { data: assigneeTypeMaster } = useSWR<{ assignee_types: string[] }>(
    `api/v1/master/contact/form/assignee_type`,
    fetcher
  );

  useEffect(() => {
    if (!companyUser) {
      ax.get("/api/v1/vendor_users").then((res) => {
        setCompanyUser(res.data);
      });
    }
  }, [companyUser]);
  return (
    <Styled
      setContactFormDetail={setContactFormDetail}
      contactFormDetail={contactFormDetail}
      companyUser={companyUser}
      assignee_types={assigneeTypeMaster?.assignee_types}
    />
  );
};
3;
