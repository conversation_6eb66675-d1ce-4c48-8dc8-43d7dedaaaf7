import styled from "@emotion/styled";
import { ContactFormColumnTypeLabel } from "constants/commons";
import { useEffect, useState } from "react";
import { propcolors } from "styles/colors";
import useSWR from "swr";
import { ax } from "utils/axios";

type PresentationProps = {
  className?: string;
  inputColumn: string[];
  insertColumn: string[];
  handleAddColumn: (columnType: ContactFormColumnTypes) => void;
};

type NewColumnProps = {
  addColumn: (newColumn: ContactFormColumn) => void;
  close: () => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  inputColumn,
  insertColumn,
  handleAddColumn,
}) => {
  return (
    <div className={className}>
      <p>入力カラム</p>
      <div className="types">
        {inputColumn.map((column_type) => (
          <div
            key={column_type}
            className="type"
            onClick={() =>
              handleAddColumn(column_type as ContactFormColumnTypes)
            }
          >
            {ContactFormColumnTypeLabel[column_type]}
          </div>
        ))}
      </div>

      <p>挿入</p>
      <div className="types">
        {insertColumn.map((column_type) => (
          <div
            key={column_type}
            className="type"
            onClick={() =>
              handleAddColumn(column_type as ContactFormColumnTypes)
            }
          >
            {ContactFormColumnTypeLabel[column_type]}
          </div>
        ))}
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  padding: 0 16px;
  font-size: 12px;
  p {
    margin: 16px 0;
  }
  .types {
    display: flex;
    flex-direction: column;
    gap: 12px;
    .type {
      width: 208px;
      padding: 8px 24px;
      border-radius: 8px;
      background: ${propcolors.white};

      &:hover {
        background: ${propcolors.gray[200]};
      }
    }
  }
`;
export const NewColumn: React.FC<NewColumnProps> = ({ addColumn, close }) => {
  const fetcherMaster = (url: string) =>
    ax
      .get(`api/v1/master/${url}`)
      .then((res) => res.data)
      .catch(() => {});
  const { data: COLUMN_TYPE } = useSWR<{ column_types: string[] }>(
    `contact/form/column_type`,
    fetcherMaster
  );
  const [inputColumn, setInputColumn] = useState([""]);
  const [insertColumn, setInsertColumn] = useState([""]);

  const handleAddColumn = (columnType: ContactFormColumnTypes) => {
    let column_label = "";
    let column_description = "";
    let column_is_required = undefined;
    let select_columns = null;
    if (columnType !== "DESCRIPTION" && columnType !== "LOGO") {
      column_label = ContactFormColumnTypeLabel[columnType];
      column_is_required = false;
    } else if (columnType === "DESCRIPTION") {
      column_description = "補足の文章が入ります。";
    }
    if (columnType === "CHECKBOX" || columnType === "RADIO") {
      const newUuid = new Date().getTime().toString();
      select_columns = [
        {
          label: "選択肢1",
          uuid: newUuid + "0",
          sort_order: 0,
        },
        {
          label: "選択肢2",
          uuid: newUuid + "1",
          sort_order: 1,
        },
      ];
    }
    addColumn({
      uuid: "",
      column_type: columnType,
      label: column_label,
      select_columns: select_columns,
      description: column_description,
      logo: null,
      logo_path: "",
      is_required: column_is_required,
      sort_order: 0,
      is_new: true,
    });
    close();
  };

  useEffect(() => {
    if (COLUMN_TYPE?.column_types) {
      const columnTypes = COLUMN_TYPE.column_types;

      // "DESCRIPTION" の位置を探す
      const indexDescription = columnTypes.findIndex(
        (type) => type === "DESCRIPTION"
      );

      // 1) 「DESCRIPTION より前の要素」を入力カラムとして仮取得
      const beforeDescription = columnTypes.slice(0, indexDescription);

      // 2) 「DESCRIPTION 以降の要素」を挿入カラムとして仮取得
      const afterDescription = columnTypes.slice(indexDescription);

      // 3) DATETIME_LOCAL を入力カラムに含めたいので、
      //    仮取得後に強制的に移動・または削除
      //    - 入力カラムに無ければ追加
      //    - 挿入カラムにあれば除外

      // DATETIME_LOCAL を含めておきたい
      const finalInput = [...beforeDescription];
      if (!finalInput.includes("DATETIME_LOCAL")) {
        finalInput.push("DATETIME_LOCAL");
      }
      // 重複しないようにユニーク化
      const uniqueInput = Array.from(new Set(finalInput));

      // 挿入カラムから DATETIME_LOCAL を除外
      const finalInsert = afterDescription.filter(
        (type) => type !== "DATETIME_LOCAL"
      );

      setInputColumn(uniqueInput);
      setInsertColumn(finalInsert);
    }
  }, [COLUMN_TYPE]);
  return (
    <Styled
      inputColumn={inputColumn}
      insertColumn={insertColumn}
      handleAddColumn={handleAddColumn}
    />
  );
};
