import styled from "@emotion/styled";
import { Switch, Textarea, TextInput } from "@mantine/core";
import { propcolors } from "styles/colors";

type PresentationProps = {
  className?: string;
} & TitleSettingProps;

type TitleSettingProps = {
  setContactFormDetail: (contactFormDetail: ContactFormDetail | null) => void;
  contactFormDetail: ContactFormDetail | null;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  setContactFormDetail,
  contactFormDetail,
}) => {
  const WordCounter = styled.p`
    font-size: 12px;
    color: #666666;
    text-align: right;
    margin-top: 4px;

    &.error {
      color: ${propcolors.red[500]};
    }
  `;
  return (
    <div className={className}>
      <div className="text-input">
        <TextInput
          label="タイトル"
          placeholder="タイトル"
          value={contactFormDetail?.title}
          onChange={(e) =>
            setContactFormDetail(
              contactFormDetail
                ? {
                    ...contactFormDetail,
                    title: e.currentTarget.value,
                  }
                : null
            )
          }
        />
        <WordCounter
          className={
            contactFormDetail?.title && contactFormDetail?.title.length > 50
              ? "error"
              : ""
          }
        >
          {contactFormDetail?.title && contactFormDetail?.title.length}
          /50
        </WordCounter>
      </div>
      <div className="textarea-input">
        <Textarea
          label="冒頭文"
          placeholder="冒頭文"
          value={contactFormDetail?.description ?? ""}
          minRows={8}
          onChange={(e) =>
            setContactFormDetail(
              contactFormDetail
                ? {
                    ...contactFormDetail,
                    description: e.currentTarget.value,
                  }
                : null
            )
          }
        />
        <WordCounter
          className={
            contactFormDetail?.description &&
            contactFormDetail?.description.length > 300
              ? "error"
              : ""
          }
        >
          {contactFormDetail?.description &&
            contactFormDetail?.description.length}
          /300
        </WordCounter>
      </div>
      <div className="switch-input">
        <p className="label">冒頭分を表示する</p>
        <Switch
          label="冒頭文を表示する"
          checked={contactFormDetail?.is_description_visible ?? false}
          onChange={(e) =>
            setContactFormDetail(
              contactFormDetail
                ? {
                    ...contactFormDetail,
                    is_description_visible: e.currentTarget.checked,
                  }
                : null
            )
          }
        />
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  padding: 16px;
  font-size: 12px;
  display: grid;
  gap: 16px;
  label {
    font-size: 12px;
    font-weight: 600;
  }
  label,
  .label {
    font-weight: 600;
    font-size: 12px;
    color: #666666;
  }
  .label {
    margin-bottom: 8px;
  }
  input {
    font-size: 14px;
  }
  textarea {
    font-size: 14px;
    padding-right: 44px;
  }
`;
export const TitleSettings: React.FC<TitleSettingProps> = ({
  setContactFormDetail,
  contactFormDetail,
}) => {
  return (
    <Styled
      setContactFormDetail={setContactFormDetail}
      contactFormDetail={contactFormDetail}
    />
  );
};
