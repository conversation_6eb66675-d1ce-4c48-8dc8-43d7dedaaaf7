import styled from "@emotion/styled";
import { But<PERSON>, Select, Switch } from "@mantine/core";
import { ContactFormNotificationTypeLabel } from "constants/commons";
import { useEffect } from "react";
import { propcolors } from "styles/colors";
import useSWR from "swr";
import { ax } from "utils/axios";
import {
  useCompanyUser,
  useSetCompanyUser,
} from "utils/recoil/company/companyUserState";
import { UserSelect } from "../../UserSelect";

type PresentationProps = {
  className?: string;
  companyUser: ManagedUser[] | null;
  notifiedTypes: string[] | undefined;
} & MailNotificationProps;

type MailNotificationProps = {
  setContactFormDetail: (contactFormDetail: ContactFormDetail | null) => void;
  contactFormDetail: ContactFormDetail | null;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  setContactFormDetail,
  contactFormDetail,
  companyUser,
  notifiedTypes,
}) => {
  return (
    <div className={className}>
      <div className="switch-input">
        <p className="label">担当パートナーユーザーにメール通知する</p>
        <Switch
          label="通知する"
          checked={contactFormDetail?.is_partner_inquirer_notified ?? false}
          onChange={(e) =>
            setContactFormDetail(
              contactFormDetail
                ? {
                    ...contactFormDetail,
                    is_partner_inquirer_notified: e.currentTarget.checked,
                  }
                : null
            )
          }
        />
      </div>
      <div className="switch-input">
        <p className="label">社内ユーザーにメール通知する</p>
        <Switch
          label="通知する"
          checked={contactFormDetail?.is_vendor_notified ?? false}
          onChange={(e) =>
            setContactFormDetail(
              contactFormDetail
                ? {
                    ...contactFormDetail,
                    is_vendor_notified: e.currentTarget.checked,
                  }
                : null
            )
          }
        />
      </div>
      {contactFormDetail?.is_vendor_notified && (
        <div className="select-input">
          <Select
            label="通知先タイプ"
            placeholder="通知先を選択"
            searchable
            data={
              notifiedTypes
                ? notifiedTypes.map((type) => ({
                    value: type,
                    label: ContactFormNotificationTypeLabel[type] || type,
                  }))
                : []
            }
            value={contactFormDetail?.vendor_notified_type}
            onChange={(e) =>
              setContactFormDetail(
                contactFormDetail
                  ? {
                      ...contactFormDetail,
                      vendor_notified_type:
                        (e as ContactFormVendorNotifiedType) ??
                        contactFormDetail.vendor_notified_type,
                      vendor_notified_users: [],
                    }
                  : null
              )
            }
          />
        </div>
      )}
      {contactFormDetail?.is_vendor_notified &&
        contactFormDetail?.vendor_notified_type === "MEMBER" && (
          <div className="user-select">
            <p className="label">社内の通知するユーザーを選択</p>
            <UserSelect
              isLoading={companyUser === undefined}
              isInputDisabled={false}
              isFilter={true}
              isShareAll={false}
              onClick={(params) => {
                setContactFormDetail(
                  contactFormDetail
                    ? {
                        ...contactFormDetail,
                        vendor_notified_users:
                          contactFormDetail.vendor_notified_users
                            ? [
                                ...contactFormDetail.vendor_notified_users,
                                { id: params.value, name: params.label },
                              ]
                            : [{ id: params.value, name: params.label }],
                        vendor_notified_type: "MEMBER",
                      }
                    : null
                );
              }}
              data={
                companyUser
                  ? companyUser
                      .filter(
                        (company_user) =>
                          !contactFormDetail?.vendor_notified_users?.some(
                            (user) => company_user.id === user.id
                          )
                      )
                      .map((user) => ({
                        value: user.id,
                        label: user.name,
                        button_label: "追加",
                        active: true,
                      }))
                  : null
              }
            />
          </div>
        )}
      {contactFormDetail?.is_vendor_notified &&
        contactFormDetail?.vendor_notified_type === "MEMBER" && (
          <div className="notified_users">
            <p className="label">通知する社内ユーザー</p>
            <ul>
              {contactFormDetail?.vendor_notified_users?.map((user) => {
                const userDetail = companyUser?.find(
                  (companyUser) => companyUser.id === user.id
                );
                return (
                  <li key={user.id}>
                    <span>{userDetail?.name}</span>
                    <Button
                      className="delete-button"
                      onClick={() => {
                        setContactFormDetail(
                          contactFormDetail
                            ? {
                                ...contactFormDetail,
                                vendor_notified_users:
                                  contactFormDetail.vendor_notified_users?.filter(
                                    (current_user) => current_user !== user
                                  ) ?? null,
                              }
                            : null
                        );
                      }}
                    >
                      削除
                    </Button>
                  </li>
                );
              }) ?? []}
            </ul>
          </div>
        )}
    </div>
  );
};

const Styled = styled(Presentation)`
  padding: 16px;
  font-size: 12px;
  display: grid;
  gap: 16px;
  label {
    font-size: 12px;
    font-weight: 600;
  }
  label,
  .label {
    font-weight: 600;
    font-size: 12px;
    color: #666666;
  }
  .label {
    margin-bottom: 8px;
  }
  input {
    font-size: 14px;
  }
  textarea {
    font-size: 14px;
    padding-right: 44px;
  }
  .notified_users {
    ul {
      display: grid;
      border-radius: 8px;
      border: 1px solid ${propcolors.gray[200]};
      li {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 16px;
        gap: 8px;
        background: ${propcolors.white};
        font-size: 14px;

        &:nth-child(odd) {
          background: ${propcolors.gray[150]};
        }

        .delete-button {
          width: 52px;
          min-width: 52px;
          padding: 0;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 400 !important;
          border 1px solid transparent;
          color: #C13515;
          border-color: #C13515;
          background-color: #ffffff;
      }
      }
    }
  }
`;
export const MailNotification: React.FC<MailNotificationProps> = ({
  setContactFormDetail,
  contactFormDetail,
}) => {
  const companyUser = useCompanyUser();
  const setCompanyUser = useSetCompanyUser();
  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch(() => {});
  const { data: notifiedTypeMaster } = useSWR<{ notified_types: string[] }>(
    `api/v1/master/contact/form/notified_type`,
    fetcher
  );

  useEffect(() => {
    if (!companyUser) {
      ax.get("/api/v1/vendor_users").then((res) => {
        setCompanyUser(res.data);
      });
    }
  }, [companyUser]);
  return (
    <Styled
      setContactFormDetail={setContactFormDetail}
      contactFormDetail={contactFormDetail}
      companyUser={companyUser}
      notifiedTypes={notifiedTypeMaster?.notified_types}
    />
  );
};
3;
