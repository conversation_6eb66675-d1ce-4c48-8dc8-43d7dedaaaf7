import styled from "@emotion/styled";
import Head from "next/head";
import { useEffect, useState } from "react";
import { propcolors } from "styles/colors";
import { ax } from "utils/axios";
import { useRouter } from "next/router";
import { notifications } from "@mantine/notifications";
import { ActionIcon, Button, Tooltip, Menu } from "@mantine/core";
import { CommonListLayout } from "components/layouts/commonListLayout";
import { ColDef } from "ag-grid-community";
import { AGGrid } from "components/Grid";
import useSWR from "swr";
import CellWebTestStatusRender from "./components/cell/CellWebTestStatusRender";
import EditIcon from "public/icons/edit.svg";
import TrashIcon from "public/icons/trash.svg";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";
import { CustomBreadcrumb } from "components/breadcrumb";
import { openDeleteConfirmModal } from "components/Modals/confirm";
import { PER_PAGE_OF_CONTACT_FORM } from "constants/commonSetting";
import { showExportModal } from "./components/modals/ContactAnswerExportModal";

type PresentationProps = {
  className?: string;
  contactForms: ContactForm[] | null;
  colDef: ColDef[];
  exportExcel: () => void;
};

const Presentation: React.FC<PresentationProps> = ({
  className,
  contactForms,
  colDef,
  exportExcel,
}) => {
  const router = useRouter();
  return (
    <CommonListLayout className={className}>
      <Head>
        <title>問い合わせ | PartnerProp</title>
      </Head>
      <header>
        <CustomBreadcrumb
          title="問い合わせ"
          list={[{ title: "フォーム設定", href: "/contact/form" }]}
        />
        <div className="header-buttons">
          <Button
            onClick={exportExcel}
            variant="default"
            css={{
              border: `1px solid ${propcolors.gray[200]}`,
              color: propcolors.blackLight,
              fontSize: "14px",
              fontWeight: 500,
              borderRadius: "8px",
              height: "42px",
            }}
          >
            エクスポート
          </Button>
          <Button onClick={() => router.push("/contact/form/new")}>
            新規作成
          </Button>
        </div>
      </header>
      <AGGrid
        className="commonList-grid"
        columnDefs={colDef}
        rowData={contactForms}
        suppressRowClickSelection={true}
        paginationPageSize={PER_PAGE_OF_CONTACT_FORM}
      />
    </CommonListLayout>
  );
};

const Styled = styled(Presentation)`
  header {
    padding: 15px 24px;
  }
  .mantine-Tooltip-tooltip {
    height: 23px;
    font-size: 12px;
    line-height: 14px;
    font-weight: 400;
    top: 2px !important;
  }
  .mantine-Tooltip-arrow {
    border-left: 4px solid transparent !important;
    border-right: 4px solid transparent !important;
    border-bottom: 4px solid #212529 !important;
    -moz-transform: rotate(180deg) !important;
    -ms-transform: rotate(180deg) !important;
    transform: rotate(180deg) !important;
    position: absolute !important;
    top: 25px !important;
    background-color: transparent;
  }

  .header-buttons {
    button {
      padding: 0 15px;
      width: auto;
      border-radius: 8px;
    }
  }

  .ag-root-wrapper {
    border-left: none;
  }

  .commonList-grid {
    border-top: 1px solid #e8eaed;
  }
  }

  .action-buttons {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    align-items: center;
    position: relative;
    z-index: 10;
    button {
      width: 32px;
      height: 32px;
      svg {
        path {
          fill: #8992a0;
        }
      }
    }
    .toPreview {
      width: auto;
      height: 32px;
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 8px 16px;
      border-radius: 4px;
      background-color: #ffffff;
      border: 1px solid #e8eaed;
      color: #222222;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;

      .dropdown-icon {
        margin-left: 8px;
        transition: transform 0.2s ease;
        font-size: 8px;
        display: inline-block;
        height: 4px;
        line-height: 0;
        color: #222222;
      }

      &:hover {
        background-color: ${propcolors.gray[150]};
        .dropdown-icon {
          transform: translateY(2px);
        }
      }

      svg {
        path {
          fill: #222222;
        }
      }
      &.disabled {
        background-color: ${propcolors.gray[150]};
        border: 1px solid ${propcolors.gray[150]};
        color: ${propcolors.gray[400]};

        svg {
          path {
            fill: ${propcolors.gray[400]};
          }
        }
      }
    }
  }

  .mantine-Menu-root[data-opened] .toPreview .dropdown-icon {
    transform: rotate(180deg);
  }

  .partner_list {
    margin-top: 2rem;
    &-header {
      font-size: 1rem;
      margin-bottom: 1rem;
    }
    &-row {
      list-style: none;
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid ${propcolors.gray[200]};
      .button {
        color: ${propcolors.red[600]};
      }
    }
  }

  .mantine-Menu-dropdown {
    z-index: 9999 !important;
  }
`;
export const ContactFormPage = () => {
  const router = useRouter();
  const [contactForms, setContactForms] = useState<ContactForm[] | null>(null);
  const fetcher = (url: string) =>
    ax
      .get(url)
      .then((res) => res.data)
      .catch(() => {});
  const { data: contactFormsData, mutate: mutateContactFormsData } = useSWR(
    `api/v1/contact/form/list`,
    fetcher
  );

  useEffect(() => {
    if (contactFormsData?.contact_forms === undefined) return;
    setContactForms(contactFormsData?.contact_forms);
  }, [contactFormsData]);

  const deleteForm = (uuid: string) => {
    openDeleteConfirmModal({
      title: "フォームを削除",
      body: "本当にこのフォームを削除しますか？",
      bottomMessage: "変更完了後に再読み込みが行われます。",
      onConfirm: () => {
        ax.delete(`/api/v1/contact/form/${uuid}`)
          .then(() => {
            notifications.show({
              icon: <IconNotiSuccess />,
              title: "フォームの削除に成功しました。",
              message: "削除が正常に処理されました。",
              autoClose: 5000,
            });
            mutateContactFormsData();
          })
          .catch((err) => {
            notifications.show({
              icon: <IconNotiFailed />,
              title: "フォームの削除に失敗しました。",
              message: "削除が正常に処理されませんでした。",
              autoClose: 5000,
            });
          });
      },
    });
  };

  const push = (id: string) => {
    router.push(`/contact/form/${id}`);
  };

  /**
   * Format Date
   * Convert format yyyy
   * @param date string ISO 8601 YYYY-MM-DDTHH:MM:SS.sssZ
   * @returns string YYYY-MM-DD HH:MM
   */
  const formatDate = (date: string): string => {
    const parsedDate = new Date(date);
    const year = parsedDate.getFullYear();
    const month = String(parsedDate.getMonth() + 1).padStart(2, "0"); // 月は0から始まるので+1
    const day = String(parsedDate.getDate()).padStart(2, "0");
    const hours = String(parsedDate.getHours()).padStart(2, "0");
    const minutes = String(parsedDate.getMinutes()).padStart(2, "0");
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  };

  const colDef = [
    {
      headerName: "フォーム名",
      field: "title",
      width: 300,
      sortable: true,
      filter: true,
    },
    {
      headerName: "作成日時",
      field: "created_at",
      width: 140,
      sortable: true,
      filter: true,
      cellStyle: { color: "#666666", fontSize: "12px" },
      cellRenderer: (params: any) => {
        return formatDate(params.data?.created_at);
      },
    },
    {
      headerName: "最終更新日",
      field: "updated_at",
      width: 140,
      sortable: true,
      filter: true,
      cellStyle: { color: "#666666", fontSize: "12px" },
      cellRenderer: (params: any) => {
        return formatDate(params.data?.updated_at);
      },
    },
    {
      headerName: "公開ステータス",
      field: "is_published",
      width: 180,
      sortable: true,
      filter: true,
      comparator: (
        valueA: { status: boolean },
        valueB: { status: boolean }
      ) => {
        if (valueA.status === valueB.status) return 0;
        return valueA.status ? 1 : -1; // true を大きいと見なす場合
      },
      valueGetter: (params: any) => ({
        status: params?.data.is_published,
      }),
      cellRenderer: CellWebTestStatusRender,
    },
    {
      headerName: "",
      field: "action",
      minWidth: 180,
      flex: 1,
      suppressAutoSize: true,
      suppressSizeToFit: true,
      cellClass: "action-cell",
      cellRenderer: (params: any) => {
        return (
          <div className="action-buttons">
            <Tooltip
              label="編集する"
              withArrow
              offset={8}
              position="top"
              transitionProps={{ duration: 200 }}
            >
              <ActionIcon onClick={() => push(params.data.uuid)}>
                <EditIcon />
              </ActionIcon>
            </Tooltip>

            <Tooltip
              label="削除する"
              withArrow
              offset={8}
              position="top"
              transitionProps={{ duration: 200 }}
            >
              <ActionIcon onClick={() => deleteForm(params.data.uuid)}>
                <TrashIcon />
              </ActionIcon>
            </Tooltip>

            <Menu position="bottom-end" withinPortal>
              <Menu.Target>
                <Button
                  className="toPreview"
                  styles={{
                    root: {
                      padding: 0,
                      height: "auto",
                      border: "none",
                      background: "transparent",
                    },
                    inner: {
                      padding: 0,
                      margin: 0,
                    },
                  }}
                >
                  アクション
                  <span className="dropdown-icon">▼</span>
                </Button>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item
                  onClick={() =>
                    push(`answer/${params.data.uuid}?mode=preview`)
                  }
                >
                  プレビュー
                </Menu.Item>
                <Menu.Item
                  onClick={() => push(`answer/${params.data.uuid}`)}
                  disabled={!params.data.is_published}
                >
                  チケットを起票
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </div>
        );
      },
    },
  ];

  return (
    <Styled
      contactForms={contactForms}
      colDef={colDef}
      exportExcel={showExportModal}
    />
  );
};
