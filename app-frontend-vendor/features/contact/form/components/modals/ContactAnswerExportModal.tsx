import { DatePickerInput } from "@mantine/dates";
import { Group } from "@mantine/core";
import { modals } from "@mantine/modals";
import { notifications } from "@mantine/notifications";
import { RiCalendar2Fill } from "@remixicon/react";
import IconNotiSuccess from "public/icons/icon-noti-success.svg";
import IconNotiFailed from "public/icons/icon-noti-failed.svg";

import { propcolors } from "styles/colors";
import { getApiErrorMessage } from "utils/values/errorMessages";
import { ax } from "utils/axios";
import React, { useState } from "react";
import { Button, Text } from "@mantine/core";

/**
 * 日付入力コンポーネント
 */
interface DateInputProps {
  label: string;
  is_required: boolean;
  value: Date | null;
  onChange: (value: Date | null) => void;
}

const DateSearchInput: React.FC<DateInputProps> = ({
  label,
  is_required,
  value,
  onChange,
}) => (
  <div style={{ display: "flex", flexDirection: "column" }}>
    <p style={{ fontSize: 12, marginBottom: "1rem" }}>
      {label}
      {is_required && <span style={{ color: propcolors.red[700] }}>*</span>}
    </p>
    <DatePickerInput
      style={{ width: "200px" }}
      valueFormat="YYYY-MM-DD"
      locale="ja"
      monthLabelFormat="YYYY-M"
      yearLabelFormat="YYYY"
      monthsListFormat="M"
      yearsListFormat="YYYY"
      firstDayOfWeek={0}
      value={value}
      onChange={onChange}
      rightSection={<RiCalendar2Fill />}
      popoverProps={{ withinPortal: true }}
    />
  </div>
);

/**
 * モーダルコンテンツ
 */
interface ExportModalContentProps {
  onConfirm: (startDate: string, endDate: string) => void;
  onCancel: () => void;
}

const ExportModalContent: React.FC<ExportModalContentProps> = ({
  onConfirm,
  onCancel,
}) => {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [errors, setErrors] = useState<{
    startDate?: string;
    endDate?: string;
    dateRange?: string;
  }>({});

  const validate = (): boolean => {
    const newErrors: {
      startDate?: string;
      endDate?: string;
      dateRange?: string;
    } = {};

    if (!startDate) {
      newErrors.startDate = "開始日を入力してください。";
    }

    if (!endDate) {
      newErrors.endDate = "終了日を入力してください。";
    }

    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      if (start > end) {
        newErrors.dateRange = "開始日は終了日以前に設定してください。";
      }
    }

    setErrors(newErrors);

    return Object.keys(newErrors).length === 0;
  };

  const handleExport = () => {
    if (validate()) {
      onConfirm(startDate!.toISOString(), endDate!.toISOString());
    } else {
      notifications.show({
        title: "入力エラー",
        message: "エクスポートを続行する前に、入力エラーを修正してください。",
        icon: <IconNotiFailed />,
      });
    }
  };

  return (
    <div>
      <div
        style={{
          fontSize: "18px",
          color: propcolors.blackLight,
          margin: "25px 10px 0px 10px",
        }}
      >
        データをExcelファイルにエクスポートします。
      </div>
      <div
        style={{
          color: propcolors.blackLightLabel,
          fontSize: "12px",
          margin: "12px 10px 19px 10px",
        }}
      >
        出力結果はメールアドレス宛に送付されます。
      </div>
      <Group noWrap align="flex-end" spacing="xs" style={{ marginTop: "10px" }}>
        <DateSearchInput
          label="開始日"
          is_required={true}
          value={startDate}
          onChange={(value: Date | null) => setStartDate(value)}
        />
        <Text style={{ marginBottom: "5px" }}>～</Text>
        <DateSearchInput
          label="終了日"
          is_required={true}
          value={endDate}
          onChange={(value: Date | null) => setEndDate(value)}
        />
      </Group>
      {Object.values(errors).map((error, index) => (
        <Text
          key={index}
          style={{ color: "red", fontSize: "12px", marginTop: "5px" }}
        >
          {error}
        </Text>
      ))}
      <Group
        position="center"
        spacing="xs"
        style={{ marginTop: "20px", width: "100%" }}
      >
        <Button
          onClick={onCancel}
          styles={{
            root: {
              marginRight: "10px",
              flex: 1,
              height: "42px",
              fontSize: "14px",
              fontWeight: 400,
              borderRadius: "8px",
              borderColor: propcolors.greyDefault,
              backgroundColor: propcolors.greyDefault,
              color: propcolors.white,
              "&:hover": {
                backgroundColor: propcolors.greyDefault,
              },
            },
          }}
        >
          キャンセル
        </Button>
        <Button
          onClick={handleExport}
          styles={{
            root: {
              flex: 1,
              height: "42px",
              fontSize: "14px",
              fontWeight: 400,
              borderRadius: "8px",
              backgroundColor: propcolors.black,
              color: propcolors.white,
              "&:hover": {
                backgroundColor: propcolors.black,
              },
            },
          }}
        >
          エクスポート
        </Button>
      </Group>
    </div>
  );
};

// エクスポートモーダルを表示
export const showExportModal = () => {
  const handleConfirm = (startDate: string, endDate: string) => {
    ax.post(`api/v1/contact/export`, {
      start_date: startDate,
      end_date: endDate,
    })
      .then(() => {
        notifications.show({
          title: "エクスポートリクエストを受け付けました。",
          message:
            "処理が完了したらExcelファイルがメールアドレス宛に送付されます。",
          icon: <IconNotiSuccess />,
        });
        modals.closeAll();
      })
      .catch((err) => {
        let title = "";
        let message = "";
        if (err.response.status === 404) {
          title = "入力エラー";
          message = "エクスポート対象のデータが見つかりませんでした";
        } else if (err.response.status === 400) {
          title = "入力エラー";
          message = "入力内容に誤りがあります";
        } else if (err.response.status === 422) {
          title = "入力エラー";
          const errorMessage: string[][] = Object.values(err.response.data);
          if (
            errorMessage.some((error_list: string[]) => {
              return error_list.includes(
                "The period between the start date and end date must be within 6 months."
              );
            })
          ) {
            message =
              message + "開始日と終了日の期間は6ヶ月以内に指定してください";
          } else {
            message = "入力内容に誤りがあります";
          }
        } else {
          title = "エラーが発生しました";
          message = getApiErrorMessage(err.response.data?.message);
        }

        notifications.show({
          title: title,
          message: message,
          icon: <IconNotiFailed />,
        });
      });
  };

  modals.open({
    title: (
      <div
        style={{
          width: "100%",
          textAlign: "center",
          fontWeight: 600,
          borderBottom: `1px solid ${propcolors.gray[200]}`,
          paddingBottom: "1rem",
          top: "1.3rem",
          position: "absolute",
          margin: "0 -1rem",
        }}
      >
        データをダウンロード
      </div>
    ),
    size: "640px",
    closeButtonProps: { size: "24px" },
    children: (
      <ExportModalContent
        onConfirm={handleConfirm}
        onCancel={() => modals.closeAll()}
      />
    ),
  });
};
