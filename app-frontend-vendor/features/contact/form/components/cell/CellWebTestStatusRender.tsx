import styled from "@emotion/styled";
import WebTestStatus from "constants/enums/webTestStatus.enum";

interface IPresentationProp {
  status: WebTestStatus;
}

interface ICellLeadStatusRender {
  value: IPresentationProp;
}

const Presentation: React.FC<IPresentationProp> = (props) => {
  const { status } = props;

  return (
    <div
      className="leads-status-render"
      style={{
        display: "flex",
        justifyContent: "left",
        alignItems: "center",
        height: "100%",
        width: "100%",
      }}
    >
      <div
        className="leads-status-badge"
        style={{
          padding: "6px",
          width: 84,
          height: 24,
          alignItems: "center",
          justifyContent: "center",
          display: "flex",
          fontSize: 12,
          fontWeight: 600,
          lineHeight: "12px",
          borderRadius: 4,
          color: status ? "#FF7B5E" : "#5285E9",
          backgroundColor: status ? "#FFEDEC" : "#D2E1FF",
        }}
      >
        {status ? "公開 " : "非公開"}
      </div>
    </div>
  );
};

const Styled = styled(Presentation)`
  display: flex;
`;

const CellWebTestStatusRender: React.FC<ICellLeadStatusRender> = (props) => {
  const { status } = props.value;

  return <Styled status={status} />;
};

export default CellWebTestStatusRender;
