import React from "react";
import { useTicketInbox } from "../hooks/useTicketInbox";

type StatusLabelProps = {
    status: ContactTicketStatus
}

const StatusLabel:React.FC<StatusLabelProps> = ({ status }) => {

    const { getColors, getLabel } = useTicketInbox()
    const colors = getColors(status)

    return (
        <span
            style={{
                padding: "6px",
                width: 84,
                height: 26,
                alignItems: "center",
                justifyContent: "center",
                display: "flex",
                fontSize: 12,
                lineHeight: "12px",
                fontWeight: "600",
                borderRadius: 4,
                color: colors?.color,
                backgroundColor: colors?.backgroundColor
            }}
        >
            { getLabel(status) }
        </span>
    )
}

export default StatusLabel
