import styled from "@emotion/styled";
import { Box } from "@mantine/core";
import Link from "next/link";
import React from "react";
import { propcolors } from "styles/colors";
import { useTicketInbox } from "../hooks/useTicketInbox";

type InboxMenuProps = {
    active: InboxStatusType
}

const InboxMenu: React.FC<InboxMenuProps> = ({active}) => {
    const path = '/contact/ticket'
    const { getMenuList } = useTicketInbox()

    return (
        <Menu>
            {
                getMenuList().map((inbox) =>
                    <Box className={`inbox-link-item ${active  === inbox.value ? 'active' : ''} `} key={inbox.value}>
                        <Link href={`${path}?inbox=${inbox.value}`} className='inbox-link-item-a'>{inbox.label}</Link>
                    </Box>
                )
            }
        </Menu>
    )
}

const Menu = styled.div`
  width: 250px;
  height: 100%;
  border-top: solid 1px ${propcolors.gray[200]};
  border-right: solid 1px ${propcolors.gray[200]};
  .inbox-link-item {
    padding: 25px 20px;
    border-bottom: solid 1px ${propcolors.gray[200]};
    &-a {
      width: 100%;
      display: inline-block;
      color: ${propcolors.black} !important;
    }
  }
  .inbox-link-item.active {
    background-color: ${propcolors.black};
  }
  .inbox-link-item.active > .inbox-link-item-a {
    color: ${propcolors.white} !important;
  }
`

export default InboxMenu
