import { <PERSON>, Button, Card, Flex, Text } from "@mantine/core";
import IconUserPartner from "../../../../public/icons/user-icon.svg";
import React from "react";
import { propcolors } from "../../../../styles/colors";
import { Comment, fetchCommentFile } from "../ticket-api";
import styled from "@emotion/styled";
import { RiAttachment2, RiDownloadFill } from "@remixicon/react"
import { formatDateTime } from "../../../../utils/func/formatDateString";
import Image from "next/image";

type CommentCardProps = {
    comment: Comment,
    ticketId: string
}

const CommentCard:React.FC<CommentCardProps> = ({comment, ticketId}) => {
    const handleDownload = async () => {
        if (!comment.file_id) return
        const fileUrl = await fetchCommentFile(ticketId, comment.file_id)
        window.open(fileUrl, '_blank')
    }

    return (
        <Style>
            <Card className="comment-card">
                <Flex>
                    <Box className='comment-card-icon-wrapper'>
                        {
                            comment.user_icon_path?
                                <Image src={comment.user_icon_path} style={{borderRadius: '40px'}} width={40} height={40} alt={comment.user_name} />:
                                <IconUserPartner className="imgSpan"/>
                        }
                    </Box>
                    <Box>
                        <Flex className='comment-card-name-wrapper'>
                            <Text className='comment-card-name'>{comment.user_name}</Text>
                            <Text className='gray-text'>{formatDateTime(comment.sent_at)}</Text>
                        </Flex>
                        <Box className="comment-card-text">
                            <Text>{comment.comment}</Text>
                        </Box>
                        <Box className='file-download'>
                            {
                                comment.file_name &&
                                <Button
                                    size='xs'
                                    variant='outline'
                                    leftIcon={<RiAttachment2 size={15}/>}
                                    rightIcon={<RiDownloadFill size={15}/>}
                                    onClick={handleDownload}
                                >
                                    {comment.file_name}
                                </Button>
                            }
                        </Box>
                    </Box>
                </Flex>
            </Card>
        </Style>
    )
}

const Style = styled.div`
  .gray-text {
    color: ${propcolors.gray[500]};
  }
  .comment-card {
    margin-bottom: 20px;
  }
  .comment-card-icon-wrapper {
    margin-right: 20px;
  }
  .comment-card-name-wrapper {
    margin-bottom: 10px;
  }
  .comment-card-name {
    margin-right: 10px; 
  }
  .comment-card-text {
    white-space: pre-wrap;
  }
  .file-download {
    margin-top: 5px;
  }
  .mantine-Button-root {
    border-color: ${propcolors.gray[500]};
  }
`

export default CommentCard
