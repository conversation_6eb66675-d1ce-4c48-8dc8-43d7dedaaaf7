import { <PERSON>, Button, Card, <PERSON>lex, Skeleton, Text } from "@mantine/core";
import IconUserPartner from "../../../../public/icons/user-icon.svg";
import React from "react";
import { FormAnswer } from "../ticket-api";
import { RiAttachment2, RiDownloadFill } from "@remixicon/react";
import styled from "@emotion/styled";
import { propcolors } from "../../../../styles/colors";

type FormAnswerCardProps = {
  loading: boolean;
  name: string;
  firstContactAt: string;
  formAnswers: FormAnswer[];
};

const FormAnswerCard: React.FC<FormAnswerCardProps> = ({
  loading,
  name,
  firstContactAt,
  formAnswers,
}) => {
  return (
    <Style>
      <Skeleton visible={loading}>
        <Card>
          <Flex>
            <Box mr={20}>
              <IconUserPartner className="imgSpan" />
            </Box>
            <Box>
              <Flex mb={10}>
                <Text mr={10}>{name}</Text>
                <Text className="gray-text">{firstContactAt}</Text>
              </Flex>
              {formAnswers.map((formAnswer, index) => (
                <Box mb={20} key={index}>
                  <Text className="gray-text">{formAnswer.label}</Text>
                  {formAnswer.column_type === "FILE" &&
                  typeof formAnswer.answer === "string" ? (
                    <Button
                      size="xs"
                      variant="outline"
                      style={{
                        borderColor: `${propcolors.gray[500]} !important`,
                      }}
                      leftIcon={<RiAttachment2 size={15} />}
                      rightIcon={<RiDownloadFill size={15} />}
                      onClick={() => {
                        typeof formAnswer.answer === "string" &&
                          window.open(formAnswer.answer, "_blank");
                      }}
                    >
                      ファイル
                    </Button>
                  ) : (
                    <Text style={{ whiteSpace: "pre-wrap" }}>
                      {Array.isArray(formAnswer.answer)
                        ? formAnswer.answer.join(",  ")
                        : formAnswer.answer}
                    </Text>
                  )}
                </Box>
              ))}
            </Box>
          </Flex>
        </Card>
      </Skeleton>
    </Style>
  );
};

const Style = styled.div`
  .mantine-Button-root {
    border-color: ${propcolors.gray[500]} !important;
    margin-top: 5px;
  }
`;

export default FormAnswerCard;
