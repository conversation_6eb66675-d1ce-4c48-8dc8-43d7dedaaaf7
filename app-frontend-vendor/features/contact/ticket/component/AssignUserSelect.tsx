import React, { forwardRef, useEffect, useState } from "react";
import { Box, Select } from "@mantine/core";
import { AssigneeUser, fetchVendorUsers } from "../ticket-api";

type AssignUserSelectProps = {
  value: string | null;
  onChange: (value: string | null) => void;
};

type SelectOption = {
  label: string;
  value: string;
};

const assigneeTypes: AssigneeTypes[] = [
  { value: "ALL_VENDOR_USER", label: "全ベンダーユーザ" },
  { value: "ADMIN_GROUP", label: "管理者" },
  { value: "MEMBER_GROUP", label: "一般" },
  { value: "NONE", label: "担当者なし" },
];

const AssignUserSelect: React.FC<AssignUserSelectProps> = ({
  value,
  onChange,
}) => {
  const [selectValue, setStatus] = useState(value);
  const [selectOptions, setSelectOptions] =
    useState<SelectOption[]>(assigneeTypes);
  useEffect(() => {
    setStatus(value);
  }, [value]);

  useEffect(() => {
    (async () => {
      const vendorUsers = await fetchVendorUsers();
      const addOptions: SelectOption[] = vendorUsers.map(
        (user: AssigneeUser) => {
          return { label: user.name, value: user.id.toString() };
        }
      );
      setSelectOptions([...selectOptions, ...addOptions]);
    })();
  }, []);
  return (
    <Select
      data={selectOptions}
      label={"担当者"}
      itemComponent={SelectItem}
      searchable
      nothingFound="一致する選択肢がありません"
      value={selectValue}
      onChange={onChange}
    />
  );
};

const SelectItem = forwardRef<HTMLDivElement, TicketType>(
  ({ value, label, ...others }: TicketType, ref) => (
    <div ref={ref} {...others}>
      <Box py={5}>{label}</Box>
    </div>
  )
);

export default AssignUserSelect;
