import { Box, Select } from "@mantine/core";
import React, { forwardRef, useEffect, useState } from "react";
import StatusLabel from "./StatusLable";
import styled from "@emotion/styled";
import { propcolors } from "../../../../styles/colors";
import { useTicketInbox } from "../hooks/useTicketInbox";

type StatusSelectProps = {
    value: ContactTicketStatus | null
    onChange: (value: string| null) => void
}

const StatusSelect: React.FC<StatusSelectProps> = ({value, onChange}) => {
    const [status, setStatus] = useState(value)
    const { getStatusList } = useTicketInbox()
    useEffect(() => {
        setStatus(value)
    }, [value])
    return (
        <SelectStyle>
            <Select
                data={getStatusList()}
                label={'ステータス'}
                itemComponent={SelectItem}
                value={status}
                onChange={onChange}
            />
        </SelectStyle>
    )
}

const SelectItem = forwardRef<HTMLDivElement, TicketType>(
    ({ value, label, ...others }: TicketType, ref) => (
        <div ref={ref} {...others}>
            <Box py={5}>
                <StatusLabel status={value as ContactTicketStatus} />
            </Box>
        </div>
    )
)

const SelectStyle = styled.div`
  
  .mantine-Select-input {
    padding: 6px;
    align-items: center;
    justify-content: center;
    display: flex;
    font-size: 12px;
    line-height: 12px;
    font-weight: 600;
    border-radius: 4px;
  }
  .mantine-Select-input[value="未対応"] {
    color: #FF569D;
    background-color: #FFE5F9;
  }
  .mantine-Select-input[value="対応中"] {
    color: #13BFB5;
    background-color: #CBF4EF;
  }
  .mantine-Select-input[value="保留中"] {
    color: #1AABF4;
    background-color: #D7EEFE;
  }
  .mantine-Select-input[value="解決済み"] {
    color: #8992A0;
    background-color: #E8EAED;
  }

  .mantine-Select-item[data-selected] {
    background-color: ${propcolors.gray[150]} !important;
  }

  .mantine-Select-item[data-selected]:hover {
    background-color: ${propcolors.gray[150]} !important;
  }
  
`

export default StatusSelect
