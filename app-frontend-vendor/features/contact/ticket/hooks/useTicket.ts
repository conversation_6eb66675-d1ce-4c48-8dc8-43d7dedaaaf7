import { useCallback, useState } from "react";
import {
  Comment,
  fetchTicket,
  FormAnswer,
  registerComment,
  updateTicket,
} from "../ticket-api";
import { formatDateTime } from "../../../../utils/func/formatDateString";

export const useTicket = () => {
  const [ticketId, setTicketId] = useState("");
  const [title, setTitle] = useState("");
  const [contactPartnerName, setContactPartnerName] = useState("");
  const [contactPartnerUserName, setContactPartnerUserName] = useState("");
  const [firstContactAt, setFirstContactAt] = useState("");
  const [formAnswers, setFormAnswers] = useState<FormAnswer[]>([]);
  const [status, setStatus] = useState<ContactTicketStatus | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [vendorAssigneeType, setVendorAssigneeType] =
    useState<ContactFromVendorAssigneeType | null>(null);
  const [vendorAssigneeUserId, setVendorAssigneeUserId] = useState<
    number | null
  >(null);

  const getTicket = useCallback(async (id: string) => {
    const ticketResponse = await fetchTicket(id);
    setTicketId(ticketResponse.ticket_id);
    setTitle(ticketResponse.title);
    setContactPartnerName(ticketResponse.contact_partner_name);
    setContactPartnerUserName(ticketResponse.contact_partner_user_name);
    setFirstContactAt(formatDateTime(ticketResponse.first_contact_at));
    setFormAnswers(ticketResponse.form_answers);
    setStatus(ticketResponse.status);
    setComments(ticketResponse.comments);
    setVendorAssigneeType(ticketResponse.vendor_assignee_type);
    setVendorAssigneeUserId(ticketResponse.vendor_assignee_user_id);
  }, []);

  const changeStatus = useCallback(
    async (status: ContactTicketStatus) => {
      await updateTicket(ticketId, { status });
      setStatus(status);
    },
    [ticketId]
  );

  const changeAssignUser = useCallback(
    async (
      userId: number | null,
      assigneeType: ContactFromVendorAssigneeType
    ) => {
      await updateTicket(ticketId, {
        vendor_assignee_type: assigneeType,
        vendor_assignee_user_id: userId,
      });
      setVendorAssigneeType(assigneeType);
      setVendorAssigneeUserId(userId);
    },
    [ticketId]
  );

  const postComment = useCallback(
    async (comment: string, file: File | null) => {
      await registerComment(ticketId, { comment, file });
      const ticketResponse = await fetchTicket(ticketId);
      setComments(ticketResponse.comments);
    },
    [ticketId]
  );

  return {
    ticketId,
    title,
    contactPartnerName,
    contactPartnerUserName,
    firstContactAt,
    formAnswers,
    status,
    comments,
    vendorAssigneeType,
    vendorAssigneeUserId,
    getTicket,
    changeStatus,
    changeAssignUser,
    postComment,
  };
};

export default useTicket;
