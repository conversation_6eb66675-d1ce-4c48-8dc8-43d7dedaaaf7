import { useCallback } from "react";

type TicketStatus = {
    label: string,
    value: ContactTicketStatus
    apiSearchParam: FetchTicketListRequest,
    labelColor: { color: string, backgroundColor: string, }
}

type Inbox = {
    label: string,
    value: InboxType,
    apiSearchParam: FetchTicketListRequest
}

const ticketStatuses: TicketStatus[] = [
    { label: '未対応', value: 'UN_RESOLVED', apiSearchParam: { status: 'UN_RESOLVED' }, labelColor: { color: '#FF569D', backgroundColor: '#FFE5F9' } },
    { label: '対応中', value: 'IN_PROGRESS', apiSearchParam: { status: 'IN_PROGRESS' }, labelColor: { color: '#13BFB5', backgroundColor: '#CBF4EF' }  },
    { label: '保留中', value: 'PENDING', apiSearchParam: { status: 'PENDING' }, labelColor: { color: '#1AABF4', backgroundColor: '#D7EEFE' }  },
    { label: '解決済み', value: 'RESOLVED', apiSearchParam: { status: 'RESOLVED'} , labelColor: { color: '#8992A0', backgroundColor: '#E8EAED' } },
]

const inboxes: Inbox[] = [
    { label: 'すべて', value: 'ALL', apiSearchParam: {} },
    { label: '新着', value: 'NEW', apiSearchParam: { is_latest: 1 } },
    { label: '自分が担当', value: 'MY_TICKET', apiSearchParam: { is_assigned: 1 } },
    { label: '自分が未解決', value: 'UNRESOLVED_BY_ME', apiSearchParam: { status: 'UN_RESOLVED', is_assigned: 1 } }
]

export const useTicketInbox = () => {

    const getSearchParam = useCallback((inboxType: string) => {
        const ticketStatus = ticketStatuses.find((ticketStatus) => ticketStatus.value === inboxType)
        const inbox = inboxes.find((inbox) => inbox.value === inboxType)
        return ticketStatus? ticketStatus.apiSearchParam : inbox?.apiSearchParam?? {}
    }, [])

    const getColors = useCallback((status: ContactTicketStatus) => {
        const ticketStatus = ticketStatuses.find((ticketStatus) => ticketStatus.value === status)
        return ticketStatus?.labelColor
    }, [])

    const getLabel = useCallback((status: ContactTicketStatus) => {
        const ticketStatus = ticketStatuses.find((ticketStatus) => ticketStatus.value === status)
        return ticketStatus?.label
    }, [])

    const getMenuList = useCallback(() => {
         const inboxList = [
            inboxes[0],
            inboxes[1],
            ...ticketStatuses,
            inboxes[2],
            inboxes[3],
        ]
        return inboxList.map((inbox) => {
            return {label: inbox.label, value: inbox.value}
        })
    }, [])

    const getStatusList = useCallback(() => {
        return ticketStatuses.map((status) => {
            return { label: status.label, value: status.value }
        })
    }, [])

    return { getSearchParam, getColors, getLabel, getMenuList, getStatusList }
}