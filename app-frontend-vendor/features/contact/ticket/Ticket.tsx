import styled from "@emotion/styled";
import { CustomBreadcrumb } from "../../../components/breadcrumb";
import {
  Box,
  Button,
  Center,
  FileButton,
  Skeleton,
  Text,
  Textarea,
} from "@mantine/core";
import { propcolors } from "../../../styles/colors";
import { useRouter } from "next/router";
import React, { useEffect, useMemo, useRef, useState } from "react";
import StatusSelect from "./component/StatusSelect";
import { Comment, fetchVendorUsers } from "./ticket-api";
import CommentCard from "./component/CommentCard";
import AssignUserSelect from "./component/AssignUserSelect";
import FormAnswerCard from "./component/FormAnswerCard";
import useTicket from "./hooks/useTicket";
import { notifications } from "@mantine/notifications";
import { getApiErrorMessage } from "../../../utils/values/errorMessages";
import IconNotiFailed from "../../../public/icons/icon-noti-failed.svg";
import { CONTACT_ACCEPT_FILES } from "../../../constants/commonSetting";

const Presentation: React.FC = () => {
  const router = useRouter();
  const query = router.query;
  const [loading, setLoading] = useState(true);
  const bottomRef = useRef<HTMLDivElement>(null);
  const [isPosting, setIsPosting] = useState(false);
  const {
    ticketId,
    title,
    contactPartnerName,
    contactPartnerUserName,
    firstContactAt,
    formAnswers,
    status,
    comments,
    vendorAssigneeType,
    vendorAssigneeUserId,
    getTicket,
    changeStatus,
    changeAssignUser,
    postComment,
  } = useTicket();

  // 送信部分
  const [newTextComment, setNewComment] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [file, setFile] = useState<File | null>(null);

  const selectedAssignUserValue = useMemo(() => {
    if (vendorAssigneeUserId && vendorAssigneeType === "MEMBER") {
      return vendorAssigneeUserId.toString();
    }
    return vendorAssigneeType;
  }, [vendorAssigneeType, vendorAssigneeUserId]);
  const href = useMemo(() => {
    const { keyword, inbox } = router.query;
    let url = "/contact/ticket";
    const params = new URLSearchParams();
    if (keyword) params.append("keyword", keyword as string);
    if (inbox) params.append("inbox", inbox as string);

    const queryString = params.toString();
    return queryString ? `${url}?${queryString}` : url;
  }, [router.query]);

  useEffect(() => {
    (async () => {
      const id = query.id;
      if (!router.isReady || typeof id !== "string") return;

      try {
        await Promise.all([fetchVendorUsers(), getTicket(id)]);
      } catch (e: any) {
        await notifications.show({
          title: "エラーが発生しました。",
          message: getApiErrorMessage(e.response.data.message),
          icon: <IconNotiFailed />,
        });
        router.push("/contact/ticket").then();
        return;
      }
      setLoading(false);
    })();
  }, [router, query]);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      const newHeight = textareaRef.current.scrollHeight;
      if (newHeight > 380) {
        textareaRef.current.style.height = "380px";
      } else {
        textareaRef.current.style.height = `${newHeight}px`;
      }
      textareaRef.current.style.overflowY = "hidden";
    }
  }, [newTextComment]);

  const handleStatusChange = async (status: string | null) => {
    await changeStatus(status as ContactTicketStatus);
  };

  const handleAssignUserChange = async (value: string | null) => {
    // 個別指定の場合
    if (!Number.isNaN(Number(value))) {
      await changeAssignUser(Number(value), "MEMBER");
      return;
    }
    // 全体,管理者,一般など
    await changeAssignUser(null, value as ContactFromVendorAssigneeType);
  };

  const handlePostComment = async () => {
    setIsPosting(true);
    await postComment(newTextComment, file);
    setNewComment("");
    handleResetFile();
    bottomRef.current?.scrollIntoView({
      behavior: "smooth",
      block: "end",
    });
    setIsPosting(false);
  };

  const handleChangeFile = (file: File | null) => {
    setFile(file);
  };

  const handleResetFile = () => {
    setFile(null);
  };

  return (
    <>
      <header>
        <Box>
          <Button
            variant="subtle"
            color="red"
            radius="xs"
            size="lg"
            onClick={() => {
              const { keyword, inbox } = router.query;
              router.push({
                pathname: "/contact/ticket",
                query: { keyword, inbox },
              });
            }}
          >
            ←戻る
          </Button>
        </Box>
        <CustomBreadcrumb
          title="問い合わせ"
          list={[{ title: "受信箱", href: href }, { title: title }]}
        />
      </header>
      <Box className="main">
        <Box className="ticket-main">
          <Box className="ticket-main-header">
            {loading ? (
              <Skeleton w={300} height={10} />
            ) : (
              <Text className="ticket-id-text">問い合わせID: {ticketId}</Text>
            )}
            <SeparateLine />
            {loading ? (
              <Skeleton w={300} height={10} />
            ) : (
              <Text className="agency-name-text">
                パートナー企業名: {contactPartnerName}
              </Text>
            )}
            <SeparateLine />
            {loading ? (
              <Skeleton w={100} height={10} />
            ) : (
              <Text className="gray-text">{firstContactAt}</Text>
            )}
          </Box>

          <Box className="ticket-main-content">
            <Box className="ticket-form-answer">
              <FormAnswerCard
                loading={loading}
                name={contactPartnerUserName}
                firstContactAt={firstContactAt}
                formAnswers={formAnswers}
              />
              <DashedLine />
              <Center mb={30}>
                <Text className="gray-text">
                  {comments.length
                    ? "以下にコメントが続きます"
                    : "まだコメントはありません"}
                </Text>
              </Center>
              {comments.map((comment: Comment, index) => (
                <CommentCard
                  comment={comment}
                  ticketId={ticketId}
                  key={index}
                />
              ))}
              <Box className="ticket-main-bottom-index" ref={bottomRef} />
            </Box>

            <Box className="ticket-comment-input-box">
              <Textarea
                placeholder="コメントを入力する"
                radius="md"
                size="md"
                value={newTextComment}
                ref={textareaRef}
                onChange={(e) => setNewComment(e.currentTarget.value)}
                style={{
                  overflow: "hidden",
                  resize: "none",
                  maxHeight: "380px",
                }}
              />
              <Box className="ticket-comment-input-file-box">
                {file && (
                  <FileLabel>
                    {file.name}
                    <Button onClick={handleResetFile} />
                  </FileLabel>
                )}
              </Box>
              <Box className="ticket-comment-input-button-box">
                <FileButton
                  onChange={handleChangeFile}
                  accept={CONTACT_ACCEPT_FILES}
                  disabled={!!file || isPosting}
                >
                  {(props) => (
                    <Button
                      variant="default"
                      color="dark"
                      {...props}
                      disabled={!!file || isPosting}
                    >
                      ファイルを添付する
                    </Button>
                  )}
                </FileButton>
                <Button
                  color="dark"
                  onClick={handlePostComment}
                  disabled={!newTextComment && !file}
                  loading={isPosting}
                >
                  送信する
                </Button>
              </Box>
            </Box>
          </Box>
        </Box>
        <Box>
          <Center className="status-control-header">
            <Text className="status-control-header-text">ステータス変更</Text>
          </Center>
          <Box className="status-control-main">
            <StatusSelect value={status} onChange={handleStatusChange} />
            <AssignUserSelect
              value={selectedAssignUserValue}
              onChange={handleAssignUserChange}
            />
          </Box>
        </Box>
      </Box>
    </>
  );
};

const Styled = styled.div`
  header {
    display: flex;
    justify-content: start;
    border-top: solid 1px ${propcolors.gray[200]};
    border-bottom: solid 1px ${propcolors.gray[200]};
  }
  .main {
    display: flex;
    height: 85%;
  }
  .ticket-id-text {
    font-weight: 600;
    margin-left: 20px;
  }
  .agency-name-text {
    font-weight: 600;
  }
  .ticket-main {
    height: 100%;
    width: 85%;
    position: relative;
  }
  .ticket-main-header {
    display: flex;
    align-items: center;
    height: 50px;
    padding: 24px 10px;
    border-bottom: solid 1px ${propcolors.gray[200]};
  }
  .ticket-main-content {
    background-color: ${propcolors.gray[200]};
    height: 100%;
    padding: 20px;
    position: relative;
  }
  .ticket-form-answer {
    background-color: ${propcolors.gray[200]};
    height: calc(100vh - 220px);
    overflow-y: scroll;
  }
  .ticket-main-bottom-index {
    margin-top: 350px;
  }
  .ticket-comment-input-box {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    box-sizing: border-box;
    padding: 20px 10px 0 10px;
    margin: 0 20px;
    background-color: white;
    border-top: solid 1px ${propcolors.gray[200]};
  }
  .ticket-comment-input-file-box {
    margin-top: 15px;
    min-height: 30px;
  }
  .ticket-comment-input-button-box {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    margin-bottom: 15px;
  }
  .gray-text {
    color: ${propcolors.gray[500]};
  }
  .status-control {
    height: 100%;
    width: 15%;
  }
  .status-control-header {
    background-color: ${propcolors.blackColor};
    height: 50px;
  }
  .status-control-header-text {
    color: ${propcolors.white};
  }
  .status-control-main {
    padding: 10px 20px;
  }
`;
const SeparateLine = styled.span`
  width: 1px;
  height: 24px;
  display: inline-block;
  margin: 0 24px;
  vertical-align: middle;
  background-color: ${propcolors.gray[200]};
`;

const DashedLine = styled.hr`
  margin-top: 25px;
  border-top: 1px dashed #aaa;
`;

const FileLabel = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 15px 8px;
  height: 26px;
  font-size: 12px;
  line-height: 12px;
  font-weight: 600;
  border-radius: 4px;
  border: solid 1px ${propcolors.gray[300]};
`;

export const Ticket: React.FC = () => {
  return (
    <Styled>
      <Presentation />
    </Styled>
  );
};
