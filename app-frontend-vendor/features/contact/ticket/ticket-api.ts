import {ax} from "utils/axios"


/**
 * API /api/v1/contact/ticket/list
 * @param request
 */
export const fetchTicketList = async (request: FetchTicketListRequest) => {
    const searchParams = new URLSearchParams(request as Record<string, any>)
    const response = await ax.get(`/api/v1/contact/ticket/list?${searchParams.toString()}`)

    const users = response.data.users
    const vendorPartners = response.data.vendor_linked_partners

    const tickets = response.data.ticket_list.map((ticket: any) => {
        const vendorAssigneeUser = users.find((user: any) => ticket.vendor_assignee_user_id !== null && user.vendor_user_id === ticket.vendor_assignee_user_id)
        const contactPartnerUser = users.find((user: any) => ticket.contact_partner_user_id !== null && user.partner_user_id === ticket.contact_partner_user_id)
        const contactPartner = vendorPartners.find((partner: any) => ticket.contact_vendor_linked_partner_id !== null && partner.vendor_linked_partner_id === ticket.contact_vendor_linked_partner_id)
        return {
            ...ticket,
            vendor_assignee_user_name: vendorAssigneeUser?.name?? null,
            vendor_assignee_user_icon: vendorAssigneeUser?.icon_path?? null,
            contact_partner_name: contactPartner?.company_name?? null,
            contact_partner_icon: contactPartner?.icon_path?? null,
            contact_partner_user_name: contactPartnerUser?.name?? null,
            contact_partner_user_icon: contactPartnerUser?.icon_path?? null
        }
    })
    return { tickets, total: response.data.meta.total }
}


/**
 * API GET /contact/ticket/${id}でチケット情報を取得する
 * @param id
 * @return TicketResponse
 */
export const fetchTicket = async (id: string) => {
    const response = await ax.get(`/api/v1/contact/ticket/${id}`)
    const ticket = response.data.contact_ticket
    const comments = response.data.contact_ticket.comments
    const users = response.data.contact_ticket.users
    ticket.comments = comments.map((comment: any) => {
        const vendorAssigneeUser = users.find((user: any) => comment.vendor_user_id !== null && user.vendor_user_id === comment.vendor_user_id)
        const contactPartnerUser = users.find((user: any) => comment.partner_user_id !== null && user.partner_user_id === comment.partner_user_id)
        if (vendorAssigneeUser) {
            return {...comment, user_icon_path: vendorAssigneeUser.icon_path, user_name: vendorAssigneeUser.name}
        }
        if (contactPartnerUser) {
            return {...comment, user_icon_path: contactPartnerUser.icon_path, user_name: contactPartnerUser.name}
        }
    })
    return ticket as Ticket
}

/**
 * API PUT /contact/ticket/${id}でチケット情報を更新する
 * @param id
 * @param payload
 */
export const updateTicket = async (id: string, payload: UpdateTicketRequest) => {
    await ax.put(`/api/v1/contact/ticket/${id}`, payload)
}

/**
 * API /vendor_usersからuser情報を取得する
 * @return AssigneeUser[]
 */
export const fetchVendorUsers = async (): Promise<AssigneeUser[]> => {
    const response = await ax.get('/api/v1/vendor_users')
    return response.data.map((user: any) => {
        return {id: user.id, name: user.name}
    })
}

/**
 * POST /contact/ticket/${id}でコメントを登録する
 * @param id
 * @param payload
 */
export const registerComment = async (id: string, payload: PostCommentRequest) => {
    const formData = new FormData()
    formData.append('comment', payload.comment)
    formData.append('file', payload.file?? '')
    await ax.postForm(`/api/v1/contact/ticket/${id}`, formData)
}

/**
 * GET /contact/ticket/${ticketId}/file/${fileId}でファイルURLを取得する
 * @param ticketId
 * @param fileId
 */
export const fetchCommentFile = async (ticketId: string, fileId: string): Promise<string> => {
    const response = await ax.get(`/api/v1/contact/ticket/${ticketId}/file/${fileId}`)
    return response.data.data.file_path
}

export type Ticket = {
    comments: Comment[]
    contact_partner_name: string
    contact_partner_user_id: number
    contact_partner_user_name: string
    contact_vendor_linked_partner_id: number | null
    first_contact_at: string
    form_answers: FormAnswer[]
    status: ContactTicketStatus
    ticket_id: string
    title: string
    users: any[]
    vendor_assignee_type: ContactFromVendorAssigneeType
    vendor_assignee_user_id: number | null
    vendor_assignee_user_name: string | null
}

export type FormAnswer = {
    answer: string | []
    column_type: string
    is_enabled: boolean
    label: string
}

export type Comment = {
    comment: string
    file_id: string | null
    file_name: string | null
    partner_user_id: number | null
    sent_at: string
    vendor_user_id: number | null
    user_icon_path: string | null
    user_name: string
}

export type UpdateTicketRequest = {
    status?: ContactTicketStatus
    vendor_assignee_type?: ContactFromVendorAssigneeType
    vendor_assignee_user_id?: number | null
}

export type PostCommentRequest = {
    comment: string
    file?: File | null
}

export type AssigneeUser = {
    id: number
    name: string
}

