import styled from "@emotion/styled";
import { Box, Button, Flex, Group, Input, Select } from "@mantine/core";
import type { ColDef, GridApi } from "ag-grid-community";
import type { AgGridReact } from "ag-grid-react";
import { CustomBreadcrumb } from "components/breadcrumb";
import { SEARCH_CONTACT_PLACEHOLDER } from "constants/ja/common";
import { useRouter } from "next/router";
import IconSearch from "public/icons/search-line.svg";
import type React from "react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { usePrevPagePath } from "utils/recoil/navigation/navigationState";
import {
  useInboxTypeState,
  usePageState,
  useSetInboxTypeState,
  useSetPageState,
} from "utils/recoil/ticket/TicketState";
import LoadingSkeleton from "../../../components/LoadingSkeleton";
import { CommonListLayout } from "../../../components/layouts/commonListLayout";
import { SSRAGGrid } from "../../../components/SSRGrid";
import { propcolors } from "../../../styles/colors";
import InboxMenu from "./component/InboxMenu";
import {
  CompanyNameWithIcon,
  UserNameWithIcon,
} from "./component/NameWithIcon";
import StatusLabel from "./component/StatusLable";
import { useTicketInbox } from "./hooks/useTicketInbox";
import { fetchTicketList } from "./ticket-api";

const PER_PAGE = 20;

const Presentation: React.FC = () => {
  const router = useRouter();
  const { getSearchParam, getMenuList } = useTicketInbox();
  const [activeInboxType, setActiveInboxType] =
    useState<InboxStatusType>("ALL");
  const [isLoading, setIsLoading] = useState(true);
  const gridRef = useRef<AgGridReact>();
  const [gridApi, setGridApi] = useState<GridApi>();
  const onGridReady = (params: any) => {
    setGridApi(params.api);
  };
  const [keyword, setKeyword] = useState("");
  const prevPage = usePrevPagePath();
  const setInboxTypeState = useSetInboxTypeState();
  const setPageState = useSetPageState();
  const pageState = usePageState();
  const inboxTypeState = useInboxTypeState();
  const handleRowClick = (id: string) => {
    const currentQuery = router.query;
    const inboxType = router.query.inbox;
    if (typeof inboxType === "string") {
      setInboxTypeState(inboxType);
    }
    if (inboxType === undefined) {
      setInboxTypeState("ALL");
    }
    if (gridRef.current && gridRef.current.api) {
      setPageState(gridRef.current.api.paginationGetCurrentPage());
    } else {
      setPageState(0);
    }
    router.push({
      pathname: `/contact/ticket/${id}`,
      query: currentQuery,
    });
  };
  const overViewColumns: ColDef[] = [
    {
      field: "title",
      headerName: "フォーム名",
      resizable: true,
    },
    {
      field: "summary",
      headerName: "問い合わせ内容",
      resizable: true,
    },
    {
      field: "status",
      headerName: "ステータス",
      cellRenderer: (params: any) => {
        return params.data?.status ? (
          StatusLabel({ status: params.data?.status })
        ) : (
          <></>
        );
      },
      cellStyle: { display: "flex", alignItems: "center" },
      resizable: true,
    },
    {
      field: "first_contact_at",
      headerName: "受付日時",
      resizable: true,
    },
    {
      field: "vendor_assignee_user_name",
      headerName: "担当者",
      cellRenderer: (params: any) => {
        return params.data?.vendor_assignee_user_name ? (
          <UserNameWithIcon
            userName={params.data.vendor_assignee_user_name}
            thumbnail={params.data.vendor_assignee_user_icon}
          />
        ) : (
          <></>
        );
      },
      cellClass: "cell-partner-name",
      resizable: true,
    },
    {
      field: "contact_partner_name",
      headerName: "パートナー企業",
      cellRenderer: (params: any) => {
        return params.data?.contact_partner_name ? (
          <CompanyNameWithIcon
            userName={params.data.contact_partner_name}
            thumbnail={params.data.contact_partner_icon}
          />
        ) : (
          <></>
        );
      },
      cellClass: "cell-partner-name",
      resizable: true,
      width: 300,
    },
    {
      field: "contact_partner_user_name",
      headerName: "パートナー担当者",
      cellRenderer: (params: any) => {
        return params.data?.contact_partner_user_name ? (
          <UserNameWithIcon
            userName={params.data.contact_partner_user_name}
            thumbnail={params.data.contact_partner_user_icon}
          />
        ) : (
          <></>
        );
      },
      cellClass: "cell-partner-name",
      resizable: true,
      width: 300,
    },
  ];

  const showNoRow = useCallback(() => {
    if (gridRef.current) {
      gridRef.current.api.showNoRowsOverlay();
    }
  }, []);

  const handleChangeSelect = (value: string) => {
    const href = `/contact/ticket?inbox=${value}&keyword=${keyword}`;
    router.push(href).then();
  };

  useEffect(() => {
    if (router.query.keyword) {
      setKeyword(router.query.keyword as string);
    }
  }, [router.query.keyword]);

  const handleSearch = () => {
    const currentPath = router.pathname;
    const currentQuery = { ...router.query, keyword };
    router.push({
      pathname: currentPath,
      query: currentQuery,
    });
  };

  useEffect(() => {
    if (router.query.keyword) {
      setKeyword(router.query.keyword as string);
    }
    const inboxType = router.query.inbox;
    if (typeof inboxType === "string") {
      setActiveInboxType(inboxType as InboxStatusType);
    }
  }, [router.query]);

  const buildQueryString = useCallback((): FetchTicketListRequest => {
    const inboxType = router.query.inbox;
    const keyword = router.query.keyword;

    let searchParams = {};
    if (typeof inboxType === "string") {
      searchParams = getSearchParam(inboxType);
    }

    if (typeof keyword === "string" && keyword.trim() !== "") {
      return { ...searchParams, keyword: keyword.trim() };
    } else {
      return searchParams;
    }
  }, [router.query.inbox, router.query.keyword, getSearchParam]);

  const datasource = useMemo(
    () => ({
      getRows: async (params: any) => {
        setIsLoading(true);
        const page = params.endRow / PER_PAGE;
        const searchPrams = buildQueryString();

        const { tickets, total } = await fetchTicketList({
          page,
          ...searchPrams,
        });

        if (total === 0) {
          showNoRow();
          params.successCallback(null, 0);
        } else {
          params.successCallback(tickets, total);

          if (pageState && prevPage === "/contact/ticket/[id]") {
            const inboxQuery = router.query.inbox;
            if (
              inboxQuery === activeInboxType &&
              gridRef.current?.api &&
              pageState !== 0
            ) {
              gridRef.current.api.paginationGoToPage(Number(pageState));
              setPageState(0);
            }
          }
        }

        setIsLoading(false);
      },
    }),
    [
      buildQueryString,
      showNoRow,
      setPageState,
      pageState,
      prevPage,
      router.query.inbox,
      activeInboxType,
    ],
  );

  useEffect(() => {
    gridApi?.setDatasource(datasource);
  }, [gridApi, datasource]);

  useEffect(() => {
    if (!router.isReady) return;
    if (
      inboxTypeState &&
      prevPage === "/contact/ticket/[id]" &&
      router.pathname === "/contact/ticket"
    ) {
      router.replace({
        pathname: router.pathname,
        query: { ...router.query, inbox: inboxTypeState },
      });
      setInboxTypeState("");
    }

    const inboxType = router.query.inbox;

    if (typeof inboxType === "string") {
      setActiveInboxType(inboxType as InboxStatusType);
      return;
    }
  }, [router, router.query, prevPage, inboxTypeState, setInboxTypeState]);

  return (
    <CommonListLayout>
      {isLoading && <LoadingSkeleton />}
      <header>
        <CustomBreadcrumb title="問い合わせ" list={[{ title: "受信箱" }]} />
      </header>
      <Flex>
        <InboxMenu active={activeInboxType} />
        <Box w="100%">
          <Group
            p={20}
            css={{ borderTop: `1px solid ${propcolors.gray[200]}` }}
          >
            <Select
              data={getMenuList()}
              value={activeInboxType}
              onChange={handleChangeSelect}
              w={350}
            />
            <Input
              icon={<IconSearch style={{ color: "red" }} />}
              placeholder={SEARCH_CONTACT_PLACEHOLDER}
              onChange={(event) => setKeyword(event.target.value)}
              value={keyword}
              style={{ width: 300 }}
            />
            <Button onClick={handleSearch}>検索</Button>
          </Group>
          <TicketTable>
            <SSRAGGrid
              className="grid-height-full"
              columnDefs={overViewColumns}
              onGridReady={onGridReady}
              gridRef={gridRef}
              onRowSelected={(event) => handleRowClick(event.data.ticket_id)}
              paginationPageSize={PER_PAGE}
            />
          </TicketTable>
        </Box>
      </Flex>
    </CommonListLayout>
  );
};

const Styled = styled(Presentation)``;

const FilterSelect = styled.div`
  border-top: solid 1px ${propcolors.gray[200]};
  padding: 10px;
`;

const TicketTable = styled.div`
  .grid-height-full {
    height: calc(100vh - 220px);
    width: 100%;
    .ag-root-wrapper-body {
      min-height: 500px;
    }
  }
`;

export const TicketList: React.FC = () => {
  return <Styled />;
};
