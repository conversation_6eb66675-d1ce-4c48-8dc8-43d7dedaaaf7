import {
  Box,
  Button,
  Center,
  Select,
  Skeleton,
  Text,
  Title,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import { RiCheckboxCircleLine } from "@remixicon/react";
import Image from "next/image";
import { useRouter } from "next/router";
import type React from "react";
import { useEffect, useState } from "react";
import { CustomBreadcrumb } from "../../../components/breadcrumb";
import IconNotiFailed from "../../../public/icons/icon-noti-failed.svg";
import { propcolors } from "../../../styles/colors";
import FormColumn from "./components/FormColumn";
import type { RequestFormColumn } from "./contact-form-api";
import useContactForm from "./hooks/useContactForm";

const Presentation = () => {
  const router = useRouter();
  const form = useForm<Record<string, unknown>>({
    initialValues: {
      team_member_id: null,
    },
  });
  const [loading, setLoading] = useState(true);
  const [formId, setFormId] = useState("");
  const [answered, setAnswered] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPartnerId, setSelectedPartnerId] = useState<string | null>(
    null,
  );
  const [selectedTeamMemberId, setSelectedTeamMemberId] = useState<
    string | null
  >(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const {
    title,
    description,
    formColumns,
    getContactForm,
    answerForm,
    logoPath,
    partnerOptions,
    userOptionsByPartner,
  } = useContactForm();

  useEffect(() => {
    (async () => {
      if (!router.isReady) return;
      const id = router.query.id;
      if (typeof id !== "string") return;
      await getContactForm(id);
      setFormId(id);
      if (router.query.mode === "preview") {
        setIsPreviewMode(true);
      } else {
        setIsPreviewMode(false);
      }
      setLoading(false);
    })();
  }, [router, router.query, getContactForm]);

  useEffect(() => {
    setInitForm();
  }, []);

  const setInitForm = () => {
    const newInitialValues: Record<string, unknown> = {};
    formColumns
      .filter((column) =>
        [
          "STRING",
          "TEXT",
          "DATE",
          "DATETIME_LOCAL",
          "CHECKBOX",
          "RADIO",
          "FILE",
        ].includes(column.column_type),
      )
      .forEach((column) => {
        newInitialValues[column.uuid] =
          column.column_type === "CHECKBOX" ? [] : null;
      });
    newInitialValues.team_member_id = null;
    form.setValues(newInitialValues);
    setSelectedPartnerId(null);
    setSelectedTeamMemberId(null);
  };

  const hasError = () => {
    let hasErrorFlag = false;
    const values = form.values;
    formColumns.forEach((column) => {
      const value = values[column.uuid];
      if (column.is_required) {
        if (column.column_type === "CHECKBOX") {
          if (!Array.isArray(value) || value.length === 0) {
            hasErrorFlag = true;
            form.setFieldError(column.uuid, "必須項目です");
          }
        } else if (column.column_type === "FILE") {
          if (
            value === null ||
            value === undefined ||
            (typeof value === "object" &&
              value !== null &&
              !(value as { base64?: string }).base64)
          ) {
            hasErrorFlag = true;
            form.setFieldError(column.uuid, "必須項目です");
          }
        } else {
          if (
            value === null ||
            value === undefined ||
            String(value).trim() === ""
          ) {
            hasErrorFlag = true;
            form.setFieldError(column.uuid, "必須項目です");
          }
        }
      }
    });
    if (!selectedTeamMemberId) {
      hasErrorFlag = true;
      form.setFieldError("team_member_id", "必須項目です");
    }
    return hasErrorFlag;
  };

  const showError = () => {
    notifications.show({
      title: "エラーが発生しました",
      message: "入力内容をご確認ください",
      icon: <IconNotiFailed />,
    });
  };

  const handleSubmit = async () => {
    if (isPreviewMode) {
      notifications.show({
        title: "プレビューモード",
        message: "プレビューモードではフォームを送信できません。",
        color: "blue",
        icon: <IconNotiFailed />,
      });
      return;
    }

    if (hasError()) {
      showError();
      return;
    }

    const formValues = form.values;
    const answerableColumnTypes = [
      "STRING",
      "TEXT",
      "DATE",
      "DATETIME_LOCAL",
      "RADIO",
      "CHECKBOX",
      "FILE",
    ];
    const answerPayload: RequestFormColumn[] = formColumns
      .filter((column) => answerableColumnTypes.includes(column.column_type))
      .map((column) => {
        const value = formValues[column.uuid];
        if (column.column_type === "RADIO") {
          return { uuid: column.uuid, value: value === null ? [] : [value] };
        }
        if (column.column_type === "FILE" && value) {
          const { base64, fileName } = value as {
            base64: string;
            fileName: string;
          };
          return {
            uuid: column.uuid,
            value: base64,
            ...(fileName && { file_name: fileName }),
          };
        }
        return { uuid: column.uuid, value: value };
      });

    setIsSubmitting(true);
    await answerForm(formId, answerPayload, selectedTeamMemberId);
    setAnswered(true);
    setIsSubmitting(false);
  };

  return (
    <>
      <Box
        css={{
          display: "flex",
          justifyContent: "start",
          borderTop: `solid 1px ${propcolors.gray[200]}`,
          borderBottom: `solid 1px ${propcolors.gray[200]}`,
        }}
      >
        <Box>
          <Button
            variant="subtle"
            color="red"
            radius="xs"
            size="lg"
            onClick={() => router.push("/contact/form")}
          >
            ←戻る
          </Button>
        </Box>
        <CustomBreadcrumb title={title} />
      </Box>
      <Box>
        <Skeleton visible={loading} mih={"80vh"}>
          <Box
            css={{
              backgroundColor: propcolors.gray[200],
              paddingTop: "10px",
              paddingBottom: "10px",
            }}
          >
            {logoPath && (
              <Center>
                <Image
                  height={1000}
                  width={1000}
                  src={logoPath}
                  alt={"logo"}
                  style={{
                    objectFit: "contain",
                    width: "100%",
                    height: "4.5rem",
                  }}
                  quality={100}
                />
              </Center>
            )}
            <Box>
              <Title
                order={3}
                css={{
                  width: "100%",
                  fontSize: "24px",
                  lineHeight: "36px",
                  textAlign: "center",
                  borderRadius: "16px 16px 0",
                }}
              >
                {title}
              </Title>
            </Box>
          </Box>
          {answered ? (
            <Box
              css={{
                padding: "50px 150px 100px 150px",
                whiteSpace: "pre-wrap",
                display: "flex",
                flexDirection: "column",
                alignItems: "stretch",
                justifyContent: "center",
              }}
            >
              <Center>
                <RiCheckboxCircleLine
                  size={120}
                  color={propcolors.green[400]}
                />
              </Center>
              <Center>
                <Text>送信が完了いたしました</Text>
              </Center>
            </Box>
          ) : (
            <Box
              css={{
                padding: "50px 150px 100px 150px",
                width: "100%",
                display: "flex",
                justifyContent: "center",
              }}
            >
              <Box
                css={{
                  whiteSpace: "pre-wrap",
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "stretch",
                  justifyContent: "center",
                  gap: "3rem",
                  width: "636px",
                }}
              >
                {description && description !== "" && (
                  <Text
                    css={{
                      textAlign: "center",
                      fontWeight: 600,
                      whiteSpace: "pre-wrap",
                    }}
                  >
                    {description}
                  </Text>
                )}

                {!isPreviewMode && (
                  <>
                    {/* Partner Selection */}
                    <Select
                      label="共有先パートナー"
                      placeholder="パートナーを選択してください"
                      searchable
                      data={partnerOptions}
                      value={selectedPartnerId}
                      onChange={(value: string | null) => {
                        setSelectedPartnerId(value);
                        setSelectedTeamMemberId(null);
                        form.setFieldValue("team_member_id", null);
                      }}
                      clearable
                      required
                      error={
                        partnerOptions.length === 0
                          ? "共有可能なパートナーユーザが見つかりません。編集画面より公開範囲を設定してください。"
                          : undefined
                      }
                    />
                    {/* Team Member Selection */}
                    <Select
                      label="担当パートナーユーザ"
                      placeholder="パートナーユーザを選択してください"
                      searchable
                      data={
                        selectedPartnerId
                          ? userOptionsByPartner[selectedPartnerId] || []
                          : []
                      }
                      value={selectedTeamMemberId}
                      onChange={(value: string | null) => {
                        setSelectedTeamMemberId(value);
                        form.setFieldValue("team_member_id", value);
                      }}
                      clearable
                      required
                      disabled={Boolean(
                        !selectedPartnerId ||
                          partnerOptions.length === 0 || // Disable if no partners
                          (selectedPartnerId &&
                            (userOptionsByPartner[selectedPartnerId] || [])
                              .length === 0),
                      )}
                      error={
                        form.errors.team_member_id
                          ? String(form.errors.team_member_id)
                          : undefined
                      }
                    />
                  </>
                )}

                {formColumns.map((formColumn) => (
                  <FormColumn
                    formColumn={formColumn}
                    key={formColumn.uuid}
                    {...form.getInputProps(formColumn.uuid)}
                  />
                ))}
                <Box
                  css={{
                    display: "flex",
                    justifyContent: "center",
                    marginTop: "100px",
                  }}
                >
                  <Button
                    disabled={isSubmitting}
                    css={{
                      minWidth: "256px",
                      padding: "0 100px",
                      backgroundColor: propcolors.blackColor,
                    }}
                    radius="xl"
                    onClick={handleSubmit}
                    loading={isSubmitting}
                  >
                    送信
                  </Button>
                </Box>
              </Box>
            </Box>
          )}
        </Skeleton>
      </Box>
    </>
  );
};

export const FormAnswer: React.FC = () => {
  return <Presentation />;
};
