import { Box, Checkbox, Radio, Text, Textarea, TextInput } from "@mantine/core";
import { DateInput, DateTimePicker } from "@mantine/dates";
import Image from "next/image";
import type React from "react";
import { FileUpload } from "../../../../components/FileUpload";
import { propcolors } from "../../../../styles/colors";
import { convertFileToBase64 } from "../../../../utils/func/base64Converter";
import type { ContactFormColumn } from "../contact-form-api";

// フォームで使用される値の型定義
type FormValue =
  | string
  | string[]
  | Date
  | null
  | { base64: string; fileName: string };

type FormColumnProps = {
  formColumn: ContactFormColumn;
  value?: FormValue;
  onChange?: (value: FormValue) => void;
  error?: string | null;
};

const Column: React.FC<FormColumnProps> = ({
  formColumn,
  value,
  onChange,
  error,
}) => {
  const fileChange = async (file: File | null) => {
    if (onChange && file) {
      const base64 = await convertFileToBase64(file);
      onChange({
        base64: typeof base64 === "string" ? base64 : "",
        fileName: file.name,
      });
    }
  };
  switch (formColumn.column_type) {
    case "STRING":
      return (
        <TextInput
          label={formColumn.label}
          required={formColumn.is_required}
          value={(value as string) ?? ""}
          onChange={(event) => onChange?.(event.currentTarget.value)}
        />
      );

    case "TEXT":
      return (
        <Box>
          <Textarea
            label={formColumn.label}
            required={formColumn.is_required}
            minRows={8}
            value={(value as string) ?? ""}
            onChange={(event) => onChange?.(event.currentTarget.value)}
            error={error}
          />
        </Box>
      );
    case "DATE":
      return (
        <Box>
          <DateInput
            label={formColumn.label}
            valueFormat="YYYY年MM月DD日"
            locale="ja"
            monthLabelFormat="YYYY年MM月"
            yearLabelFormat="YYYY年"
            monthsListFormat="MM"
            yearsListFormat="YYYY"
            firstDayOfWeek={0}
            required={formColumn.is_required}
            value={value as Date | null}
            onChange={onChange}
            error={error}
          />
        </Box>
      );
    case "DATETIME_LOCAL":
      return (
        <Box>
          <DateTimePicker
            label={formColumn.label}
            valueFormat="YYYY年MM月DD日 HH:mm"
            locale="ja"
            firstDayOfWeek={0}
            required={formColumn.is_required}
            value={value as Date | null}
            onChange={onChange}
            error={error}
          />
        </Box>
      );
    case "CHECKBOX":
      return (
        <Box
          css={{
            display: "flex",
            flexDirection: "column",
            alignItems: "stretch",
            justifyContent: "flex-start",
            gap: "0.5rem",
          }}
        >
          <Checkbox.Group
            label={formColumn.label}
            withAsterisk={formColumn.is_required}
            styles={{
              root: {
                display: "flex",
                flexDirection: "column",
                alignItems: "stretch",
                justifyContent: "flex-start",
                gap: "0.5rem",
              },
            }}
            value={value as string[]}
            onChange={onChange}
            error={error}
          >
            {formColumn.select_options?.map((option) => (
              <Checkbox
                label={option.key}
                value={option.value}
                key={option.value}
                styles={{
                  body: {
                    paddingLeft: "5px",
                  },
                }}
              />
            ))}
          </Checkbox.Group>
        </Box>
      );
    case "RADIO":
      return (
        <Box
          css={{
            display: "flex",
            flexDirection: "column",
            alignItems: "stretch",
            justifyContent: "flex-start",
            gap: "0.5rem",
          }}
        >
          <Radio.Group
            label={formColumn.label}
            withAsterisk={formColumn.is_required}
            styles={{
              root: {
                display: "flex",
                flexDirection: "column",
                alignItems: "stretch",
                justifyContent: "flex-start",
                gap: "0.5rem",
              },
            }}
            name={formColumn.uuid}
            value={value as string}
            onChange={onChange}
            error={error}
          >
            {formColumn.select_options?.map((option) => (
              <Radio
                label={option.key}
                value={option.value}
                key={option.value}
                styles={{
                  body: {
                    paddingLeft: "5px",
                  },
                }}
              />
            ))}
          </Radio.Group>
        </Box>
      );
    case "FILE":
      return (
        <Box>
          <Text css={{ marginBottom: "-5px" }}>
            {formColumn.label}
            {formColumn.is_required && (
              <span css={{ paddingLeft: "3px", color: propcolors.red[500] }}>
                *
              </span>
            )}
          </Text>
          <FileUpload
            label={""}
            onFileChange={fileChange}
            key={formColumn.uuid}
            msgErrorFile={error}
          />
        </Box>
      );
    case "DESCRIPTION":
      return (
        <Box>
          <Text>{formColumn.description}</Text>
        </Box>
      );
    case "LOGO":
      return (
        <>
          {formColumn.logo_path && (
            <Image
              width={10000}
              height={10000}
              style={{ width: "100%", height: "auto" }}
              src={formColumn.logo_path}
              alt={"logo"}
              quality={100}
            />
          )}
        </>
      );
    default:
      throw new Error("invalid column_type");
  }
};

const FormColumn: React.FC<FormColumnProps> = ({
  formColumn,
  value,
  onChange,
  error,
}) => {
  return (
    <Column
      formColumn={formColumn}
      value={value}
      onChange={onChange}
      error={error}
    />
  );
};

export default FormColumn;
