import { useCallback, useState } from "react";
import {
  ContactFormColumn,
  fetchAllLinkedPartners,
  fetchContactForm,
  fetchFormPermissions,
  FormPermissionUser,
  postForm,
  RequestFormColumn,
} from "../contact-form-api";
import { useMemo } from "react";

const useContactForm = () => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [formColumns, setFormColumns] = useState<ContactFormColumn[]>([]);
  const [logoPath, setLogoPath] = useState<string | null>();
  const [allLinkedPartners, setAllLinkedPartners] = useState<
    { id: number; name: string }[]
  >([]);
  const [sharedUsers, setSharedUsers] = useState<FormPermissionUser[]>([]);

  const getContactForm = useCallback(async (id: string) => {
    const contactForm = await fetchContactForm(id);
    setTitle(contactForm.title);
    setDescription(contactForm.description);
    setFormColumns(contactForm.form_columns);
    setLogoPath(contactForm.logo_path);

    const [permissions, linkedPartners] = await Promise.all([
      fetchFormPermissions(id),
      fetchAllLinkedPartners(),
    ]);

    setAllLinkedPartners(linkedPartners);
    setSharedUsers(
      permissions.shared_partner_users.filter((user) => user.has_access)
    );
  }, []);

  const answerForm = useCallback(
    async (
      id: string,
      formColumns: RequestFormColumn[],
      teamMemberId: string | null
    ) => {
      const numericTeamMemberId = teamMemberId
        ? parseInt(teamMemberId, 10)
        : null;
      await postForm(id, formColumns, numericTeamMemberId);
    },
    []
  );

  const partnerOptions = useMemo(() => {
    const partnerIdsInSharedUsers = new Set(
      sharedUsers.map((user) => user.partner_id)
    );
    return allLinkedPartners
      .filter((partner) => partnerIdsInSharedUsers.has(partner.id))
      .map((partner) => ({
        value: String(partner.id),
        label: partner.name || `ID: ${partner.id}`,
      }));
  }, [allLinkedPartners, sharedUsers]);

  const userOptionsByPartner = useMemo(() => {
    const options: { [key: string]: { value: string; label: string }[] } = {};
    const selectablePartnerIds = new Set(
      partnerOptions.map((p) => parseInt(p.value, 10))
    );

    selectablePartnerIds.forEach((partnerId) => {
      options[String(partnerId)] = sharedUsers
        .filter((user) => user.partner_id === partnerId)
        .map((user) => ({
          value: String(user.id),
          label: user.name || `ID: ${user.id}`,
        }));
    });
    return options;
  }, [partnerOptions, sharedUsers]);

  return {
    title,
    description,
    formColumns,
    getContactForm,
    answerForm,
    logoPath,
    partnerOptions,
    userOptionsByPartner,
  };
};

export default useContactForm;
