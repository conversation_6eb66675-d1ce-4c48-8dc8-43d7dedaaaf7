import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { CustomBreadcrumb } from "components/breadcrumb";
import { propcolors } from "styles/colors";
import LoadingSkeletonTable from "components/LoadingSkeleton/table";
import { useDashboardData } from "hooks/useDashboardData";
import { TabNames } from "constants/tab_names";

type PageInfo = {
  title: string;
  key: "dashboard" | "analysis" | "";
};

const QuickSightEmbed: React.FC = () => {
  const { dashboardUrl, isValidating, mutate } = useDashboardData();
  const [pageInfo, setPageInfo] = useState<PageInfo>({
    title: "",
    key: "",
  });
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const router = useRouter();

  // 現在のページに応じてタイトルとキーを設定
  useEffect(() => {
    setPageInfo({ title: TabNames.DASHBOARD_VIEW, key: "dashboard" });

    // タブ切り替え時にデータをリフレッシュ
    const refreshData = async () => {
      setIsRefreshing(true);
      try {
        await mutate();
      } finally {
        setIsRefreshing(false);
      }
    };

    refreshData();
  }, [router.pathname, mutate]);

  const embedUrl = dashboardUrl;

  return (
    <div>
      <header>
        <CustomBreadcrumb
          title={pageInfo.title}
          styleWrapper={{
            padding: "22px 24px",
            borderBottom: `1px solid ${propcolors.border}`,
            borderTop: `1px solid ${propcolors.border}`,
          }}
        />
      </header>
      <div>
        {isValidating || isRefreshing ? (
          <LoadingSkeletonTable />
        ) : embedUrl ? (
          <iframe
            title="QuickSight Dashboard"
            src={embedUrl}
            width="100%"
            height="710"
            allowFullScreen
          />
        ) : (
          <div>URLが取得できません。</div>
        )}
      </div>
    </div>
  );
};

export default QuickSightEmbed;
